<?php
/**
 * Analytics Dashboard Manager Class
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles analytics dashboard functionality including widget management,
 * real-time data updates, and interactive dashboard creation.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Analytics_Dashboard_Manager {
    
    /**
     * Initialize the Analytics Dashboard Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_save_analytics_dashboard', array(__CLASS__, 'save_dashboard'));
        add_action('wp_ajax_dab_load_analytics_dashboard', array(__CLASS__, 'load_dashboard'));
        add_action('wp_ajax_dab_delete_analytics_dashboard', array(__CLASS__, 'delete_dashboard'));
        add_action('wp_ajax_dab_add_dashboard_widget', array(__CLASS__, 'add_widget'));
        add_action('wp_ajax_dab_update_dashboard_widget', array(__CLASS__, 'update_widget'));
        add_action('wp_ajax_dab_delete_dashboard_widget', array(__CLASS__, 'delete_widget'));
        add_action('wp_ajax_dab_get_dashboard_data', array(__CLASS__, 'get_dashboard_data'));
        add_action('wp_ajax_nopriv_dab_get_dashboard_data', array(__CLASS__, 'get_dashboard_data'));
    }
    
    /**
     * Create database tables for analytics dashboards
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Analytics dashboards table
        $dashboards_table = $wpdb->prefix . 'dab_analytics_dashboards';
        $sql_dashboards = "CREATE TABLE $dashboards_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            layout_config longtext,
            theme_config longtext,
            access_config longtext,
            created_by int(11) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_public tinyint(1) DEFAULT 0,
            is_default tinyint(1) DEFAULT 0,
            PRIMARY KEY (id),
            KEY created_by (created_by),
            KEY is_public (is_public)
        ) $charset_collate;";
        
        // Dashboard widgets table
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        $sql_widgets = "CREATE TABLE $widgets_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            dashboard_id int(11) NOT NULL,
            widget_type varchar(50) NOT NULL,
            widget_title varchar(255) NOT NULL,
            widget_config longtext,
            position_x int(11) DEFAULT 0,
            position_y int(11) DEFAULT 0,
            width int(11) DEFAULT 4,
            height int(11) DEFAULT 3,
            widget_order int(11) DEFAULT 0,
            is_visible tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY dashboard_id (dashboard_id),
            KEY widget_type (widget_type),
            KEY widget_order (widget_order)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_dashboards);
        dbDelta($sql_widgets);
    }
    
    /**
     * Save analytics dashboard
     */
    public static function save_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_analytics_dashboards';
        
        $dashboard_id = isset($_POST['dashboard_id']) ? intval($_POST['dashboard_id']) : 0;
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $layout_config = wp_json_encode($_POST['layout_config']);
        $theme_config = wp_json_encode($_POST['theme_config']);
        $access_config = wp_json_encode($_POST['access_config']);
        $is_public = isset($_POST['is_public']) ? 1 : 0;
        $is_default = isset($_POST['is_default']) ? 1 : 0;
        
        $data = array(
            'name' => $name,
            'description' => $description,
            'layout_config' => $layout_config,
            'theme_config' => $theme_config,
            'access_config' => $access_config,
            'is_public' => $is_public,
            'is_default' => $is_default
        );
        
        if ($dashboard_id > 0) {
            // Update existing dashboard
            $result = $wpdb->update($dashboards_table, $data, array('id' => $dashboard_id));
        } else {
            // Create new dashboard
            $data['created_by'] = get_current_user_id();
            $result = $wpdb->insert($dashboards_table, $data);
            $dashboard_id = $wpdb->insert_id;
        }
        
        // If this is set as default, remove default from other dashboards
        if ($is_default) {
            $wpdb->update(
                $dashboards_table,
                array('is_default' => 0),
                array('id' => array('!=', $dashboard_id))
            );
        }
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Dashboard saved successfully',
                'dashboard_id' => $dashboard_id
            ));
        } else {
            wp_send_json_error('Failed to save dashboard');
        }
    }
    
    /**
     * Load analytics dashboard
     */
    public static function load_dashboard() {
        $dashboard_id = intval($_GET['dashboard_id']);
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_analytics_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $dashboard = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $dashboards_table WHERE id = %d",
            $dashboard_id
        ));
        
        if (!$dashboard) {
            wp_send_json_error('Dashboard not found');
            return;
        }
        
        // Check permissions
        if (!$dashboard->is_public && !current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        // Get widgets
        $widgets = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $widgets_table WHERE dashboard_id = %d ORDER BY widget_order ASC",
            $dashboard_id
        ));
        
        // Decode JSON fields
        $dashboard->layout_config = json_decode($dashboard->layout_config, true);
        $dashboard->theme_config = json_decode($dashboard->theme_config, true);
        $dashboard->access_config = json_decode($dashboard->access_config, true);
        
        foreach ($widgets as &$widget) {
            $widget->widget_config = json_decode($widget->widget_config, true);
        }
        
        $dashboard->widgets = $widgets;
        
        wp_send_json_success($dashboard);
    }
    
    /**
     * Delete analytics dashboard
     */
    public static function delete_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $dashboard_id = intval($_POST['dashboard_id']);
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_analytics_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        // Delete widgets first
        $wpdb->delete($widgets_table, array('dashboard_id' => $dashboard_id));
        
        // Delete dashboard
        $result = $wpdb->delete($dashboards_table, array('id' => $dashboard_id));
        
        if ($result !== false) {
            wp_send_json_success('Dashboard deleted successfully');
        } else {
            wp_send_json_error('Failed to delete dashboard');
        }
    }
    
    /**
     * Add dashboard widget
     */
    public static function add_widget() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $dashboard_id = intval($_POST['dashboard_id']);
        $widget_type = sanitize_text_field($_POST['widget_type']);
        $widget_title = sanitize_text_field($_POST['widget_title']);
        $widget_config = wp_json_encode($_POST['widget_config']);
        $position_x = intval($_POST['position_x']);
        $position_y = intval($_POST['position_y']);
        $width = intval($_POST['width']);
        $height = intval($_POST['height']);
        
        $data = array(
            'dashboard_id' => $dashboard_id,
            'widget_type' => $widget_type,
            'widget_title' => $widget_title,
            'widget_config' => $widget_config,
            'position_x' => $position_x,
            'position_y' => $position_y,
            'width' => $width,
            'height' => $height,
            'widget_order' => self::get_next_widget_order($dashboard_id)
        );
        
        $result = $wpdb->insert($widgets_table, $data);
        
        if ($result !== false) {
            $widget_id = $wpdb->insert_id;
            wp_send_json_success(array(
                'message' => 'Widget added successfully',
                'widget_id' => $widget_id
            ));
        } else {
            wp_send_json_error('Failed to add widget');
        }
    }
    
    /**
     * Update dashboard widget
     */
    public static function update_widget() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $widget_id = intval($_POST['widget_id']);
        $widget_title = sanitize_text_field($_POST['widget_title']);
        $widget_config = wp_json_encode($_POST['widget_config']);
        $position_x = intval($_POST['position_x']);
        $position_y = intval($_POST['position_y']);
        $width = intval($_POST['width']);
        $height = intval($_POST['height']);
        $is_visible = isset($_POST['is_visible']) ? 1 : 0;
        
        $data = array(
            'widget_title' => $widget_title,
            'widget_config' => $widget_config,
            'position_x' => $position_x,
            'position_y' => $position_y,
            'width' => $width,
            'height' => $height,
            'is_visible' => $is_visible
        );
        
        $result = $wpdb->update($widgets_table, $data, array('id' => $widget_id));
        
        if ($result !== false) {
            wp_send_json_success('Widget updated successfully');
        } else {
            wp_send_json_error('Failed to update widget');
        }
    }
    
    /**
     * Delete dashboard widget
     */
    public static function delete_widget() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $widget_id = intval($_POST['widget_id']);
        
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $result = $wpdb->delete($widgets_table, array('id' => $widget_id));
        
        if ($result !== false) {
            wp_send_json_success('Widget deleted successfully');
        } else {
            wp_send_json_error('Failed to delete widget');
        }
    }
    
    /**
     * Get dashboard data for frontend display
     */
    public static function get_dashboard_data() {
        $dashboard_id = intval($_GET['dashboard_id']);
        $widget_id = isset($_GET['widget_id']) ? intval($_GET['widget_id']) : 0;
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_analytics_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $dashboard = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $dashboards_table WHERE id = %d",
            $dashboard_id
        ));
        
        if (!$dashboard) {
            wp_send_json_error('Dashboard not found');
            return;
        }
        
        // Check permissions
        if (!$dashboard->is_public && !current_user_can('read')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        if ($widget_id > 0) {
            // Get specific widget data
            $widget = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $widgets_table WHERE id = %d AND dashboard_id = %d",
                $widget_id,
                $dashboard_id
            ));
            
            if (!$widget) {
                wp_send_json_error('Widget not found');
                return;
            }
            
            try {
                $widget_data = self::generate_widget_data($widget);
                wp_send_json_success($widget_data);
            } catch (Exception $e) {
                wp_send_json_error('Failed to generate widget data: ' . $e->getMessage());
            }
        } else {
            // Get all dashboard data
            $widgets = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $widgets_table WHERE dashboard_id = %d AND is_visible = 1 ORDER BY widget_order ASC",
                $dashboard_id
            ));
            
            $dashboard_data = array(
                'dashboard' => $dashboard,
                'widgets' => array()
            );
            
            foreach ($widgets as $widget) {
                try {
                    $widget_data = self::generate_widget_data($widget);
                    $dashboard_data['widgets'][] = $widget_data;
                } catch (Exception $e) {
                    // Log error but continue with other widgets
                    error_log('Widget data generation failed: ' . $e->getMessage());
                }
            }
            
            wp_send_json_success($dashboard_data);
        }
    }
    
    /**
     * Generate data for a specific widget
     */
    private static function generate_widget_data($widget) {
        $widget_config = json_decode($widget->widget_config, true);
        $widget_type = $widget->widget_type;
        
        switch ($widget_type) {
            case 'chart':
                return self::generate_chart_widget_data($widget, $widget_config);
            case 'table':
                return self::generate_table_widget_data($widget, $widget_config);
            case 'metric':
                return self::generate_metric_widget_data($widget, $widget_config);
            case 'progress':
                return self::generate_progress_widget_data($widget, $widget_config);
            case 'list':
                return self::generate_list_widget_data($widget, $widget_config);
            default:
                throw new Exception('Unsupported widget type: ' . $widget_type);
        }
    }
    
    /**
     * Generate chart widget data
     */
    private static function generate_chart_widget_data($widget, $config) {
        if (empty($config['report_id'])) {
            throw new Exception('No report configured for chart widget');
        }
        
        // Load report and generate chart data
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $config['report_id']
        ));
        
        if (!$report) {
            throw new Exception('Report not found');
        }
        
        $result = DAB_Report_Builder::execute_report($report);
        $chart_config = DAB_Chart_Generator::build_chart_config(
            $config['chart_type'],
            $result['data'],
            $config
        );
        
        return array(
            'widget_id' => $widget->id,
            'widget_type' => 'chart',
            'widget_title' => $widget->widget_title,
            'chart_config' => $chart_config,
            'position' => array(
                'x' => $widget->position_x,
                'y' => $widget->position_y,
                'width' => $widget->width,
                'height' => $widget->height
            )
        );
    }
    
    /**
     * Generate table widget data
     */
    private static function generate_table_widget_data($widget, $config) {
        if (empty($config['table_id'])) {
            throw new Exception('No table configured for table widget');
        }
        
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $config['table_id']
        ));
        
        if (!$table) {
            throw new Exception('Table not found');
        }
        
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        
        // Get fields
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
            $config['table_id']
        ));
        
        // Build query with limits
        $limit = isset($config['limit']) ? intval($config['limit']) : 10;
        $order_by = isset($config['order_by']) ? esc_sql($config['order_by']) : 'id';
        $order_direction = isset($config['order_direction']) ? esc_sql($config['order_direction']) : 'DESC';
        
        $data = $wpdb->get_results(
            "SELECT * FROM $data_table ORDER BY $order_by $order_direction LIMIT $limit"
        );
        
        return array(
            'widget_id' => $widget->id,
            'widget_type' => 'table',
            'widget_title' => $widget->widget_title,
            'fields' => $fields,
            'data' => $data,
            'position' => array(
                'x' => $widget->position_x,
                'y' => $widget->position_y,
                'width' => $widget->width,
                'height' => $widget->height
            )
        );
    }
    
    /**
     * Generate metric widget data
     */
    private static function generate_metric_widget_data($widget, $config) {
        if (empty($config['table_id']) || empty($config['field'])) {
            throw new Exception('Table and field must be configured for metric widget');
        }
        
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $config['table_id']
        ));
        
        if (!$table) {
            throw new Exception('Table not found');
        }
        
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        $field = esc_sql($config['field']);
        $aggregation = isset($config['aggregation']) ? $config['aggregation'] : 'count';
        
        switch ($aggregation) {
            case 'count':
                $result = $wpdb->get_var("SELECT COUNT(*) FROM $data_table");
                break;
            case 'sum':
                $result = $wpdb->get_var("SELECT SUM($field) FROM $data_table");
                break;
            case 'avg':
                $result = $wpdb->get_var("SELECT AVG($field) FROM $data_table");
                break;
            case 'min':
                $result = $wpdb->get_var("SELECT MIN($field) FROM $data_table");
                break;
            case 'max':
                $result = $wpdb->get_var("SELECT MAX($field) FROM $data_table");
                break;
            default:
                $result = $wpdb->get_var("SELECT COUNT(*) FROM $data_table");
        }
        
        return array(
            'widget_id' => $widget->id,
            'widget_type' => 'metric',
            'widget_title' => $widget->widget_title,
            'value' => $result,
            'aggregation' => $aggregation,
            'position' => array(
                'x' => $widget->position_x,
                'y' => $widget->position_y,
                'width' => $widget->width,
                'height' => $widget->height
            )
        );
    }
    
    /**
     * Get next widget order for dashboard
     */
    private static function get_next_widget_order($dashboard_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $max_order = $wpdb->get_var($wpdb->prepare(
            "SELECT MAX(widget_order) FROM $widgets_table WHERE dashboard_id = %d",
            $dashboard_id
        ));
        
        return ($max_order ?? 0) + 1;
    }
    
    /**
     * Generate progress widget data
     */
    private static function generate_progress_widget_data($widget, $config) {
        // Implementation for progress widget
        return array(
            'widget_id' => $widget->id,
            'widget_type' => 'progress',
            'widget_title' => $widget->widget_title,
            'value' => 75, // Placeholder
            'max_value' => 100,
            'position' => array(
                'x' => $widget->position_x,
                'y' => $widget->position_y,
                'width' => $widget->width,
                'height' => $widget->height
            )
        );
    }
    
    /**
     * Generate list widget data
     */
    private static function generate_list_widget_data($widget, $config) {
        // Implementation for list widget
        return array(
            'widget_id' => $widget->id,
            'widget_type' => 'list',
            'widget_title' => $widget->widget_title,
            'items' => array(), // Placeholder
            'position' => array(
                'x' => $widget->position_x,
                'y' => $widget->position_y,
                'width' => $widget->width,
                'height' => $widget->height
            )
        );
    }
}
