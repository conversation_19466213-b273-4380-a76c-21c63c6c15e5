/**
 * Advanced Dropdown functionality for Database App Builder
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all advanced dropdowns
    const advancedDropdowns = document.querySelectorAll('.dab-advanced-dropdown-field');
    
    advancedDropdowns.forEach(function(dropdown) {
        initializeAdvancedDropdown(dropdown);
    });
    
    /**
     * Initialize a single advanced dropdown
     */
    function initializeAdvancedDropdown(dropdown) {
        const targetTableId = dropdown.getAttribute('data-target-table');
        const parentFieldId = dropdown.getAttribute('data-parent-field');
        const isSearchable = dropdown.getAttribute('data-searchable') === 'true';
        
        // If this dropdown depends on a parent field
        if (parentFieldId) {
            const parentField = document.getElementById(parentFieldId);
            
            if (parentField) {
                // Update options when parent field changes
                parentField.addEventListener('change', function() {
                    updateDropdownOptions(dropdown, parentField.value);
                });
                
                // Initial load of options based on parent value
                if (parentField.value) {
                    updateDropdownOptions(dropdown, parentField.value);
                }
            }
        }
        
        // Add search functionality if enabled
        if (isSearchable) {
            addSearchToDropdown(dropdown);
        }
    }
    
    /**
     * Update dropdown options based on parent field value
     */
    function updateDropdownOptions(dropdown, parentValue) {
        const targetTableId = dropdown.getAttribute('data-target-table');
        
        // Save current selection if any
        const currentValue = dropdown.value;
        
        // AJAX request to get filtered options
        const xhr = new XMLHttpRequest();
        xhr.open('POST', dab_vars.ajax_url, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    
                    if (response.success) {
                        // Clear existing options except the first placeholder
                        while (dropdown.options.length > 1) {
                            dropdown.remove(1);
                        }
                        
                        // Add new options
                        response.data.options.forEach(function(option) {
                            const optElement = document.createElement('option');
                            optElement.value = option.value;
                            optElement.textContent = option.label;
                            
                            // Restore selection if it still exists
                            if (option.value == currentValue) {
                                optElement.selected = true;
                            }
                            
                            dropdown.appendChild(optElement);
                        });
                        
                        // Trigger change event to cascade to child dropdowns
                        const event = new Event('change');
                        dropdown.dispatchEvent(event);
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }
            }
        };
        
        xhr.send(
            'action=dab_filter_dropdown' + 
            '&nonce=' + encodeURIComponent(dab_vars.nonce) + 
            '&parent_value=' + encodeURIComponent(parentValue) + 
            '&target_table=' + encodeURIComponent(targetTableId)
        );
    }
    
    /**
     * Add search functionality to dropdown
     */
    function addSearchToDropdown(dropdown) {
        // Create wrapper div
        const wrapper = document.createElement('div');
        wrapper.className = 'dab-searchable-dropdown-wrapper';
        dropdown.parentNode.insertBefore(wrapper, dropdown);
        wrapper.appendChild(dropdown);
        
        // Create search input
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'dab-dropdown-search';
        searchInput.placeholder = 'Search...';
        wrapper.insertBefore(searchInput, dropdown);
        
        // Add event listener for search
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            
            // Filter options
            Array.from(dropdown.options).forEach(function(option, index) {
                // Skip the first placeholder option
                if (index === 0) return;
                
                const optionText = option.textContent.toLowerCase();
                const match = optionText.includes(searchText);
                
                // Hide/show option
                option.style.display = match ? '' : 'none';
            });
        });
    }
});
