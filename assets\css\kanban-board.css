/**
 * Kanban Board Styles
 * 
 * Modern, responsive Kanban board styling for the Database App Builder plugin
 */

/* Kanban Container */
.dab-kanban-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Kanban Header */
.dab-kanban-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.dab-kanban-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.dab-kanban-controls {
  display: flex;
  gap: 10px;
}

/* Buttons */
.dab-btn {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.dab-btn-primary {
  background: #3498db;
  color: white;
}

.dab-btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.dab-btn-secondary {
  background: #95a5a6;
  color: white;
}

.dab-btn-secondary:hover {
  background: #7f8c8d;
}

.dab-btn-danger {
  background: #e74c3c;
  color: white;
}

.dab-btn-danger:hover {
  background: #c0392b;
}

.dab-btn-icon {
  padding: 6px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.dab-btn-icon:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

/* Kanban Board */
.dab-kanban-board {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
  min-height: 400px;
}

/* Kanban Columns */
.dab-kanban-column {
  flex: 0 0 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: 600px;
}

.dab-kanban-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ecf0f1;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.dab-column-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  border: none;
  background: transparent;
  outline: none;
  cursor: text;
}

.dab-column-title:focus {
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #3498db;
}

.dab-column-actions {
  display: flex;
  gap: 5px;
}

/* Kanban Cards Container */
.dab-kanban-cards {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  min-height: 200px;
}

/* Kanban Cards */
.dab-kanban-card {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid #bdc3c7;
}

.dab-kanban-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.dab-kanban-card.ui-sortable-helper {
  transform: rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Priority Colors */
.dab-kanban-card.dab-priority-low {
  border-left-color: #27ae60;
}

.dab-kanban-card.dab-priority-medium {
  border-left-color: #f39c12;
}

.dab-kanban-card.dab-priority-high {
  border-left-color: #e74c3c;
}

/* Card Labels */
.dab-card-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.dab-card-label {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  background: #3498db;
}

/* Card Content */
.dab-card-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.dab-card-description {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #7f8c8d;
  line-height: 1.4;
}

/* Card Footer */
.dab-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.dab-card-due-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #7f8c8d;
}

.dab-card-due-date.dab-overdue {
  color: #e74c3c;
  font-weight: 600;
}

.dab-card-assignee img {
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.dab-card-priority {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.dab-card-priority.dab-priority-low {
  background: #27ae60;
}

.dab-card-priority.dab-priority-medium {
  background: #f39c12;
}

.dab-card-priority.dab-priority-high {
  background: #e74c3c;
}

/* Modal Styles */
.dab-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dab-modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dab-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.dab-modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.dab-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.dab-modal-close:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.dab-modal-body {
  padding: 20px;
}

.dab-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #ecf0f1;
  background: #f8f9fa;
}

/* Form Styles */
.dab-form-field {
  margin-bottom: 15px;
}

.dab-form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
}

.dab-form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.dab-form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.dab-form-row {
  display: flex;
  gap: 15px;
}

.dab-form-field-half {
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dab-kanban-board {
    flex-direction: column;
  }
  
  .dab-kanban-column {
    flex: none;
    max-height: none;
  }
  
  .dab-kanban-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .dab-kanban-controls {
    justify-content: center;
  }
  
  .dab-form-row {
    flex-direction: column;
  }
}

/* Drag and Drop Styles */
.dab-kanban-cards.ui-sortable-placeholder {
  background: #ecf0f1;
  border: 2px dashed #bdc3c7;
  border-radius: 6px;
  height: 60px;
  margin-bottom: 10px;
}

.dab-kanban-column.dab-drop-target {
  background: #e8f6f3;
  border: 2px dashed #27ae60;
}

/* Loading States */
.dab-kanban-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #7f8c8d;
}

.dab-kanban-loading .spinner {
  margin-right: 10px;
}

/* Empty State */
.dab-kanban-empty {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.dab-kanban-empty .dashicons {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

/* Animation */
@keyframes dab-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dab-kanban-card {
  animation: dab-fade-in 0.3s ease;
}
