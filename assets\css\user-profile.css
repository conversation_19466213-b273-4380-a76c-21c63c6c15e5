/**
 * User Profile Styles
 * 
 * Styles for the user profile management interface
 */

/* Base Profile Styles */
.dab-user-profile {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Profile Header */
.dab-profile-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 40px;
    padding: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.dab-profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.dab-profile-info {
    display: flex;
    align-items: center;
    gap: 24px;
    position: relative;
    z-index: 1;
}

.dab-profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dab-profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dab-avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
}

.dab-avatar-placeholder .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
}

.dab-profile-details h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.dab-profile-email {
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    opacity: 0.9;
}

.dab-profile-role {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Badge Styles */
.dab-verified-badge, .dab-unverified-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-verified-badge {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.dab-unverified-badge {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.dab-profile-actions {
    position: relative;
    z-index: 1;
}

/* Profile Content */
.dab-profile-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.dab-profile-section {
    background: white;
    padding: 32px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.dab-section-title {
    margin: 0 0 24px 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 700;
    padding-bottom: 12px;
    border-bottom: 2px solid #f3f4f6;
    letter-spacing: -0.5px;
}

/* Form Styles */
.dab-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 0;
}

.dab-form-col-6 {
    flex: 1;
}

.dab-form-group {
    margin-bottom: 24px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.dab-form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.2s ease;
    background-color: #ffffff;
    box-sizing: border-box;
}

.dab-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: #ffffff;
}

.dab-form-help {
    display: block;
    margin-top: 6px;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
}

/* Button Styles */
.dab-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 24px;
    border: 2px solid transparent;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: none;
    position: relative;
    overflow: hidden;
}

.dab-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-color: #3b82f6;
}

.dab-btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    border-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.dab-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.dab-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.dab-btn-loading {
    display: none;
}

.dab-btn.loading .dab-btn-text {
    display: none;
}

.dab-btn.loading .dab-btn-loading {
    display: inline-flex;
    align-items: center;
}

/* Spinner */
.dab-spinner {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: dab-spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes dab-spin {
    to { transform: rotate(360deg); }
}

/* Alert Styles */
.dab-alert {
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
}

.dab-alert-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.dab-alert-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* Account Information */
.dab-account-info {
    display: grid;
    gap: 20px;
}

.dab-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;
}

.dab-info-item:last-child {
    border-bottom: none;
}

.dab-info-item label {
    font-weight: 600;
    color: #374151;
    margin: 0;
    font-size: 14px;
}

.dab-info-item span {
    color: #6b7280;
    font-size: 14px;
}

/* Status Badges */
.dab-status-badge {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-status-active {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.dab-status-inactive {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Auth Required Message */
.dab-auth-required {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    max-width: 400px;
    margin: 40px auto;
}

.dab-auth-required p {
    margin-bottom: 24px;
    color: #6b7280;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-user-profile {
        padding: 16px;
    }
    
    .dab-profile-header {
        flex-direction: column;
        gap: 24px;
        align-items: stretch;
        padding: 24px;
    }
    
    .dab-profile-info {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .dab-profile-details h1 {
        font-size: 24px;
    }
    
    .dab-profile-actions {
        display: flex;
        justify-content: center;
    }
    
    .dab-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .dab-profile-section {
        padding: 24px;
    }
    
    .dab-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .dab-auth-required {
        padding: 40px 24px;
        margin: 20px auto;
    }
}

@media (max-width: 480px) {
    .dab-profile-header {
        padding: 20px;
    }
    
    .dab-profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .dab-avatar-placeholder .dashicons {
        font-size: 36px;
        width: 36px;
        height: 36px;
    }
    
    .dab-profile-details h1 {
        font-size: 20px;
    }
    
    .dab-profile-section {
        padding: 20px;
    }
    
    .dab-form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .dab-btn {
        padding: 16px 24px;
        font-size: 16px;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dab-profile-section {
        background: #1f2937;
        border-color: #374151;
    }
    
    .dab-section-title {
        color: #f9fafb;
        border-color: #374151;
    }
    
    .dab-form-control {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .dab-form-control:focus {
        background-color: #374151;
        border-color: #3b82f6;
    }
    
    .dab-form-group label {
        color: #e5e7eb;
    }
    
    .dab-form-help {
        color: #9ca3af;
    }
    
    .dab-info-item {
        border-color: #374151;
    }
    
    .dab-info-item label {
        color: #e5e7eb;
    }
    
    .dab-info-item span {
        color: #9ca3af;
    }
}
