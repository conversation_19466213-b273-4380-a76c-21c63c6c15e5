<?php
/**
 * Calendar View Field Type
 * 
 * Interactive calendar interface for scheduling and event management
 * 
 * @package Database App Builder
 * @subpackage Modern UI Components
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Calendar_Field {
    
    /**
     * Initialize the Calendar field
     */
    public static function init() {
        add_filter('dab_field_types', array(__CLASS__, 'register_calendar_field_type'));
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_save_calendar_event', array(__CLASS__, 'save_calendar_event'));
        add_action('wp_ajax_nopriv_dab_save_calendar_event', array(__CLASS__, 'save_calendar_event'));
        add_action('wp_ajax_dab_get_calendar_events', array(__CLASS__, 'get_calendar_events'));
        add_action('wp_ajax_nopriv_dab_get_calendar_events', array(__CLASS__, 'get_calendar_events'));
        add_action('wp_ajax_dab_delete_calendar_event', array(__CLASS__, 'delete_calendar_event'));
        add_action('wp_ajax_nopriv_dab_delete_calendar_event', array(__CLASS__, 'delete_calendar_event'));
        
        // Create database tables
        add_action('init', array(__CLASS__, 'create_tables'));
    }
    
    /**
     * Register the Calendar field type
     */
    public static function register_calendar_field_type($field_types) {
        $field_types['calendar_view'] = __('Calendar View', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'dab-calendar-view',
            plugin_dir_url(__FILE__) . '../../assets/css/calendar-view.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'dab-calendar-view',
            plugin_dir_url(__FILE__) . '../../assets/js/calendar-view.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('dab-calendar-view', 'dabCalendarData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_calendar_nonce'),
            'i18n' => array(
                'addEvent' => __('Add Event', 'db-app-builder'),
                'editEvent' => __('Edit Event', 'db-app-builder'),
                'deleteEvent' => __('Delete Event', 'db-app-builder'),
                'allDay' => __('All Day', 'db-app-builder'),
                'startTime' => __('Start Time', 'db-app-builder'),
                'endTime' => __('End Time', 'db-app-builder'),
                'title' => __('Title', 'db-app-builder'),
                'description' => __('Description', 'db-app-builder'),
                'location' => __('Location', 'db-app-builder'),
                'category' => __('Category', 'db-app-builder'),
                'confirmDelete' => __('Are you sure you want to delete this event?', 'db-app-builder'),
                'saveSuccess' => __('Event saved successfully', 'db-app-builder'),
                'saveError' => __('Error saving event', 'db-app-builder'),
                'today' => __('Today', 'db-app-builder'),
                'month' => __('Month', 'db-app-builder'),
                'week' => __('Week', 'db-app-builder'),
                'day' => __('Day', 'db-app-builder'),
                'months' => array(
                    __('January', 'db-app-builder'),
                    __('February', 'db-app-builder'),
                    __('March', 'db-app-builder'),
                    __('April', 'db-app-builder'),
                    __('May', 'db-app-builder'),
                    __('June', 'db-app-builder'),
                    __('July', 'db-app-builder'),
                    __('August', 'db-app-builder'),
                    __('September', 'db-app-builder'),
                    __('October', 'db-app-builder'),
                    __('November', 'db-app-builder'),
                    __('December', 'db-app-builder')
                ),
                'days' => array(
                    __('Sunday', 'db-app-builder'),
                    __('Monday', 'db-app-builder'),
                    __('Tuesday', 'db-app-builder'),
                    __('Wednesday', 'db-app-builder'),
                    __('Thursday', 'db-app-builder'),
                    __('Friday', 'db-app-builder'),
                    __('Saturday', 'db-app-builder')
                ),
                'daysShort' => array(
                    __('Sun', 'db-app-builder'),
                    __('Mon', 'db-app-builder'),
                    __('Tue', 'db-app-builder'),
                    __('Wed', 'db-app-builder'),
                    __('Thu', 'db-app-builder'),
                    __('Fri', 'db-app-builder'),
                    __('Sat', 'db-app-builder')
                )
            )
        ));
    }
    
    /**
     * Create database tables for Calendar data
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Calendar events table
        $calendar_events_table = $wpdb->prefix . 'dab_calendar_events';
        $sql_events = "CREATE TABLE $calendar_events_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            field_id bigint(20) NOT NULL,
            record_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            start_date datetime NOT NULL,
            end_date datetime NOT NULL,
            all_day tinyint(1) DEFAULT 0,
            location varchar(255),
            category varchar(100),
            color varchar(7) DEFAULT '#3498db',
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY field_id (field_id),
            KEY record_id (record_id),
            KEY start_date (start_date),
            KEY end_date (end_date),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_events);
    }
    
    /**
     * Render the Calendar field in forms
     */
    public static function render_field($field, $value = '', $record_id = 0) {
        $field_id = 'dab-calendar-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        
        echo '<div class="dab-calendar-container" id="' . $field_id . '" data-field-id="' . $field->id . '" data-record-id="' . $record_id . '">';
        
        // Calendar header with controls
        echo '<div class="dab-calendar-header">';
        echo '<div class="dab-calendar-title">' . esc_html($field->field_label) . '</div>';
        echo '<div class="dab-calendar-controls">';
        
        // View switcher
        echo '<div class="dab-view-switcher">';
        echo '<button type="button" class="dab-view-btn active" data-view="month">' . __('Month', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-view-btn" data-view="week">' . __('Week', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-view-btn" data-view="day">' . __('Day', 'db-app-builder') . '</button>';
        echo '</div>';
        
        // Navigation
        echo '<div class="dab-calendar-nav">';
        echo '<button type="button" class="dab-nav-btn dab-prev-btn" title="' . __('Previous', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-arrow-left-alt2"></span>';
        echo '</button>';
        echo '<button type="button" class="dab-nav-btn dab-today-btn">' . __('Today', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-nav-btn dab-next-btn" title="' . __('Next', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-arrow-right-alt2"></span>';
        echo '</button>';
        echo '</div>';
        
        // Add event button
        echo '<button type="button" class="dab-btn dab-btn-primary dab-add-event">';
        echo '<span class="dashicons dashicons-plus-alt"></span> ' . __('Add Event', 'db-app-builder');
        echo '</button>';
        
        echo '</div>';
        echo '</div>';
        
        // Calendar navigation info
        echo '<div class="dab-calendar-info">';
        echo '<h3 class="dab-current-period"></h3>';
        echo '</div>';
        
        // Calendar grid
        echo '<div class="dab-calendar-grid" id="calendar-grid-' . $field->id . '">';
        echo '<div class="dab-calendar-loading">';
        echo '<span class="spinner is-active"></span>';
        echo '<p>' . __('Loading calendar...', 'db-app-builder') . '</p>';
        echo '</div>';
        echo '</div>';
        
        // Hidden input to store calendar data
        echo '<input type="hidden" name="' . $field_name . '" id="' . $field_name . '" value="' . esc_attr($value) . '">';
        
        echo '</div>';
        
        // Add modal for event editing
        self::render_event_modal();
    }
    
    /**
     * Render event editing modal
     */
    private static function render_event_modal() {
        echo '<div id="dab-event-modal" class="dab-modal" style="display: none;">';
        echo '<div class="dab-modal-content">';
        echo '<div class="dab-modal-header">';
        echo '<h3 id="dab-event-modal-title">' . __('Add Event', 'db-app-builder') . '</h3>';
        echo '<button type="button" class="dab-modal-close">&times;</button>';
        echo '</div>';
        echo '<div class="dab-modal-body">';
        
        // Event form fields
        echo '<div class="dab-form-field">';
        echo '<label for="dab-event-title">' . __('Title', 'db-app-builder') . ' <span class="required">*</span></label>';
        echo '<input type="text" id="dab-event-title" class="dab-form-control" required>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-event-description">' . __('Description', 'db-app-builder') . '</label>';
        echo '<textarea id="dab-event-description" class="dab-form-control" rows="3"></textarea>';
        echo '</div>';
        
        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-start-date">' . __('Start Date', 'db-app-builder') . ' <span class="required">*</span></label>';
        echo '<input type="date" id="dab-event-start-date" class="dab-form-control" required>';
        echo '</div>';
        
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-end-date">' . __('End Date', 'db-app-builder') . '</label>';
        echo '<input type="date" id="dab-event-end-date" class="dab-form-control">';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label>';
        echo '<input type="checkbox" id="dab-event-all-day" class="dab-checkbox">';
        echo ' ' . __('All Day Event', 'db-app-builder');
        echo '</label>';
        echo '</div>';
        
        echo '<div class="dab-time-fields">';
        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-start-time">' . __('Start Time', 'db-app-builder') . '</label>';
        echo '<input type="time" id="dab-event-start-time" class="dab-form-control">';
        echo '</div>';
        
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-end-time">' . __('End Time', 'db-app-builder') . '</label>';
        echo '<input type="time" id="dab-event-end-time" class="dab-form-control">';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-location">' . __('Location', 'db-app-builder') . '</label>';
        echo '<input type="text" id="dab-event-location" class="dab-form-control">';
        echo '</div>';
        
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-event-category">' . __('Category', 'db-app-builder') . '</label>';
        echo '<select id="dab-event-category" class="dab-form-control">';
        echo '<option value="">' . __('Select Category', 'db-app-builder') . '</option>';
        echo '<option value="meeting">' . __('Meeting', 'db-app-builder') . '</option>';
        echo '<option value="appointment">' . __('Appointment', 'db-app-builder') . '</option>';
        echo '<option value="task">' . __('Task', 'db-app-builder') . '</option>';
        echo '<option value="reminder">' . __('Reminder', 'db-app-builder') . '</option>';
        echo '<option value="event">' . __('Event', 'db-app-builder') . '</option>';
        echo '<option value="other">' . __('Other', 'db-app-builder') . '</option>';
        echo '</select>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-event-color">' . __('Color', 'db-app-builder') . '</label>';
        echo '<div class="dab-color-picker">';
        $colors = array('#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22');
        foreach ($colors as $color) {
            echo '<button type="button" class="dab-color-option" data-color="' . $color . '" style="background-color: ' . $color . '"></button>';
        }
        echo '</div>';
        echo '<input type="hidden" id="dab-event-color" value="#3498db">';
        echo '</div>';
        
        echo '</div>';
        echo '<div class="dab-modal-footer">';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-modal-close">' . __('Cancel', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-danger dab-delete-event" style="display: none;">' . __('Delete', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-save-event">' . __('Save', 'db-app-builder') . '</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Get events for a field and record
     */
    public static function get_events($field_id, $record_id, $start_date = null, $end_date = null) {
        global $wpdb;
        
        $events_table = $wpdb->prefix . 'dab_calendar_events';
        
        $where_clause = "WHERE field_id = %d AND record_id = %d";
        $params = array($field_id, $record_id);
        
        if ($start_date && $end_date) {
            $where_clause .= " AND ((start_date BETWEEN %s AND %s) OR (end_date BETWEEN %s AND %s) OR (start_date <= %s AND end_date >= %s))";
            $params[] = $start_date;
            $params[] = $end_date;
            $params[] = $start_date;
            $params[] = $end_date;
            $params[] = $start_date;
            $params[] = $end_date;
        }
        
        $sql = "SELECT * FROM $events_table $where_clause ORDER BY start_date ASC";
        
        return $wpdb->get_results($wpdb->prepare($sql, $params));
    }
    
    /**
     * AJAX handler to save calendar event
     */
    public static function save_calendar_event() {
        check_ajax_referer('dab_calendar_nonce', 'nonce');
        
        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        $event_id = isset($_POST['event_id']) ? intval($_POST['event_id']) : 0;
        
        $event_data = array(
            'field_id' => $field_id,
            'record_id' => $record_id,
            'title' => sanitize_text_field($_POST['title']),
            'description' => sanitize_textarea_field($_POST['description']),
            'start_date' => sanitize_text_field($_POST['start_date']),
            'end_date' => sanitize_text_field($_POST['end_date']),
            'all_day' => isset($_POST['all_day']) ? 1 : 0,
            'location' => sanitize_text_field($_POST['location']),
            'category' => sanitize_text_field($_POST['category']),
            'color' => sanitize_hex_color($_POST['color'])
        );
        
        if (!$field_id || !$event_data['title'] || !$event_data['start_date']) {
            wp_send_json_error(__('Required fields are missing', 'db-app-builder'));
        }
        
        global $wpdb;
        $events_table = $wpdb->prefix . 'dab_calendar_events';
        
        try {
            if ($event_id > 0) {
                // Update existing event
                $result = $wpdb->update($events_table, $event_data, array('id' => $event_id));
            } else {
                // Create new event
                $event_data['created_by'] = get_current_user_id();
                $result = $wpdb->insert($events_table, $event_data);
                $event_id = $wpdb->insert_id;
            }
            
            if ($result !== false) {
                wp_send_json_success(array(
                    'event_id' => $event_id,
                    'message' => __('Event saved successfully', 'db-app-builder')
                ));
            } else {
                wp_send_json_error(__('Failed to save event', 'db-app-builder'));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('Error saving event', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to get calendar events
     */
    public static function get_calendar_events() {
        check_ajax_referer('dab_calendar_nonce', 'nonce');
        
        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        $start_date = sanitize_text_field($_POST['start_date']);
        $end_date = sanitize_text_field($_POST['end_date']);
        
        $events = self::get_events($field_id, $record_id, $start_date, $end_date);
        
        wp_send_json_success($events);
    }
    
    /**
     * AJAX handler to delete calendar event
     */
    public static function delete_calendar_event() {
        check_ajax_referer('dab_calendar_nonce', 'nonce');
        
        $event_id = intval($_POST['event_id']);
        
        if (!$event_id) {
            wp_send_json_error(__('Invalid event ID', 'db-app-builder'));
        }
        
        global $wpdb;
        $events_table = $wpdb->prefix . 'dab_calendar_events';
        
        $result = $wpdb->delete($events_table, array('id' => $event_id));
        
        if ($result !== false) {
            wp_send_json_success(__('Event deleted successfully', 'db-app-builder'));
        } else {
            wp_send_json_error(__('Failed to delete event', 'db-app-builder'));
        }
    }
}
