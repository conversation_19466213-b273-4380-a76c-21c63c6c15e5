<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Automation Tutorial - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .workflow-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        .workflow-step {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            margin: 10px;
            font-weight: bold;
            position: relative;
        }
        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.5em;
        }
        .workflow-step:last-child::after {
            display: none;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .example-workflow {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .example-workflow h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
            font-size: 1.2em;
        }
        .trigger-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .trigger-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .trigger-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .trigger-card h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .trigger-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .action-card {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
        }
        .action-card h4 {
            color: #856404;
            margin-top: 0;
            border-bottom: 2px solid #ffc107;
            padding-bottom: 10px;
        }
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .approval-flow {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #6c757d;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .approval-step {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .approval-step .status {
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        .status.approved {
            background: #d4edda;
            color: #155724;
        }
        .status.rejected {
            background: #f8d7da;
            color: #721c24;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h5 {
            color: #721c24;
            margin-top: 0;
            font-weight: bold;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ Workflow Automation Tutorial - Automate Everything</h1>

        <div class="workflow-diagram">
            <h3>🔄 Complete Workflow Process</h3>
            <div style="margin: 20px 0;">
                <div class="workflow-step">Trigger Event</div>
                <div class="workflow-step">Check Conditions</div>
                <div class="workflow-step">Execute Actions</div>
                <div class="workflow-step">Send Notifications</div>
                <div class="workflow-step">Complete</div>
            </div>
        </div>

        <h2>🎯 Understanding Workflow Triggers</h2>

        <div class="trigger-types">
            <div class="trigger-card">
                <h4><div class="trigger-icon">📝</div>Form Submission</h4>
                <p>Automatically trigger when a form is submitted</p>
                <ul>
                    <li>New customer registration</li>
                    <li>Support ticket creation</li>
                    <li>Order placement</li>
                    <li>Application submission</li>
                </ul>
            </div>

            <div class="trigger-card">
                <h4><div class="trigger-icon">📊</div>Data Change</h4>
                <p>Trigger when specific data is modified</p>
                <ul>
                    <li>Status field updates</li>
                    <li>Price changes</li>
                    <li>Inventory levels</li>
                    <li>User role changes</li>
                </ul>
            </div>

            <div class="trigger-card">
                <h4><div class="trigger-icon">⏰</div>Scheduled Events</h4>
                <p>Time-based automation triggers</p>
                <ul>
                    <li>Daily reports</li>
                    <li>Weekly reminders</li>
                    <li>Monthly billing</li>
                    <li>Anniversary notifications</li>
                </ul>
            </div>

            <div class="trigger-card">
                <h4><div class="trigger-icon">👤</div>User Actions</h4>
                <p>Trigger based on user behavior</p>
                <ul>
                    <li>User login/logout</li>
                    <li>Profile updates</li>
                    <li>File uploads</li>
                    <li>Comment posting</li>
                </ul>
            </div>
        </div>

        <h2>🔧 Creating Your First Workflow</h2>

        <div class="step-by-step">
            <h4><span class="step-number">1</span>Basic Workflow Setup</h4>
            <ol>
                <li>Navigate to <strong>Database App Builder → Workflows</strong></li>
                <li>Click <strong>"Create New Workflow"</strong></li>
                <li>Enter a descriptive workflow name</li>
                <li>Select the <strong>trigger type</strong> (form submission, data change, etc.)</li>
                <li>Choose the <strong>source table</strong> or form</li>
                <li>Set trigger conditions (optional)</li>
                <li>Configure workflow actions</li>
                <li>Test and activate the workflow</li>
            </ol>
        </div>

        <div class="example-workflow">
            <h5>📋 Example: Customer Onboarding Workflow</h5>
            <p><strong>Trigger:</strong> New customer registration form submission</p>
            
            <div class="code-example">
Workflow Name: Customer Onboarding
Trigger: Form Submission
Source Form: Customer Registration
Conditions: Status = "New"

Actions:
1. Send welcome email to customer
2. Create task for sales team
3. Add customer to CRM system
4. Schedule follow-up reminder
5. Update customer status to "Onboarded"
</div>
        </div>

        <h2>🎬 Workflow Actions</h2>

        <div class="action-grid">
            <div class="action-card">
                <h4>📧 Email Actions</h4>
                <ul>
                    <li><strong>Send Email:</strong> Custom email templates</li>
                    <li><strong>Email Notifications:</strong> Alert team members</li>
                    <li><strong>Auto-responders:</strong> Immediate confirmations</li>
                    <li><strong>Drip Campaigns:</strong> Scheduled email series</li>
                </ul>
            </div>

            <div class="action-card">
                <h4>📊 Data Actions</h4>
                <ul>
                    <li><strong>Update Records:</strong> Modify existing data</li>
                    <li><strong>Create Records:</strong> Add new entries</li>
                    <li><strong>Delete Records:</strong> Remove data</li>
                    <li><strong>Calculate Values:</strong> Formula-based updates</li>
                </ul>
            </div>

            <div class="action-card">
                <h4>👥 User Actions</h4>
                <ul>
                    <li><strong>Assign Tasks:</strong> Create to-do items</li>
                    <li><strong>Change Permissions:</strong> Update user roles</li>
                    <li><strong>Send Notifications:</strong> In-app alerts</li>
                    <li><strong>Create Accounts:</strong> Auto user registration</li>
                </ul>
            </div>

            <div class="action-card">
                <h4>🔗 Integration Actions</h4>
                <ul>
                    <li><strong>Webhook Calls:</strong> External API integration</li>
                    <li><strong>Zapier Triggers:</strong> Connect 3000+ apps</li>
                    <li><strong>Payment Processing:</strong> Charge customers</li>
                    <li><strong>File Operations:</strong> Generate documents</li>
                </ul>
            </div>
        </div>

        <h2>✅ Approval Workflows</h2>

        <div class="step-by-step">
            <h4><span class="step-number">2</span>Setting Up Approval Processes</h4>
            <ol>
                <li>Navigate to <strong>Database App Builder → Approval Workflows</strong></li>
                <li>Click <strong>"Create Approval Workflow"</strong></li>
                <li>Define approval stages and approvers</li>
                <li>Set approval conditions and rules</li>
                <li>Configure notification settings</li>
                <li>Test the approval flow</li>
                <li>Activate the workflow</li>
            </ol>
        </div>

        <div class="approval-flow">
            <h5>🔄 Example: Expense Approval Process</h5>
            
            <div class="approval-step">
                <div>
                    <strong>Stage 1:</strong> Manager Approval
                    <br><small>Expenses under $500</small>
                </div>
                <div class="status pending">Pending</div>
            </div>
            
            <div class="approval-step">
                <div>
                    <strong>Stage 2:</strong> Finance Director
                    <br><small>Expenses $500 - $2000</small>
                </div>
                <div class="status pending">Pending</div>
            </div>
            
            <div class="approval-step">
                <div>
                    <strong>Stage 3:</strong> CEO Approval
                    <br><small>Expenses over $2000</small>
                </div>
                <div class="status pending">Pending</div>
            </div>
        </div>

        <div class="example-workflow">
            <h5>⚙️ Approval Workflow Configuration</h5>
            <table>
                <tr>
                    <th>Condition</th>
                    <th>Approver</th>
                    <th>Action if Approved</th>
                    <th>Action if Rejected</th>
                </tr>
                <tr>
                    <td>Amount < $500</td>
                    <td>Direct Manager</td>
                    <td>Process Payment</td>
                    <td>Return to Submitter</td>
                </tr>
                <tr>
                    <td>Amount $500-$2000</td>
                    <td>Finance Director</td>
                    <td>Process Payment</td>
                    <td>Return to Submitter</td>
                </tr>
                <tr>
                    <td>Amount > $2000</td>
                    <td>CEO</td>
                    <td>Process Payment</td>
                    <td>Return to Submitter</td>
                </tr>
            </table>
        </div>

        <h2>🔄 Advanced Workflow Features</h2>

        <div class="step-by-step">
            <h4><span class="step-number">3</span>Conditional Logic in Workflows</h4>
            <p>Create intelligent workflows that adapt based on data:</p>
            
            <div class="code-example">
IF customer_type = "Premium"
    THEN send_priority_email()
    AND assign_to_senior_agent()
ELSE
    send_standard_email()
    AND assign_to_regular_queue()
END IF

IF order_total > 1000
    THEN require_manager_approval()
    AND send_vip_notification()
END IF
</div>
        </div>

        <div class="example-workflow">
            <h5>🎯 Real-World Workflow Examples</h5>
            
            <h6>🏢 HR Onboarding Workflow</h6>
            <ul>
                <li>New hire form submission triggers workflow</li>
                <li>Create IT account and email</li>
                <li>Order equipment and setup workspace</li>
                <li>Schedule orientation meetings</li>
                <li>Send welcome package and handbook</li>
                <li>Create training schedule</li>
            </ul>

            <h6>🛒 E-commerce Order Processing</h6>
            <ul>
                <li>Order placement triggers workflow</li>
                <li>Check inventory availability</li>
                <li>Process payment authorization</li>
                <li>Generate picking list for warehouse</li>
                <li>Send order confirmation to customer</li>
                <li>Update inventory levels</li>
                <li>Schedule shipping and tracking</li>
            </ul>

            <h6>🎫 Support Ticket Management</h6>
            <ul>
                <li>Support form submission triggers workflow</li>
                <li>Categorize ticket by priority and type</li>
                <li>Assign to appropriate team member</li>
                <li>Send acknowledgment to customer</li>
                <li>Set SLA timers and escalation rules</li>
                <li>Track resolution time and satisfaction</li>
            </ul>
        </div>

        <div class="tip-box">
            <h5>💡 Workflow Best Practices</h5>
            <ul>
                <li><strong>Start Simple:</strong> Begin with basic workflows and add complexity gradually</li>
                <li><strong>Test Thoroughly:</strong> Always test workflows before activating them</li>
                <li><strong>Document Processes:</strong> Keep clear documentation of workflow logic</li>
                <li><strong>Monitor Performance:</strong> Track workflow execution and success rates</li>
                <li><strong>Regular Reviews:</strong> Periodically review and optimize workflows</li>
                <li><strong>Error Handling:</strong> Plan for failure scenarios and error recovery</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Workflow Testing</h5>
            <p>Always test workflows in a staging environment before deploying to production. Automated workflows can affect real data and send actual emails, so thorough testing is crucial.</p>
        </div>

        <div class="navigation-buttons">
            <a href="form-builder-tutorial.html" class="nav-button">← Previous: Form Builder</a>
            <a href="analytics-tutorial.html" class="nav-button">Next: Analytics & Reporting →</a>
        </div>
    </div>
</body>
</html>
