/**
 * Enhanced Formula Calculator for Database App Builder
 * 
 * This script provides enhanced formula calculation functionality with real-time updates,
 * better error handling, and visual indicators for fields used in formulas.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all formula fields
    initializeFormulaFields();
    
    // Add event listeners to all form fields that might be used in formulas
    addFieldListeners();
    
    /**
     * Initialize all formula fields on the page
     */
    function initializeFormulaFields() {
        const formulaFields = document.querySelectorAll('.dab-formula-field');
        
        formulaFields.forEach(function(field) {
            // Get the formula expression
            const formula = field.getAttribute('data-formula');
            
            if (formula) {
                // Parse the formula to find field references
                const fieldReferences = extractFieldReferences(formula);
                
                // Mark fields used in this formula
                markFieldsUsedInFormula(fieldReferences, field.id);
                
                // Calculate initial value
                calculateFormulaValue(field, formula);
            }
        });
    }
    
    /**
     * Add event listeners to all form fields that might be used in formulas
     */
    function addFieldListeners() {
        // Get all input fields that might be used in formulas
        const inputFields = document.querySelectorAll('.dab-form input, .dab-form select, .dab-form textarea');
        
        inputFields.forEach(function(field) {
            // Skip formula fields themselves
            if (field.classList.contains('dab-formula-field')) {
                return;
            }
            
            // Add event listeners for different field types
            if (field.tagName === 'SELECT') {
                field.addEventListener('change', updateFormulas);
            } else if (field.type === 'checkbox' || field.type === 'radio') {
                field.addEventListener('change', updateFormulas);
            } else {
                field.addEventListener('input', updateFormulas);
                field.addEventListener('blur', updateFormulas);
            }
        });
    }
    
    /**
     * Update all formula fields when a field value changes
     */
    function updateFormulas() {
        const formulaFields = document.querySelectorAll('.dab-formula-field');
        
        formulaFields.forEach(function(field) {
            const formula = field.getAttribute('data-formula');
            if (formula) {
                calculateFormulaValue(field, formula);
            }
        });
    }
    
    /**
     * Calculate the value of a formula field
     */
    function calculateFormulaValue(field, formula) {
        try {
            // Replace field references with actual values
            const evaluatableFormula = replaceFieldReferencesWithValues(formula);
            
            // Evaluate the formula
            const result = evaluateFormula(evaluatableFormula);
            
            // Update the field value
            field.value = formatResult(result);
            
            // Update the preview
            const previewElement = field.parentNode.querySelector('.dab-formula-preview');
            if (previewElement) {
                previewElement.textContent = 'Result: ' + formatResult(result);
                previewElement.style.color = '#28a745';
            }
        } catch (error) {
            // Handle calculation errors
            console.error('Formula calculation error:', error);
            
            // Update the field with error indicator
            field.value = '';
            
            // Update the preview with error message
            const previewElement = field.parentNode.querySelector('.dab-formula-preview');
            if (previewElement) {
                previewElement.textContent = 'Error: ' + error.message;
                previewElement.style.color = '#dc3545';
            }
        }
    }
    
    /**
     * Extract field references from a formula
     */
    function extractFieldReferences(formula) {
        // Match field references like {field_name}
        const regex = /{([^}]+)}/g;
        const matches = formula.match(regex) || [];
        
        // Extract field names without the braces
        return matches.map(match => match.slice(1, -1));
    }
    
    /**
     * Mark fields that are used in a formula with a visual indicator
     */
    function markFieldsUsedInFormula(fieldReferences, formulaId) {
        fieldReferences.forEach(function(fieldName) {
            // Find the field in the form
            const field = document.querySelector(`.dab-form [name="${fieldName}"]`);
            
            if (field) {
                // Add a class to the field's container
                const fieldContainer = field.closest('.dab-form-field');
                if (fieldContainer) {
                    fieldContainer.classList.add('dab-used-in-formula');
                    
                    // Add a data attribute to track which formula uses this field
                    const usedInFormulas = field.getAttribute('data-used-in-formulas') || '';
                    const formulas = usedInFormulas ? usedInFormulas.split(',') : [];
                    
                    if (!formulas.includes(formulaId)) {
                        formulas.push(formulaId);
                        field.setAttribute('data-used-in-formulas', formulas.join(','));
                    }
                    
                    // Add a visual indicator
                    if (!fieldContainer.querySelector('.dab-formula-indicator')) {
                        const indicator = document.createElement('div');
                        indicator.className = 'dab-formula-indicator';
                        indicator.title = 'This field is used in a formula';
                        indicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg>';
                        
                        // Add the indicator after the field
                        field.parentNode.insertBefore(indicator, field.nextSibling);
                    }
                }
            }
        });
    }
    
    /**
     * Replace field references in a formula with their actual values
     */
    function replaceFieldReferencesWithValues(formula) {
        // Replace {field_name} with the actual field value
        return formula.replace(/{([^}]+)}/g, function(match, fieldName) {
            const field = document.querySelector(`.dab-form [name="${fieldName}"]`);
            
            if (!field) {
                throw new Error(`Field "${fieldName}" not found`);
            }
            
            let value = getFieldValue(field);
            
            // Make sure we have a numeric value for calculations
            if (value === '' || value === null || isNaN(value)) {
                value = 0;
            }
            
            return value;
        });
    }
    
    /**
     * Get the value of a field based on its type
     */
    function getFieldValue(field) {
        if (field.type === 'checkbox') {
            return field.checked ? 1 : 0;
        } else if (field.type === 'radio') {
            const checkedRadio = document.querySelector(`input[name="${field.name}"]:checked`);
            return checkedRadio ? checkedRadio.value : '';
        } else {
            return field.value;
        }
    }
    
    /**
     * Safely evaluate a formula expression
     */
    function evaluateFormula(formula) {
        // Replace common math functions with their JavaScript equivalents
        const preparedFormula = formula
            .replace(/SUM\s*\(/gi, 'sum(')
            .replace(/AVG\s*\(/gi, 'avg(')
            .replace(/MIN\s*\(/gi, 'Math.min(')
            .replace(/MAX\s*\(/gi, 'Math.max(')
            .replace(/ROUND\s*\(/gi, 'Math.round(')
            .replace(/FLOOR\s*\(/gi, 'Math.floor(')
            .replace(/CEIL\s*\(/gi, 'Math.ceil(')
            .replace(/ABS\s*\(/gi, 'Math.abs(');
        
        // Define custom functions
        const sum = function() {
            return Array.from(arguments).reduce((a, b) => parseFloat(a) + parseFloat(b), 0);
        };
        
        const avg = function() {
            const args = Array.from(arguments);
            return args.length ? args.reduce((a, b) => parseFloat(a) + parseFloat(b), 0) / args.length : 0;
        };
        
        // Use Function constructor to create a safe evaluation environment
        try {
            return Function('sum', 'avg', 'Math', `"use strict"; return (${preparedFormula});`)(sum, avg, Math);
        } catch (error) {
            throw new Error('Invalid formula: ' + error.message);
        }
    }
    
    /**
     * Format the result for display
     */
    function formatResult(result) {
        // Handle different result types
        if (typeof result === 'number') {
            // Format number with up to 2 decimal places, but only if needed
            return parseFloat(result.toFixed(2)).toString();
        } else {
            return result.toString();
        }
    }
});
