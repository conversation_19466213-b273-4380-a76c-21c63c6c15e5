/**
 * Simple Dashboard List Styles
 */

/* Dashboards Grid */
.dab-dashboards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Dashboard Card */
.dab-dashboard-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
}

.dab-dashboard-card:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.dab-dashboard-card-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-dashboard-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.dab-dashboard-card-badge {
    background-color: #0073aa;
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.dab-dashboard-card-body {
    padding: 15px;
    flex: 1;
}

.dab-dashboard-card-description {
    margin: 0 0 15px 0;
    color: #555;
}

.dab-empty-description {
    color: #999;
    font-style: italic;
}

.dab-dashboard-card-meta {
    font-size: 12px;
    color: #777;
}

.dab-dashboard-card-footer {
    padding: 15px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

/* Shortcode Display */
.dab-dashboard-card-shortcode {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.dab-dashboard-card-shortcode code {
    padding: 5px 8px;
    background-color: #f0f0f0;
    border-radius: 3px;
    font-size: 12px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dab-copy-shortcode {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.dab-copy-shortcode:hover {
    color: #0073aa;
}

/* No Dashboards Message */
.dab-no-dashboards {
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
    margin-top: 20px;
}

.dab-no-dashboards p {
    margin-bottom: 20px;
    color: #555;
}

/* Responsive */
@media (max-width: 576px) {
    .dab-dashboards-grid {
        grid-template-columns: 1fr;
    }
}
