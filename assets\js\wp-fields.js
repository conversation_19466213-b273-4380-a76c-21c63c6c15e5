/**
 * WordPress Fields JavaScript
 * 
 * This script handles the WordPress role and user fields.
 */
jQuery(document).ready(function($) {
    // Initialize WordPress fields
    initWpFields();
    
    /**
     * Initialize WordPress fields
     */
    function initWpFields() {
        // Handle role field change to update user field
        $('.dab-wp-role-field').on('change', function() {
            const roleValue = $(this).val();
            const formContainer = $(this).closest('form');
            
            // Find all user fields that are filtered by this role field
            formContainer.find('.dab-wp-user-field[data-filter-by-role="1"]').each(function() {
                const userField = $(this);
                const roleFieldName = userField.data('role-field');
                
                // Check if this user field is linked to the changed role field
                if (roleFieldName === $(this).attr('name')) {
                    updateUserFieldOptions(userField, roleValue);
                }
            });
        });
        
        // Initialize user fields that are filtered by role
        $('.dab-wp-user-field[data-filter-by-role="1"]').each(function() {
            const userField = $(this);
            const roleFieldName = userField.data('role-field');
            
            // Find the linked role field
            const roleField = $('select[name="' + roleFieldName + '"]');
            if (roleField.length) {
                const roleValue = roleField.val();
                if (roleValue) {
                    updateUserFieldOptions(userField, roleValue);
                }
            }
        });
    }
    
    /**
     * Update user field options based on selected role
     * 
     * @param {jQuery} userField The user field element
     * @param {string} role The selected role
     */
    function updateUserFieldOptions(userField, role) {
        if (!role) {
            // Clear options if no role selected
            userField.html('<option value="">' + dabWpFields.i18n.selectUser + '</option>');
            return;
        }
        
        // Show loading state
        userField.html('<option value="">' + dabWpFields.i18n.loading + '</option>');
        
        // Get the currently selected value
        const currentValue = userField.val();
        
        // Get users for the selected role via AJAX
        $.ajax({
            url: dabWpFields.ajaxurl,
            type: 'POST',
            data: {
                action: 'dab_get_users_by_role',
                role: role,
                nonce: dabWpFields.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Clear existing options
                    userField.html('<option value="">' + dabWpFields.i18n.selectUser + '</option>');
                    
                    // Add new options
                    if (response.data.length > 0) {
                        $.each(response.data, function(index, user) {
                            const selected = (user.id == currentValue) ? ' selected' : '';
                            userField.append('<option value="' + user.id + '"' + selected + '>' + user.text + '</option>');
                        });
                    } else {
                        userField.append('<option value="" disabled>' + dabWpFields.i18n.noUsers + '</option>');
                    }
                } else {
                    // Show error
                    userField.html('<option value="">' + dabWpFields.i18n.selectUser + '</option>');
                    console.error('Error loading users:', response.data.message);
                }
            },
            error: function() {
                // Show error
                userField.html('<option value="">' + dabWpFields.i18n.selectUser + '</option>');
                console.error('AJAX error when loading users');
            }
        });
    }
});
