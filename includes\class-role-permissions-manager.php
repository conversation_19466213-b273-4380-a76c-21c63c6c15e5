<?php
/**
 * Role Permissions Manager
 *
 * Handles role-based permissions for viewing, editing, and deleting records.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Role_Permissions_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Create tables if they don't exist
        self::create_tables();

        // Register AJAX handlers
        add_action('wp_ajax_dab_save_table_permissions', array(__CLASS__, 'ajax_save_table_permissions'));
        add_action('wp_ajax_dab_save_field_permissions', array(__CLASS__, 'ajax_save_field_permissions'));
    }

    /**
     * Create necessary tables
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Table for storing role permissions
        $table_name = $wpdb->prefix . 'dab_role_permissions';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            role VARCHAR(100) NOT NULL,
            can_view TINYINT(1) DEFAULT 0,
            can_edit TINYINT(1) DEFAULT 0,
            can_delete TINYINT(1) DEFAULT 0,
            can_export TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY table_role (table_id, role)
        ) $charset_collate;";

        // Table for storing field-level permissions
        $field_permissions_table = $wpdb->prefix . 'dab_field_permissions';
        $field_sql = "CREATE TABLE IF NOT EXISTS $field_permissions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            field_id BIGINT(20) UNSIGNED NOT NULL,
            role VARCHAR(100) NOT NULL,
            can_view TINYINT(1) DEFAULT 0,
            can_edit TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY field_role (field_id, role)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($field_sql);
    }

    /**
     * Check if a user can view records in a table
     *
     * @param int $user_id The user ID
     * @param int $table_id The table ID
     * @return bool Whether the user can view records
     */
    public static function can_user_view_records($user_id, $table_id) {
        // Administrators can always view records
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have view permission
        return self::check_role_permission($table_id, $user_roles, 'can_view');
    }

    /**
     * Check if a user can edit records in a table
     *
     * @param int $user_id The user ID
     * @param int $table_id The table ID
     * @param int $record_id The record ID (optional)
     * @return bool Whether the user can edit records
     */
    public static function can_user_edit_records($user_id, $table_id, $record_id = null) {
        // Administrators can always edit records
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have edit permission
        $can_edit = self::check_role_permission($table_id, $user_roles, 'can_edit');

        // If record_id is provided, also check if the user is the owner of the record
        if ($record_id && !$can_edit) {
            $is_owner = self::is_user_record_owner($user_id, $table_id, $record_id);
            return $is_owner;
        }

        return $can_edit;
    }

    /**
     * Check if a user can delete records in a table
     *
     * @param int $user_id The user ID
     * @param int $table_id The table ID
     * @param int $record_id The record ID (optional)
     * @return bool Whether the user can delete records
     */
    public static function can_user_delete_records($user_id, $table_id, $record_id = null) {
        // Administrators can always delete records
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have delete permission
        $can_delete = self::check_role_permission($table_id, $user_roles, 'can_delete');

        // If record_id is provided, also check if the user is the owner of the record
        if ($record_id && !$can_delete) {
            $is_owner = self::is_user_record_owner($user_id, $table_id, $record_id);
            return $is_owner;
        }

        return $can_delete;
    }

    /**
     * Check if a user has a specific permission for a table based on their roles
     *
     * @param int $table_id The table ID
     * @param array $roles The user roles
     * @param string $permission The permission to check (can_view, can_edit, can_delete)
     * @return bool Whether the user has the permission
     */
    private static function check_role_permission($table_id, $roles, $permission) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_role_permissions';

        // Create placeholders for the roles
        $placeholders = implode(',', array_fill(0, count($roles), '%s'));

        // Prepare the query parameters
        $query_params = array_merge([$table_id], $roles);

        // Check if any of the roles have the permission
        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name
            WHERE table_id = %d
            AND role IN ($placeholders)
            AND $permission = 1",
            $query_params
        );

        $count = $wpdb->get_var($query);

        return $count > 0;
    }

    /**
     * Check if a user is the owner of a record
     *
     * @param int $user_id The user ID
     * @param int $table_id The table ID
     * @param int $record_id The record ID
     * @return bool Whether the user is the owner
     */
    private static function is_user_record_owner($user_id, $table_id, $record_id) {
        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $table_id
        ));

        if (!$table_info) {
            return false;
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if the user is the owner
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM $data_table WHERE id = %d",
            $record_id
        ));

        return $owner_id == $user_id;
    }

    /**
     * Check if a user is an administrator
     *
     * @param int $user_id The user ID
     * @return bool Whether the user is an administrator
     */
    private static function is_user_admin($user_id) {
        $user = get_userdata($user_id);
        return $user && in_array('administrator', $user->roles);
    }

    /**
     * Get a user's roles
     *
     * @param int $user_id The user ID
     * @return array The user's roles
     */
    private static function get_user_roles($user_id) {
        $user = get_userdata($user_id);
        return $user ? $user->roles : [];
    }

    /**
     * Get table permissions for a specific table
     *
     * @param int $table_id The table ID
     * @return array The table permissions
     */
    public static function get_table_permissions($table_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_role_permissions';

        $permissions = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE table_id = %d",
            $table_id
        ), ARRAY_A);

        return $permissions;
    }

    /**
     * Save table permissions
     *
     * @param int $table_id The table ID
     * @param array $permissions The permissions to save
     * @return bool Whether the permissions were saved successfully
     */
    public static function save_table_permissions($table_id, $permissions) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_role_permissions';

        // Delete existing permissions
        $wpdb->delete($table_name, ['table_id' => $table_id]);

        // Insert new permissions
        foreach ($permissions as $role => $perms) {
            $wpdb->insert($table_name, [
                'table_id' => $table_id,
                'role' => $role,
                'can_view' => isset($perms['can_view']) ? 1 : 0,
                'can_edit' => isset($perms['can_edit']) ? 1 : 0,
                'can_delete' => isset($perms['can_delete']) ? 1 : 0,
                'can_export' => isset($perms['can_export']) ? 1 : 0,
                'created_at' => current_time('mysql')
            ]);
        }

        return true;
    }

    /**
     * Check if a user can export records from a table
     *
     * @param int $user_id The user ID
     * @param int $table_id The table ID
     * @return bool Whether the user can export records
     */
    public static function can_user_export_records($user_id, $table_id) {
        // Administrators can always export records
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have export permission
        return self::check_role_permission($table_id, $user_roles, 'can_export');
    }

    /**
     * Check if a user can view a specific field
     *
     * @param int $user_id The user ID
     * @param int $field_id The field ID
     * @return bool Whether the user can view the field
     */
    public static function can_user_view_field($user_id, $field_id) {
        // Administrators can always view all fields
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have view permission for this field
        return self::check_field_permission($field_id, $user_roles, 'can_view');
    }

    /**
     * Check if a user can edit a specific field
     *
     * @param int $user_id The user ID
     * @param int $field_id The field ID
     * @return bool Whether the user can edit the field
     */
    public static function can_user_edit_field($user_id, $field_id) {
        // Administrators can always edit all fields
        if (self::is_user_admin($user_id)) {
            return true;
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return false;
        }

        // Check if any of the user's roles have edit permission for this field
        return self::check_field_permission($field_id, $user_roles, 'can_edit');
    }

    /**
     * Check if a user has a specific permission for a field based on their roles
     *
     * @param int $field_id The field ID
     * @param array $roles The user roles
     * @param string $permission The permission to check (can_view, can_edit)
     * @return bool Whether the user has the permission
     */
    private static function check_field_permission($field_id, $roles, $permission) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_field_permissions';

        // Create placeholders for the roles
        $placeholders = implode(',', array_fill(0, count($roles), '%s'));

        // Prepare the query parameters
        $query_params = array_merge([$field_id], $roles);

        // Check if any of the roles have the permission
        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name
            WHERE field_id = %d
            AND role IN ($placeholders)
            AND $permission = 1",
            $query_params
        );

        $count = $wpdb->get_var($query);

        return $count > 0;
    }

    /**
     * Get field permissions for a specific field
     *
     * @param int $field_id The field ID
     * @return array The field permissions
     */
    public static function get_field_permissions($field_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_field_permissions';

        $permissions = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE field_id = %d",
            $field_id
        ), ARRAY_A);

        return $permissions;
    }

    /**
     * Save field permissions
     *
     * @param int $field_id The field ID
     * @param array $permissions The permissions to save
     * @return bool Whether the permissions were saved successfully
     */
    public static function save_field_permissions($field_id, $permissions) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_field_permissions';

        // Delete existing permissions
        $wpdb->delete($table_name, ['field_id' => $field_id]);

        // Insert new permissions
        foreach ($permissions as $role => $perms) {
            $wpdb->insert($table_name, [
                'field_id' => $field_id,
                'role' => $role,
                'can_view' => isset($perms['can_view']) ? 1 : 0,
                'can_edit' => isset($perms['can_edit']) ? 1 : 0,
                'created_at' => current_time('mysql')
            ]);
        }

        return true;
    }

    /**
     * AJAX handler for saving field permissions
     */
    public static function ajax_save_field_permissions() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check if user is admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Check required parameters
        if (!isset($_POST['field_id']) || !isset($_POST['permissions'])) {
            wp_send_json_error('Missing required parameters');
        }

        $field_id = intval($_POST['field_id']);
        $permissions = $_POST['permissions'];

        // Save permissions
        $result = self::save_field_permissions($field_id, $permissions);

        if ($result) {
            wp_send_json_success('Field permissions saved successfully');
        } else {
            wp_send_json_error('Failed to save field permissions');
        }
    }

    /**
     * AJAX handler for saving table permissions
     */
    public static function ajax_save_table_permissions() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check if user is admin
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['permissions'])) {
            wp_send_json_error('Missing required parameters');
        }

        $table_id = intval($_POST['table_id']);
        $permissions = $_POST['permissions'];

        // Save permissions
        $result = self::save_table_permissions($table_id, $permissions);

        if ($result) {
            wp_send_json_success('Permissions saved successfully');
        } else {
            wp_send_json_error('Failed to save permissions');
        }
    }

    /**
     * Get all tables that a user has permission to view
     *
     * @param int $user_id The user ID
     * @return array The table IDs that the user can view
     */
    public static function get_user_accessible_tables($user_id) {
        global $wpdb;

        // Administrators can view all tables
        if (self::is_user_admin($user_id)) {
            // Get all tables
            $tables_table = $wpdb->prefix . 'dab_tables';
            $tables = $wpdb->get_results("SELECT id FROM $tables_table", ARRAY_A);
            return array_column($tables, 'id');
        }

        // Get user roles
        $user_roles = self::get_user_roles($user_id);
        if (empty($user_roles)) {
            return array();
        }

        // Get tables that the user has permission to view
        $permissions_table = $wpdb->prefix . 'dab_role_permissions';

        // Create placeholders for the roles
        $placeholders = implode(',', array_fill(0, count($user_roles), '%s'));

        // Prepare the query parameters
        $query_params = $user_roles;

        // Get tables with view permission for any of the user's roles
        $query = $wpdb->prepare(
            "SELECT DISTINCT table_id FROM $permissions_table
            WHERE role IN ($placeholders)
            AND can_view = 1",
            $query_params
        );

        $tables = $wpdb->get_results($query, ARRAY_A);

        return array_column($tables, 'table_id');
    }
}
