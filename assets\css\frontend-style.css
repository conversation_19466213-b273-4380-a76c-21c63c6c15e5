/**
 * Database App Builder Frontend Styles
 *
 * Styles for the frontend forms and views
 */

/* Form Container */
.dab-form {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Form Title */
.dab-form-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Form Description */
.dab-form-description {
    margin-bottom: 20px;
    color: #666;
}

/* Form Fields */
.dab-form-field,
.dab-field-group {
    margin-bottom: 20px;
    clear: both;
    width: 100%;
    overflow: hidden;
}

.dab-form-field label,
.dab-field-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
    width: 100%;
}

.dab-form-field input[type="text"],
.dab-form-field input[type="email"],
.dab-form-field input[type="number"],
.dab-form-field input[type="date"],
.dab-form-field input[type="password"],
.dab-form-field select,
.dab-form-field textarea,
.dab-field-group input[type="text"],
.dab-field-group input[type="email"],
.dab-field-group input[type="number"],
.dab-field-group input[type="date"],
.dab-field-group input[type="password"],
.dab-field-group select,
.dab-field-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    color: #333;
    background-color: #f9f9f9;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.dab-form-field input[type="text"]:focus,
.dab-form-field input[type="email"]:focus,
.dab-form-field input[type="number"]:focus,
.dab-form-field input[type="date"]:focus,
.dab-form-field input[type="password"]:focus,
.dab-form-field select:focus,
.dab-form-field textarea:focus,
.dab-field-group input[type="text"]:focus,
.dab-field-group input[type="email"]:focus,
.dab-field-group input[type="number"]:focus,
.dab-field-group input[type="date"]:focus,
.dab-field-group input[type="password"]:focus,
.dab-field-group select:focus,
.dab-field-group textarea:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
    outline: none;
}

.dab-form-field textarea,
.dab-field-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* Checkbox and Radio Styles */
.dab-form-field input[type="checkbox"],
.dab-form-field input[type="radio"],
.dab-field-group input[type="checkbox"],
.dab-field-group input[type="radio"] {
    margin-right: 5px;
}

.dab-radio-option,
.dab-checkbox-option {
    margin-bottom: 5px;
}

/* Required Field Indicator */
.dab-required {
    color: #e74c3c;
    margin-left: 3px;
}

/* Field Error Styles */
.dab-field-error {
    border-color: #e74c3c !important;
    background-color: #fff8f8 !important;
}

.dab-field-error-message {
    color: #e74c3c;
    font-size: 0.85em;
    margin-top: 5px;
    font-weight: 500;
}

/* Submit Button */
.dab-submit-button {
    margin-top: 20px;
    text-align: center;
}

.dab-submit-button button,
.dab-submit-button input[type="submit"] {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dab-submit-button button:hover,
.dab-submit-button input[type="submit"]:hover {
    background-color: #135e96;
}

/* Success and Error Messages */
.dab-success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

.dab-error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: 4px solid #dc3545;
}

.dab-field-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

.dab-field-has-error {
    border-color: #dc3545 !important;
}

/* Enhanced Select Field Styles */
.dab-enhanced-select-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
}

.dab-select-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 1;
    pointer-events: none;
}

.dab-select-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 1;
    pointer-events: none;
}

/* Enhanced Dropdown Styles */
.dab-enhanced-dropdown {
    width: 100%;
    padding: 10px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    color: #333;
    background-color: #f9f9f9;
    transition: border-color 0.3s, box-shadow 0.3s;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
}

.dab-enhanced-dropdown:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
    outline: none;
}

.dab-enhanced-dropdown option {
    padding: 8px;
    background-color: #fff;
}

.dab-enhanced-dropdown option:hover {
    background-color: #f0f7fc;
}

/* Radio Group Styles */
.dab-radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 5px;
}

.dab-radio-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.dab-radio-option:hover {
    background-color: #f5f5f5;
}

.dab-radio-option input[type="radio"] {
    margin-right: 10px;
}

.dab-radio-option label {
    margin-bottom: 0 !important;
    font-weight: normal !important;
    cursor: pointer;
}

/* Lookup Field Styles */
.dab-lookup-field,
.dab-relationship-field {
    width: 100%;
    padding: 10px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    color: #333;
    background-color: #f9f9f9;
    transition: border-color 0.3s, box-shadow 0.3s;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
}

.dab-lookup-field:focus,
.dab-relationship-field:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
    outline: none;
}

.dab-lookup-field option,
.dab-relationship-field option {
    padding: 8px;
}

.dab-lookup-field optgroup,
.dab-relationship-field optgroup {
    font-weight: bold;
    color: #555;
    background-color: #f5f5f5;
}

/* Formula Field Styles */
.dab-formula-field-container {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
}

.dab-formula-field {
    width: 100%;
    padding: 10px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    color: #333;
    background-color: #f0f8ff;
    transition: border-color 0.3s, box-shadow 0.3s;
    cursor: default;
}

.dab-formula-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a90e2;
    z-index: 1;
}

.dab-formula-preview {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

.dab-formula-display {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.dab-formula-explanation {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-top: 5px;
    font-family: system-ui, -apple-system, sans-serif;
}

.dab-formula-field-ref {
    display: inline-block;
    padding: 2px 5px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 3px;
    color: #1890ff;
    margin: 0 2px;
    font-weight: 500;
}

.dab-formula-operator {
    color: #f5222d;
    font-weight: bold;
    margin: 0 3px;
}

.dab-formula-function {
    color: #722ed1;
    font-weight: bold;
}

.dab-formula-parenthesis {
    color: #fa8c16;
    font-weight: bold;
}

/* Advanced Dropdown Styles */
.dab-searchable-dropdown-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
}

.dab-dropdown-search {
    width: 100%;
    padding: 10px 35px;
    margin-bottom: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.dab-dropdown-search:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
    outline: none;
}

.dab-advanced-dropdown-field {
    width: 100%;
    padding: 10px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    color: #333;
    background-color: #f9f9f9;
    transition: border-color 0.3s, box-shadow 0.3s;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
}

.dab-advanced-dropdown-field:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
    outline: none;
}

.dab-search-toggle {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    cursor: pointer;
    z-index: 2;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.dab-search-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #4a90e2;
}

/* Optgroup styling */
.dab-lookup-field optgroup,
.dab-relationship-field optgroup,
.dab-advanced-dropdown-field optgroup {
    font-weight: bold;
    padding: 5px;
    background-color: #f0f0f0;
    color: #555;
}

/* Error message styling */
.dab-error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    padding: 5px;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 4px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Cascading Dropdown Styles */
.dab-cascading-dropdown {
    margin-bottom: 15px;
}

.dab-cascading-dropdown label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

/* Loading indicator */
.dab-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: dab-spin 1s linear infinite;
}

@keyframes dab-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* View Table Styles */
.dab-view-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.dab-view-table th {
    background-color: #f2f2f2;
    padding: 10px;
    text-align: left;
    font-weight: 600;
    border: 1px solid #ddd;
}

.dab-view-table td {
    padding: 10px;
    border: 1px solid #ddd;
}

.dab-view-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.dab-view-table tr:hover {
    background-color: #f1f1f1;
}

/* Filter and Search */
.dab-filter-form {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: flex-end;
}

.dab-filter-form select,
.dab-filter-form input[type="text"] {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-filter-form button {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.dab-filter-form button:hover {
    background-color: #135e96;
}

/* Pagination */
.dab-pagination {
    margin-top: 20px;
    text-align: center;
}

.dab-pagination a,
.dab-pagination span {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 3px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
}

.dab-pagination a:hover {
    background-color: #f5f5f5;
}

.dab-pagination .current {
    background-color: #2271b1;
    color: white;
    border-color: #2271b1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dab-form {
        padding: 15px;
    }

    .dab-form-field input[type="text"],
    .dab-form-field input[type="email"],
    .dab-form-field input[type="number"],
    .dab-form-field input[type="date"],
    .dab-form-field select,
    .dab-form-field textarea,
    .dab-field-group input[type="text"],
    .dab-field-group input[type="email"],
    .dab-field-group input[type="number"],
    .dab-field-group input[type="date"],
    .dab-field-group select,
    .dab-field-group textarea {
        font-size: 14px;
    }

    .dab-submit-button button,
    .dab-submit-button input[type="submit"] {
        width: 100%;
    }
}