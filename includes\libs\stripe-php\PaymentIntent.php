<?php
/**
 * Stripe PaymentIntent Mock Class
 * 
 * This is a simplified mock version for demonstration purposes.
 * In a production environment, you would use the actual Stripe PHP SDK.
 */

namespace Stripe;

if (!defined('ABSPATH')) exit;

class PaymentIntent {
    public $id;
    public $client_secret;
    public $amount;
    public $currency;
    public $status;
    public $metadata;
    
    public static function create($params) {
        $intent = new self();
        $intent->id = 'pi_' . uniqid();
        $intent->client_secret = $intent->id . '_secret_' . uniqid();
        $intent->amount = $params['amount'];
        $intent->currency = $params['currency'];
        $intent->status = 'requires_payment_method';
        $intent->metadata = $params['metadata'] ?? [];
        
        return $intent;
    }
    
    public static function retrieve($id) {
        $intent = new self();
        $intent->id = $id;
        $intent->client_secret = $id . '_secret_' . uniqid();
        $intent->status = 'succeeded';
        
        return $intent;
    }
    
    public function capture() {
        $this->status = 'succeeded';
        return $this;
    }
}
