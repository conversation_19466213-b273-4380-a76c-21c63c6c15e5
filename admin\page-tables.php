<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$message = '';

// Handle Table Deletion
if (isset($_GET['delete_table'])) {
    $delete_id = intval($_GET['delete_table']);
    $wpdb->delete($tables_table, ['id' => $delete_id]);
    $message = "Table deleted successfully.";
}

// Handle messages from redirects
if (isset($_GET['created']) && $_GET['created'] == 1) {
    $message = "Table created successfully with default fields and corresponding data table was initialized.";
}

if (isset($_GET['updated']) && $_GET['updated'] == 1) {
    $message = "Table updated successfully.";
}

// Handle Edit
$edit_mode = false;
$edit_table = null;
if (isset($_GET['edit_table'])) {
    $edit_mode = true;
    $edit_id = intval($_GET['edit_table']);
    $edit_table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $edit_id));
}

// Debug output
error_log('POST data: ' . print_r($_POST, true));

// Handle Create/Update
if (isset($_POST['dab_save_table'])) {
    // Debug output
    error_log('Table form submitted with dab_save_table: ' . print_r($_POST, true));

    // Simple direct approach - no nonce check for now to eliminate potential issues
    $label = sanitize_text_field($_POST['table_label']);
    $slug = sanitize_title($_POST['table_slug']);
    $description = sanitize_textarea_field($_POST['description']);

    if (isset($_POST['table_id']) && $_POST['table_id']) {
        // Update existing table
        $wpdb->update($tables_table, [
            'table_label' => $label,
            'table_slug' => $slug,
            'description' => $description,
        ], ['id' => intval($_POST['table_id'])]);

        $message = "Table updated successfully.";
        $edit_mode = false;

        // Redirect to tables page after update
        // Make sure headers haven't been sent yet
        if (!headers_sent()) {
            wp_redirect(admin_url('admin.php?page=dab_tables&updated=1'));
            exit;
        } else {
            // If headers already sent, use JavaScript redirect
            echo '<script>window.location.href = "' . esc_url(admin_url('admin.php?page=dab_tables&updated=1')) . '";</script>';
            exit;
        }
    } else {
        // Create new table - direct approach
        $wpdb->insert($tables_table, [
            'table_label' => $label,
            'table_slug' => $slug,
            'description' => $description,
            'created_at' => current_time('mysql'),
        ]);

        // Get the new table ID
        $table_id = $wpdb->insert_id;

        // Automatically create data table
        DAB_DB_Manager::create_data_table($slug);

        // Automatically create default fields based on table name
        $created_fields = DAB_DB_Manager::create_default_fields($table_id, $slug);

        $field_count = count($created_fields);
        $field_message = $field_count > 0 ? " with $field_count default fields" : "";

        $message = "Table created successfully$field_message and corresponding data table was initialized.";

        // Redirect to tables page after creation
        // Make sure headers haven't been sent yet
        if (!headers_sent()) {
            wp_redirect(admin_url('admin.php?page=dab_tables&created=1'));
            exit;
        } else {
            // If headers already sent, use JavaScript redirect
            echo '<script>window.location.href = "' . esc_url(admin_url('admin.php?page=dab_tables&created=1')) . '";</script>';
            exit;
        }
    }
}

// Fetch all tables
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY id DESC");
?>

<style>
    .dashicons.spin {
        animation: spin 2s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .dab-form-submitting {
        opacity: 0.7;
        pointer-events: none;
        position: relative;
    }
    .dab-form-submitting::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.5);
        z-index: 10;
    }
    .button-loading {
        position: relative;
        color: transparent !important;
    }
    .button-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-top: -8px;
        margin-left: -8px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.5);
        border-top-color: white;
        animation: spin 1s linear infinite;
    }
</style>



<div class="wrap dab-admin-wrap dab-animate-fade-in">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title"><?php echo $edit_mode ? 'Edit Table' : 'Create Table'; ?></h1>
        <?php if (!$edit_mode): ?>
        <div class="dab-admin-actions">
            <a href="<?php echo admin_url('admin.php?page=dab_dashboard'); ?>" class="dab-btn dab-btn-outline-primary">
                <span class="dashicons dashicons-dashboard" style="margin-right: 5px;"></span> Dashboard
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($message)): ?>
        <div class="dab-admin-notice dab-admin-notice-success dab-animate-slide-down">
            <p><?php echo esc_html($message); ?></p>
        </div>
    <?php endif; ?>

    <?php if (!$edit_mode): ?>
    <div class="dab-card dab-animate-scale-in" style="margin-bottom: 20px;">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Quick Create Table</h2>
        </div>
        <div class="dab-card-body">
            <form method="post" action="" id="simple-table-form">
                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                    <div style="flex: 1;">
                        <label for="quick_table_label" style="display: block; margin-bottom: 5px;">Table Name</label>
                        <input type="text" id="quick_table_label" name="table_label" style="width: 100%; padding: 8px;" required>
                    </div>
                    <div style="flex: 1;">
                        <label for="quick_table_slug" style="display: block; margin-bottom: 5px;">Table Slug</label>
                        <input type="text" id="quick_table_slug" name="table_slug" style="width: 100%; padding: 8px;" required>
                    </div>
                    <div style="flex: 1;">
                        <label for="quick_description" style="display: block; margin-bottom: 5px;">Description</label>
                        <input type="text" id="quick_description" name="description" style="width: 100%; padding: 8px;">
                    </div>
                    <div style="align-self: flex-end;">
                        <input type="submit" name="dab_save_table" value="Create Table" style="padding: 8px 16px; background-color: #2271b1; color: white; border: none; border-radius: 4px; cursor: pointer;" id="create-table-button">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <?php else: ?>
    <div class="dab-card dab-animate-scale-in">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Edit Table</h2>

            <!-- Tabs Navigation -->
            <div class="dab-admin-tabs">
                <a href="#dab-tab-details" class="dab-admin-tab-link active">Details</a>
                <a href="#dab-tab-permissions" class="dab-admin-tab-link">Permissions</a>
            </div>
        </div>
        <div class="dab-card-body">
            <!-- Details Tab -->
            <div id="dab-tab-details" class="dab-admin-tab-content" style="display: block;">
                <form method="post" action="" class="dab-admin-form">
                    <input type="hidden" name="table_id" value="<?php echo esc_attr($edit_table->id); ?>">

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="table_label" class="dab-admin-form-label">Table Label</label>
                            <input type="text" id="table_label" name="table_label" class="dab-admin-form-control"
                                value="<?php echo esc_attr($edit_table->table_label); ?>" required>
                            <span class="dab-admin-form-help">The display name for your table</span>
                        </div>

                        <div class="dab-admin-form-group">
                            <label for="table_slug" class="dab-admin-form-label">Table Slug</label>
                            <input type="text" id="table_slug" name="table_slug" class="dab-admin-form-control"
                                value="<?php echo esc_attr($edit_table->table_slug); ?>" required>
                            <span class="dab-admin-form-help">The unique identifier for your table (lowercase, no spaces)</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group dab-admin-form-group-full">
                            <label for="description" class="dab-admin-form-label">Description</label>
                            <textarea id="description" name="description" class="dab-admin-form-control" rows="4"><?php echo esc_textarea($edit_table->description); ?></textarea>
                            <span class="dab-admin-form-help">A brief description of what this table is used for</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-actions">
                        <input type="submit" name="dab_save_table" value="Update Table" class="dab-btn dab-btn-primary">
                        <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-btn dab-btn-outline-primary">Cancel</a>
                    </div>
                </form>
            </div>

            <!-- Permissions Tab -->
            <div id="dab-tab-permissions" class="dab-admin-tab-content" style="display: none;">
                <?php
                // Include the permissions tab content
                $table_id = $edit_table->id;
                include(plugin_dir_path(__FILE__) . 'partials/table-permissions-tab.php');
                ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!$edit_mode): ?>
    <div class="dab-card dab-animate-slide-up" style="margin-top: 20px;">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Existing Tables</h2>
        </div>
        <div class="dab-card-body">
            <?php if (!empty($tables)): ?>
                <div class="dab-table-responsive">
                    <table class="dab-admin-table">
                        <thead>
                            <tr>
                                <th>Label</th>
                                <th>Slug</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tables as $tbl): ?>
                                <tr>
                                    <td><?php echo esc_html($tbl->table_label); ?></td>
                                    <td><code><?php echo esc_html($tbl->table_slug); ?></code></td>
                                    <td><?php echo esc_html($tbl->description); ?></td>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($tbl->created_at))); ?></td>
                                    <td class="dab-admin-table-actions">
                                        <a href="<?php echo admin_url("admin.php?page=dab_fields&table_id={$tbl->id}"); ?>" class="dab-btn dab-btn-sm dab-btn-info" title="Manage Fields">
                                            <span class="dashicons dashicons-editor-table"></span>
                                        </a>
                                        <a href="<?php echo admin_url("admin.php?page=dab_tables&edit_table={$tbl->id}"); ?>" class="dab-btn dab-btn-sm dab-btn-primary" title="Edit Table">
                                            <span class="dashicons dashicons-edit"></span>
                                        </a>
                                        <a href="<?php echo admin_url("admin.php?page=dab_tables&delete_table={$tbl->id}"); ?>"
                                           onclick="return confirm('Are you sure you want to delete this table? This action cannot be undone.');"
                                           class="dab-btn dab-btn-sm dab-btn-danger" title="Delete Table">
                                            <span class="dashicons dashicons-trash"></span>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="dab-alert dab-alert-info">
                    <p>No tables created yet. Use the form above to create your first table.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Function to generate slug from label
    function generateSlug(label) {
        return label.toLowerCase()
            .replace(/\s+/g, '_')
            .replace(/[^a-z0-9_]/g, '');
    }

    // Auto-generate slug from label for edit form
    $('#table_label').on('input', function() {
        var label = $(this).val();
        var slugField = $('#table_slug');

        if (label && !slugField.val()) {
            slugField.val(generateSlug(label));
        }
    });

    // Auto-generate slug from label for quick create form
    $('#quick_table_label').on('input blur', function() {
        var label = $(this).val();
        var slugField = $('#quick_table_slug');

        if (label && !slugField.val()) {
            slugField.val(generateSlug(label));
        }
    });

    // Clear form fields after successful submission
    if ($('.dab-admin-notice-success').length > 0) {
        // Clear the form fields
        $('#quick_table_label').val('');
        $('#quick_table_slug').val('');
        $('#quick_description').val('');
    }

    // Handle form submission with AJAX
    $('#simple-table-form').on('submit', function(e) {
        e.preventDefault(); // Prevent normal form submission

        // Add loading indicator
        var submitButton = $(this).find('input[type="submit"]');
        var originalText = submitButton.val();
        submitButton.addClass('button-loading');
        submitButton.prop('disabled', true);
        $(this).addClass('dab-form-submitting');

        // Get form data
        var formData = $(this).serialize();
        formData += '&dab_save_table=1'; // Add the submit button value

        // Send AJAX request
        $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
            action: 'dab_ajax_create_table',
            form_data: formData,
            nonce: '<?php echo wp_create_nonce('dab_create_table_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                // Show success message
                var successMessage = $('<div class="dab-admin-notice dab-admin-notice-success dab-animate-slide-down"><p>' + response.data.message + '</p></div>');
                $('.dab-admin-header').after(successMessage);

                // Clear form fields
                $('#quick_table_label').val('');
                $('#quick_table_slug').val('');
                $('#quick_description').val('');

                // Add the new table to the table list
                if (response.data.table) {
                    var table = response.data.table;
                    var newRow = '<tr>' +
                        '<td>' + table.table_label + '</td>' +
                        '<td><code>' + table.table_slug + '</code></td>' +
                        '<td>' + (table.description || '') + '</td>' +
                        '<td>' + new Date(table.created_at).toLocaleDateString() + '</td>' +
                        '<td class="dab-admin-table-actions">' +
                        '<a href="<?php echo admin_url("admin.php?page=dab_fields&table_id="); ?>' + table.id + '" class="dab-btn dab-btn-sm dab-btn-info" title="Manage Fields">' +
                        '<span class="dashicons dashicons-editor-table"></span>' +
                        '</a>' +
                        '<a href="<?php echo admin_url("admin.php?page=dab_tables&edit_table="); ?>' + table.id + '" class="dab-btn dab-btn-sm dab-btn-primary" title="Edit Table">' +
                        '<span class="dashicons dashicons-edit"></span>' +
                        '</a>' +
                        '<a href="<?php echo admin_url("admin.php?page=dab_tables&delete_table="); ?>' + table.id + '" onclick="return confirm(\'Are you sure you want to delete this table? This action cannot be undone.\');" class="dab-btn dab-btn-sm dab-btn-danger" title="Delete Table">' +
                        '<span class="dashicons dashicons-trash"></span>' +
                        '</a>' +
                        '</td>' +
                        '</tr>';

                    // Add to table or show table if empty
                    if ($('.dab-admin-table tbody').length) {
                        $('.dab-admin-table tbody').prepend(newRow);
                    } else {
                        // Replace the "no tables" message with a new table
                        $('.dab-alert.dab-alert-info').replaceWith(
                            '<div class="dab-table-responsive">' +
                            '<table class="dab-admin-table">' +
                            '<thead><tr><th>Label</th><th>Slug</th><th>Description</th><th>Created</th><th>Actions</th></tr></thead>' +
                            '<tbody>' + newRow + '</tbody>' +
                            '</table>' +
                            '</div>'
                        );
                    }
                }
            } else {
                // Silently handle error without displaying message
            }

            // Remove loading state
            submitButton.removeClass('button-loading');
            submitButton.prop('disabled', false);
            $('#simple-table-form').removeClass('dab-form-submitting');
        }).fail(function() {
            // Show error message for AJAX failure
            var errorMessage = $('<div class="dab-admin-notice dab-admin-notice-error dab-animate-slide-down"><p>Server error. Please try again.</p></div>');
            $('.dab-admin-header').after(errorMessage);

            // Remove loading state
            submitButton.removeClass('button-loading');
            submitButton.prop('disabled', false);
            $('#simple-table-form').removeClass('dab-form-submitting');
        });
    });

    // Also handle the update form with AJAX
    $('.dab-admin-form').on('submit', function(e) {
        e.preventDefault(); // Prevent normal form submission

        var submitButton = $(this).find('input[type="submit"]');
        submitButton.addClass('button-loading');
        submitButton.prop('disabled', true);
        $(this).addClass('dab-form-submitting');

        // Get form data
        var formData = $(this).serialize();
        formData += '&dab_save_table=1'; // Add the submit button value

        // Send AJAX request
        $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
            action: 'dab_ajax_update_table',
            form_data: formData,
            nonce: '<?php echo wp_create_nonce('dab_update_table_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                // Show success message
                var successMessage = $('<div class="dab-admin-notice dab-admin-notice-success dab-animate-slide-down"><p>' + response.data.message + '</p></div>');
                $('.dab-admin-header').after(successMessage);

                // Redirect to tables page after a short delay
                setTimeout(function() {
                    window.location.href = '<?php echo admin_url("admin.php?page=dab_tables&updated=1"); ?>';
                }, 1000);
            } else {
                // Show error message
                var errorMessage = $('<div class="dab-admin-notice dab-admin-notice-error dab-animate-slide-down"><p>' + (response.data || 'Error updating table') + '</p></div>');
                $('.dab-admin-header').after(errorMessage);

                // Remove loading state
                submitButton.removeClass('button-loading');
                submitButton.prop('disabled', false);
                $('.dab-admin-form').removeClass('dab-form-submitting');
            }
        }).fail(function() {
            // Silently handle AJAX failure

            // Remove loading state
            submitButton.removeClass('button-loading');
            submitButton.prop('disabled', false);
            $('.dab-admin-form').removeClass('dab-form-submitting');
        });
    });

    // Handle tab navigation
    $('.dab-admin-tab-link').on('click', function(e) {
        e.preventDefault();

        // Get the target tab
        var target = $(this).attr('href');

        // Update active tab
        $('.dab-admin-tab-link').removeClass('active');
        $(this).addClass('active');

        // Show the target tab content
        $('.dab-admin-tab-content').hide();
        $(target).show();
    });
});
</script>
