<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

global $wpdb;
$tables_table = $wpdb->prefix . 'dab_tables';

// Handle Export to CSV
if ((isset($_GET['export_csv']) || isset($_POST['dab_export_csv'])) && !empty($_REQUEST['table_id'])) {
    $table_id = intval($_REQUEST['table_id']);
    DAB_Data_Manager::export_records_to_csv($table_id);
    exit;
}

// Handle Bulk Delete
if (isset($_POST['dab_bulk_delete']) && !empty($_POST['selected_records'])) {
    $table_id = intval($_POST['table_id']);
    foreach ($_POST['selected_records'] as $record_id) {
        DAB_Data_Manager::delete_record($table_id, intval($record_id));
    }
    echo '<div class="notice notice-success is-dismissible"><p>Selected records deleted successfully!</p></div>';
}

// Handle Inline Update
if (isset($_POST['dab_inline_update'])) {
    $table_id = intval($_POST['table_id']);
    $record_id = intval($_POST['record_id']);
    $field_slug = sanitize_text_field($_POST['field_slug']);
    $field_value = sanitize_text_field($_POST['field_value']);
    DAB_Data_Manager::update_record_field($table_id, $record_id, $field_slug, $field_value);
    echo '<div class="notice notice-success is-dismissible"><p>Record updated successfully!</p></div>';
}

// Handle Approval Workflow
if (isset($_POST['dab_approve_record']) && current_user_can('administrator')) {
    $table_id = intval($_POST['table_id']);
    $record_id = intval($_POST['record_id']);
    $status = sanitize_text_field($_POST['approval_status']);
    $note = sanitize_text_field($_POST['approval_notes']);
    DAB_Data_Manager::update_approval_status($table_id, $record_id, $status, $note);
    echo '<div class="notice notice-success is-dismissible"><p>Approval status updated.</p></div>';
}

// Fetch Tables
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY id DESC");

$selected_table_id = isset($_GET['table']) ? intval($_GET['table']) : 0;
$records = [];
$fields = [];

if ($selected_table_id) {
    $records = DAB_Data_Manager::get_records($selected_table_id);
    $fields = DAB_Data_Manager::get_fields($selected_table_id);
}
?>

<div class="wrap">
    <h1>Data Manager</h1>

    <form method="get" action="">
        <input type="hidden" name="page" value="dab_data">
        <select name="table" onchange="this.form.submit();">
            <option value="">-- Select Table --</option>
            <?php foreach ($tables as $tbl) : ?>
                <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($selected_table_id, $tbl->id); ?>>
                    <?php echo esc_html($tbl->table_label); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </form>

    <?php if (!empty($records)) : ?>
        <form method="post" id="dab-data-form">
            <input type="hidden" name="table_id" value="<?php echo esc_attr($selected_table_id); ?>">

            <h2>Records</h2>

            <p>
                <button type="submit" name="dab_bulk_delete" class="button button-danger" onclick="return confirm('Are you sure you want to delete selected records?');">Delete Selected</button>
                <a href="<?php echo admin_url("admin.php?page=dab_data&export_csv=1&table_id=" . $selected_table_id); ?>" class="button button-primary">Export to CSV</a>
            </p>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="dab-select-all"></th>
                        <?php foreach ($fields as $field) : ?>
                            <th><?php echo esc_html($field->field_label); ?></th>
                        <?php endforeach; ?>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($records as $record) : ?>
                        <tr>
                            <td><input type="checkbox" name="selected_records[]" value="<?php echo esc_attr($record->id); ?>"></td>
                            <?php foreach ($fields as $field) : ?>
                                <td>
                                    <span class="dab-inline-field" data-record-id="<?php echo esc_attr($record->id); ?>" data-field-slug="<?php echo esc_attr($field->field_slug); ?>">
                                        <?php echo DAB_Data_Manager::render_field_value($selected_table_id, $field, $record->{$field->field_slug}, $record->id); ?>
                                    </span>
                                </td>
                            <?php endforeach; ?>

                            <td>
                                <strong><?php echo esc_html($record->approval_status ?? 'Pending'); ?></strong>
                                <?php if (!empty($record->approval_notes)) : ?>
                                    <br><small><em><?php echo esc_html($record->approval_notes); ?></em></small>
                                <?php endif; ?>
                            </td>

                            <td>
                                <a href="<?php echo admin_url('admin.php?page=dab_data&dab_action=delete&table=' . $selected_table_id . '&id=' . $record->id); ?>" class="button button-small" onclick="return confirm('Delete this record?');">Delete</a>

                                <?php if (current_user_can('administrator')) : ?>
                                    <form method="post" style="margin-top:5px;">
                                        <input type="hidden" name="table_id" value="<?php echo esc_attr($selected_table_id); ?>">
                                        <input type="hidden" name="record_id" value="<?php echo esc_attr($record->id); ?>">
                                        <select name="approval_status">
                                            <option value="Pending" <?php selected($record->approval_status, 'Pending'); ?>>Pending</option>
                                            <option value="Approved" <?php selected($record->approval_status, 'Approved'); ?>>Approved</option>
                                            <option value="Rejected" <?php selected($record->approval_status, 'Rejected'); ?>>Rejected</option>
                                        </select>
                                        <input type="text" name="approval_notes" placeholder="Notes" value="<?php echo esc_attr($record->approval_notes); ?>" style="width: 100px;">
                                        <button type="submit" name="dab_approve_record" class="button">Update</button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </form>
    <?php elseif ($selected_table_id) : ?>
        <p>No records found.</p>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const selectAll = document.getElementById('dab-select-all');
    if (selectAll) {
        selectAll.addEventListener('change', function () {
            const checkboxes = document.querySelectorAll('input[name="selected_records[]"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });
    }

    const fields = document.querySelectorAll('.dab-inline-field');
    fields.forEach(field => {
        field.addEventListener('dblclick', function () {
            const oldValue = this.innerText;
            const input = document.createElement('input');
            input.type = 'text';
            input.value = oldValue;
            input.style.width = '100%';

            this.innerHTML = '';
            this.appendChild(input);
            input.focus();

            input.addEventListener('blur', function () {
                const newValue = this.value;
                const recordId = field.getAttribute('data-record-id');
                const fieldSlug = field.getAttribute('data-field-slug');

                const formData = new FormData();
                formData.append('dab_inline_update', 1);
                formData.append('table_id', <?php echo esc_js($selected_table_id); ?>);
                formData.append('record_id', recordId);
                formData.append('field_slug', fieldSlug);
                formData.append('field_value', newValue);

                fetch('<?php echo admin_url('admin.php?page=dab_data'); ?>', {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        field.innerText = newValue;
                    } else {
                        field.innerText = oldValue;
                    }
                }).catch(() => {
                    field.innerText = oldValue;
                });
            });
        });
    });
});
</script>
