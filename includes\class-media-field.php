<?php
/**
 * Audio/Video Media Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Media_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the media field type
        add_filter('dab_field_types', array($this, 'register_media_field_type'));

        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_media_field_options'), 10, 2);

        // Register field renderer
        add_action('dab_render_field_media', array($this, 'render_media_field'), 10, 2);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Format media value for display
        add_filter('dab_format_field_value', array($this, 'format_media_value'), 10, 3);

        // Handle media upload
        add_action('wp_ajax_dab_upload_media', array($this, 'handle_media_upload'));
        add_action('wp_ajax_nopriv_dab_upload_media', array($this, 'handle_media_upload'));
    }

    /**
     * Register the media field type
     *
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_media_field_type($field_types) {
        $field_types['media'] = __('Audio/Video Media', 'db-app-builder');
        return $field_types;
    }

    /**
     * Add media field options
     *
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_media_field_options($options, $field_type) {
        if ($field_type === 'media') {
            $options = array(
                'media_type' => array(
                    'label' => __('Media Type', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'both' => __('Both Audio & Video', 'db-app-builder'),
                        'audio' => __('Audio Only', 'db-app-builder'),
                        'video' => __('Video Only', 'db-app-builder'),
                    ),
                    'default' => 'both',
                    'description' => __('Type of media to allow.', 'db-app-builder'),
                ),
                'allow_recording' => array(
                    'label' => __('Allow Recording', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Allow users to record audio/video directly.', 'db-app-builder'),
                ),
                'allow_upload' => array(
                    'label' => __('Allow Upload', 'db-app-builder'),
                    'type' => 'checkbox',
                    'default' => true,
                    'description' => __('Allow users to upload media files.', 'db-app-builder'),
                ),
                'allow_embed' => array(
                    'label' => __('Allow Embed', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Allow users to embed media from external sources (YouTube, Vimeo, etc.).', 'db-app-builder'),
                ),
                'max_file_size' => array(
                    'label' => __('Max File Size (MB)', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 1,
                    'max' => 100,
                    'default' => 10,
                    'description' => __('Maximum file size in megabytes.', 'db-app-builder'),
                ),
                'allowed_formats' => array(
                    'label' => __('Allowed Formats', 'db-app-builder'),
                    'type' => 'multiselect',
                    'options' => array(
                        'mp3' => 'MP3',
                        'wav' => 'WAV',
                        'ogg' => 'OGG',
                        'mp4' => 'MP4',
                        'webm' => 'WebM',
                        'mov' => 'MOV',
                    ),
                    'default' => array('mp3', 'mp4'),
                    'description' => __('Allowed file formats.', 'db-app-builder'),
                ),
                'player_width' => array(
                    'label' => __('Player Width', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 200,
                    'max' => 1200,
                    'default' => 400,
                    'description' => __('Width of the media player in pixels.', 'db-app-builder'),
                ),
                'player_height' => array(
                    'label' => __('Player Height', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 100,
                    'max' => 800,
                    'default' => 300,
                    'description' => __('Height of the media player in pixels (for video).', 'db-app-builder'),
                ),
            );
        }

        return $options;
    }

    /**
     * Render the media field
     *
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_media_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        // Get field settings with defaults
        // Check for both old and new option names for backward compatibility
        $media_type = isset($options['media_type']) ? $options['media_type'] : 'both';
        $allow_recording = isset($options['allow_recording']) ? (bool)$options['allow_recording'] : false;
        $allow_upload = isset($options['allow_upload']) ? (bool)$options['allow_upload'] : true;
        $allow_embed = isset($options['allow_embed']) ? (bool)$options['allow_embed'] : false;
        $max_file_size = isset($options['max_file_size']) ? intval($options['max_file_size']) : 10;
        $allowed_formats = isset($options['allowed_formats']) ? $options['allowed_formats'] : array('mp3', 'mp4');
        $player_width = isset($options['player_width']) ? intval($options['player_width']) : 400;
        $player_height = isset($options['player_height']) ? intval($options['player_height']) : 300;

        // Parse the value
        $media_data = $this->parse_media_value($value);

        // Generate a unique ID for the field
        $field_id = 'dab-media-' . $field->id . '-' . uniqid();

        // Output the field HTML
        ?>
        <div class="dab-media-field"
             id="<?php echo esc_attr($field_id); ?>"
             data-field-slug="<?php echo esc_attr($field->field_slug); ?>"
             data-media-type="<?php echo esc_attr($media_type); ?>"
             data-allow-recording="<?php echo $allow_recording ? 'true' : 'false'; ?>"
             data-allow-upload="<?php echo $allow_upload ? 'true' : 'false'; ?>"
             data-allow-embed="<?php echo $allow_embed ? 'true' : 'false'; ?>"
             data-max-file-size="<?php echo esc_attr($max_file_size); ?>"
             data-allowed-formats="<?php echo esc_attr(implode(',', $allowed_formats)); ?>"
             data-player-width="<?php echo esc_attr($player_width); ?>"
             data-player-height="<?php echo esc_attr($player_height); ?>">

            <!-- Tabs for different media sources -->
            <div class="dab-media-tabs">
                <?php if ($allow_upload): ?>
                <button type="button" class="dab-media-tab active" data-tab="upload">
                    <?php _e('Upload', 'db-app-builder'); ?>
                </button>
                <?php endif; ?>

                <?php if ($allow_recording): ?>
                <button type="button" class="dab-media-tab" data-tab="record">
                    <?php _e('Record', 'db-app-builder'); ?>
                </button>
                <?php endif; ?>

                <?php if ($allow_embed): ?>
                <button type="button" class="dab-media-tab" data-tab="embed">
                    <?php _e('Embed', 'db-app-builder'); ?>
                </button>
                <?php endif; ?>
            </div>

            <!-- Tab content -->
            <div class="dab-media-tab-content">
                <!-- Upload tab -->
                <?php if ($allow_upload): ?>
                <div class="dab-media-tab-pane active" data-tab-pane="upload">
                    <input type="file"
                           id="<?php echo esc_attr($field_id . '-upload'); ?>"
                           class="dab-media-upload"
                           accept="<?php echo esc_attr($this->get_accept_attribute($media_type, $allowed_formats)); ?>"
                           <?php echo $field->required ? 'required' : ''; ?>>
                    <div class="dab-media-upload-info">
                        <?php printf(__('Max file size: %s MB. Allowed formats: %s', 'db-app-builder'),
                                    esc_html($max_file_size),
                                    esc_html(implode(', ', $allowed_formats))); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Record tab -->
                <?php if ($allow_recording): ?>
                <div class="dab-media-tab-pane" data-tab-pane="record">
                    <div class="dab-media-recorder">
                        <div class="dab-media-recorder-preview"></div>
                        <div class="dab-media-recorder-controls">
                            <button type="button" class="button dab-media-record-start">
                                <span class="dashicons dashicons-controls-play"></span> <?php _e('Start Recording', 'db-app-builder'); ?>
                            </button>
                            <button type="button" class="button dab-media-record-stop" style="display:none;">
                                <span class="dashicons dashicons-controls-pause"></span> <?php _e('Stop Recording', 'db-app-builder'); ?>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Embed tab -->
                <?php if ($allow_embed): ?>
                <div class="dab-media-tab-pane" data-tab-pane="embed">
                    <input type="text"
                           id="<?php echo esc_attr($field_id . '-embed'); ?>"
                           class="dab-media-embed-url"
                           placeholder="<?php esc_attr_e('Enter YouTube, Vimeo, or other media URL', 'db-app-builder'); ?>">
                    <button type="button" class="button dab-media-embed-preview">
                        <?php _e('Preview', 'db-app-builder'); ?>
                    </button>
                    <div class="dab-media-embed-preview-container"></div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Media preview -->
            <?php if (!empty($media_data['url'])): ?>
            <div class="dab-media-preview">
                <?php echo $this->get_media_player_html($media_data, $player_width, $player_height); ?>
            </div>
            <?php endif; ?>

            <!-- Hidden field to store the media data -->
            <input type="hidden"
                   name="<?php echo esc_attr($field->field_slug); ?>"
                   id="<?php echo esc_attr($field_id . '-value'); ?>"
                   value="<?php echo esc_attr($value); ?>"
                   <?php echo $field->required ? 'required' : ''; ?>>
        </div>
        <?php
    }

    /**
     * Format media value for display
     *
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_media_value($value, $field, $context) {
        if ($field->field_type !== 'media') {
            return $value;
        }

        // Parse the value
        $media_data = $this->parse_media_value($value);

        if (empty($media_data['url'])) {
            return '';
        }

        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        $player_width = isset($options['player_width']) ? intval($options['player_width']) : 400;
        $player_height = isset($options['player_height']) ? intval($options['player_height']) : 300;

        // Format the media
        if ($context === 'html') {
            return $this->get_media_player_html($media_data, $player_width, $player_height);
        } else {
            return $media_data['type'] === 'embed' ? $media_data['url'] : wp_get_attachment_url($media_data['id']);
        }
    }

    /**
     * Parse media value
     *
     * @param string $value Media value
     * @return array Parsed media data
     */
    private function parse_media_value($value) {
        $media_data = array(
            'type' => '',
            'url' => '',
            'id' => 0,
            'mime_type' => '',
        );

        if (empty($value)) {
            return $media_data;
        }

        // Try to parse as JSON
        if (is_string($value) && strpos($value, '{') === 0) {
            $parsed_value = json_decode($value, true);
            if (is_array($parsed_value)) {
                return array_merge($media_data, $parsed_value);
            }
        }

        // If not JSON, assume it's an attachment ID
        if (is_numeric($value)) {
            $attachment_id = intval($value);
            $attachment_url = wp_get_attachment_url($attachment_id);
            $mime_type = get_post_mime_type($attachment_id);

            if ($attachment_url) {
                $media_data['type'] = 'upload';
                $media_data['url'] = $attachment_url;
                $media_data['id'] = $attachment_id;
                $media_data['mime_type'] = $mime_type;
            }
        } else {
            // Assume it's an embed URL
            $media_data['type'] = 'embed';
            $media_data['url'] = $value;
        }

        return $media_data;
    }

    /**
     * Get media player HTML
     *
     * @param array $media_data Media data
     * @param int $width Player width
     * @param int $height Player height
     * @return string Media player HTML
     */
    private function get_media_player_html($media_data, $width, $height) {
        $html = '';

        if (empty($media_data['url'])) {
            return $html;
        }

        if ($media_data['type'] === 'embed') {
            // Handle YouTube
            if (preg_match('/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/', $media_data['url'], $matches) ||
                preg_match('/youtu\.be\/([a-zA-Z0-9_-]+)/', $media_data['url'], $matches)) {
                $video_id = $matches[1];
                $html = '<iframe width="' . esc_attr($width) . '" height="' . esc_attr($height) . '" src="https://www.youtube.com/embed/' . esc_attr($video_id) . '" frameborder="0" allowfullscreen></iframe>';
            }
            // Handle Vimeo
            else if (preg_match('/vimeo\.com\/([0-9]+)/', $media_data['url'], $matches)) {
                $video_id = $matches[1];
                $html = '<iframe width="' . esc_attr($width) . '" height="' . esc_attr($height) . '" src="https://player.vimeo.com/video/' . esc_attr($video_id) . '" frameborder="0" allowfullscreen></iframe>';
            }
            // Default embed
            else {
                $html = '<a href="' . esc_url($media_data['url']) . '" target="_blank">' . esc_html($media_data['url']) . '</a>';
            }
        } else {
            // Handle uploaded media
            $mime_type = $media_data['mime_type'];
            $url = $media_data['url'];

            if (strpos($mime_type, 'audio/') === 0) {
                $html = '<audio controls style="width:' . esc_attr($width) . 'px;"><source src="' . esc_url($url) . '" type="' . esc_attr($mime_type) . '">Your browser does not support the audio element.</audio>';
            } else if (strpos($mime_type, 'video/') === 0) {
                $html = '<video controls width="' . esc_attr($width) . '" height="' . esc_attr($height) . '"><source src="' . esc_url($url) . '" type="' . esc_attr($mime_type) . '">Your browser does not support the video element.</video>';
            } else {
                $html = '<a href="' . esc_url($url) . '" target="_blank">' . esc_html(basename($url)) . '</a>';
            }
        }

        return $html;
    }

    /**
     * Get accept attribute for file input
     *
     * @param string $media_type Media type
     * @param array $allowed_formats Allowed formats
     * @return string Accept attribute
     */
    private function get_accept_attribute($media_type, $allowed_formats) {
        $accept = array();

        if ($media_type === 'audio' || $media_type === 'both') {
            $audio_formats = array_intersect($allowed_formats, array('mp3', 'wav', 'ogg'));
            foreach ($audio_formats as $format) {
                $accept[] = 'audio/' . $format;
            }
        }

        if ($media_type === 'video' || $media_type === 'both') {
            $video_formats = array_intersect($allowed_formats, array('mp4', 'webm', 'mov'));
            foreach ($video_formats as $format) {
                $mime = $format === 'mov' ? 'video/quicktime' : 'video/' . $format;
                $accept[] = $mime;
            }
        }

        return implode(',', $accept);
    }

    /**
     * Handle media upload
     */
    public function handle_media_upload() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_media_upload')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check if file is uploaded
        if (empty($_FILES['media_file'])) {
            wp_send_json_error('No file uploaded');
        }

        // Handle file upload
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        $attachment_id = media_handle_upload('media_file', 0);

        if (is_wp_error($attachment_id)) {
            wp_send_json_error($attachment_id->get_error_message());
        }

        $attachment_url = wp_get_attachment_url($attachment_id);
        $mime_type = get_post_mime_type($attachment_id);

        wp_send_json_success(array(
            'id' => $attachment_id,
            'url' => $attachment_url,
            'mime_type' => $mime_type,
            'type' => 'upload'
        ));
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue custom styles
        wp_enqueue_style(
            'dab-media-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/media-field.css',
            array(),
            DAB_VERSION
        );

        // Enqueue custom scripts
        wp_enqueue_script(
            'dab-media-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/media-field.js',
            array('jquery'),
            DAB_VERSION,
            true
        );

        // Localize script
        wp_localize_script('dab-media-field', 'dabMediaField', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_media_upload'),
            'i18n' => array(
                'startRecording' => __('Start Recording', 'db-app-builder'),
                'stopRecording' => __('Stop Recording', 'db-app-builder'),
                'errorRecording' => __('Error accessing media device', 'db-app-builder'),
                'browserNotSupported' => __('Your browser does not support media recording', 'db-app-builder'),
                'uploadError' => __('Error uploading file', 'db-app-builder'),
                'invalidUrl' => __('Please enter a valid media URL', 'db-app-builder'),
                'fileTooBig' => __('File size exceeds the maximum allowed size', 'db-app-builder'),
                'invalidFileType' => __('File type not allowed', 'db-app-builder'),
            )
        ));
    }
}

// Initialize the class
new DAB_Media_Field();
