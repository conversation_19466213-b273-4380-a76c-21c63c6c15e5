<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Relationship_Manager {

    public static function get_relationship_by_field($table_id, $field_slug) {
        global $wpdb;
        $relationships_table = $wpdb->prefix . 'dab_relationships';

        $relationship = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $relationships_table WHERE table_a_id = %d AND field_a_slug = %s",
                $table_id, $field_slug
            )
        );

        return $relationship;
    }

    public static function get_related_records($table_id, $display_column = '') {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';

        // Fetch the related table slug
        $related_table = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT table_slug FROM $tables_table WHERE id = %d",
                $table_id
            )
        );

        if (!$related_table) {
            return [];
        }

        $related_table_name = $wpdb->prefix . 'dab_' . $related_table->table_slug;

        if ($display_column) {
            $query = "SELECT id, `$display_column` FROM $related_table_name ORDER BY id DESC";
        } else {
            $query = "SELECT id FROM $related_table_name ORDER BY id DESC";
        }

        return $wpdb->get_results($query);
    }

    /**
     * Create a relationship between two tables
     */
    public static function create_relationship($table_a_id, $table_b_id, $relationship_type, $field_a_slug, $field_b_slug = null) {
        global $wpdb;

        $relationships_table = $wpdb->prefix . 'dab_relationships';

        // Check if relationship already exists
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $relationships_table
            WHERE (table_a_id = %d AND table_b_id = %d)
            OR (table_a_id = %d AND table_b_id = %d)",
            $table_a_id, $table_b_id, $table_b_id, $table_a_id
        ));

        if ($existing) {
            return false; // Relationship already exists
        }

        // Create the relationship
        $result = $wpdb->insert(
            $relationships_table,
            [
                'table_a_id' => $table_a_id,
                'table_b_id' => $table_b_id,
                'relationship_type' => $relationship_type,
                'field_a_slug' => $field_a_slug,
                'field_b_slug' => $field_b_slug,
                'created_at' => current_time('mysql')
            ]
        );

        if ($result) {
            // Ensure foreign key columns exist in both tables
            $tables_table = $wpdb->prefix . 'dab_tables';

            $table_a = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id = %d",
                $table_a_id
            ));

            $table_b = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id = %d",
                $table_b_id
            ));

            if ($table_a && $table_b) {
                $table_a_name = $wpdb->prefix . 'dab_' . sanitize_title($table_a->table_slug);
                $table_b_name = $wpdb->prefix . 'dab_' . sanitize_title($table_b->table_slug);

                // Create foreign key columns
                DAB_DB_Manager::ensure_column_exists($table_b_name, $field_a_slug, 'BIGINT(20)');

                if ($field_b_slug) {
                    DAB_DB_Manager::ensure_column_exists($table_a_name, $field_b_slug, 'BIGINT(20)');
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Placeholder for backward compatibility
     */
    public static function create_inline_table_relationship($parent_table_id, $inline_table_id) {
        // This function is kept as a placeholder for backward compatibility
        // but doesn't do anything since inline table functionality has been removed
        return false;
    }
}
