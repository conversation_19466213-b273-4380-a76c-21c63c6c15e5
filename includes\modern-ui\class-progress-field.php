<?php
/**
 * Progress Tracker Field Type
 * 
 * Progress bars and status indicators for tracking completion
 * 
 * @package Database App Builder
 * @subpackage Modern UI Components
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Progress_Field {
    
    /**
     * Initialize the Progress field
     */
    public static function init() {
        add_filter('dab_field_types', array(__CLASS__, 'register_progress_field_type'));
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_update_progress', array(__CLASS__, 'update_progress'));
        add_action('wp_ajax_nopriv_dab_update_progress', array(__CLASS__, 'update_progress'));
        
        // Create database tables
        add_action('init', array(__CLASS__, 'create_tables'));
    }
    
    /**
     * Register the Progress field type
     */
    public static function register_progress_field_type($field_types) {
        $field_types['progress_tracker'] = __('Progress Tracker', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'dab-progress-tracker',
            plugin_dir_url(__FILE__) . '../../assets/css/progress-tracker.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'dab-progress-tracker',
            plugin_dir_url(__FILE__) . '../../assets/js/progress-tracker.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('dab-progress-tracker', 'dabProgressData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_progress_nonce'),
            'i18n' => array(
                'updateProgress' => __('Update Progress', 'db-app-builder'),
                'addMilestone' => __('Add Milestone', 'db-app-builder'),
                'editMilestone' => __('Edit Milestone', 'db-app-builder'),
                'deleteMilestone' => __('Delete Milestone', 'db-app-builder'),
                'completed' => __('Completed', 'db-app-builder'),
                'inProgress' => __('In Progress', 'db-app-builder'),
                'notStarted' => __('Not Started', 'db-app-builder'),
                'saveSuccess' => __('Progress updated successfully', 'db-app-builder'),
                'saveError' => __('Error updating progress', 'db-app-builder')
            )
        ));
    }
    
    /**
     * Create database tables for Progress data
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Progress trackers table
        $progress_trackers_table = $wpdb->prefix . 'dab_progress_trackers';
        $sql_trackers = "CREATE TABLE $progress_trackers_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            field_id bigint(20) NOT NULL,
            record_id bigint(20) NOT NULL,
            current_value decimal(5,2) DEFAULT 0.00,
            target_value decimal(5,2) DEFAULT 100.00,
            unit varchar(50) DEFAULT 'percent',
            status varchar(50) DEFAULT 'not-started',
            milestones longtext,
            settings longtext,
            updated_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY field_id (field_id),
            KEY record_id (record_id),
            KEY status (status),
            KEY updated_by (updated_by)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_trackers);
    }
    
    /**
     * Render the Progress field in forms
     */
    public static function render_field($field, $value = '', $record_id = 0) {
        $field_id = 'dab-progress-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        
        // Get existing progress data
        $progress_data = self::get_progress_data($field->id, $record_id);
        
        // Parse field options
        $field_options = !empty($field->field_options) ? json_decode($field->field_options, true) : array();
        $progress_type = isset($field_options['progress_type']) ? $field_options['progress_type'] : 'percentage';
        $show_milestones = isset($field_options['show_milestones']) ? $field_options['show_milestones'] : true;
        $allow_manual_update = isset($field_options['allow_manual_update']) ? $field_options['allow_manual_update'] : true;
        
        echo '<div class="dab-progress-container" id="' . $field_id . '" data-field-id="' . $field->id . '" data-record-id="' . $record_id . '" data-progress-type="' . $progress_type . '">';
        
        // Progress header
        echo '<div class="dab-progress-header">';
        echo '<div class="dab-progress-title">' . esc_html($field->field_label) . '</div>';
        if ($allow_manual_update) {
            echo '<div class="dab-progress-controls">';
            echo '<button type="button" class="dab-btn dab-btn-sm dab-btn-primary dab-update-progress">';
            echo '<span class="dashicons dashicons-edit"></span> ' . __('Update', 'db-app-builder');
            echo '</button>';
            echo '</div>';
        }
        echo '</div>';
        
        // Progress display
        self::render_progress_display($progress_data, $progress_type);
        
        // Milestones
        if ($show_milestones) {
            self::render_milestones($progress_data);
        }
        
        // Hidden input to store progress data
        echo '<input type="hidden" name="' . $field_name . '" id="' . $field_name . '" value="' . esc_attr(json_encode($progress_data)) . '">';
        
        echo '</div>';
        
        // Add modal for progress editing
        if ($allow_manual_update) {
            self::render_progress_modal($progress_type);
        }
    }
    
    /**
     * Render progress display
     */
    private static function render_progress_display($progress_data, $progress_type) {
        $current_value = isset($progress_data['current_value']) ? floatval($progress_data['current_value']) : 0;
        $target_value = isset($progress_data['target_value']) ? floatval($progress_data['target_value']) : 100;
        $unit = isset($progress_data['unit']) ? $progress_data['unit'] : 'percent';
        $status = isset($progress_data['status']) ? $progress_data['status'] : 'not-started';
        
        // Calculate percentage for progress bar
        $percentage = $target_value > 0 ? ($current_value / $target_value) * 100 : 0;
        $percentage = min(100, max(0, $percentage));
        
        // Status colors
        $status_colors = array(
            'not-started' => '#95a5a6',
            'in-progress' => '#3498db',
            'completed' => '#27ae60',
            'overdue' => '#e74c3c',
            'on-hold' => '#f39c12'
        );
        
        $status_color = isset($status_colors[$status]) ? $status_colors[$status] : '#3498db';
        
        echo '<div class="dab-progress-display">';
        
        // Progress bar
        echo '<div class="dab-progress-bar-container">';
        echo '<div class="dab-progress-bar">';
        echo '<div class="dab-progress-fill" style="width: ' . $percentage . '%; background-color: ' . $status_color . '"></div>';
        echo '</div>';
        echo '<div class="dab-progress-percentage">' . round($percentage, 1) . '%</div>';
        echo '</div>';
        
        // Progress details
        echo '<div class="dab-progress-details">';
        echo '<div class="dab-progress-values">';
        
        if ($progress_type === 'percentage') {
            echo '<span class="dab-current-value">' . round($percentage, 1) . '%</span>';
        } else {
            echo '<span class="dab-current-value">' . $current_value . '</span>';
            echo '<span class="dab-separator"> / </span>';
            echo '<span class="dab-target-value">' . $target_value . '</span>';
            if ($unit !== 'percent') {
                echo ' <span class="dab-unit">' . esc_html($unit) . '</span>';
            }
        }
        
        echo '</div>';
        
        echo '<div class="dab-progress-status">';
        echo '<span class="dab-status-badge dab-status-' . $status . '">' . ucfirst(str_replace('-', ' ', $status)) . '</span>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render milestones
     */
    private static function render_milestones($progress_data) {
        $milestones = isset($progress_data['milestones']) ? $progress_data['milestones'] : array();
        
        if (empty($milestones)) {
            return;
        }
        
        echo '<div class="dab-progress-milestones">';
        echo '<h4 class="dab-milestones-title">' . __('Milestones', 'db-app-builder') . '</h4>';
        
        echo '<div class="dab-milestones-list">';
        foreach ($milestones as $milestone) {
            $milestone_value = isset($milestone['value']) ? floatval($milestone['value']) : 0;
            $milestone_title = isset($milestone['title']) ? $milestone['title'] : '';
            $milestone_status = isset($milestone['status']) ? $milestone['status'] : 'pending';
            
            $status_class = 'dab-milestone-' . $milestone_status;
            
            echo '<div class="dab-milestone-item ' . $status_class . '">';
            echo '<div class="dab-milestone-marker">';
            if ($milestone_status === 'completed') {
                echo '<span class="dashicons dashicons-yes"></span>';
            } else {
                echo '<span class="dashicons dashicons-marker"></span>';
            }
            echo '</div>';
            echo '<div class="dab-milestone-content">';
            echo '<div class="dab-milestone-title">' . esc_html($milestone_title) . '</div>';
            echo '<div class="dab-milestone-value">' . $milestone_value . '%</div>';
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render progress editing modal
     */
    private static function render_progress_modal($progress_type) {
        echo '<div id="dab-progress-modal" class="dab-modal" style="display: none;">';
        echo '<div class="dab-modal-content">';
        echo '<div class="dab-modal-header">';
        echo '<h3>' . __('Update Progress', 'db-app-builder') . '</h3>';
        echo '<button type="button" class="dab-modal-close">&times;</button>';
        echo '</div>';
        echo '<div class="dab-modal-body">';
        
        if ($progress_type === 'percentage') {
            echo '<div class="dab-form-field">';
            echo '<label for="dab-progress-percentage">' . __('Progress Percentage', 'db-app-builder') . '</label>';
            echo '<div class="dab-progress-input-group">';
            echo '<input type="number" id="dab-progress-percentage" class="dab-form-control" min="0" max="100" step="0.1">';
            echo '<span class="dab-input-suffix">%</span>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="dab-form-row">';
            echo '<div class="dab-form-field dab-form-field-half">';
            echo '<label for="dab-progress-current">' . __('Current Value', 'db-app-builder') . '</label>';
            echo '<input type="number" id="dab-progress-current" class="dab-form-control" min="0" step="0.01">';
            echo '</div>';
            echo '<div class="dab-form-field dab-form-field-half">';
            echo '<label for="dab-progress-target">' . __('Target Value', 'db-app-builder') . '</label>';
            echo '<input type="number" id="dab-progress-target" class="dab-form-control" min="0" step="0.01">';
            echo '</div>';
            echo '</div>';
            
            echo '<div class="dab-form-field">';
            echo '<label for="dab-progress-unit">' . __('Unit', 'db-app-builder') . '</label>';
            echo '<input type="text" id="dab-progress-unit" class="dab-form-control" placeholder="e.g., tasks, hours, points">';
            echo '</div>';
        }
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-progress-status">' . __('Status', 'db-app-builder') . '</label>';
        echo '<select id="dab-progress-status" class="dab-form-control">';
        echo '<option value="not-started">' . __('Not Started', 'db-app-builder') . '</option>';
        echo '<option value="in-progress">' . __('In Progress', 'db-app-builder') . '</option>';
        echo '<option value="completed">' . __('Completed', 'db-app-builder') . '</option>';
        echo '<option value="on-hold">' . __('On Hold', 'db-app-builder') . '</option>';
        echo '<option value="overdue">' . __('Overdue', 'db-app-builder') . '</option>';
        echo '</select>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-progress-notes">' . __('Notes', 'db-app-builder') . '</label>';
        echo '<textarea id="dab-progress-notes" class="dab-form-control" rows="3" placeholder="Optional notes about this progress update..."></textarea>';
        echo '</div>';
        
        echo '</div>';
        echo '<div class="dab-modal-footer">';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-modal-close">' . __('Cancel', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-save-progress">' . __('Update Progress', 'db-app-builder') . '</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Get progress data for a field and record
     */
    private static function get_progress_data($field_id, $record_id) {
        global $wpdb;
        
        $progress_trackers_table = $wpdb->prefix . 'dab_progress_trackers';
        
        $progress = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $progress_trackers_table WHERE field_id = %d AND record_id = %d",
            $field_id, $record_id
        ));
        
        if ($progress) {
            return array(
                'current_value' => $progress->current_value,
                'target_value' => $progress->target_value,
                'unit' => $progress->unit,
                'status' => $progress->status,
                'milestones' => !empty($progress->milestones) ? json_decode($progress->milestones, true) : array(),
                'settings' => !empty($progress->settings) ? json_decode($progress->settings, true) : array()
            );
        }
        
        return array(
            'current_value' => 0,
            'target_value' => 100,
            'unit' => 'percent',
            'status' => 'not-started',
            'milestones' => array(),
            'settings' => array()
        );
    }
    
    /**
     * AJAX handler to update progress
     */
    public static function update_progress() {
        check_ajax_referer('dab_progress_nonce', 'nonce');
        
        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        
        $progress_data = array(
            'field_id' => $field_id,
            'record_id' => $record_id,
            'current_value' => floatval($_POST['current_value']),
            'target_value' => floatval($_POST['target_value']),
            'unit' => sanitize_text_field($_POST['unit']),
            'status' => sanitize_text_field($_POST['status']),
            'updated_by' => get_current_user_id()
        );
        
        if (!$field_id) {
            wp_send_json_error(__('Invalid field ID', 'db-app-builder'));
        }
        
        global $wpdb;
        $progress_trackers_table = $wpdb->prefix . 'dab_progress_trackers';
        
        // Check if progress record exists
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM $progress_trackers_table WHERE field_id = %d AND record_id = %d",
            $field_id, $record_id
        ));
        
        try {
            if ($existing) {
                // Update existing progress
                $result = $wpdb->update($progress_trackers_table, $progress_data, array('id' => $existing->id));
            } else {
                // Create new progress record
                $result = $wpdb->insert($progress_trackers_table, $progress_data);
            }
            
            if ($result !== false) {
                wp_send_json_success(__('Progress updated successfully', 'db-app-builder'));
            } else {
                wp_send_json_error(__('Failed to update progress', 'db-app-builder'));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(__('Error updating progress', 'db-app-builder'));
        }
    }
}
