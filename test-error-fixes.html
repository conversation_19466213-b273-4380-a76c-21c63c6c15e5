<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - JavaScript Error Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #0073aa;
        }
        .test-button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .success {
            color: #46b450;
            font-weight: bold;
        }
        .error {
            color: #dc3232;
            font-weight: bold;
        }
        .info {
            color: #0073aa;
            font-weight: bold;
        }
        #test-results {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
            min-height: 100px;
        }
        .share-test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Database App Builder - JavaScript Error Fixes Test</h1>
    <p>This page tests the JavaScript error fixes implemented for the Database App Builder plugin.</p>

    <div class="test-section">
        <h2>1. Error Handler Test</h2>
        <p>Tests the global error handler and safe DOM operations.</p>
        <button class="test-button" onclick="testErrorHandler()">Test Error Handler</button>
        <button class="test-button" onclick="testSafeSelectors()">Test Safe Selectors</button>
        <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
    </div>

    <div class="test-section">
        <h2>2. Share Modal Test</h2>
        <p>Tests the share modal functionality and social media integration.</p>
        <div class="share-test-buttons">
            <button class="test-button dab-share-btn" 
                    data-share-url="https://example.com" 
                    data-share-title="Test Share" 
                    data-share-text="Testing the share functionality">
                Test Share Modal
            </button>
            <button class="test-button" onclick="testNativeShare()">Test Native Share</button>
            <button class="test-button" onclick="testCopyToClipboard()">Test Copy to Clipboard</button>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Passive Event Listener Test</h2>
        <p>Tests passive event listeners to prevent scroll blocking warnings.</p>
        <div id="scroll-test-area" style="height: 200px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px;">
            <div style="height: 500px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);">
                <p>Scroll this area to test passive event listeners.</p>
                <p>Check the browser console for any passive event listener warnings.</p>
                <p>This content is intentionally tall to enable scrolling.</p>
                <p>The scroll events should be handled with passive: true option.</p>
                <p>No violations should appear in the console.</p>
            </div>
        </div>
        <button class="test-button" onclick="testScrollEvents()">Add Scroll Listeners</button>
    </div>

    <div class="test-section">
        <h2>4. Missing Element Test</h2>
        <p>Tests handling of missing DOM elements to prevent null reference errors.</p>
        <button class="test-button" onclick="testMissingElements()">Test Missing Elements</button>
        <button class="test-button" onclick="testPlaceholderCreation()">Test Placeholder Creation</button>
    </div>

    <div id="test-results">
        <h3>Test Results</h3>
        <p>Click the test buttons above to see results here.</p>
    </div>

    <!-- Include jQuery for compatibility -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Include our error handler and share modal scripts -->
    <script>
        // Simulate the DAB error handler and share modal
        // In a real WordPress environment, these would be loaded from the plugin files
        
        // Basic error handler simulation
        window.DAB_ErrorHandler = {
            init: function() {
                this.setupGlobalErrorHandlers();
                this.setupDOMHelpers();
                this.setupEventHelpers();
            },
            
            setupGlobalErrorHandlers: function() {
                window.addEventListener('error', function(e) {
                    logResult('Error caught by global handler: ' + e.message, 'info');
                    return true;
                }, { passive: true });
            },
            
            setupDOMHelpers: function() {
                window.DAB_safeSelect = function(selector) {
                    try {
                        return document.querySelector(selector);
                    } catch (e) {
                        logResult('Safe selector prevented error for: ' + selector, 'success');
                        return null;
                    }
                };
            },
            
            setupEventHelpers: function() {
                window.DAB_safeAddEventListener = function(element, event, handler, options) {
                    try {
                        if (!element || typeof element.addEventListener !== 'function') {
                            return false;
                        }
                        const safeOptions = options || { passive: true };
                        element.addEventListener(event, handler, safeOptions);
                        return true;
                    } catch (e) {
                        logResult('Safe event listener prevented error', 'success');
                        return false;
                    }
                };
            },
            
            safeModal: {
                show: function(modalId) {
                    const modal = document.getElementById(modalId);
                    if (modal) {
                        modal.style.display = 'flex';
                        logResult('Modal shown: ' + modalId, 'success');
                    } else {
                        logResult('Modal not found, but error prevented: ' + modalId, 'info');
                    }
                }
            }
        };

        // Initialize error handler
        DAB_ErrorHandler.init();

        // Test functions
        function logResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function testErrorHandler() {
            logResult('Testing error handler...', 'info');
            
            // Test global error handling
            try {
                throw new Error('Test error for global handler');
            } catch (e) {
                logResult('Error thrown and caught: ' + e.message, 'success');
            }
            
            // Test safe operations
            const result = DAB_safeSelect('#non-existent-element');
            if (result === null) {
                logResult('Safe selector correctly returned null for missing element', 'success');
            }
        }

        function testSafeSelectors() {
            logResult('Testing safe selectors...', 'info');
            
            // Test valid selector
            const validElement = DAB_safeSelect('body');
            if (validElement) {
                logResult('Safe selector found valid element: body', 'success');
            }
            
            // Test invalid selector
            const invalidElement = DAB_safeSelect('invalid>>selector');
            logResult('Safe selector handled invalid selector gracefully', 'success');
        }

        function testEventListeners() {
            logResult('Testing event listeners...', 'info');
            
            const testDiv = document.createElement('div');
            document.body.appendChild(testDiv);
            
            const success = DAB_safeAddEventListener(testDiv, 'click', function() {
                logResult('Safe event listener fired successfully', 'success');
            });
            
            if (success) {
                testDiv.click();
                document.body.removeChild(testDiv);
            }
        }

        function testNativeShare() {
            logResult('Testing native share...', 'info');
            
            if (navigator.share) {
                navigator.share({
                    title: 'Test Share',
                    text: 'Testing native share functionality',
                    url: window.location.href
                }).then(() => {
                    logResult('Native share completed successfully', 'success');
                }).catch((error) => {
                    logResult('Native share cancelled or failed: ' + error, 'info');
                });
            } else {
                logResult('Native share not supported in this browser', 'info');
            }
        }

        function testCopyToClipboard() {
            logResult('Testing copy to clipboard...', 'info');
            
            const testText = 'Test clipboard functionality';
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(testText).then(() => {
                    logResult('Text copied to clipboard successfully', 'success');
                }).catch(() => {
                    logResult('Clipboard copy failed, but error handled', 'info');
                });
            } else {
                logResult('Clipboard API not supported in this browser', 'info');
            }
        }

        function testScrollEvents() {
            logResult('Testing scroll events...', 'info');
            
            const scrollArea = document.getElementById('scroll-test-area');
            
            // Add scroll listener with passive option
            scrollArea.addEventListener('scroll', function() {
                // This should not cause passive event warnings
            }, { passive: true });
            
            logResult('Scroll event listener added with passive: true', 'success');
            logResult('Scroll the test area and check console for warnings', 'info');
        }

        function testMissingElements() {
            logResult('Testing missing element handling...', 'info');
            
            // Try to access elements that don't exist
            const missing1 = document.getElementById('non-existent-modal');
            const missing2 = document.querySelector('.missing-class');
            
            if (!missing1 && !missing2) {
                logResult('Missing elements correctly returned null/undefined', 'success');
            }
            
            // Test safe modal operation on missing element
            DAB_ErrorHandler.safeModal.show('non-existent-modal');
        }

        function testPlaceholderCreation() {
            logResult('Testing placeholder creation...', 'info');
            
            // Create placeholder for missing share modal
            if (!document.getElementById('share-modal')) {
                const placeholder = document.createElement('div');
                placeholder.id = 'share-modal';
                placeholder.style.display = 'none';
                placeholder.className = 'dab-error-prevention-placeholder';
                document.body.appendChild(placeholder);
                
                logResult('Placeholder created for share-modal', 'success');
            }
            
            // Test that placeholder prevents errors
            DAB_ErrorHandler.safeModal.show('share-modal');
        }

        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            logResult('Page loaded, error fixes initialized', 'success');
            logResult('Open browser console to monitor for JavaScript errors', 'info');
        });
    </script>
</body>
</html>
