/**
 * Form Wizard JavaScript
 * 
 * Multi-step form functionality for the Database App Builder plugin
 */

(function($) {
    'use strict';

    // Global variables
    let currentStep = 1;
    let totalSteps = 0;
    let formData = {};
    let isDirty = false;
    let autoSaveInterval = null;

    /**
     * Initialize form wizards
     */
    function initFormWizards() {
        $('.dab-multistep-form-container').each(function() {
            const container = $(this);
            initFormWizard(container);
        });
    }

    /**
     * Initialize a single form wizard
     */
    function initFormWizard(container) {
        const form = container.find('.dab-multistep-form');
        const steps = form.find('.dab-form-step');
        
        totalSteps = steps.length;
        currentStep = parseInt(container.find('#dab_current_step').val()) || 1;
        
        // Initialize form data
        loadFormData(container);
        
        // Bind events
        bindWizardEvents(container);
        
        // Setup auto-save
        setupAutoSave(container);
        
        // Setup form validation
        setupFormValidation(container);
        
        // Update progress indicator
        updateProgressIndicator(container);
        
        // Show current step
        showStep(container, currentStep);
        
        // Setup conditional logic
        initConditionalLogic(container);
        
        // Warn before leaving with unsaved changes
        setupUnloadWarning();
    }

    /**
     * Bind wizard events
     */
    function bindWizardEvents(container) {
        // Next step button
        container.on('click', '.dab-next-step', function() {
            if (validateCurrentStep(container)) {
                nextStep(container);
            }
        });

        // Previous step button
        container.on('click', '.dab-prev-step', function() {
            prevStep(container);
        });

        // Save progress button
        container.on('click', '.dab-save-progress', function() {
            saveProgress(container);
        });

        // Form submission
        container.find('.dab-multistep-form').on('submit', function(e) {
            e.preventDefault();
            if (validateCurrentStep(container)) {
                submitForm(container);
            }
        });

        // Track form changes
        container.on('change input', '.dab-form-control', function() {
            isDirty = true;
            updateFormData(container);
        });

        // Progress step click navigation
        container.on('click', '.dab-progress-step', function() {
            const targetStep = parseInt($(this).data('step'));
            if (targetStep < currentStep || validateStepsUpTo(container, targetStep - 1)) {
                goToStep(container, targetStep);
            }
        });
    }

    /**
     * Load existing form data
     */
    function loadFormData(container) {
        const multistepFormId = container.find('input[name="dab_multistep_form_id"]').val();
        
        $.ajax({
            url: dabFormWizardData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_get_form_progress',
                multistep_form_id: multistepFormId,
                nonce: dabFormWizardData.nonce
            },
            success: function(response) {
                if (response.success && response.data.form_data) {
                    formData = response.data.form_data;
                    populateFormFields(container, formData);
                    currentStep = response.data.current_step || 1;
                    updateProgressIndicator(container);
                    showStep(container, currentStep);
                }
            },
            error: function() {
                console.error('Error loading form data');
            }
        });
    }

    /**
     * Populate form fields with data
     */
    function populateFormFields(container, data) {
        $.each(data, function(fieldName, value) {
            const field = container.find('[name="' + fieldName + '"]');
            
            if (field.length) {
                if (field.is(':radio')) {
                    field.filter('[value="' + value + '"]').prop('checked', true);
                } else if (field.is(':checkbox')) {
                    if (Array.isArray(value)) {
                        field.each(function() {
                            if (value.indexOf($(this).val()) !== -1) {
                                $(this).prop('checked', true);
                            }
                        });
                    } else {
                        field.prop('checked', value == '1' || value === true);
                    }
                } else {
                    field.val(value);
                }
            }
        });
    }

    /**
     * Update form data from current field values
     */
    function updateFormData(container) {
        const form = container.find('.dab-multistep-form');
        const serializedData = form.serializeArray();
        
        formData = {};
        
        $.each(serializedData, function(i, field) {
            if (field.name.startsWith('dab_')) {
                return; // Skip system fields
            }
            
            if (formData[field.name]) {
                // Handle multiple values (checkboxes)
                if (!Array.isArray(formData[field.name])) {
                    formData[field.name] = [formData[field.name]];
                }
                formData[field.name].push(field.value);
            } else {
                formData[field.name] = field.value;
            }
        });
        
        // Handle unchecked checkboxes
        container.find('input[type="checkbox"]').each(function() {
            const name = $(this).attr('name');
            if (name && !name.startsWith('dab_') && !$(this).is(':checked') && !formData[name]) {
                formData[name] = '';
            }
        });
    }

    /**
     * Show specific step
     */
    function showStep(container, stepNumber) {
        const steps = container.find('.dab-form-step');
        
        steps.hide();
        steps.eq(stepNumber - 1).show();
        
        currentStep = stepNumber;
        container.find('#dab_current_step').val(currentStep);
        
        updateProgressIndicator(container);
        updateNavigationButtons(container);
        
        // Focus first field in step
        setTimeout(function() {
            const firstField = steps.eq(stepNumber - 1).find('.dab-form-control:visible:first');
            if (firstField.length) {
                firstField.focus();
            }
        }, 100);
    }

    /**
     * Go to next step
     */
    function nextStep(container) {
        if (currentStep < totalSteps) {
            showStep(container, currentStep + 1);
            saveProgress(container, false); // Auto-save progress
        }
    }

    /**
     * Go to previous step
     */
    function prevStep(container) {
        if (currentStep > 1) {
            showStep(container, currentStep - 1);
        }
    }

    /**
     * Go to specific step
     */
    function goToStep(container, stepNumber) {
        if (stepNumber >= 1 && stepNumber <= totalSteps) {
            showStep(container, stepNumber);
        }
    }

    /**
     * Update progress indicator
     */
    function updateProgressIndicator(container) {
        const progressSteps = container.find('.dab-progress-step');
        const progressFill = container.find('.dab-progress-fill');
        
        // Update step states
        progressSteps.each(function(index) {
            const step = $(this);
            const stepNumber = index + 1;
            
            step.removeClass('current completed');
            
            if (stepNumber === currentStep) {
                step.addClass('current');
            } else if (stepNumber < currentStep) {
                step.addClass('completed');
            }
        });
        
        // Update progress bar
        const progressPercentage = ((currentStep - 1) / totalSteps) * 100;
        progressFill.css('width', progressPercentage + '%');
    }

    /**
     * Update navigation buttons
     */
    function updateNavigationButtons(container) {
        const prevBtn = container.find('.dab-prev-step');
        const nextBtn = container.find('.dab-next-step');
        const submitBtn = container.find('.dab-submit-form');
        
        // Previous button
        if (currentStep === 1) {
            prevBtn.hide();
        } else {
            prevBtn.show();
        }
        
        // Next/Submit button
        if (currentStep === totalSteps) {
            nextBtn.hide();
            submitBtn.show();
        } else {
            nextBtn.show();
            submitBtn.hide();
        }
    }

    /**
     * Validate current step
     */
    function validateCurrentStep(container) {
        const currentStepElement = container.find('.dab-form-step').eq(currentStep - 1);
        const fields = currentStepElement.find('.dab-form-control');
        let isValid = true;
        
        // Clear previous errors
        currentStepElement.find('.dab-field-error').hide();
        currentStepElement.find('.dab-form-field').removeClass('has-error');
        
        fields.each(function() {
            const field = $(this);
            const fieldContainer = field.closest('.dab-form-field');
            const errorContainer = fieldContainer.find('.dab-field-error');
            
            // Required field validation
            if (field.prop('required') && !field.val().trim()) {
                showFieldError(fieldContainer, errorContainer, 'This field is required');
                isValid = false;
                return;
            }
            
            // Email validation
            if (field.attr('type') === 'email' && field.val()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(field.val())) {
                    showFieldError(fieldContainer, errorContainer, 'Please enter a valid email address');
                    isValid = false;
                    return;
                }
            }
            
            // Number validation
            if (field.attr('type') === 'number' && field.val()) {
                const min = field.attr('min');
                const max = field.attr('max');
                const value = parseFloat(field.val());
                
                if (min && value < parseFloat(min)) {
                    showFieldError(fieldContainer, errorContainer, `Value must be at least ${min}`);
                    isValid = false;
                    return;
                }
                
                if (max && value > parseFloat(max)) {
                    showFieldError(fieldContainer, errorContainer, `Value must be no more than ${max}`);
                    isValid = false;
                    return;
                }
            }
            
            // Custom validation
            const customValidation = field.data('validation');
            if (customValidation && field.val()) {
                // Add custom validation logic here
            }
        });
        
        if (!isValid) {
            // Scroll to first error
            const firstError = currentStepElement.find('.has-error:first');
            if (firstError.length) {
                firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            showNotification(dabFormWizardData.i18n.validationError, 'error');
        }
        
        return isValid;
    }

    /**
     * Validate steps up to a certain point
     */
    function validateStepsUpTo(container, stepNumber) {
        for (let i = 1; i <= stepNumber; i++) {
            const originalStep = currentStep;
            currentStep = i;
            if (!validateCurrentStep(container)) {
                currentStep = originalStep;
                return false;
            }
        }
        currentStep = stepNumber + 1;
        return true;
    }

    /**
     * Show field error
     */
    function showFieldError(fieldContainer, errorContainer, message) {
        fieldContainer.addClass('has-error');
        errorContainer.text(message).show();
    }

    /**
     * Save progress
     */
    function saveProgress(container, showNotification = true) {
        updateFormData(container);
        
        const multistepFormId = container.find('input[name="dab_multistep_form_id"]').val();
        const completedSteps = [];
        
        // Mark completed steps
        for (let i = 1; i < currentStep; i++) {
            completedSteps.push(i);
        }
        
        $.ajax({
            url: dabFormWizardData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_save_form_progress',
                multistep_form_id: multistepFormId,
                current_step: currentStep,
                form_data: formData,
                completed_steps: completedSteps,
                nonce: dabFormWizardData.nonce
            },
            success: function(response) {
                if (response.success) {
                    isDirty = false;
                    if (showNotification) {
                        showNotification(dabFormWizardData.i18n.saveSuccess, 'success');
                    }
                } else {
                    if (showNotification) {
                        showNotification(response.data || dabFormWizardData.i18n.saveError, 'error');
                    }
                }
            },
            error: function() {
                if (showNotification) {
                    showNotification(dabFormWizardData.i18n.saveError, 'error');
                }
            }
        });
    }

    /**
     * Submit form
     */
    function submitForm(container) {
        updateFormData(container);
        
        // Add completion data
        const form = container.find('.dab-multistep-form');
        form.append('<input type="hidden" name="dab_multistep_submit" value="1">');
        form.append('<input type="hidden" name="dab_form_data" value="' + JSON.stringify(formData) + '">');
        
        // Submit the form
        form.off('submit').submit();
    }

    /**
     * Setup auto-save
     */
    function setupAutoSave(container) {
        autoSaveInterval = setInterval(function() {
            if (isDirty) {
                saveProgress(container, false);
            }
        }, 30000); // Auto-save every 30 seconds
    }

    /**
     * Setup form validation
     */
    function setupFormValidation(container) {
        // Real-time validation
        container.on('blur', '.dab-form-control', function() {
            const field = $(this);
            const fieldContainer = field.closest('.dab-form-field');
            const errorContainer = fieldContainer.find('.dab-field-error');
            
            // Clear previous state
            fieldContainer.removeClass('has-error has-success');
            errorContainer.hide();
            
            // Validate field
            if (field.val().trim()) {
                if (field[0].checkValidity()) {
                    fieldContainer.addClass('has-success');
                } else {
                    fieldContainer.addClass('has-error');
                    errorContainer.text(field[0].validationMessage).show();
                }
            }
        });
    }

    /**
     * Initialize conditional logic
     */
    function initConditionalLogic(container) {
        // This would integrate with the conditional logic engine
        // For now, just a placeholder
        container.on('change', '.dab-form-control', function() {
            // Trigger conditional logic evaluation
            evaluateConditionalLogic(container);
        });
    }

    /**
     * Evaluate conditional logic
     */
    function evaluateConditionalLogic(container) {
        // Placeholder for conditional logic
        // This would be implemented in the conditional logic engine
    }

    /**
     * Setup unload warning
     */
    function setupUnloadWarning() {
        $(window).on('beforeunload', function() {
            if (isDirty) {
                return dabFormWizardData.i18n.confirmLeave;
            }
        });
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        // Create notification element
        const notification = $('<div class="dab-notification dab-notification-' + type + '">' + message + '</div>');
        
        // Add to page
        $('body').append(notification);
        
        // Show with animation
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        // Auto-hide after 3 seconds
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Initialize on document ready
     */
    $(document).ready(function() {
        initFormWizards();
    });

    // Cleanup on page unload
    $(window).on('unload', function() {
        if (autoSaveInterval) {
            clearInterval(autoSaveInterval);
        }
    });

})(jQuery);
