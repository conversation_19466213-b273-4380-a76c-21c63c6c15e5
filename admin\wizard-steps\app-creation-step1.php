<?php
/**
 * Application Creation Wizard - Step 1: Application Details
 */
if (!defined('ABSPATH')) exit;

// Get saved data
$app_name = isset($progress['data']['app_name']) ? $progress['data']['app_name'] : '';
$app_description = isset($progress['data']['app_description']) ? $progress['data']['app_description'] : '';
$app_type = isset($progress['data']['app_type']) ? $progress['data']['app_type'] : 'custom';
?>

<div class="dab-wizard-form">
    <div class="dab-wizard-form-group">
        <label for="app_name" class="dab-wizard-form-label"><?php _e('Application Name', 'db-app-builder'); ?> <span class="required">*</span></label>
        <input type="text" id="app_name" name="app_name" class="dab-wizard-form-input" value="<?php echo esc_attr($app_name); ?>" required>
        <p class="dab-wizard-form-help"><?php _e('Enter a descriptive name for your application.', 'db-app-builder'); ?></p>
    </div>

    <div class="dab-wizard-form-group">
        <label for="app_description" class="dab-wizard-form-label"><?php _e('Application Description', 'db-app-builder'); ?></label>
        <textarea id="app_description" name="app_description" class="dab-wizard-form-textarea"><?php echo esc_textarea($app_description); ?></textarea>
        <p class="dab-wizard-form-help"><?php _e('Provide a brief description of what this application will be used for.', 'db-app-builder'); ?></p>
    </div>

    <div class="dab-wizard-form-group">
        <label class="dab-wizard-form-label"><?php _e('Application Type', 'db-app-builder'); ?></label>
        
        <div class="dab-wizard-template-options">
            <div class="dab-wizard-template-option <?php echo $app_type === 'custom' ? 'selected' : ''; ?>">
                <input type="radio" id="app_type_custom" name="app_type" value="custom" <?php checked($app_type, 'custom'); ?>>
                <label for="app_type_custom">
                    <div class="dab-wizard-template-icon">
                        <span class="dashicons dashicons-admin-customizer"></span>
                    </div>
                    <div class="dab-wizard-template-content">
                        <h4><?php _e('Custom Application', 'db-app-builder'); ?></h4>
                        <p><?php _e('Start with a blank slate and build your application from scratch.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>

            <div class="dab-wizard-template-option <?php echo $app_type === 'crm' ? 'selected' : ''; ?>">
                <input type="radio" id="app_type_crm" name="app_type" value="crm" <?php checked($app_type, 'crm'); ?>>
                <label for="app_type_crm">
                    <div class="dab-wizard-template-icon">
                        <span class="dashicons dashicons-groups"></span>
                    </div>
                    <div class="dab-wizard-template-content">
                        <h4><?php _e('Customer Relationship Management (CRM)', 'db-app-builder'); ?></h4>
                        <p><?php _e('Manage customers, contacts, and interactions with pre-configured tables and fields.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>

            <div class="dab-wizard-template-option <?php echo $app_type === 'project' ? 'selected' : ''; ?>">
                <input type="radio" id="app_type_project" name="app_type" value="project" <?php checked($app_type, 'project'); ?>>
                <label for="app_type_project">
                    <div class="dab-wizard-template-icon">
                        <span class="dashicons dashicons-calendar-alt"></span>
                    </div>
                    <div class="dab-wizard-template-content">
                        <h4><?php _e('Project Management', 'db-app-builder'); ?></h4>
                        <p><?php _e('Track projects, tasks, and deadlines with pre-configured tables and fields.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>

            <div class="dab-wizard-template-option <?php echo $app_type === 'inventory' ? 'selected' : ''; ?>">
                <input type="radio" id="app_type_inventory" name="app_type" value="inventory" <?php checked($app_type, 'inventory'); ?>>
                <label for="app_type_inventory">
                    <div class="dab-wizard-template-icon">
                        <span class="dashicons dashicons-archive"></span>
                    </div>
                    <div class="dab-wizard-template-content">
                        <h4><?php _e('Inventory Management', 'db-app-builder'); ?></h4>
                        <p><?php _e('Track products, stock levels, and orders with pre-configured tables and fields.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>
        </div>
    </div>
</div>

<style>
.dab-wizard-template-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 10px;
}

.dab-wizard-template-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.dab-wizard-template-option:hover {
    border-color: #c0c0c0;
}

.dab-wizard-template-option.selected {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

.dab-wizard-template-option input[type="radio"] {
    display: none;
}

.dab-wizard-template-option label {
    display: flex;
    cursor: pointer;
    padding: 15px;
}

.dab-wizard-template-icon {
    margin-right: 15px;
}

.dab-wizard-template-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: #2271b1;
}

.dab-wizard-template-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.dab-wizard-template-content p {
    margin: 0;
    font-size: 13px;
    color: #666;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Template option selection
    $('.dab-wizard-template-option').on('click', function() {
        $('.dab-wizard-template-option').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
    });
});
</script>
