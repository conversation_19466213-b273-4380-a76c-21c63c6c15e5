/**
 * Database App Builder Design System
 * 
 * A modern, consistent design system for the Database App Builder plugin
 */

:root {
  /* Color Palette */
  --dab-primary: #4361ee;
  --dab-primary-light: #738aff;
  --dab-primary-dark: #2541b2;
  --dab-secondary: #3a0ca3;
  --dab-accent: #4cc9f0;
  --dab-success: #2ec4b6;
  --dab-warning: #ff9f1c;
  --dab-danger: #e71d36;
  --dab-info: #4cc9f0;
  
  /* Neutral Colors */
  --dab-white: #ffffff;
  --dab-gray-100: #f8f9fa;
  --dab-gray-200: #e9ecef;
  --dab-gray-300: #dee2e6;
  --dab-gray-400: #ced4da;
  --dab-gray-500: #adb5bd;
  --dab-gray-600: #6c757d;
  --dab-gray-700: #495057;
  --dab-gray-800: #343a40;
  --dab-gray-900: #212529;
  --dab-black: #000000;
  
  /* Typography */
  --dab-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  --dab-font-size-xs: 0.75rem;   /* 12px */
  --dab-font-size-sm: 0.875rem;  /* 14px */
  --dab-font-size-md: 1rem;      /* 16px */
  --dab-font-size-lg: 1.125rem;  /* 18px */
  --dab-font-size-xl: 1.25rem;   /* 20px */
  --dab-font-size-2xl: 1.5rem;   /* 24px */
  --dab-font-size-3xl: 1.875rem; /* 30px */
  --dab-font-size-4xl: 2.25rem;  /* 36px */
  
  /* Spacing */
  --dab-spacing-xs: 0.25rem;  /* 4px */
  --dab-spacing-sm: 0.5rem;   /* 8px */
  --dab-spacing-md: 1rem;     /* 16px */
  --dab-spacing-lg: 1.5rem;   /* 24px */
  --dab-spacing-xl: 2rem;     /* 32px */
  --dab-spacing-2xl: 3rem;    /* 48px */
  
  /* Border Radius */
  --dab-border-radius-sm: 0.25rem;  /* 4px */
  --dab-border-radius-md: 0.5rem;   /* 8px */
  --dab-border-radius-lg: 0.75rem;  /* 12px */
  --dab-border-radius-xl: 1rem;     /* 16px */
  --dab-border-radius-full: 9999px;
  
  /* Shadows */
  --dab-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --dab-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --dab-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --dab-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --dab-transition-fast: 150ms;
  --dab-transition-normal: 300ms;
  --dab-transition-slow: 500ms;
  
  /* Z-index */
  --dab-z-index-dropdown: 1000;
  --dab-z-index-sticky: 1020;
  --dab-z-index-fixed: 1030;
  --dab-z-index-modal-backdrop: 1040;
  --dab-z-index-modal: 1050;
  --dab-z-index-popover: 1060;
  --dab-z-index-tooltip: 1070;
}

/* Base Styles */
.dab-container * {
  box-sizing: border-box;
  font-family: var(--dab-font-family);
}

.dab-container {
  width: 100%;
  padding-right: var(--dab-spacing-md);
  padding-left: var(--dab-spacing-md);
  margin-right: auto;
  margin-left: auto;
  max-width: 1200px;
}

/* Typography */
.dab-heading {
  margin-top: 0;
  margin-bottom: var(--dab-spacing-md);
  font-weight: 600;
  line-height: 1.2;
  color: var(--dab-gray-900);
}

.dab-heading-1 {
  font-size: var(--dab-font-size-3xl);
}

.dab-heading-2 {
  font-size: var(--dab-font-size-2xl);
}

.dab-heading-3 {
  font-size: var(--dab-font-size-xl);
}

.dab-text {
  margin-top: 0;
  margin-bottom: var(--dab-spacing-md);
  color: var(--dab-gray-700);
}

.dab-text-sm {
  font-size: var(--dab-font-size-sm);
}

.dab-text-md {
  font-size: var(--dab-font-size-md);
}

.dab-text-lg {
  font-size: var(--dab-font-size-lg);
}

/* Buttons */
.dab-btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: var(--dab-font-size-md);
  line-height: 1.5;
  border-radius: var(--dab-border-radius-md);
  transition: all var(--dab-transition-normal) ease-in-out;
  cursor: pointer;
  text-decoration: none;
}

.dab-btn:focus, .dab-btn:hover {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.dab-btn-primary {
  color: var(--dab-white);
  background-color: var(--dab-primary);
  border-color: var(--dab-primary);
}

.dab-btn-primary:hover {
  background-color: var(--dab-primary-dark);
  border-color: var(--dab-primary-dark);
}

.dab-btn-secondary {
  color: var(--dab-white);
  background-color: var(--dab-secondary);
  border-color: var(--dab-secondary);
}

.dab-btn-success {
  color: var(--dab-white);
  background-color: var(--dab-success);
  border-color: var(--dab-success);
}

.dab-btn-danger {
  color: var(--dab-white);
  background-color: var(--dab-danger);
  border-color: var(--dab-danger);
}

.dab-btn-warning {
  color: var(--dab-gray-900);
  background-color: var(--dab-warning);
  border-color: var(--dab-warning);
}

.dab-btn-info {
  color: var(--dab-white);
  background-color: var(--dab-info);
  border-color: var(--dab-info);
}

.dab-btn-outline {
  background-color: transparent;
}

.dab-btn-outline-primary {
  color: var(--dab-primary);
  border-color: var(--dab-primary);
}

.dab-btn-outline-primary:hover {
  color: var(--dab-white);
  background-color: var(--dab-primary);
}

.dab-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--dab-font-size-sm);
}

.dab-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--dab-font-size-lg);
}

/* Cards */
.dab-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--dab-white);
  background-clip: border-box;
  border: 1px solid var(--dab-gray-300);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-sm);
  transition: box-shadow var(--dab-transition-normal) ease;
}

.dab-card:hover {
  box-shadow: var(--dab-shadow-md);
}

.dab-card-header {
  padding: var(--dab-spacing-md);
  margin-bottom: 0;
  background-color: var(--dab-gray-100);
  border-bottom: 1px solid var(--dab-gray-300);
  border-top-left-radius: var(--dab-border-radius-md);
  border-top-right-radius: var(--dab-border-radius-md);
}

.dab-card-body {
  flex: 1 1 auto;
  padding: var(--dab-spacing-md);
}

.dab-card-footer {
  padding: var(--dab-spacing-md);
  background-color: var(--dab-gray-100);
  border-top: 1px solid var(--dab-gray-300);
  border-bottom-left-radius: var(--dab-border-radius-md);
  border-bottom-right-radius: var(--dab-border-radius-md);
}

/* Forms */
.dab-form-group {
  margin-bottom: var(--dab-spacing-md);
}

.dab-form-label {
  display: inline-block;
  margin-bottom: var(--dab-spacing-sm);
  font-weight: 500;
}

.dab-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: var(--dab-font-size-md);
  line-height: 1.5;
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  background-clip: padding-box;
  border: 1px solid var(--dab-gray-400);
  border-radius: var(--dab-border-radius-md);
  transition: border-color var(--dab-transition-normal) ease-in-out, box-shadow var(--dab-transition-normal) ease-in-out;
}

.dab-form-control:focus {
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  border-color: var(--dab-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Animations */
@keyframes dab-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes dab-slide-up {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes dab-slide-down {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes dab-scale-in {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.dab-animate-fade-in {
  animation: dab-fade-in var(--dab-transition-normal) ease forwards;
}

.dab-animate-slide-up {
  animation: dab-slide-up var(--dab-transition-normal) ease forwards;
}

.dab-animate-slide-down {
  animation: dab-slide-down var(--dab-transition-normal) ease forwards;
}

.dab-animate-scale-in {
  animation: dab-scale-in var(--dab-transition-normal) ease forwards;
}
