/**
 * Enhanced Table Search and Export Functionality
 *
 * This script provides improved search, filtering, and export capabilities for data tables.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced search for all data tables
    initEnhancedTableSearch();

    // Initialize export functionality
    initExportFunctionality();

    /**
     * Initialize enhanced search functionality
     */
    function initEnhancedTableSearch() {
        const viewContainers = document.querySelectorAll('.dab-view-container');

        viewContainers.forEach(function(container) {
            const table = container.querySelector('.dab-view-table');
            if (!table) return;

            const viewId = container.getAttribute('data-view-id');

            // Check if container already has search functionality
            let searchContainer = container.previousElementSibling;
            if (searchContainer && searchContainer.classList.contains('dab-enhanced-search')) {
                // Search container already exists, skip this container
                return;
            }

            // Also check if there's a search container with the data-has-export attribute
            const existingSearchContainers = document.querySelectorAll('.dab-enhanced-search[data-has-export="true"]');
            if (existingSearchContainers.length > 0) {
                // There's already a search container with export functionality, skip this container
                return;
            }

            // Create enhanced search container
            searchContainer = document.createElement('div');
            searchContainer.className = 'dab-enhanced-search';
            container.insertBefore(searchContainer, table);

            // Create search form
            const searchForm = document.createElement('form');
            searchForm.className = 'dab-search-form';
            searchForm.method = 'get';
            searchContainer.appendChild(searchForm);

            // Add search input
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.name = 'dab_search';
            searchInput.className = 'dab-search-input';
            searchInput.placeholder = 'Search...';

            // Get current search value from URL
            const urlParams = new URLSearchParams(window.location.search);
            const currentSearch = urlParams.get('dab_search') || '';
            searchInput.value = currentSearch;

            searchForm.appendChild(searchInput);

            // Add column filter dropdown
            const columnFilter = document.createElement('select');
            columnFilter.name = 'dab_column';
            columnFilter.className = 'dab-column-filter';

            // Add "All Columns" option
            const allOption = document.createElement('option');
            allOption.value = '';
            allOption.textContent = 'All Columns';
            columnFilter.appendChild(allOption);

            // Add options for each column
            const headers = table.querySelectorAll('thead th');
            headers.forEach(function(header, index) {
                // Skip the Actions column
                if (header.textContent.trim() !== 'Actions') {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = header.textContent;

                    // Check if this column is currently selected
                    const currentColumn = urlParams.get('dab_column');
                    if (currentColumn === index.toString()) {
                        option.selected = true;
                    }

                    columnFilter.appendChild(option);
                }
            });

            searchForm.appendChild(columnFilter);

            // Add search button
            const searchButton = document.createElement('button');
            searchButton.type = 'submit';
            searchButton.className = 'dab-search-button';
            searchButton.textContent = 'Search';
            searchForm.appendChild(searchButton);

            // Add reset button
            const resetButton = document.createElement('button');
            resetButton.type = 'button';
            resetButton.className = 'dab-reset-button';
            resetButton.textContent = 'Reset';
            searchForm.appendChild(resetButton);

            // Reset button event listener
            resetButton.addEventListener('click', function() {
                // Clear search input and column filter
                searchInput.value = '';
                columnFilter.selectedIndex = 0;

                // Remove search parameters from URL and reload
                const url = new URL(window.location.href);
                url.searchParams.delete('dab_search');
                url.searchParams.delete('dab_column');
                window.location.href = url.toString();
            });

            // Add client-side filtering for faster results
            if (table.rows.length > 0) {
                searchInput.addEventListener('input', function() {
                    const searchText = this.value.toLowerCase();
                    const columnIndex = parseInt(columnFilter.value);

                    // Skip header row
                    for (let i = 1; i < table.rows.length; i++) {
                        const row = table.rows[i];
                        let showRow = false;

                        // If no column is selected, search all columns
                        if (isNaN(columnIndex)) {
                            for (let j = 0; j < row.cells.length; j++) {
                                const cell = row.cells[j];
                                if (cell.textContent.toLowerCase().includes(searchText)) {
                                    showRow = true;
                                    break;
                                }
                            }
                        } else {
                            // Search only the selected column
                            if (row.cells.length > columnIndex) {
                                const cell = row.cells[columnIndex];
                                if (cell.textContent.toLowerCase().includes(searchText)) {
                                    showRow = true;
                                }
                            }
                        }

                        row.style.display = showRow ? '' : 'none';
                    }
                });
            }
        });
    }

    /**
     * Initialize export functionality
     */
    function initExportFunctionality() {
        const viewContainers = document.querySelectorAll('.dab-view-container');

        viewContainers.forEach(function(container) {
            const table = container.querySelector('.dab-view-table');
            if (!table) return;

            const viewId = container.getAttribute('data-view-id');

            // Find the search container
            let searchContainer = container.previousElementSibling;
            if (!searchContainer || !searchContainer.classList.contains('dab-enhanced-search')) {
                // No search container found, skip this container
                return;
            }

            // Check if export container already exists
            if (searchContainer.querySelector('.dab-export-container')) {
                // Export container already exists, skip this container
                return;
            }

            // Also check if there's a search container with the data-has-export attribute
            const existingSearchContainers = document.querySelectorAll('.dab-enhanced-search[data-has-export="true"]');
            if (existingSearchContainers.length > 0) {
                // There's already a search container with export functionality, skip this container
                return;
            }

            // Create export container
            const exportContainer = document.createElement('div');
            exportContainer.className = 'dab-export-container';

            searchContainer.appendChild(exportContainer);

            // Add export button
            const exportButton = document.createElement('button');
            exportButton.type = 'button';
            exportButton.className = 'dab-export-button';
            exportButton.textContent = 'Export';
            exportContainer.appendChild(exportButton);

            // Add export options dropdown
            const exportOptions = document.createElement('div');
            exportOptions.className = 'dab-export-options';
            exportOptions.style.display = 'none';
            exportContainer.appendChild(exportOptions);

            // Add CSV export option
            const csvOption = document.createElement('a');
            csvOption.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'dab_export=csv';
            csvOption.className = 'dab-export-option';
            csvOption.textContent = 'Export as CSV';
            exportOptions.appendChild(csvOption);

            // Add Excel export option
            const excelOption = document.createElement('a');
            excelOption.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'dab_export=excel';
            excelOption.className = 'dab-export-option';
            excelOption.textContent = 'Export as Excel';
            exportOptions.appendChild(excelOption);

            // Add Print option
            const printOption = document.createElement('a');
            printOption.href = '#';
            printOption.className = 'dab-export-option';
            printOption.textContent = 'Print Table';
            exportOptions.appendChild(printOption);

            // Toggle export options on button click
            exportButton.addEventListener('click', function() {
                exportOptions.style.display = exportOptions.style.display === 'none' ? 'block' : 'none';
            });

            // Close export options when clicking outside
            document.addEventListener('click', function(event) {
                if (!exportContainer.contains(event.target)) {
                    exportOptions.style.display = 'none';
                }
            });

            // Print table functionality
            printOption.addEventListener('click', function(e) {
                e.preventDefault();
                printTable(table);
            });
        });
    }

    /**
     * Print a table
     */
    function printTable(table) {
        const printWindow = window.open('', '_blank');

        printWindow.document.write(`
            <html>
            <head>
                <title>Print Table</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    tr:nth-child(even) { background-color: #f9f9f9; }
                    @media print {
                        body { margin: 0; padding: 15px; }
                        table { page-break-inside: auto; }
                        tr { page-break-inside: avoid; page-break-after: auto; }
                        thead { display: table-header-group; }
                    }
                </style>
            </head>
            <body>
                ${table.outerHTML}
                <script>
                    window.onload = function() {
                        window.print();
                        window.setTimeout(function() { window.close(); }, 500);
                    };
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();
    }
});
