<?php
/**
 * Application Creation Wizard
 *
 * This wizard guides users through creating a new database application
 */
if (!defined('ABSPATH')) exit;

// Get wizard manager
global $wpdb;
$wizard_manager = new DAB_Wizard_Manager();

// Define wizard type
$wizard_type = 'app_creation';

// Get current step
$progress = $wizard_manager->get_wizard_progress($wizard_type);
$current_step = isset($_GET['step']) ? intval($_GET['step']) : $progress['current_step'];

// Define wizard steps
$steps = array(
    array(
        'title' => __('Application Details', 'db-app-builder'),
        'description' => __('Define your application name and purpose', 'db-app-builder')
    ),
    array(
        'title' => __('Table Structure', 'db-app-builder'),
        'description' => __('Create the main database table', 'db-app-builder')
    ),
    array(
        'title' => __('Fields Configuration', 'db-app-builder'),
        'description' => __('Add and configure table fields', 'db-app-builder')
    ),
    array(
        'title' => __('Form Creation', 'db-app-builder'),
        'description' => __('Create data entry forms', 'db-app-builder')
    ),
    array(
        'title' => __('Data View', 'db-app-builder'),
        'description' => __('Configure how data is displayed', 'db-app-builder')
    ),
    array(
        'title' => __('Completion', 'db-app-builder'),
        'description' => __('Finalize and deploy your application', 'db-app-builder')
    )
);

// Enqueue styles and scripts
wp_enqueue_style('dab-admin-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-style.css', array(), DAB_VERSION);
wp_enqueue_style('dab-modern-admin', plugin_dir_url(dirname(__FILE__)) . 'assets/css/modern-admin.css', array(), DAB_VERSION);
wp_enqueue_style('dab-wizard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/wizard.css', array(), DAB_VERSION);

// Enqueue scripts
wp_enqueue_script('dab-wizard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/wizard.js', array('jquery'), DAB_VERSION, true);

// Localize script
wp_localize_script('dab-wizard', 'dab_wizard_data', array(
    'wizard_type' => $wizard_type,
    'current_step' => $current_step,
    'steps' => count($steps),
    'progress' => $progress
));

// Also localize the main wizard script with admin URL
wp_localize_script('dab-wizard', 'dab_wizard', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'admin_url' => admin_url(),
    'nonce' => wp_create_nonce('dab_wizard_nonce'),
    'strings' => array(
        'next' => __('Next', 'db-app-builder'),
        'previous' => __('Previous', 'db-app-builder'),
        'finish' => __('Finish', 'db-app-builder'),
        'saving' => __('Saving...', 'db-app-builder'),
        'error' => __('An error occurred. Please try again.', 'db-app-builder'),
        'confirm_exit' => __('Are you sure you want to exit the wizard? Your progress will be saved.', 'db-app-builder')
    )
));

// Get step content
ob_start();

switch ($current_step) {
    case 1:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step1.php';
        break;
    case 2:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step2.php';
        break;
    case 3:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step3.php';
        break;
    case 4:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step4.php';
        break;
    case 5:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step5.php';
        break;
    case 6:
        include_once plugin_dir_path(__FILE__) . 'wizard-steps/app-creation-step6.php';
        break;
    default:
        echo '<div class="dab-error-message">' . __('Invalid step', 'db-app-builder') . '</div>';
}

$content = ob_get_clean();
?>

<div class="wrap dab-admin-wrap">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title"><?php _e('Create New Application Wizard', 'db-app-builder'); ?></h1>
        <div class="dab-admin-actions">
            <a href="<?php echo admin_url('admin.php?page=dab_app_templates'); ?>" class="dab-btn dab-btn-outline-primary">
                <span class="dashicons dashicons-arrow-left-alt"></span>
                <?php _e('Back to App Templates', 'db-app-builder'); ?>
            </a>
        </div>
    </div>

    <div class="dab-wizard-container" data-wizard-type="<?php echo $wizard_type; ?>">
        <?php
        // Include wizard steps template
        include plugin_dir_path(dirname(__FILE__)) . 'templates/wizard-steps.php';
        ?>
    </div>
</div>

<!-- Create placeholder directories for wizard steps -->
<?php
$wizard_steps_dir = plugin_dir_path(__FILE__) . 'wizard-steps';
if (!file_exists($wizard_steps_dir)) {
    mkdir($wizard_steps_dir, 0755, true);
}
?>
