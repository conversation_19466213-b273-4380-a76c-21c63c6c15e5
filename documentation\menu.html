<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Documentation Menu</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 300px;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            height: 100vh;
            overflow-y: auto;
        }
        .menu-container {
            padding: 20px;
        }
        h1 {
            font-size: 20px;
            color: #2c3e50;
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        li {
            margin-bottom: 5px;
        }
        li.section {
            margin-top: 15px;
            font-weight: bold;
            color: #2c3e50;
        }
        li.subsection {
            padding-left: 15px;
        }
        a {
            text-decoration: none;
            color: #3498db;
            display: block;
            padding: 5px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }
        a:hover {
            background-color: #e9f7fe;
        }
        .active {
            background-color: #e9f7fe;
            font-weight: bold;
        }
        .version {
            font-size: 12px;
            color: #777;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="menu-container">
        <h1>Documentation</h1>
        <ul>
            <li><a href="index.html" target="content">Home</a></li>
            <li><a href="quick-start.html" target="content">Quick Start Guide</a></li>
            <li><a href="video-tutorials.html" target="content">Video Tutorials</a></li>
            
            <li class="section">Getting Started</li>
            <li class="subsection"><a href="index.html#introduction" target="content">Introduction</a></li>
            <li class="subsection"><a href="index.html#installation" target="content">Installation</a></li>
            <li class="subsection"><a href="index.html#getting-started" target="content">Getting Started</a></li>
            
            <li class="section">Core Features</li>
            <li class="subsection"><a href="index.html#tables" target="content">Creating Tables</a></li>
            <li class="subsection"><a href="index.html#fields" target="content">Managing Fields</a></li>
            <li class="subsection"><a href="index.html#forms" target="content">Building Forms</a></li>
            <li class="subsection"><a href="index.html#views" target="content">Creating Views</a></li>
            <li class="subsection"><a href="index.html#dashboards" target="content">Building Dashboards</a></li>
            
            <li class="section">Advanced Features</li>
            <li class="subsection"><a href="index.html#approval-workflows" target="content">Approval Workflows</a></li>
            <li class="subsection"><a href="index.html#permissions" target="content">Role-Based Permissions</a></li>
            <li class="subsection"><a href="index.html#integrations" target="content">Third-Party Integrations</a></li>
            <li class="subsection"><a href="index.html#payment-gateways" target="content">Payment Gateways</a></li>
            
            <li class="section">Reference</li>
            <li class="subsection"><a href="index.html#shortcodes" target="content">Shortcodes Reference</a></li>
            <li class="subsection"><a href="index.html#faq" target="content">FAQ</a></li>
            <li class="subsection"><a href="index.html#troubleshooting" target="content">Troubleshooting</a></li>
        </ul>
        
        <div class="version">Version 1.0.4</div>
    </div>

    <script>
        // Highlight the active menu item
        document.addEventListener('DOMContentLoaded', function() {
            // Get all links
            var links = document.querySelectorAll('a');
            
            // Add click event listener to each link
            links.forEach(function(link) {
                link.addEventListener('click', function() {
                    // Remove active class from all links
                    links.forEach(function(l) {
                        l.classList.remove('active');
                    });
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                });
            });
            
            // Set the first link as active by default
            links[0].classList.add('active');
        });
    </script>
</body>
</html>
