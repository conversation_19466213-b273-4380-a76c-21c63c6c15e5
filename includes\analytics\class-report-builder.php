<?php
/**
 * Report Builder Class
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles advanced reporting functionality including drag-and-drop report builder,
 * pivot tables, scheduled reports, and export capabilities.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Report_Builder {
    
    /**
     * Initialize the Report Builder
     */
    public static function init() {
        add_action('wp_ajax_dab_save_report', array(__CLASS__, 'save_report'));
        add_action('wp_ajax_dab_load_report', array(__CLASS__, 'load_report'));
        add_action('wp_ajax_dab_delete_report', array(__CLASS__, 'delete_report'));
        add_action('wp_ajax_dab_generate_report', array(__CLASS__, 'generate_report'));
        add_action('wp_ajax_dab_export_report', array(__CLASS__, 'export_report'));
        add_action('wp_ajax_dab_get_report_data', array(__CLASS__, 'get_report_data'));
        
        // Schedule report generation
        add_action('dab_generate_scheduled_report', array(__CLASS__, 'generate_scheduled_report'));
    }
    
    /**
     * Create database tables for reports
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Reports table
        $reports_table = $wpdb->prefix . 'dab_reports';
        $sql_reports = "CREATE TABLE $reports_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            table_id int(11) NOT NULL,
            report_type varchar(50) NOT NULL DEFAULT 'table',
            report_config longtext,
            chart_config longtext,
            filters longtext,
            grouping longtext,
            sorting longtext,
            created_by int(11) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_public tinyint(1) DEFAULT 0,
            is_scheduled tinyint(1) DEFAULT 0,
            schedule_config longtext,
            PRIMARY KEY (id),
            KEY table_id (table_id),
            KEY created_by (created_by),
            KEY report_type (report_type)
        ) $charset_collate;";
        
        // Report executions table
        $executions_table = $wpdb->prefix . 'dab_report_executions';
        $sql_executions = "CREATE TABLE $executions_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            report_id int(11) NOT NULL,
            execution_type varchar(50) NOT NULL DEFAULT 'manual',
            execution_status varchar(50) NOT NULL DEFAULT 'pending',
            result_data longtext,
            error_message text,
            executed_by int(11),
            started_at datetime DEFAULT CURRENT_TIMESTAMP,
            completed_at datetime,
            PRIMARY KEY (id),
            KEY report_id (report_id),
            KEY execution_status (execution_status),
            KEY executed_by (executed_by)
        ) $charset_collate;";
        
        // Report shares table
        $shares_table = $wpdb->prefix . 'dab_report_shares';
        $sql_shares = "CREATE TABLE $shares_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            report_id int(11) NOT NULL,
            share_token varchar(255) NOT NULL,
            share_type varchar(50) NOT NULL DEFAULT 'view',
            expires_at datetime,
            created_by int(11) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            access_count int(11) DEFAULT 0,
            last_accessed datetime,
            PRIMARY KEY (id),
            UNIQUE KEY share_token (share_token),
            KEY report_id (report_id),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_reports);
        dbDelta($sql_executions);
        dbDelta($sql_shares);
    }
    
    /**
     * Save a report configuration
     */
    public static function save_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report_id = isset($_POST['report_id']) ? intval($_POST['report_id']) : 0;
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $table_id = intval($_POST['table_id']);
        $report_type = sanitize_text_field($_POST['report_type']);
        $report_config = wp_json_encode($_POST['report_config']);
        $chart_config = wp_json_encode($_POST['chart_config']);
        $filters = wp_json_encode($_POST['filters']);
        $grouping = wp_json_encode($_POST['grouping']);
        $sorting = wp_json_encode($_POST['sorting']);
        $is_public = isset($_POST['is_public']) ? 1 : 0;
        $is_scheduled = isset($_POST['is_scheduled']) ? 1 : 0;
        $schedule_config = wp_json_encode($_POST['schedule_config']);
        
        $data = array(
            'name' => $name,
            'description' => $description,
            'table_id' => $table_id,
            'report_type' => $report_type,
            'report_config' => $report_config,
            'chart_config' => $chart_config,
            'filters' => $filters,
            'grouping' => $grouping,
            'sorting' => $sorting,
            'is_public' => $is_public,
            'is_scheduled' => $is_scheduled,
            'schedule_config' => $schedule_config
        );
        
        if ($report_id > 0) {
            // Update existing report
            $result = $wpdb->update($reports_table, $data, array('id' => $report_id));
        } else {
            // Create new report
            $data['created_by'] = get_current_user_id();
            $result = $wpdb->insert($reports_table, $data);
            $report_id = $wpdb->insert_id;
        }
        
        if ($result !== false) {
            // Schedule report if needed
            if ($is_scheduled && !empty($_POST['schedule_config'])) {
                self::schedule_report($report_id, $_POST['schedule_config']);
            }
            
            wp_send_json_success(array(
                'message' => 'Report saved successfully',
                'report_id' => $report_id
            ));
        } else {
            wp_send_json_error('Failed to save report');
        }
    }
    
    /**
     * Load a report configuration
     */
    public static function load_report() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $report_id = intval($_GET['report_id']);
        
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));
        
        if (!$report) {
            wp_send_json_error('Report not found');
            return;
        }
        
        // Check permissions
        if (!$report->is_public && !current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        // Decode JSON fields
        $report->report_config = json_decode($report->report_config, true);
        $report->chart_config = json_decode($report->chart_config, true);
        $report->filters = json_decode($report->filters, true);
        $report->grouping = json_decode($report->grouping, true);
        $report->sorting = json_decode($report->sorting, true);
        $report->schedule_config = json_decode($report->schedule_config, true);
        
        wp_send_json_success($report);
    }
    
    /**
     * Delete a report
     */
    public static function delete_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $report_id = intval($_POST['report_id']);
        
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $result = $wpdb->delete($reports_table, array('id' => $report_id));
        
        if ($result !== false) {
            // Remove scheduled events
            wp_clear_scheduled_hook('dab_generate_scheduled_report', array($report_id));
            
            wp_send_json_success('Report deleted successfully');
        } else {
            wp_send_json_error('Failed to delete report');
        }
    }
    
    /**
     * Generate report data
     */
    public static function generate_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $report_id = intval($_POST['report_id']);
        
        // Load report configuration
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));
        
        if (!$report) {
            wp_send_json_error('Report not found');
            return;
        }
        
        // Check permissions
        if (!$report->is_public && !current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        try {
            $result = self::execute_report($report);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Report generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute a report and return data
     */
    public static function execute_report($report) {
        global $wpdb;
        
        // Get table information
        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $report->table_id
        ));
        
        if (!$table) {
            throw new Exception('Table not found');
        }
        
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        
        // Get fields
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
            $report->table_id
        ));
        
        // Build query based on report configuration
        $report_config = json_decode($report->report_config, true);
        $filters = json_decode($report->filters, true);
        $grouping = json_decode($report->grouping, true);
        $sorting = json_decode($report->sorting, true);
        
        $query = self::build_report_query($data_table, $fields, $report_config, $filters, $grouping, $sorting);
        
        $data = $wpdb->get_results($query);
        
        return array(
            'data' => $data,
            'fields' => $fields,
            'report_type' => $report->report_type,
            'chart_config' => json_decode($report->chart_config, true)
        );
    }
    
    /**
     * Build SQL query for report
     */
    private static function build_report_query($data_table, $fields, $report_config, $filters, $grouping, $sorting) {
        global $wpdb;
        
        // Select clause
        $select_fields = array('id');
        if (!empty($report_config['selected_fields'])) {
            foreach ($report_config['selected_fields'] as $field_slug) {
                $select_fields[] = "`" . esc_sql($field_slug) . "`";
            }
        } else {
            foreach ($fields as $field) {
                $select_fields[] = "`" . esc_sql($field->field_slug) . "`";
            }
        }
        
        $select = "SELECT " . implode(', ', $select_fields);
        
        // From clause
        $from = "FROM $data_table";
        
        // Where clause
        $where = "WHERE 1=1";
        if (!empty($filters)) {
            foreach ($filters as $filter) {
                if (!empty($filter['field']) && !empty($filter['operator']) && isset($filter['value'])) {
                    $field_slug = esc_sql($filter['field']);
                    $operator = $filter['operator'];
                    $value = $filter['value'];
                    
                    switch ($operator) {
                        case 'equals':
                            $where .= $wpdb->prepare(" AND `$field_slug` = %s", $value);
                            break;
                        case 'not_equals':
                            $where .= $wpdb->prepare(" AND `$field_slug` != %s", $value);
                            break;
                        case 'contains':
                            $where .= $wpdb->prepare(" AND `$field_slug` LIKE %s", '%' . $value . '%');
                            break;
                        case 'starts_with':
                            $where .= $wpdb->prepare(" AND `$field_slug` LIKE %s", $value . '%');
                            break;
                        case 'ends_with':
                            $where .= $wpdb->prepare(" AND `$field_slug` LIKE %s", '%' . $value);
                            break;
                        case 'greater_than':
                            $where .= $wpdb->prepare(" AND `$field_slug` > %s", $value);
                            break;
                        case 'less_than':
                            $where .= $wpdb->prepare(" AND `$field_slug` < %s", $value);
                            break;
                    }
                }
            }
        }
        
        // Group by clause
        $group_by = "";
        if (!empty($grouping['fields'])) {
            $group_fields = array();
            foreach ($grouping['fields'] as $field_slug) {
                $group_fields[] = "`" . esc_sql($field_slug) . "`";
            }
            $group_by = "GROUP BY " . implode(', ', $group_fields);
        }
        
        // Order by clause
        $order_by = "ORDER BY id DESC";
        if (!empty($sorting)) {
            $order_fields = array();
            foreach ($sorting as $sort) {
                if (!empty($sort['field']) && !empty($sort['direction'])) {
                    $field_slug = esc_sql($sort['field']);
                    $direction = strtoupper($sort['direction']) === 'ASC' ? 'ASC' : 'DESC';
                    $order_fields[] = "`$field_slug` $direction";
                }
            }
            if (!empty($order_fields)) {
                $order_by = "ORDER BY " . implode(', ', $order_fields);
            }
        }
        
        return "$select $from $where $group_by $order_by";
    }
    
    /**
     * Schedule a report for automatic generation
     */
    private static function schedule_report($report_id, $schedule_config) {
        if (empty($schedule_config['frequency'])) {
            return;
        }
        
        $frequency = $schedule_config['frequency'];
        $next_run = time();
        
        switch ($frequency) {
            case 'daily':
                $next_run = strtotime('tomorrow ' . ($schedule_config['time'] ?? '09:00'));
                break;
            case 'weekly':
                $day = $schedule_config['day'] ?? 'monday';
                $time = $schedule_config['time'] ?? '09:00';
                $next_run = strtotime("next $day $time");
                break;
            case 'monthly':
                $day = $schedule_config['day'] ?? 1;
                $time = $schedule_config['time'] ?? '09:00';
                $next_run = strtotime("first day of next month $time");
                break;
        }
        
        wp_schedule_event($next_run, $frequency, 'dab_generate_scheduled_report', array($report_id));
    }
    
    /**
     * Generate scheduled report
     */
    public static function generate_scheduled_report($report_id) {
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d AND is_scheduled = 1",
            $report_id
        ));
        
        if (!$report) {
            return;
        }
        
        try {
            $result = self::execute_report($report);
            
            // Log execution
            $executions_table = $wpdb->prefix . 'dab_report_executions';
            $wpdb->insert($executions_table, array(
                'report_id' => $report_id,
                'execution_type' => 'scheduled',
                'execution_status' => 'completed',
                'result_data' => wp_json_encode($result),
                'completed_at' => current_time('mysql')
            ));
            
            // Send email if configured
            $schedule_config = json_decode($report->schedule_config, true);
            if (!empty($schedule_config['email_recipients'])) {
                self::email_report($report, $result, $schedule_config['email_recipients']);
            }
            
        } catch (Exception $e) {
            // Log error
            $executions_table = $wpdb->prefix . 'dab_report_executions';
            $wpdb->insert($executions_table, array(
                'report_id' => $report_id,
                'execution_type' => 'scheduled',
                'execution_status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => current_time('mysql')
            ));
        }
    }
    
    /**
     * Email report to recipients
     */
    private static function email_report($report, $result, $recipients) {
        $subject = 'Scheduled Report: ' . $report->name;
        $message = "Your scheduled report '{$report->name}' has been generated.\n\n";
        $message .= "Report Description: {$report->description}\n\n";
        $message .= "Generated on: " . current_time('Y-m-d H:i:s') . "\n\n";
        $message .= "Total Records: " . count($result['data']) . "\n\n";
        
        foreach ($recipients as $email) {
            wp_mail($email, $subject, $message);
        }
    }
}
