<?php
/**
 * Frontend Users Management Page
 *
 * Provides admin interface for managing frontend users
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_user':
                handle_create_user();
                break;
            case 'update_user':
                handle_update_user();
                break;
            case 'delete_user':
                handle_delete_user();
                break;
            case 'bulk_action':
                handle_bulk_action();
                break;
        }
    }
}

function handle_create_user() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_frontend_users_nonce')) {
        wp_die('Security check failed');
    }

    $username = sanitize_text_field($_POST['username']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $first_name = sanitize_text_field($_POST['first_name']);
    $last_name = sanitize_text_field($_POST['last_name']);
    $role = sanitize_text_field($_POST['role']);
    $status = sanitize_text_field($_POST['status']);

    $result = DAB_Frontend_User_Manager::register_user($username, $email, $password, array(
        'first_name' => $first_name,
        'last_name' => $last_name,
        'role' => $role,
        'status' => $status,
        'email_verified' => ($status === 'active') ? 1 : 0
    ));

    if (is_wp_error($result)) {
        echo '<div class="notice notice-error"><p>' . $result->get_error_message() . '</p></div>';
    } else {
        echo '<div class="notice notice-success"><p>User created successfully!</p></div>';
    }
}

function handle_update_user() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_frontend_users_nonce')) {
        wp_die('Security check failed');
    }

    global $wpdb;
    $user_id = intval($_POST['user_id']);
    $users_table = $wpdb->prefix . 'dab_frontend_users';

    $update_data = array(
        'username' => sanitize_text_field($_POST['username']),
        'email' => sanitize_email($_POST['email']),
        'first_name' => sanitize_text_field($_POST['first_name']),
        'last_name' => sanitize_text_field($_POST['last_name']),
        'role' => sanitize_text_field($_POST['role']),
        'status' => sanitize_text_field($_POST['status']),
        'email_verified' => intval($_POST['email_verified'])
    );

    if (!empty($_POST['password'])) {
        $update_data['password'] = wp_hash_password($_POST['password']);
    }

    $result = $wpdb->update($users_table, $update_data, array('id' => $user_id));

    if ($result !== false) {
        echo '<div class="notice notice-success"><p>User updated successfully!</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>Failed to update user.</p></div>';
    }
}

function handle_delete_user() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_frontend_users_nonce')) {
        wp_die('Security check failed');
    }

    global $wpdb;
    $user_id = intval($_POST['user_id']);
    $users_table = $wpdb->prefix . 'dab_frontend_users';

    $result = $wpdb->delete($users_table, array('id' => $user_id));

    if ($result !== false) {
        echo '<div class="notice notice-success"><p>User deleted successfully!</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>Failed to delete user.</p></div>';
    }
}

function handle_bulk_action() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_frontend_users_nonce')) {
        wp_die('Security check failed');
    }

    $action = sanitize_text_field($_POST['bulk_action']);
    $user_ids = array_map('intval', $_POST['user_ids']);

    if (empty($user_ids)) {
        echo '<div class="notice notice-error"><p>No users selected.</p></div>';
        return;
    }

    global $wpdb;
    $users_table = $wpdb->prefix . 'dab_frontend_users';
    $placeholders = implode(',', array_fill(0, count($user_ids), '%d'));

    switch ($action) {
        case 'activate':
            $result = $wpdb->query($wpdb->prepare(
                "UPDATE $users_table SET status = 'active' WHERE id IN ($placeholders)",
                ...$user_ids
            ));
            echo '<div class="notice notice-success"><p>' . $result . ' users activated.</p></div>';
            break;

        case 'deactivate':
            $result = $wpdb->query($wpdb->prepare(
                "UPDATE $users_table SET status = 'inactive' WHERE id IN ($placeholders)",
                ...$user_ids
            ));
            echo '<div class="notice notice-success"><p>' . $result . ' users deactivated.</p></div>';
            break;

        case 'delete':
            $result = $wpdb->query($wpdb->prepare(
                "DELETE FROM $users_table WHERE id IN ($placeholders)",
                ...$user_ids
            ));
            echo '<div class="notice notice-success"><p>' . $result . ' users deleted.</p></div>';
            break;
    }
}

// Get users for display
global $wpdb;
$users_table = $wpdb->prefix . 'dab_frontend_users';

// Handle search and filtering
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$role_filter = isset($_GET['role']) ? sanitize_text_field($_GET['role']) : '';

// Build query
$where_conditions = array();
$query_params = array();

if (!empty($search)) {
    $where_conditions[] = "(username LIKE %s OR email LIKE %s OR first_name LIKE %s OR last_name LIKE %s)";
    $search_term = '%' . $wpdb->esc_like($search) . '%';
    $query_params = array_merge($query_params, array($search_term, $search_term, $search_term, $search_term));
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = %s";
    $query_params[] = $status_filter;
}

if (!empty($role_filter)) {
    $where_conditions[] = "role = %s";
    $query_params[] = $role_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Pagination
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Get total count
$total_query = "SELECT COUNT(*) FROM $users_table $where_clause";
$total_users = empty($query_params) ? $wpdb->get_var($total_query) : $wpdb->get_var($wpdb->prepare($total_query, $query_params));

// Get users
$users_query = "SELECT * FROM $users_table $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";
$final_params = array_merge($query_params, array($per_page, $offset));
$users = $wpdb->get_results($wpdb->prepare($users_query, $final_params));

// Calculate pagination
$total_pages = ceil($total_users / $per_page);

// Get available roles and statuses for filters
$roles = $wpdb->get_col("SELECT DISTINCT role FROM $users_table WHERE role IS NOT NULL AND role != ''");
$statuses = array('active', 'inactive', 'pending');
?>

<div class="wrap">
    <h1 class="wp-heading-inline">Frontend Users</h1>
    <a href="#" class="page-title-action" id="add-new-user">Add New User</a>
    <a href="<?php echo admin_url('admin-ajax.php?action=dab_export_users_csv'); ?>" class="page-title-action">Export CSV</a>
    <hr class="wp-header-end">

    <!-- Statistics Dashboard -->
    <div class="dab-stats-dashboard" style="margin-bottom: 20px;">
        <div class="dab-stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div class="dab-stat-card">
                <div class="dab-stat-number"><?php echo $total_users; ?></div>
                <div class="dab-stat-label">Total Users</div>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number"><?php echo $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE status = 'active'"); ?></div>
                <div class="dab-stat-label">Active Users</div>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number"><?php echo $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE status = 'pending'"); ?></div>
                <div class="dab-stat-label">Pending Users</div>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number"><?php echo $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE email_verified = 1"); ?></div>
                <div class="dab-stat-label">Verified Users</div>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number"><?php echo $wpdb->get_var("SELECT COUNT(*) FROM $users_table WHERE DATE(created_at) = CURDATE()"); ?></div>
                <div class="dab-stat-label">Today's Registrations</div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="tablenav top">
        <div class="alignleft actions">
            <form method="get" style="display: inline-block;">
                <input type="hidden" name="page" value="dab_frontend_users">

                <select name="status">
                    <option value="">All Statuses</option>
                    <?php foreach ($statuses as $status): ?>
                        <option value="<?php echo esc_attr($status); ?>" <?php selected($status_filter, $status); ?>>
                            <?php echo ucfirst($status); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <select name="role">
                    <option value="">All Roles</option>
                    <?php foreach ($roles as $role): ?>
                        <option value="<?php echo esc_attr($role); ?>" <?php selected($role_filter, $role); ?>>
                            <?php echo ucfirst($role); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <input type="submit" class="button" value="Filter">
            </form>
        </div>

        <div class="tablenav-pages">
            <span class="displaying-num"><?php echo $total_users; ?> items</span>
            <?php if ($total_pages > 1): ?>
                <span class="pagination-links">
                    <?php
                    $page_links = paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => '&laquo;',
                        'next_text' => '&raquo;',
                        'total' => $total_pages,
                        'current' => $current_page,
                        'type' => 'array'
                    ));

                    if ($page_links) {
                        echo implode("\n", $page_links);
                    }
                    ?>
                </span>
            <?php endif; ?>
        </div>

        <div class="alignright">
            <form method="get" style="display: inline-block;">
                <input type="hidden" name="page" value="dab_frontend_users">
                <input type="search" name="search" value="<?php echo esc_attr($search); ?>" placeholder="Search users...">
                <input type="submit" class="button" value="Search">
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <form method="post" id="users-form">
        <?php wp_nonce_field('dab_frontend_users_nonce', 'nonce'); ?>
        <input type="hidden" name="action" value="bulk_action">

        <div class="tablenav top">
            <div class="alignleft actions bulkactions">
                <select name="bulk_action">
                    <option value="">Bulk Actions</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="delete">Delete</option>
                </select>
                <input type="submit" class="button action" value="Apply">
            </div>
        </div>

        <!-- Users Table -->
        <table class="wp-list-table widefat fixed striped users">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all">
                    </td>
                    <th class="manage-column column-username">Username</th>
                    <th class="manage-column column-email">Email</th>
                    <th class="manage-column column-name">Name</th>
                    <th class="manage-column column-role">Role</th>
                    <th class="manage-column column-status">Status</th>
                    <th class="manage-column column-verified">Verified</th>
                    <th class="manage-column column-registered">Registered</th>
                    <th class="manage-column column-actions">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 20px;">
                            No users found.
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <th class="check-column">
                                <input type="checkbox" name="user_ids[]" value="<?php echo $user->id; ?>">
                            </th>
                            <td class="column-username">
                                <strong><?php echo esc_html($user->username); ?></strong>
                            </td>
                            <td class="column-email">
                                <a href="mailto:<?php echo esc_attr($user->email); ?>">
                                    <?php echo esc_html($user->email); ?>
                                </a>
                            </td>
                            <td class="column-name">
                                <?php echo esc_html(trim($user->first_name . ' ' . $user->last_name)); ?>
                            </td>
                            <td class="column-role">
                                <span class="role-badge role-<?php echo esc_attr($user->role); ?>">
                                    <?php echo esc_html(ucfirst($user->role)); ?>
                                </span>
                            </td>
                            <td class="column-status">
                                <span class="status-badge status-<?php echo esc_attr($user->status); ?>">
                                    <?php echo esc_html(ucfirst($user->status)); ?>
                                </span>
                            </td>
                            <td class="column-verified">
                                <?php if ($user->email_verified): ?>
                                    <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                                <?php else: ?>
                                    <span class="dashicons dashicons-dismiss" style="color: red;"></span>
                                <?php endif; ?>
                            </td>
                            <td class="column-registered">
                                <?php echo date('Y-m-d H:i', strtotime($user->created_at)); ?>
                            </td>
                            <td class="column-actions">
                                <button type="button" class="button button-small edit-user"
                                        data-user-id="<?php echo $user->id; ?>">Edit</button>
                                <?php if (!$user->email_verified): ?>
                                    <button type="button" class="button button-small send-verification"
                                            data-user-id="<?php echo $user->id; ?>"
                                            style="color: #0073aa;">Verify</button>
                                <?php endif; ?>
                                <button type="button" class="button button-small delete-user"
                                        data-user-id="<?php echo $user->id; ?>"
                                        style="color: #a00;">Delete</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </form>
</div>

<!-- Add/Edit User Modal -->
<div id="user-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h2 id="modal-title">Add New User</h2>
            <span class="dab-modal-close">&times;</span>
        </div>
        <div class="dab-modal-body">
            <form id="user-form" method="post">
                <?php wp_nonce_field('dab_frontend_users_nonce', 'nonce'); ?>
                <input type="hidden" name="action" id="form-action" value="create_user">
                <input type="hidden" name="user_id" id="user-id" value="">

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="username">Username *</label>
                        </th>
                        <td>
                            <input type="text" name="username" id="username" class="regular-text" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="email">Email *</label>
                        </th>
                        <td>
                            <input type="email" name="email" id="email" class="regular-text" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="password">Password</label>
                        </th>
                        <td>
                            <input type="password" name="password" id="password" class="regular-text">
                            <p class="description" id="password-description">
                                Leave blank to keep current password (when editing).
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="first_name">First Name</label>
                        </th>
                        <td>
                            <input type="text" name="first_name" id="first_name" class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="last_name">Last Name</label>
                        </th>
                        <td>
                            <input type="text" name="last_name" id="last_name" class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="role">Role</label>
                        </th>
                        <td>
                            <select name="role" id="role" class="regular-text">
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                                <option value="editor">Editor</option>
                                <option value="viewer">Viewer</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="status">Status</label>
                        </th>
                        <td>
                            <select name="status" id="status" class="regular-text">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="pending">Pending</option>
                            </select>
                        </td>
                    </tr>
                    <tr id="email-verified-row" style="display: none;">
                        <th scope="row">
                            <label for="email_verified">Email Verified</label>
                        </th>
                        <td>
                            <select name="email_verified" id="email_verified" class="regular-text">
                                <option value="1">Yes</option>
                                <option value="0">No</option>
                            </select>
                        </td>
                    </tr>
                </table>

                <div class="dab-modal-footer">
                    <button type="submit" class="button button-primary">Save User</button>
                    <button type="button" class="button dab-modal-close">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h2>Confirm Delete</h2>
            <span class="dab-modal-close">&times;</span>
        </div>
        <div class="dab-modal-body">
            <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            <form id="delete-form" method="post">
                <?php wp_nonce_field('dab_frontend_users_nonce', 'nonce'); ?>
                <input type="hidden" name="action" value="delete_user">
                <input type="hidden" name="user_id" id="delete-user-id" value="">

                <div class="dab-modal-footer">
                    <button type="submit" class="button button-primary" style="background: #dc3232;">Delete User</button>
                    <button type="button" class="button dab-modal-close">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.dab-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.dab-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.dab-modal-header {
    padding: 20px;
    background-color: #f1f1f1;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px 4px 0 0;
}

.dab-modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.dab-modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.dab-modal-close:hover,
.dab-modal-close:focus {
    color: #000;
}

.dab-modal-body {
    padding: 20px;
}

.dab-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    text-align: right;
}

.dab-modal-footer .button {
    margin-left: 10px;
}

/* Status and Role Badges */
.status-badge, .role-badge {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.role-admin {
    background-color: #e7f3ff;
    color: #0073aa;
}

.role-user {
    background-color: #f0f0f1;
    color: #50575e;
}

.role-editor {
    background-color: #f0f6fc;
    color: #0969da;
}

.role-viewer {
    background-color: #f6f8fa;
    color: #656d76;
}

/* Statistics Cards */
.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #1d2327;
    line-height: 1;
    margin-bottom: 5px;
}

.dab-stat-label {
    font-size: 13px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table improvements */
.wp-list-table .column-actions {
    width: 140px;
}

.wp-list-table .column-status,
.wp-list-table .column-role {
    width: 80px;
}

.wp-list-table .column-verified {
    width: 60px;
    text-align: center;
}

.wp-list-table .column-registered {
    width: 120px;
}

/* Action buttons */
.button.button-small {
    padding: 2px 8px;
    font-size: 11px;
    line-height: 1.4;
    margin-right: 5px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Modal functionality
    function openModal(modalId) {
        $('#' + modalId).show();
    }

    function closeModal(modalId) {
        $('#' + modalId).hide();
        if (modalId === 'user-modal') {
            $('#user-form')[0].reset();
            $('#form-action').val('create_user');
            $('#user-id').val('');
            $('#modal-title').text('Add New User');
            $('#password-description').text('Leave blank to keep current password (when editing).');
            $('#email-verified-row').hide();
        }
    }

    // Add new user
    $('#add-new-user').click(function(e) {
        e.preventDefault();
        $('#modal-title').text('Add New User');
        $('#form-action').val('create_user');
        $('#password-description').text('Required for new users.');
        $('#password').prop('required', true);
        $('#email-verified-row').hide();
        openModal('user-modal');
    });

    // Edit user
    $('.edit-user').click(function() {
        var userId = $(this).data('user-id');
        var row = $(this).closest('tr');

        $('#modal-title').text('Edit User');
        $('#form-action').val('update_user');
        $('#user-id').val(userId);
        $('#password').prop('required', false);
        $('#password-description').text('Leave blank to keep current password.');
        $('#email-verified-row').show();

        // Populate form with current values
        $('#username').val(row.find('.column-username strong').text());
        $('#email').val(row.find('.column-email a').text());
        $('#first_name').val(row.find('.column-name').text().split(' ')[0] || '');
        $('#last_name').val(row.find('.column-name').text().split(' ').slice(1).join(' ') || '');
        $('#role').val(row.find('.role-badge').text().toLowerCase());
        $('#status').val(row.find('.status-badge').text().toLowerCase());
        $('#email_verified').val(row.find('.column-verified .dashicons-yes-alt').length ? '1' : '0');

        openModal('user-modal');
    });

    // Delete user
    $('.delete-user').click(function() {
        var userId = $(this).data('user-id');
        $('#delete-user-id').val(userId);
        openModal('delete-modal');
    });

    // Send verification email
    $('.send-verification').click(function() {
        var userId = $(this).data('user-id');
        var button = $(this);

        if (confirm('Send verification email to this user?')) {
            button.prop('disabled', true).text('Sending...');

            $.post(ajaxurl, {
                action: 'dab_send_verification_email',
                user_id: userId,
                nonce: '<?php echo wp_create_nonce('dab_frontend_users_nonce'); ?>'
            })
            .done(function(response) {
                if (response.success) {
                    alert('Verification email sent successfully!');
                    button.remove(); // Remove the button since email was sent
                } else {
                    alert('Error: ' + (response.data || 'Failed to send verification email'));
                    button.prop('disabled', false).text('Verify');
                }
            })
            .fail(function() {
                alert('Error: Failed to send verification email');
                button.prop('disabled', false).text('Verify');
            });
        }
    });

    // Close modal
    $('.dab-modal-close').click(function() {
        var modalId = $(this).closest('.dab-modal').attr('id');
        closeModal(modalId);
    });

    // Close modal when clicking outside
    $('.dab-modal').click(function(e) {
        if (e.target === this) {
            var modalId = $(this).attr('id');
            closeModal(modalId);
        }
    });

    // Select all checkbox
    $('#cb-select-all').change(function() {
        $('input[name="user_ids[]"]').prop('checked', this.checked);
    });

    // Individual checkbox change
    $('input[name="user_ids[]"]').change(function() {
        var allChecked = $('input[name="user_ids[]"]:checked').length === $('input[name="user_ids[]"]').length;
        $('#cb-select-all').prop('checked', allChecked);
    });
});
</script>
