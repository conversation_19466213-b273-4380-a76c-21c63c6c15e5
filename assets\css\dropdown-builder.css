/* Dropdown Options Builder Styles */
.dab-dropdown-builder {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #f9f9f9;
    margin-bottom: 15px;
}

.dab-dropdown-options-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fff;
}

.dab-dropdown-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
    transition: background-color 0.2s;
}

.dab-dropdown-option:last-child {
    border-bottom: none;
}

.dab-dropdown-option:hover {
    background-color: #f5f5f5;
}

.dab-dropdown-option-drag {
    cursor: move;
    margin-right: 10px;
    color: #999;
}

.dab-dropdown-option-drag svg {
    width: 16px;
    height: 16px;
}

.dab-dropdown-option-content {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.dab-dropdown-option-label {
    font-weight: 500;
    margin-right: 10px;
}

.dab-dropdown-option-value {
    color: #666;
    font-size: 0.9em;
    margin-left: 5px;
}

.dab-dropdown-option-value:before {
    content: "(";
}

.dab-dropdown-option-value:after {
    content: ")";
}

.dab-dropdown-option-actions {
    display: flex;
    gap: 5px;
}

.dab-dropdown-option-edit,
.dab-dropdown-option-delete {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    color: #555;
}

.dab-dropdown-option-edit:hover {
    color: #0073aa;
    background-color: rgba(0, 115, 170, 0.1);
}

.dab-dropdown-option-delete:hover {
    color: #d63638;
    background-color: rgba(214, 54, 56, 0.1);
}

.dab-dropdown-option-edit svg,
.dab-dropdown-option-delete svg {
    width: 16px;
    height: 16px;
}

.dab-dropdown-add-option {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.dab-dropdown-add-option input {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-dropdown-add-option button {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.dab-dropdown-add-option button:hover {
    background-color: #135e96;
}

.dab-dropdown-option-form {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.dab-dropdown-option-form-group {
    flex-grow: 1;
}

.dab-dropdown-option-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.dab-dropdown-option-form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-dropdown-option-form-actions {
    display: flex;
    gap: 10px;
    margin-top: 24px;
}

.dab-dropdown-option-form-actions button {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.dab-dropdown-option-form-save {
    background-color: #2271b1;
    color: white;
    border: none;
}

.dab-dropdown-option-form-save:hover {
    background-color: #135e96;
}

.dab-dropdown-option-form-cancel {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.dab-dropdown-option-form-cancel:hover {
    background-color: #e0e0e0;
}

.dab-dropdown-options-empty {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.dab-dropdown-option.ui-sortable-helper {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dab-dropdown-option.ui-sortable-placeholder {
    visibility: visible !important;
    background-color: #f0f7fc;
    border: 1px dashed #2271b1;
}
