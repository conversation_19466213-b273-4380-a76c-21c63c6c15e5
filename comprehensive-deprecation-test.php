<?php
/**
 * Comprehensive Deprecation Fixes Test
 * 
 * This file thoroughly tests all the deprecation fixes implemented
 * for PHP 8.1+ compatibility in the Database App Builder plugin.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h1>Comprehensive Deprecation Fixes Test</h1>\n";
echo "<p>Testing all implemented fixes for PHP 8.1+ deprecation warnings.</p>\n";

// Test 1: All Safe Functions
echo "<h2>Test 1: Safe Function Comprehensive Testing</h2>\n";

$safe_functions = [
    'dab_safe_string',
    'dab_safe_strpos', 
    'dab_safe_str_replace',
    'dab_safe_strlen',
    'dab_safe_substr',
    'dab_safe_trim',
    'dab_safe_explode'
];

foreach ($safe_functions as $function) {
    if (function_exists($function)) {
        echo "✅ {$function} exists\n";
    } else {
        echo "❌ {$function} missing\n";
    }
}

// Test 2: Safe Function Behavior with Null Values
echo "<h2>Test 2: Null Value Handling</h2>\n";

if (function_exists('dab_safe_string')) {
    $tests = [
        [null, ''],
        ['test', 'test'],
        [123, '123'],
        [true, '1'],
        [false, '']
    ];
    
    foreach ($tests as $test) {
        $result = dab_safe_string($test[0]);
        $expected = $test[1];
        $status = ($result === $expected) ? '✅' : '❌';
        echo "{$status} dab_safe_string(" . var_export($test[0], true) . ") = '{$result}' (expected: '{$expected}')\n";
    }
}

if (function_exists('dab_safe_strpos')) {
    $tests = [
        [null, 'test', false],
        ['hello world', 'world', 6],
        ['', 'test', false],
        ['test', null, 0] // empty needle should find at position 0
    ];
    
    foreach ($tests as $test) {
        $result = dab_safe_strpos($test[0], $test[1]);
        $expected = $test[2];
        $status = ($result === $expected) ? '✅' : '❌';
        echo "{$status} dab_safe_strpos(" . var_export($test[0], true) . ", " . var_export($test[1], true) . ") = " . var_export($result, true) . " (expected: " . var_export($expected, true) . ")\n";
    }
}

if (function_exists('dab_safe_str_replace')) {
    $tests = [
        ['world', 'PHP', null, ''],
        ['world', 'PHP', 'hello world', 'hello PHP'],
        [null, 'replacement', 'test', 'test'],
        ['test', null, 'test string', ' string']
    ];
    
    foreach ($tests as $test) {
        $result = dab_safe_str_replace($test[0], $test[1], $test[2]);
        $expected = $test[3];
        $status = ($result === $expected) ? '✅' : '❌';
        echo "{$status} dab_safe_str_replace(" . var_export($test[0], true) . ", " . var_export($test[1], true) . ", " . var_export($test[2], true) . ") = '{$result}' (expected: '{$expected}')\n";
    }
}

// Test 3: Template Manager Hook Handling
echo "<h2>Test 3: Template Manager Hook Handling</h2>\n";

if (class_exists('DAB_Template_Manager')) {
    echo "✅ DAB_Template_Manager class exists\n";
    
    // Test with various hook parameter types
    $hook_tests = [
        null,
        '',
        'plugins.php',
        'dab_app_templates',
        123,
        false
    ];
    
    foreach ($hook_tests as $hook) {
        echo "- Testing with hook: " . var_export($hook, true) . "\n";
        try {
            ob_start();
            DAB_Template_Manager::enqueue_scripts($hook);
            $output = ob_get_clean();
            echo "  ✅ No errors thrown\n";
            if (!empty($output)) {
                echo "  Output: " . substr($output, 0, 100) . (strlen($output) > 100 ? '...' : '') . "\n";
            }
        } catch (Exception $e) {
            echo "  ❌ Exception: " . $e->getMessage() . "\n";
        } catch (Error $e) {
            echo "  ❌ Error: " . $e->getMessage() . "\n";
        }
    }
} else {
    echo "❌ DAB_Template_Manager class not found\n";
}

// Test 4: Error Handler Functionality
echo "<h2>Test 4: Error Handler Testing</h2>\n";

if (function_exists('dab_error_handler')) {
    echo "✅ dab_error_handler function exists\n";
    
    // Test error handler with various error types
    $error_tests = [
        [E_DEPRECATED, 'strpos(): Passing null to parameter #1', __FILE__, __LINE__],
        [E_DEPRECATED, 'str_replace(): Passing null to parameter #3', __FILE__, __LINE__],
        [E_DEPRECATED, 'Some other deprecation warning', __FILE__, __LINE__],
        [E_WARNING, 'This is a warning', __FILE__, __LINE__],
        [E_DEPRECATED, null, __FILE__, __LINE__] // Test with null error message
    ];
    
    foreach ($error_tests as $test) {
        $result = dab_error_handler($test[0], $test[1], $test[2], $test[3]);
        $error_type = $test[0] === E_DEPRECATED ? 'E_DEPRECATED' : 'E_WARNING';
        $message = var_export($test[1], true);
        echo "- {$error_type} with message {$message}: " . ($result ? 'Suppressed ✅' : 'Not suppressed ❌') . "\n";
    }
} else {
    echo "❌ dab_error_handler function not found\n";
}

// Test 5: WordPress Hook Filters
echo "<h2>Test 5: WordPress Hook Filters</h2>\n";

// Check if filters are properly registered
$filters = [
    'admin_enqueue_scripts'
];

foreach ($filters as $filter) {
    $has_filter = has_filter($filter);
    if ($has_filter) {
        echo "✅ Filter '{$filter}' is registered\n";
    } else {
        echo "❌ Filter '{$filter}' is not registered\n";
    }
}

// Test 6: PHP Version Compatibility
echo "<h2>Test 6: PHP Version Compatibility</h2>\n";

echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- PHP 8.1+ Compatibility: " . (version_compare(PHP_VERSION, '8.1', '>=') ? 'Required ✅' : 'Not required ✅') . "\n";

if (version_compare(PHP_VERSION, '8.1', '>=')) {
    echo "- Deprecation warnings are relevant for this PHP version\n";
    echo "- All safe functions should prevent deprecation warnings\n";
} else {
    echo "- Deprecation warnings are not applicable for this PHP version\n";
    echo "- Safe functions provide future compatibility\n";
}

// Test 7: Memory and Performance Impact
echo "<h2>Test 7: Performance Impact</h2>\n";

$start_memory = memory_get_usage();
$start_time = microtime(true);

// Run a series of safe function calls
for ($i = 0; $i < 1000; $i++) {
    if (function_exists('dab_safe_string')) {
        dab_safe_string(null);
        dab_safe_string('test' . $i);
    }
    if (function_exists('dab_safe_strpos')) {
        dab_safe_strpos('test string ' . $i, 'string');
    }
    if (function_exists('dab_safe_str_replace')) {
        dab_safe_str_replace('test', 'replaced', 'test string ' . $i);
    }
}

$end_memory = memory_get_usage();
$end_time = microtime(true);

$memory_used = $end_memory - $start_memory;
$time_taken = ($end_time - $start_time) * 1000; // Convert to milliseconds

echo "- Memory used for 1000 safe function calls: " . number_format($memory_used) . " bytes\n";
echo "- Time taken: " . number_format($time_taken, 2) . " milliseconds\n";
echo "- Performance impact: " . ($memory_used < 100000 && $time_taken < 100 ? 'Minimal ✅' : 'Significant ❌') . "\n";

// Final Summary
echo "<h2>Test Summary</h2>\n";
echo "<p>All tests completed. Review the results above:</p>\n";
echo "<ul>\n";
echo "<li>✅ indicates successful tests</li>\n";
echo "<li>❌ indicates failed tests that need attention</li>\n";
echo "<li>All safe functions should handle null values gracefully</li>\n";
echo "<li>Template Manager should work with any hook parameter type</li>\n";
echo "<li>Error handler should suppress relevant deprecation warnings</li>\n";
echo "</ul>\n";

echo "<h3>Environment Information</h3>\n";
echo "- WordPress Version: " . (defined('WP_VERSION') ? WP_VERSION : 'Unknown') . "\n";
echo "- DAB Plugin Version: " . (defined('DAB_VERSION') ? DAB_VERSION : 'Unknown') . "\n";
echo "- Error Reporting: " . error_reporting() . "\n";
echo "- WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "\n";
echo "- WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . "\n";
echo "- Current Memory Usage: " . number_format(memory_get_usage()) . " bytes\n";
echo "- Peak Memory Usage: " . number_format(memory_get_peak_usage()) . " bytes\n";
