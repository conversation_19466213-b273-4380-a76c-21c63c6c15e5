/**
 * Inline Table Field JavaScript
 *
 * Handles the functionality for inline table fields
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initInlineTables();
    });

    /**
     * Initialize all inline tables on the page
     */
    function initInlineTables() {
        // Find all inline table containers
        $('.dab-inline-table-container').each(function() {
            const container = $(this);
            const parentId = container.data('parent-id');
            const parentTable = container.data('parent-table');
            const childTable = container.data('child-table');
            const foreignKey = container.data('foreign-key');

            // Initialize event handlers for this table
            initTableEvents(container, parentId, parentTable, childTable, foreignKey);
        });
    }

    /**
     * Initialize event handlers for an inline table
     */
    function initTableEvents(container, parentId, parentTable, childTable, foreignKey) {
        // Get permission settings
        const allowAdd = container.data('allow-add') !== '0';
        const allowEdit = container.data('allow-edit') !== '0';
        const allowDelete = container.data('allow-delete') !== '0';

        // Add button click handler (if allowed)
        if (allowAdd) {
            container.find('.dab-inline-add-btn').on('click', function() {
                showAddRecordModal(container, parentId, parentTable, childTable, foreignKey);
            });
        }

        // Edit button click handler (if allowed)
        if (allowEdit) {
            container.on('click', '.dab-inline-edit', function() {
                const row = $(this).closest('tr');
                const recordId = row.data('id');
                showEditRecordModal(container, row, recordId, parentTable, childTable, foreignKey);
            });

            // Double-click on field to edit inline (only if edit is allowed)
            container.on('dblclick', '.dab-inline-field', function() {
                const cell = $(this);
                const row = cell.closest('tr');
                const recordId = row.data('id');
                const fieldSlug = cell.data('field');

                // Don't allow inline editing if already editing
                if (cell.hasClass('editing')) {
                    return;
                }

                // Create input and handle editing (existing code will run)
                editCellInline(cell, row, recordId, fieldSlug, childTable);
            });
        }

        // Delete button click handler (if allowed)
        if (allowDelete) {
            container.on('click', '.dab-inline-delete', function() {
                const row = $(this).closest('tr');
                const recordId = row.data('id');

                if (confirm('Are you sure you want to delete this record?')) {
                    deleteRecord(container, row, recordId, parentTable, childTable);
                }
            });
        }

    }

    /**
     * Edit a cell inline
     */
    function editCellInline(cell, row, recordId, fieldSlug, childTable) {
        // Get current value
        const currentValue = cell.text().trim();

        // Create input field
        const input = $('<input type="text" class="dab-inline-edit-input">');
        input.val(currentValue);

        // Replace cell content with input
        cell.html(input);
        cell.addClass('editing');

        // Focus the input
        input.focus();

        // Handle blur event to save changes
        input.on('blur', function() {
            const newValue = $(this).val();

            // If value hasn't changed, just restore the display
            if (newValue === currentValue) {
                cell.html(currentValue);
                cell.removeClass('editing');
                return;
            }

            // Show loading indicator
            cell.append('<div class="dab-inline-loading"></div>');

            // Save the updated value
            $.ajax({
                url: dabVars.ajaxurl,
                type: 'POST',
                data: {
                    action: 'dab_update_inline_field',
                    table_id: childTable,
                    record_id: recordId,
                    field_slug: fieldSlug,
                    field_value: newValue,
                    nonce: dabVars.nonce
                },
                success: function(response) {
                    cell.removeClass('editing');

                    if (response.success) {
                        cell.html(response.data.formatted_value || newValue);
                    } else {
                        cell.html(currentValue);
                        alert('Error updating field: ' + (response.data?.message || 'Unknown error'));
                    }
                },
                error: function() {
                    cell.removeClass('editing');
                    cell.html(currentValue);
                    alert('Error updating field. Please try again.');
                }
            });
        });

        // Handle Enter key to save changes
        input.on('keypress', function(e) {
            if (e.which === 13) {
                $(this).blur();
            }
        });

        // Handle Escape key to cancel
        input.on('keydown', function(e) {
            if (e.which === 27) {
                cell.html(currentValue);
                cell.removeClass('editing');
            }
        });
    }

    /**
     * Show modal for adding a new record
     */
    function showAddRecordModal(container, parentId, parentTable, childTable, foreignKey) {
        // Create modal if it doesn't exist
        if ($('#dab-inline-modal').length === 0) {
            $('body').append(`
                <div id="dab-inline-modal" class="dab-inline-modal">
                    <div class="dab-inline-modal-content">
                        <span class="dab-inline-modal-close">&times;</span>
                        <h3 class="dab-inline-modal-title">Add New Record</h3>
                        <form id="dab-inline-form">
                            <div class="dab-inline-form-fields"></div>
                            <div class="dab-inline-form-actions">
                                <button type="button" class="button dab-inline-cancel">Cancel</button>
                                <button type="submit" class="button button-primary dab-inline-save">Save</button>
                            </div>
                        </form>
                    </div>
                </div>
            `);

            // Close button handler
            $('#dab-inline-modal').on('click', '.dab-inline-modal-close, .dab-inline-cancel', function() {
                $('#dab-inline-modal').hide();
            });

            // Close when clicking outside the modal
            $('#dab-inline-modal').on('click', function(e) {
                if ($(e.target).is('#dab-inline-modal')) {
                    $('#dab-inline-modal').hide();
                }
            });
        }

        // Get fields for the child table
        $.ajax({
            url: dabVars.ajaxurl,
            type: 'POST',
            data: {
                action: 'dab_get_table_fields',
                table_id: childTable,
                nonce: dabVars.nonce
            },
            success: function(response) {
                if (!response.success || !response.data) {
                    alert('Error loading fields');
                    return;
                }

                const fields = response.data;
                const formFields = $('#dab-inline-form .dab-inline-form-fields');
                formFields.empty();

                // Add hidden fields
                formFields.append(`
                    <input type="hidden" name="parent_id" value="${parentId}">
                    <input type="hidden" name="parent_table" value="${parentTable}">
                    <input type="hidden" name="child_table" value="${childTable}">
                    <input type="hidden" name="foreign_key" value="${foreignKey}">
                `);

                // Add form fields for each table field
                fields.forEach(function(field) {
                    // Skip the foreign key field - it will be set automatically
                    if (field.field_slug === foreignKey) {
                        return;
                    }

                    formFields.append(`
                        <div class="dab-inline-form-field">
                            <label for="field-${field.field_slug}">${field.field_label}</label>
                            <input type="text" id="field-${field.field_slug}" name="${field.field_slug}">
                        </div>
                    `);
                });

                // Show the modal
                $('#dab-inline-modal').show();

                // Form submit handler
                $('#dab-inline-form').off('submit').on('submit', function(e) {
                    e.preventDefault();

                    const formData = $(this).serializeArray();
                    const data = {
                        action: 'dab_add_inline_record',
                        nonce: dabVars.nonce
                    };

                    // Convert form data to object
                    formData.forEach(function(item) {
                        data[item.name] = item.value;
                    });

                    // Add the record
                    $.ajax({
                        url: dabVars.ajaxurl,
                        type: 'POST',
                        data: data,
                        success: function(response) {
                            if (response.success) {
                                // Reload the inline table
                                reloadInlineTable(container, parentId, parentTable, childTable, foreignKey);
                                $('#dab-inline-modal').hide();
                            } else {
                                alert('Error adding record: ' + (response.data?.message || 'Unknown error'));
                            }
                        },
                        error: function() {
                            alert('Error adding record. Please try again.');
                        }
                    });
                });
            },
            error: function() {
                alert('Error loading fields. Please try again.');
            }
        });
    }

    /**
     * Show modal for editing a record
     */
    function showEditRecordModal(container, row, recordId, parentTable, childTable, foreignKey) {
        // Similar to showAddRecordModal but pre-fills the form with existing values
        // Implementation details omitted for brevity
    }

    /**
     * Delete a record
     */
    function deleteRecord(container, row, recordId, parentTable, childTable) {
        $.ajax({
            url: dabVars.ajaxurl,
            type: 'POST',
            data: {
                action: 'dab_delete_inline_record',
                table_id: childTable,
                record_id: recordId,
                nonce: dabVars.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Remove the row from the table
                    row.fadeOut(300, function() {
                        row.remove();

                        // If no rows left, show empty message
                        if (container.find('tbody tr').length === 0) {
                            container.find('table').remove();
                            container.prepend('<div class="dab-inline-table-empty">No records found</div>');
                        }
                    });
                } else {
                    alert('Error deleting record: ' + (response.data?.message || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error deleting record. Please try again.');
            }
        });
    }

    /**
     * Reload the inline table
     */
    function reloadInlineTable(container, parentId, parentTable, childTable, foreignKey) {
        $.ajax({
            url: dabVars.ajaxurl,
            type: 'POST',
            data: {
                action: 'dab_get_inline_table',
                parent_id: parentId,
                parent_table: parentTable,
                child_table: childTable,
                foreign_key: foreignKey,
                nonce: dabVars.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Replace the container content
                    container.html(response.data.html);

                    // Reinitialize event handlers
                    initTableEvents(container, parentId, parentTable, childTable, foreignKey);
                } else {
                    alert('Error reloading table: ' + (response.data?.message || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error reloading table. Please try again.');
            }
        });
    }

})(jQuery);
