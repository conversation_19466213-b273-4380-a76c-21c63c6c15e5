<?php
if (!defined('ABSPATH')) exit;

global $wpdb;
$tables_table = $wpdb->prefix . 'dab_tables';
$approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
$message = '';

// Enqueue required styles and scripts
wp_enqueue_style('wp-jquery-ui-dialog');
wp_enqueue_script('jquery-ui-dialog');
wp_enqueue_script('jquery-ui-sortable');
wp_enqueue_style('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/approval-dashboard.css', array(), DAB_VERSION);

// Handle form submission
if (isset($_POST['save_approval_workflow'])) {
    $table_id = intval($_POST['table_id']);

    // Delete existing levels for this table
    $wpdb->delete($approval_levels_table, ['table_id' => $table_id]);

    // Add new levels
    if (isset($_POST['level_name']) && is_array($_POST['level_name'])) {
        foreach ($_POST['level_name'] as $index => $name) {
            if (empty($name)) continue;

            // Get specific users for this level
            $specific_users = isset($_POST['specific_users'][$index]) ? $_POST['specific_users'][$index] : [];

            $wpdb->insert($approval_levels_table, [
                'table_id' => $table_id,
                'level_name' => sanitize_text_field($name),
                'level_order' => intval($_POST['level_order'][$index]),
                'approver_roles' => serialize(isset($_POST['approver_roles'][$index]) ? $_POST['approver_roles'][$index] : []),
                'specific_users' => serialize($specific_users),
                'notification_email' => sanitize_email($_POST['notification_email'][$index]),
                'created_at' => current_time('mysql')
            ]);
        }
    }

    $message = "Approval workflow saved successfully.";
}

// Get all tables
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");
$selected_table_id = isset($_GET['table_id']) ? intval($_GET['table_id']) : 0;

// Get approval levels for selected table
$approval_levels = [];
if ($selected_table_id) {
    $approval_levels = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM $approval_levels_table WHERE table_id = %d ORDER BY level_order", $selected_table_id)
    );
}

// Get all WordPress roles
$wp_roles = wp_roles();
$all_roles = $wp_roles->get_names();

// Get all WordPress users
$users = get_users(['fields' => ['ID', 'display_name', 'user_email']]);
?>

<div class="wrap">
    <h1>Approval Workflow Configuration</h1>

    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
    <?php endif; ?>

    <form method="get">
        <input type="hidden" name="page" value="dab_approval_config">
        <select name="table_id" onchange="this.form.submit();" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; min-width: 200px; position: relative; z-index: 100; height: auto; appearance: menulist; -webkit-appearance: menulist; -moz-appearance: menulist;">
            <option value="">-- Select Table --</option>
            <?php foreach ($tables as $table): ?>
                <option value="<?php echo esc_attr($table->id); ?>" <?php selected($selected_table_id, $table->id); ?>>
                    <?php echo esc_html($table->table_label); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </form>

    <?php if ($selected_table_id): ?>
        <form method="post" id="approval-workflow-form">
            <input type="hidden" name="table_id" value="<?php echo esc_attr($selected_table_id); ?>">

            <h2>Approval Levels</h2>
            <p>Define the approval levels in sequential order. Each level must be approved before moving to the next.</p>

            <div id="approval-levels-container">
                <?php
                if (empty($approval_levels)) {
                    // Add one empty level by default
                    $approval_levels = [new stdClass()];
                }

                foreach ($approval_levels as $index => $level):
                    $level_name = isset($level->level_name) ? $level->level_name : '';
                    $level_order = isset($level->level_order) ? $level->level_order : $index + 1;
                    $approver_roles = isset($level->approver_roles) ? maybe_unserialize($level->approver_roles) : [];
                    $notification_email = isset($level->notification_email) ? $level->notification_email : '';
                ?>
                <div class="approval-level" style="margin-bottom: 20px; padding: 20px; border: 1px solid #ccc; background: #f9f9f9; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                    <h3 style="margin-top: 0; padding-bottom: 10px; border-bottom: 1px solid #eee;">Level <?php echo $index + 1; ?></h3>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Level Name:</label>
                        <input type="text" name="level_name[]" value="<?php echo esc_attr($level_name); ?>" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <input type="hidden" name="level_order[]" value="<?php echo esc_attr($level_order); ?>">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Who can approve (by role):</label>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                            <?php foreach ($all_roles as $role_id => $role_name): ?>
                                <label style="display: flex; align-items: center; padding: 5px 10px; background: #f0f0f0; border-radius: 4px; margin-right: 5px; margin-bottom: 5px;">
                                    <input type="checkbox" name="approver_roles[<?php echo $index; ?>][]" value="<?php echo esc_attr($role_id); ?>"
                                        <?php checked(in_array($role_id, $approver_roles)); ?> style="margin-right: 5px;">
                                    <?php echo esc_html($role_name); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Specific Users (optional):</label>
                        <p style="margin-top: 0; color: #666; font-style: italic; font-size: 13px;">Select specific users who can approve at this level, regardless of their role.</p>
                        <select name="specific_users[<?php echo $index; ?>][]" multiple style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <?php
                            $specific_users = isset($level->specific_users) ? maybe_unserialize($level->specific_users) : [];
                            foreach ($users as $user):
                            ?>
                                <option value="<?php echo esc_attr($user->ID); ?>" <?php selected(in_array($user->ID, $specific_users)); ?>>
                                    <?php echo esc_html($user->display_name) . ' (' . esc_html($user->user_email) . ')'; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p style="margin-top: 5px; color: #666; font-size: 12px;">Hold Ctrl/Cmd to select multiple users</p>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Notification Email:</label>
                        <input type="email" name="notification_email[]" value="<?php echo esc_attr($notification_email); ?>" placeholder="Optional notification email" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <p style="margin-top: 5px; color: #666; font-size: 12px;">Additional email to notify when approval is required at this level</p>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                        <?php if ($index > 0): ?>
                            <button type="button" class="button remove-level" style="background-color: #f44336; color: white; border: none;">Remove Level</button>
                        <?php else: ?>
                            <div></div>
                        <?php endif; ?>
                        <div class="level-handle" style="cursor: move; padding: 5px 10px; background: #f0f0f0; border-radius: 4px; color: #666;">
                            <span class="dashicons dashicons-menu"></span> Drag to reorder
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <p>
                <button type="button" id="add-level" class="button">Add Another Level</button>
                <input type="submit" name="save_approval_workflow" class="button button-primary" value="Save Approval Workflow">
            </p>
        </form>

        <script>
            jQuery(document).ready(function($) {
                // Initialize sortable for drag-and-drop reordering
                $('#approval-levels-container').sortable({
                    handle: '.level-handle',
                    placeholder: 'approval-level-placeholder',
                    opacity: 0.7,
                    update: function(event, ui) {
                        // Renumber levels after drag and drop
                        renumberLevels();
                    }
                });

                // Add new level
                $('#add-level').on('click', function() {
                    var levelCount = $('.approval-level').length;
                    var template = $('.approval-level').first().clone();

                    // Update title and clear values
                    template.find('h3').text('Level ' + (levelCount + 1));
                    template.find('input[type="text"], input[type="email"]').val('');
                    template.find('input[type="checkbox"]').prop('checked', false);
                    template.find('select').val([]);
                    template.find('input[name="level_order[]"]').val(levelCount + 1);

                    // Add remove button if not present
                    if (template.find('.remove-level').length === 0) {
                        template.find('.level-handle').before('<button type="button" class="button remove-level" style="background-color: #f44336; color: white; border: none;">Remove Level</button>');
                    }

                    // Update array indices
                    template.find('input[name^="approver_roles["]').each(function() {
                        $(this).attr('name', $(this).attr('name').replace(/approver_roles\[\d+\]/, 'approver_roles[' + levelCount + ']'));
                    });

                    template.find('select[name^="specific_users["]').each(function() {
                        $(this).attr('name', $(this).attr('name').replace(/specific_users\[\d+\]/, 'specific_users[' + levelCount + ']'));
                    });

                    $('#approval-levels-container').append(template);

                    // Scroll to the new level
                    $('html, body').animate({
                        scrollTop: template.offset().top - 100
                    }, 500);

                    // Add highlight effect
                    template.addClass('highlight-new');
                    setTimeout(function() {
                        template.removeClass('highlight-new');
                    }, 1500);
                });

                // Remove level
                $(document).on('click', '.remove-level', function() {
                    var level = $(this).closest('.approval-level');

                    // Add fade out effect
                    level.fadeOut(300, function() {
                        $(this).remove();
                        renumberLevels();
                    });
                });

                // Function to renumber levels
                function renumberLevels() {
                    $('.approval-level').each(function(index) {
                        $(this).find('h3').text('Level ' + (index + 1));
                        $(this).find('input[name="level_order[]"]').val(index + 1);

                        // Update array indices
                        $(this).find('input[name^="approver_roles["]').each(function() {
                            $(this).attr('name', $(this).attr('name').replace(/approver_roles\[\d+\]/, 'approver_roles[' + index + ']'));
                        });

                        $(this).find('select[name^="specific_users["]').each(function() {
                            $(this).attr('name', $(this).attr('name').replace(/specific_users\[\d+\]/, 'specific_users[' + index + ']'));
                        });
                    });
                }

                // Add form submission validation
                $('#approval-workflow-form').on('submit', function(e) {
                    var valid = true;
                    var errorMessage = '';

                    // Check if at least one role or specific user is selected for each level
                    $('.approval-level').each(function(index) {
                        var levelName = $(this).find('input[name="level_name[]"]').val();
                        var hasRoles = $(this).find('input[name^="approver_roles["]:checked').length > 0;
                        var hasUsers = $(this).find('select[name^="specific_users["] option:selected').length > 0;

                        if (!hasRoles && !hasUsers) {
                            valid = false;
                            errorMessage = 'Level ' + (index + 1) + ' (' + levelName + ') must have at least one role or specific user assigned.';
                            return false; // Break the loop
                        }
                    });

                    if (!valid) {
                        e.preventDefault();
                        alert(errorMessage);
                    }
                });

                // Add some styling
                $('<style>\
                    .approval-level { transition: all 0.3s ease; }\
                    .approval-level-placeholder { border: 2px dashed #ccc; background: #f5f5f5; height: 100px; margin-bottom: 20px; }\
                    .highlight-new { background-color: #e6f7ff !important; border-color: #1890ff !important; box-shadow: 0 0 8px rgba(24, 144, 255, 0.5) !important; }\
                </style>').appendTo('head');
            });
        </script>
    <?php endif; ?>
</div>