/**
 * Social Media Field Scripts
 *
 * JavaScript for social media fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all social media fields
    function initSocialMediaFields() {
        $('.dab-social-media-field').each(function() {
            var $field = $(this);
            
            // Skip if already initialized
            if ($field.data('initialized')) {
                return;
            }
            
            // Mark as initialized
            $field.data('initialized', true);
            
            var $links = $field.find('.dab-social-media-links');
            var $value = $field.find('input[type="hidden"]');
            var $template = $field.find('script[type="text/template"]');
            var maxLinks = $field.data('max-links') || 0;
            var allowCustom = $field.data('allow-custom') || false;
            var validateUrls = $field.data('validate-urls') || false;
            var platformPatterns = $field.data('platform-patterns') || {};
            
            // Initialize
            updateValue();
            toggleAddButton();
            
            // Add new social link
            $field.on('click', '.dab-add-social-link', function() {
                var index = $links.children().length;
                var newRow = $template.html().replace(/\{\{index\}\}/g, index);
                $links.append(newRow);
                toggleAddButton();
            });
            
            // Remove social link
            $field.on('click', '.dab-remove-social-link', function() {
                $(this).closest('.dab-social-link-row').remove();
                // Renumber indices
                $links.children().each(function(i) {
                    $(this).find('select, input').each(function() {
                        var name = $(this).attr('name');
                        if (name) {
                            $(this).attr('name', name.replace(/\[\d+\]/, '[' + i + ']'));
                        }
                    });
                });
                updateValue();
                toggleAddButton();
            });
            
            // Toggle custom platform input
            $field.on('change', '.dab-social-platform', function() {
                var $row = $(this).closest('.dab-social-link-row');
                var platform = $(this).val();
                
                if (platform === 'custom' && allowCustom) {
                    $row.find('.dab-custom-platform').show();
                } else {
                    $row.find('.dab-custom-platform').hide();
                }
                
                // Update icon
                if (platform !== 'custom' && platform !== '') {
                    var iconClass = 'fab fa-' + platform.toLowerCase();
                    $row.find('.dab-social-icon i').attr('class', iconClass);
                } else {
                    $row.find('.dab-social-icon i').attr('class', 'fas fa-link');
                }
                
                updateValue();
            });
            
            // Update value when inputs change
            $field.on('input change', 'input, select', function() {
                updateValue();
            });
            
            // Validate URL based on platform
            $field.on('blur', '.dab-social-url', function() {
                if (!validateUrls) return;
                
                var $row = $(this).closest('.dab-social-link-row');
                var platform = $row.find('.dab-social-platform').val();
                var url = $(this).val().trim();
                
                if (url && platform !== 'custom' && platform !== '' && platformPatterns[platform]) {
                    var pattern = new RegExp(platformPatterns[platform]);
                    if (!pattern.test(url)) {
                        $(this).addClass('dab-invalid-url');
                        // Try to fix common issues
                        if (platform === 'facebook' && url.indexOf('facebook.com') !== -1) {
                            $(this).val('https://www.facebook.com/' + url.split('facebook.com/').pop());
                        } else if (platform === 'twitter' && url.indexOf('twitter.com') !== -1) {
                            $(this).val('https://twitter.com/' + url.split('twitter.com/').pop());
                        } else if (platform === 'instagram' && url.indexOf('instagram.com') !== -1) {
                            $(this).val('https://www.instagram.com/' + url.split('instagram.com/').pop());
                        }
                        updateValue();
                    } else {
                        $(this).removeClass('dab-invalid-url');
                    }
                } else {
                    $(this).removeClass('dab-invalid-url');
                }
            });
            
            // Update the hidden value field
            function updateValue() {
                var links = [];
                
                $links.children().each(function() {
                    var $row = $(this);
                    var platform = $row.find('.dab-social-platform').val();
                    var url = $row.find('.dab-social-url').val().trim();
                    var customPlatform = $row.find('.dab-custom-platform').val().trim();
                    
                    // Skip empty rows
                    if (!platform || !url) return;
                    
                    var link = {
                        platform: platform,
                        url: url
                    };
                    
                    if (platform === 'custom' && customPlatform) {
                        link.custom = true;
                        link.custom_name = customPlatform;
                    }
                    
                    links.push(link);
                });
                
                $value.val(JSON.stringify(links));
            }
            
            // Toggle add button based on max links
            function toggleAddButton() {
                if (maxLinks > 0 && $links.children().length >= maxLinks) {
                    $field.find('.dab-add-social-link').hide();
                } else {
                    $field.find('.dab-add-social-link').show();
                }
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initSocialMediaFields();
        
        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            initSocialMediaFields();
        });
    });
    
    // Add a global function to format social media values
    window.dabFormatSocialMedia = function(value, options) {
        options = options || {};
        
        // Parse the value
        var socialLinks = [];
        if (!value) {
            return '';
        }
        
        if (typeof value === 'string' && value.indexOf('{') === 0) {
            try {
                var parsed = JSON.parse(value);
                if (Array.isArray(parsed)) {
                    socialLinks = parsed;
                }
            } catch (e) {
                return value;
            }
        } else {
            return value;
        }
        
        if (socialLinks.length === 0) {
            return '';
        }
        
        // Format options
        var displayIcons = options.displayIcons !== undefined ? options.displayIcons : true;
        var linkTarget = options.linkTarget || '_blank';
        var maxLinks = options.maxLinks || 0;
        var asHtml = options.asHtml !== undefined ? options.asHtml : true;
        
        // Get platform names
        var platformNames = options.platformNames || {
            'facebook': 'Facebook',
            'twitter': 'Twitter',
            'instagram': 'Instagram',
            'linkedin': 'LinkedIn',
            'youtube': 'YouTube',
            'pinterest': 'Pinterest',
            'tiktok': 'TikTok',
            'snapchat': 'Snapchat',
            'reddit': 'Reddit',
            'tumblr': 'Tumblr',
            'whatsapp': 'WhatsApp',
            'telegram': 'Telegram',
            'github': 'GitHub',
            'dribbble': 'Dribbble',
            'behance': 'Behance',
            'medium': 'Medium',
            'vimeo': 'Vimeo',
            'soundcloud': 'SoundCloud',
            'spotify': 'Spotify',
            'twitch': 'Twitch'
        };
        
        // Limit the number of links if specified
        if (maxLinks > 0 && socialLinks.length > maxLinks) {
            socialLinks = socialLinks.slice(0, maxLinks);
        }
        
        // Format the links
        var formattedLinks = [];
        
        for (var i = 0; i < socialLinks.length; i++) {
            var link = socialLinks[i];
            var platform = link.platform || '';
            var url = link.url || '';
            var isCustom = link.custom || false;
            var customName = link.custom_name || '';
            
            if (!platform || !url) {
                continue;
            }
            
            var platformName = isCustom ? customName : (platformNames[platform] || platform);
            
            if (asHtml) {
                var iconClass = 'fab fa-' + platform.toLowerCase();
                if (platform === 'custom') {
                    iconClass = 'fas fa-link';
                }
                
                var iconHtml = displayIcons ? '<i class="' + iconClass + '"></i> ' : '';
                
                formattedLinks.push(
                    '<a href="' + url + '" target="' + linkTarget + '" rel="noopener noreferrer" ' +
                    'class="dab-social-link dab-social-link-' + platform + '">' +
                    iconHtml + platformName + '</a>'
                );
            } else {
                formattedLinks.push(platformName + ': ' + url);
            }
        }
        
        // Join the formatted links
        if (asHtml) {
            return '<div class="dab-social-links-display">' + formattedLinks.join('') + '</div>';
        } else {
            return formattedLinks.join(', ');
        }
    };
})(jQuery);
