<?php
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$relationships_table = $wpdb->prefix . 'dab_relationships';

$message = '';
$edit_mode = false;
$edit_relationship = null;

// Handle delete
if (isset($_GET['delete_relationship'])) {
    $rel_id = intval($_GET['delete_relationship']);
    $wpdb->delete($relationships_table, ['id' => $rel_id]);
    $message = "Relationship deleted successfully.";
}

// Handle edit
if (isset($_GET['edit_relationship'])) {
    $edit_mode = true;
    $edit_id = intval($_GET['edit_relationship']);
    $edit_relationship = $wpdb->get_row($wpdb->prepare("SELECT * FROM $relationships_table WHERE id = %d", $edit_id));
}

// Handle create/update
if (isset($_POST['dab_save_relationship'])) {
    $table_a_id = intval($_POST['table_a_id']);
    $field_a_slug = sanitize_text_field($_POST['field_a_slug']);
    $table_b_id = intval($_POST['table_b_id']);
    $display_column = sanitize_text_field($_POST['display_column']);
    $relationship_type = sanitize_text_field($_POST['relationship_type']);

    if (isset($_POST['rel_id']) && $_POST['rel_id']) {
        $wpdb->update($relationships_table, [
            'table_a_id' => $table_a_id,
            'field_a_slug' => $field_a_slug,
            'table_b_id' => $table_b_id,
            'display_column' => $display_column,
            'relationship_type' => $relationship_type
        ], ['id' => intval($_POST['rel_id'])]);
        $message = "Relationship updated successfully.";
        $edit_mode = false;
    } else {
        $wpdb->insert($relationships_table, [
            'table_a_id' => $table_a_id,
            'field_a_slug' => $field_a_slug,
            'table_b_id' => $table_b_id,
            'display_column' => $display_column,
            'relationship_type' => $relationship_type,
            'created_at' => current_time('mysql')
        ]);
        $message = "Relationship created successfully.";
    }
}

$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY id DESC");
$relationships = $wpdb->get_results("SELECT * FROM $relationships_table ORDER BY id DESC");

$source_table_id = $edit_mode ? $edit_relationship->table_a_id : (isset($_POST['table_a_id']) ? intval($_POST['table_a_id']) : 0);
$target_table_id = $edit_mode ? $edit_relationship->table_b_id : (isset($_POST['table_b_id']) ? intval($_POST['table_b_id']) : 0);
$source_fields = $source_table_id ? $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $source_table_id)) : [];
$target_fields = $target_table_id ? $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $target_table_id)) : [];
?>

<div class="wrap">
    <h1><?php echo $edit_mode ? 'Edit Relationship' : 'Create Relationship'; ?></h1>

    <?php if (!empty($message)): ?>
        <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
    <?php endif; ?>

    <form method="post">
        <?php if ($edit_mode): ?>
            <input type="hidden" name="rel_id" value="<?php echo esc_attr($edit_relationship->id); ?>">
        <?php endif; ?>

        <table class="form-table">
            <tr>
                <th>Source Table</th>
                <td>
                    <select name="table_a_id" onchange="this.form.submit()" required>
                        <option value="">-- Select Table --</option>
                        <?php foreach ($tables as $tbl): ?>
                            <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($source_table_id, $tbl->id); ?>>
                                <?php echo esc_html($tbl->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Source Field (Foreign Key)</th>
                <td>
                    <select name="field_a_slug" required>
                        <option value="">-- Select Field --</option>
                        <?php foreach ($source_fields as $field): ?>
                            <option value="<?php echo esc_attr($field->field_slug); ?>"
                                <?php if ($edit_mode && $field->field_slug == $edit_relationship->field_a_slug) echo 'selected'; ?>>
                                <?php echo esc_html($field->field_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Target Table</th>
                <td>
                    <select name="table_b_id" onchange="this.form.submit()" required>
                        <option value="">-- Select Table --</option>
                        <?php foreach ($tables as $tbl): ?>
                            <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($target_table_id, $tbl->id); ?>>
                                <?php echo esc_html($tbl->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Display Column (from Target Table)</th>
                <td>
                    <select name="display_column" required>
                        <option value="">-- Select Display Field --</option>
                        <?php foreach ($target_fields as $field): ?>
                            <option value="<?php echo esc_attr($field->field_slug); ?>"
                                <?php if ($edit_mode && $field->field_slug == $edit_relationship->display_column) echo 'selected'; ?>>
                                <?php echo esc_html($field->field_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Relationship Type</th>
                <td>
                    <select name="relationship_type" required>
                        <option value="one_to_many" <?php selected($edit_mode && $edit_relationship->relationship_type == 'one_to_many'); ?>>One to Many</option>
                        <option value="many_to_one" <?php selected($edit_mode && $edit_relationship->relationship_type == 'many_to_one'); ?>>Many to One</option>
                        <option value="many_to_many" <?php selected($edit_mode && $edit_relationship->relationship_type == 'many_to_many'); ?>>Many to Many</option>
                    </select>
                </td>
            </tr>
        </table>

        <p><input type="submit" name="dab_save_relationship" class="button button-primary" value="<?php echo $edit_mode ? 'Update Relationship' : 'Create Relationship'; ?>"></p>
    </form>

    <hr>
    <h2>Existing Relationships</h2>
    <?php if (!empty($relationships)): ?>
        <table class="widefat striped">
            <thead>
                <tr>
                    <th>Source Table</th>
                    <th>Source Field</th>
                    <th>Target Table</th>
                    <th>Display Column</th>
                    <th>Type</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($relationships as $rel): ?>
                    <?php
                        $source = $wpdb->get_var($wpdb->prepare("SELECT table_label FROM $tables_table WHERE id = %d", $rel->table_a_id));
                        $target = $wpdb->get_var($wpdb->prepare("SELECT table_label FROM $tables_table WHERE id = %d", $rel->table_b_id));
                    ?>
                    <tr>
                        <td><?php echo esc_html($source); ?></td>
                        <td><?php echo esc_html($rel->field_a_slug); ?></td>
                        <td><?php echo esc_html($target); ?></td>
                        <td><?php echo esc_html($rel->display_column); ?></td>
                        <td><?php
                            // Use safe str_replace to handle potentially null relationship_type values
                            $relationship_type_name = '';
                            if (function_exists('dab_safe_str_replace')) {
                                $relationship_type_name = dab_safe_str_replace('_', ' ', $rel->relationship_type);
                            } else {
                                $relationship_type_name = str_replace('_', ' ', ($rel->relationship_type ?? ''));
                            }
                            echo esc_html(ucwords($relationship_type_name));
                        ?></td>
                        <td><?php echo esc_html($rel->created_at); ?></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=dab_relationships&edit_relationship=' . $rel->id); ?>">Edit</a> |
                            <a href="<?php echo admin_url('admin.php?page=dab_relationships&delete_relationship=' . $rel->id); ?>"
                               onclick="return confirm('Are you sure you want to delete this relationship?');">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No relationships defined yet.</p>
    <?php endif; ?>
</div>
