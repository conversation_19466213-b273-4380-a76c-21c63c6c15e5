/**
 * Enhanced Inline Table Field Styles
 */

/* Container */
.dab-enhanced-inline-table {
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
}

.dab-enhanced-inline-table-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-enhanced-inline-table-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin: 0;
}

.dab-inline-add-row {
    background-color: #2271b1;
    color: #fff;
    border: none;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dab-inline-add-row:hover {
    background-color: #135e96;
}

.dab-inline-add-row:focus {
    outline: 1px solid #135e96;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #2271b1;
}

/* Table */
.dab-enhanced-inline-table-content {
    padding: 0;
    overflow-x: auto;
}

.dab-enhanced-inline-table table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.dab-enhanced-inline-table thead th {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
    text-align: left;
    padding: 10px;
    border-bottom: 2px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.dab-enhanced-inline-table tbody td {
    padding: 8px 10px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
}

.dab-enhanced-inline-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.dab-enhanced-inline-table tbody tr:hover {
    background-color: #f0f7ff;
}

/* Row number column */
.dab-inline-row-number-col {
    width: 40px;
    text-align: center;
    color: #666;
    font-weight: 600;
}

/* Input fields */
.dab-enhanced-inline-table input,
.dab-enhanced-inline-table select,
.dab-enhanced-inline-table textarea {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.dab-enhanced-inline-table input:focus,
.dab-enhanced-inline-table select:focus,
.dab-enhanced-inline-table textarea:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Select2 enhancements */
.dab-enhanced-inline-table .select2-container {
    width: 100% !important;
}

.dab-enhanced-inline-table .select2-container--default .select2-selection--single {
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.dab-enhanced-inline-table .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px;
    padding-left: 8px;
    color: #333;
}

.dab-enhanced-inline-table .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 30px;
}

.dab-enhanced-inline-table .select2-container--default .select2-selection--single:focus,
.dab-enhanced-inline-table .select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.select2-dropdown {
    border-color: #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2271b1;
}

/* Action buttons */
.dab-inline-actions-col {
    width: 50px;
    text-align: center;
}

.dab-inline-delete-row {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    margin: 0;
    border-radius: 3px;
    color: #d63638;
}

.dab-inline-delete-row:hover {
    background-color: rgba(214, 54, 56, 0.1);
}

.dab-inline-delete-row:focus {
    outline: 1px solid #d63638;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #d63638;
}

/* Empty state */
.dab-enhanced-inline-table-empty {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
}

/* Templates (hidden) */
.dab-inline-table-template,
.dab-inline-row-template {
    display: none;
}

/* Responsive styles */
@media (max-width: 768px) {
    .dab-enhanced-inline-table table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .dab-enhanced-inline-table input,
    .dab-enhanced-inline-table select,
    .dab-enhanced-inline-table textarea {
        font-size: 16px; /* Prevent zoom on mobile */
    }
}
