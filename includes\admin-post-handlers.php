<?php
/**
 * Admin Post Handlers
 *
 * This file contains handlers for admin-post.php actions.
 */
if (!defined('ABSPATH')) exit;

/**
 * Handler for fixing approval status
 */
function dab_fix_approval_status_handler() {
    // Check if user is administrator
    if (!current_user_can('administrator')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    // Verify nonce
    if (!isset($_POST['dab_fix_approval_nonce']) || !wp_verify_nonce($_POST['dab_fix_approval_nonce'], 'dab_fix_approval_status')) {
        wp_die('Security check failed.');
    }

    // Get form data
    $table_id = isset($_POST['table_id']) ? intval($_POST['table_id']) : 0;
    $record_id = isset($_POST['record_id']) ? intval($_POST['record_id']) : 0;
    $approval_status = isset($_POST['approval_status']) ? sanitize_text_field($_POST['approval_status']) : 'Pending';
    $current_level = isset($_POST['current_level']) ? intval($_POST['current_level']) : 0;
    $notes = isset($_POST['notes']) ? sanitize_textarea_field($_POST['notes']) : '';

    if (!$table_id || !$record_id) {
        wp_die('Invalid table or record ID.');
    }

    global $wpdb;

    // Get table info
    $table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
        $table_id
    ));

    if (!$table) {
        wp_die('Table not found.');
    }

    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

    // Update record status
    $result = $wpdb->update(
        $data_table,
        [
            'approval_status' => $approval_status,
            'current_approval_level' => $current_level
        ],
        ['id' => $record_id]
    );

    if ($result === false) {
        wp_die('Failed to update record status.');
    }

    // Add entry to approval history
    $wpdb->insert(
        $wpdb->prefix . 'dab_approval_history',
        [
            'record_id' => $record_id,
            'table_id' => $table_id,
            'level_id' => $current_level,
            'status' => $approval_status,
            'notes' => $notes . ' (Manual fix by admin)',
            'user_id' => get_current_user_id(),
            'created_at' => current_time('mysql')
        ]
    );

    // Redirect back to data page
    wp_redirect(add_query_arg(
        [
            'page' => 'dab_data',
            'table_id' => $table_id,
            'updated' => '1'
        ],
        admin_url('admin.php')
    ));
    exit;
}
// Removed debug handler
