<?php
/**
 * Permissions Documentation Page
 *
 * Provides documentation on how to use the permissions system.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}
?>

<div class="wrap dab-admin-wrap">
    <h1>Role-Based Permissions Documentation</h1>
    
    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Overview</h2>
        </div>
        <div class="dab-card-body">
            <p>The Database App Builder plugin includes a comprehensive role-based permissions system that allows you to control which users can view, edit, delete, and export records in your database tables. You can also set permissions at the field level to control which fields users can view and edit.</p>
            
            <p>This documentation will guide you through setting up and managing permissions for your database tables and fields.</p>
        </div>
    </div>
    
    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Table-Level Permissions</h2>
        </div>
        <div class="dab-card-body">
            <h3>Accessing Table Permissions</h3>
            <p>To access table permissions:</p>
            <ol>
                <li>Go to the Tables page in the Database App Builder menu.</li>
                <li>Click the "Edit" button for the table you want to manage permissions for.</li>
                <li>Click the "Permissions" tab in the table editor.</li>
            </ol>
            
            <h3>Available Permissions</h3>
            <p>For each WordPress user role, you can set the following permissions:</p>
            <ul>
                <li><strong>View Records</strong>: Users with this role can view records in the table.</li>
                <li><strong>Edit Records</strong>: Users with this role can edit records in the table.</li>
                <li><strong>Delete Records</strong>: Users with this role can delete records in the table.</li>
                <li><strong>Export Records</strong>: Users with this role can export records from the table.</li>
            </ul>
            
            <h3>Default Permissions</h3>
            <p>By default, only administrators have full access to all tables. Other roles have no permissions unless you explicitly grant them.</p>
            
            <h3>Record Ownership</h3>
            <p>Users can always view, edit, and delete records they created, regardless of their role permissions. This ensures that users can manage their own data even if they don't have general permissions for the table.</p>
        </div>
    </div>
    
    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Field-Level Permissions</h2>
        </div>
        <div class="dab-card-body">
            <h3>Accessing Field Permissions</h3>
            <p>To access field permissions:</p>
            <ol>
                <li>Go to the Fields page in the Database App Builder menu.</li>
                <li>Select the table containing the field you want to manage permissions for.</li>
                <li>Click the "Edit" button for the field.</li>
                <li>Click the "Permissions" tab in the field editor.</li>
            </ol>
            
            <h3>Available Permissions</h3>
            <p>For each WordPress user role, you can set the following permissions:</p>
            <ul>
                <li><strong>View Field</strong>: Users with this role can view this field in records.</li>
                <li><strong>Edit Field</strong>: Users with this role can edit this field in records.</li>
            </ul>
            
            <h3>Default Permissions</h3>
            <p>By default, field permissions inherit from table permissions. If a user has permission to view records in a table, they can view all fields in those records unless you explicitly restrict access to specific fields.</p>
            
            <h3>Field Ownership</h3>
            <p>Users can always view and edit fields in records they created, regardless of their field permissions. This ensures that users can manage their own data even if they don't have general permissions for the field.</p>
        </div>
    </div>
    
    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Best Practices</h2>
        </div>
        <div class="dab-card-body">
            <h3>Start with Least Privilege</h3>
            <p>When setting up permissions, start with the principle of least privilege: give users only the permissions they need to perform their tasks. You can always add more permissions later if needed.</p>
            
            <h3>Use Field-Level Permissions for Sensitive Data</h3>
            <p>Use field-level permissions to restrict access to sensitive data. For example, you might allow all users to view most fields in a record, but restrict access to fields containing personal or confidential information.</p>
            
            <h3>Test Permissions</h3>
            <p>After setting up permissions, test them by logging in as different users to ensure they have the appropriate access. You can use the Test Permissions tool (available to administrators) to check permissions for different users and roles.</p>
            
            <h3>Document Your Permissions</h3>
            <p>Document your permission settings for future reference. This will help you maintain consistency and troubleshoot issues if users report problems with access.</p>
        </div>
    </div>
    
    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Troubleshooting</h2>
        </div>
        <div class="dab-card-body">
            <h3>User Can't Access Records</h3>
            <p>If a user can't access records they should be able to see:</p>
            <ol>
                <li>Check if the user has the appropriate role with view permissions for the table.</li>
                <li>Check if the user is the owner of the records (created them).</li>
                <li>Check if there are any field-level permissions that might be restricting access.</li>
            </ol>
            
            <h3>User Can't Edit or Delete Records</h3>
            <p>If a user can't edit or delete records they should be able to modify:</p>
            <ol>
                <li>Check if the user has the appropriate role with edit or delete permissions for the table.</li>
                <li>Check if the user is the owner of the records (created them).</li>
                <li>For editing specific fields, check if there are field-level permissions restricting access.</li>
            </ol>
            
            <h3>Testing Permissions</h3>
            <p>Administrators can use the Test Permissions tool to check permissions for different users and roles. This can help identify permission issues without having to log in as different users.</p>
        </div>
    </div>
</div>
