<?php
/**
 * Forms Manager
 *
 * Handles form-related functionality and database structure.
 */
if (!defined('ABSPATH')) exit;

class DAB_Forms_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Check and update forms table structure
        add_action('init', array(__CLASS__, 'check_and_update_forms_table'));
    }

    /**
     * Check and update forms table structure
     */
    public static function check_and_update_forms_table() {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$forms_table'") === $forms_table;

        if (!$table_exists) {
            self::create_forms_table();
            return;
        }

        // Check for Google Sheets columns
        $google_sheets_enabled_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'google_sheets_enabled'");

        if (!$google_sheets_enabled_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN google_sheets_enabled TINYINT(1) DEFAULT 0");
            error_log("Added google_sheets_enabled column to $forms_table");
        }

        $google_sheets_spreadsheet_id_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'google_sheets_spreadsheet_id'");

        if (!$google_sheets_spreadsheet_id_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN google_sheets_spreadsheet_id VARCHAR(255) NULL");
            error_log("Added google_sheets_spreadsheet_id column to $forms_table");
        }

        $google_sheets_worksheet_name_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'google_sheets_worksheet_name'");

        if (!$google_sheets_worksheet_name_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN google_sheets_worksheet_name VARCHAR(255) NULL");
            error_log("Added google_sheets_worksheet_name column to $forms_table");
        }

        // Check for Zapier columns
        $zapier_enabled_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'zapier_enabled'");

        if (!$zapier_enabled_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN zapier_enabled TINYINT(1) DEFAULT 0");
            error_log("Added zapier_enabled column to $forms_table");
        }

        $zapier_webhooks_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'zapier_webhooks'");

        if (!$zapier_webhooks_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN zapier_webhooks LONGTEXT NULL");
            error_log("Added zapier_webhooks column to $forms_table");
        }

        // Check for other required columns
        $conditional_logic_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'conditional_logic'");

        if (!$conditional_logic_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN conditional_logic LONGTEXT NULL");
        }

        $notify_email_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'notify_email'");

        if (!$notify_email_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN notify_email VARCHAR(255) NULL");
        }

        $notify_message_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'notify_message'");

        if (!$notify_message_exists) {
            $wpdb->query("ALTER TABLE $forms_table ADD COLUMN notify_message TEXT NULL");
        }
    }

    /**
     * Create forms table
     */
    public static function create_forms_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $forms_table = $wpdb->prefix . 'dab_forms';

        $sql_forms = "CREATE TABLE IF NOT EXISTS $forms_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            form_name VARCHAR(255) NOT NULL,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            fields LONGTEXT NULL,
            conditional_logic LONGTEXT NULL,
            notify_email VARCHAR(255) NULL,
            notify_message TEXT NULL,
            google_sheets_enabled TINYINT(1) DEFAULT 0,
            google_sheets_spreadsheet_id VARCHAR(255) NULL,
            google_sheets_worksheet_name VARCHAR(255) NULL,
            zapier_enabled TINYINT(1) DEFAULT 0,
            zapier_webhooks LONGTEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        dbDelta($sql_forms);
    }

    /**
     * Get all forms
     *
     * @return array Forms
     */
    public static function get_forms() {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        return $wpdb->get_results("SELECT * FROM $forms_table ORDER BY id DESC");
    }

    /**
     * Get form by ID
     *
     * @param int $form_id Form ID
     * @return object|null Form object or null if not found
     */
    public static function get_form($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));
    }

    /**
     * Get forms by table ID
     *
     * @param int $table_id Table ID
     * @return array Forms
     */
    public static function get_forms_by_table($table_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        return $wpdb->get_results($wpdb->prepare("SELECT * FROM $forms_table WHERE table_id = %d ORDER BY id DESC", $table_id));
    }

    /**
     * Save form
     *
     * @param array $data Form data
     * @return int|false Form ID or false on failure
     */
    public static function save_form($data) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        // Make sure the forms table has all required columns
        self::check_and_update_forms_table();

        $form_data = array(
            'form_name' => sanitize_text_field($data['form_name']),
            'table_id' => (int) $data['table_id'],
            'fields' => isset($data['fields']) ? maybe_serialize($data['fields']) : null,
            'conditional_logic' => isset($data['conditional_logic']) ? $data['conditional_logic'] : '[]',
            'notify_email' => isset($data['notify_email']) ? sanitize_email($data['notify_email']) : '',
            'notify_message' => isset($data['notify_message']) ? sanitize_textarea_field($data['notify_message']) : ''
        );

        // Add Google Sheets data if columns exist
        $google_sheets_enabled_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'google_sheets_enabled'");

        if ($google_sheets_enabled_exists) {
            $form_data['google_sheets_enabled'] = isset($data['google_sheets_enabled']) ? (int) $data['google_sheets_enabled'] : 0;
            $form_data['google_sheets_spreadsheet_id'] = isset($data['google_sheets_spreadsheet_id']) ? sanitize_text_field($data['google_sheets_spreadsheet_id']) : '';
            $form_data['google_sheets_worksheet_name'] = isset($data['google_sheets_worksheet_name']) ? sanitize_text_field($data['google_sheets_worksheet_name']) : '';
        }

        // Add Zapier data if columns exist
        $zapier_enabled_exists = $wpdb->get_var("SHOW COLUMNS FROM $forms_table LIKE 'zapier_enabled'");

        if ($zapier_enabled_exists) {
            $form_data['zapier_enabled'] = isset($data['zapier_enabled']) ? (int) $data['zapier_enabled'] : 0;
            $form_data['zapier_webhooks'] = isset($data['zapier_webhooks']) ? maybe_serialize($data['zapier_webhooks']) : 'a:0:{}';
        }

        if (isset($data['id']) && $data['id'] > 0) {
            // Update
            $wpdb->update(
                $forms_table,
                $form_data,
                array('id' => (int) $data['id'])
            );
            return (int) $data['id'];
        } else {
            // Insert
            $form_data['created_at'] = current_time('mysql');
            $wpdb->insert($forms_table, $form_data);
            return $wpdb->insert_id;
        }
    }

    /**
     * Delete form
     *
     * @param int $form_id Form ID
     * @return bool Success
     */
    public static function delete_form($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        return $wpdb->delete($forms_table, array('id' => $form_id));
    }
}

// Initialize the class
add_action('init', array('DAB_Forms_Manager', 'init'));
