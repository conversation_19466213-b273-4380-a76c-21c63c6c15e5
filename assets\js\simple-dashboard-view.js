/**
 * Simple Dashboard View
 *
 * JavaScript for the dashboard view interface.
 */
(function($) {
    'use strict';

    // Chart instances
    const chartInstances = {};

    /**
     * Initialize the dashboard view
     */
    function initDashboardView() {
        console.log('Initializing dashboard view...');

        // Load widget data
        loadWidgetData();

        // Initialize widget actions
        initWidgetActions();
    }

    /**
     * Initialize widget actions
     */
    function initWidgetActions() {
        // Refresh widget
        $('.dab-widget-refresh').on('click', function() {
            const widget = $(this).closest('.dab-dashboard-widget');
            const widgetId = widget.data('widget-id');

            loadWidgetData(widgetId);
        });
    }

    /**
     * Load widget data
     *
     * @param {number} widgetId Optional widget ID to load data for a specific widget
     */
    function loadWidgetData(widgetId = null) {
        const widgets = widgetId ?
            $(`.dab-dashboard-widget[data-widget-id="${widgetId}"]`) :
            $('.dab-dashboard-widget');

        widgets.each(function() {
            const widget = $(this);
            const id = widget.data('widget-id');
            const type = widget.data('widget-type');

            // Show loading
            widget.find('.dab-widget-loading').show();
            widget.find('.dab-widget-data').empty();

            // Load data
            $.ajax({
                url: dab_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_widget_data',
                    widget_id: id,
                    nonce: dab_dashboard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderWidgetData(widget, type, response.data);
                    } else {
                        showWidgetError(widget, response.data || dab_dashboard.i18n.error);
                    }
                },
                error: function() {
                    showWidgetError(widget, dab_dashboard.i18n.error);
                },
                complete: function() {
                    widget.find('.dab-widget-loading').hide();
                }
            });
        });
    }

    /**
     * Render widget data
     *
     * @param {jQuery} widget Widget element
     * @param {string} type Widget type
     * @param {Object} data Widget data
     */
    function renderWidgetData(widget, type, data) {
        const widgetData = widget.find('.dab-widget-data');

        console.log('Rendering widget data:', type, data);

        // Check for error
        if (data && data.error) {
            showWidgetError(widget, data.error);
            return;
        }

        // Check if data is valid
        if (!data) {
            showWidgetError(widget, 'No data returned from server');
            return;
        }

        // Render based on widget type
        switch (type) {
            case 'table':
                renderTableWidget(widgetData, data);
                break;
            case 'chart':
                renderChartWidget(widget, widgetData, data);
                break;
            case 'metric':
                renderMetricWidget(widgetData, data);
                break;
            case 'text':
                renderTextWidget(widgetData, data);
                break;
            case 'kpi':
                renderKpiWidget(widgetData, data);
                break;
            case 'list':
                renderListWidget(widgetData, data);
                break;
            case 'progress':
                renderProgressWidget(widgetData, data);
                break;
            case 'image':
                renderImageWidget(widgetData, data);
                break;
            default:
                showWidgetError(widget, 'Unknown widget type: ' + type);
        }
    }

    /**
     * Render table widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderTableWidget(container, data) {
        console.log('Rendering table widget with data:', data);

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if headers and records exist
        if (!data.headers || !Array.isArray(data.headers) || !data.records || !Array.isArray(data.records)) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        // Check if there are any records
        if (data.records.length === 0) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        let tableHtml = '<div class="dab-widget-table-container"><table class="dab-widget-table">';

        // Headers
        tableHtml += '<thead><tr>';
        data.headers.forEach(header => {
            tableHtml += `<th>${header.label || header.slug || 'Unknown'}</th>`;
        });
        tableHtml += '</tr></thead>';

        // Records
        tableHtml += '<tbody>';
        data.records.forEach(record => {
            tableHtml += '<tr>';
            data.headers.forEach(header => {
                const slug = header.slug || '';
                const value = record[slug] !== undefined ? record[slug] : '';
                tableHtml += `<td>${value}</td>`;
            });
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody>';

        tableHtml += '</table></div>';

        container.html(tableHtml);
    }

    /**
     * Render chart widget
     *
     * @param {jQuery} widget Widget element
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderChartWidget(widget, container, data) {

        const widgetId = widget.data('widget-id');

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check for the format returned by get_chart_data
        if (data.labels && Array.isArray(data.labels) && data.values && Array.isArray(data.values)) {
            // This is the format from get_chart_data
            // Transform it to the format expected by Chart.js
            const chartType = widget.data('chart-type') || 'bar';
            const chartLabel = data.label || 'Value';

            // Create datasets from values
            const datasets = [{
                label: chartLabel,
                data: data.values,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }];

            // Create canvas
            container.html('<div class="dab-widget-chart-container"><canvas></canvas></div>');

            const canvas = container.find('canvas')[0];
            const ctx = canvas.getContext('2d');

            // Destroy existing chart
            if (chartInstances[widgetId]) {
                chartInstances[widgetId].destroy();
            }

            try {
                // Create chart
                chartInstances[widgetId] = new Chart(ctx, {
                    type: chartType,
                    data: {
                        labels: data.labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: false
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error creating chart:', error);
                container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}: ${error.message}</p>`);
            }
            return;
        }

        // Check if labels and datasets exist (original format)
        if (!data.labels || !Array.isArray(data.labels) || !data.datasets || !Array.isArray(data.datasets)) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        // Check if there are any labels
        if (data.labels.length === 0) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        // Create canvas
        container.html('<div class="dab-widget-chart-container"><canvas></canvas></div>');

        const canvas = container.find('canvas')[0];
        const ctx = canvas.getContext('2d');

        // Destroy existing chart
        if (chartInstances[widgetId]) {
            chartInstances[widgetId].destroy();
        }

        try {
            // Create chart
            chartInstances[widgetId] = new Chart(ctx, {
                type: data.chart_type || 'bar',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating chart:', error);
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}: ${error.message}</p>`);
        }
    }

    /**
     * Render metric widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderMetricWidget(container, data) {


        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if value exists
        if (data.value === undefined || data.value === null) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        let value = data.value;

        // Format value
        if (typeof value === 'number') {
            if (data.type === 'average') {
                value = parseFloat(value).toFixed(2);
            } else {
                value = parseInt(value).toLocaleString();
            }
        }

        const metricHtml = `
            <div class="dab-widget-metric">
                <div class="dab-widget-metric-value">${value}</div>
                <div class="dab-widget-metric-label">${data.label || ''}</div>
            </div>
        `;

        container.html(metricHtml);
    }

    /**
     * Render text widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderTextWidget(container, data) {


        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        container.html(`<div class="dab-widget-text">${data.content || ''}</div>`);
    }

    /**
     * Render KPI widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderKpiWidget(container, data) {
        console.log('Rendering KPI widget with data:', data);

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if value exists
        if (data.value === undefined || data.value === null) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        let value = data.value;

        // Format value
        if (typeof value === 'number') {
            if (data.type === 'average') {
                value = parseFloat(value).toFixed(2);
            } else {
                value = parseInt(value).toLocaleString();
            }
        }

        // Determine status class
        let statusClass = '';
        let statusIcon = '';

        switch (data.status) {
            case 'success':
                statusClass = 'dab-kpi-success';
                statusIcon = 'arrow-up-alt';
                break;
            case 'warning':
                statusClass = 'dab-kpi-warning';
                statusIcon = 'minus';
                break;
            case 'danger':
                statusClass = 'dab-kpi-danger';
                statusIcon = 'arrow-down-alt';
                break;
            default:
                statusClass = 'dab-kpi-neutral';
                break;
        }

        // Determine color class
        let colorClass = '';
        switch (data.color) {
            case 'primary':
                colorClass = 'dab-color-primary';
                break;
            case 'success':
                colorClass = 'dab-color-success';
                break;
            case 'warning':
                colorClass = 'dab-color-warning';
                break;
            case 'danger':
                colorClass = 'dab-color-danger';
                break;
            case 'info':
                colorClass = 'dab-color-info';
                break;
            default:
                colorClass = 'dab-color-primary';
                break;
        }

        // Build target HTML if target exists
        let targetHtml = '';
        if (data.target) {
            targetHtml = `
                <div class="dab-kpi-target">
                    <span class="dab-kpi-target-label">Target:</span>
                    <span class="dab-kpi-target-value">${data.target}</span>
                </div>
            `;
        }

        // Build icon HTML if icon exists
        let iconHtml = '';
        if (data.icon) {
            iconHtml = `<span class="dab-kpi-icon dashicons dashicons-${data.icon}"></span>`;
        }

        const kpiHtml = `
            <div class="dab-widget-kpi ${statusClass} ${colorClass}">
                <div class="dab-kpi-header">
                    ${iconHtml}
                    <div class="dab-kpi-status">
                        <span class="dashicons dashicons-${statusIcon}"></span>
                    </div>
                </div>
                <div class="dab-kpi-body">
                    <div class="dab-kpi-value">${value}</div>
                    <div class="dab-kpi-label">${data.label || ''}</div>
                    ${targetHtml}
                </div>
            </div>
        `;

        container.html(kpiHtml);
    }

    /**
     * Render list widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderListWidget(container, data) {
        console.log('Rendering list widget with data:', data);

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if items exist
        if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        let listHtml = '<div class="dab-widget-list">';

        // Add items
        data.items.forEach(item => {
            // Build icon HTML if icon exists
            let iconHtml = '';
            if (item.icon) {
                iconHtml = `<div class="dab-list-item-icon"><span class="dashicons dashicons-${item.icon}"></span></div>`;
            }

            // Build description HTML if description exists
            let descriptionHtml = '';
            if (item.description) {
                descriptionHtml = `<div class="dab-list-item-description">${item.description}</div>`;
            }

            listHtml += `
                <div class="dab-list-item" data-id="${item.id}">
                    ${iconHtml}
                    <div class="dab-list-item-content">
                        <div class="dab-list-item-title">${item.title}</div>
                        ${descriptionHtml}
                    </div>
                </div>
            `;
        });

        listHtml += '</div>';

        container.html(listHtml);
    }

    /**
     * Render progress widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderProgressWidget(container, data) {
        console.log('Rendering progress widget with data:', data);

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if values exist
        if (data.current_value === undefined || data.max_value === undefined) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        // Format values
        const currentValue = parseFloat(data.current_value).toLocaleString();
        const maxValue = parseFloat(data.max_value).toLocaleString();
        const percentage = data.percentage || 0;

        // Determine color class
        let colorClass = '';
        switch (data.color) {
            case 'primary':
                colorClass = 'dab-progress-primary';
                break;
            case 'success':
                colorClass = 'dab-progress-success';
                break;
            case 'warning':
                colorClass = 'dab-progress-warning';
                break;
            case 'danger':
                colorClass = 'dab-progress-danger';
                break;
            case 'info':
                colorClass = 'dab-progress-info';
                break;
            default:
                colorClass = 'dab-progress-primary';
                break;
        }

        const progressHtml = `
            <div class="dab-widget-progress">
                ${data.label ? `<div class="dab-progress-label">${data.label}</div>` : ''}
                <div class="dab-progress-container">
                    <div class="dab-progress-bar ${colorClass}" style="width: ${percentage}%">
                        <span class="dab-progress-text">${percentage}%</span>
                    </div>
                </div>
                <div class="dab-progress-stats">
                    <span class="dab-progress-current">${currentValue}</span>
                    <span class="dab-progress-separator">/</span>
                    <span class="dab-progress-max">${maxValue}</span>
                </div>
            </div>
        `;

        container.html(progressHtml);
    }

    /**
     * Render image widget
     *
     * @param {jQuery} container Widget data container
     * @param {Object} data Widget data
     */
    function renderImageWidget(container, data) {
        console.log('Rendering image widget with data:', data);

        // Check if data is valid
        if (!data) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
            return;
        }

        // Check if image URL exists
        if (!data.image_url) {
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
            return;
        }

        const imageHtml = `
            <div class="dab-widget-image">
                <img src="${data.image_url}" alt="${data.alt_text || ''}" class="dab-image">
                ${data.caption ? `<div class="dab-image-caption">${data.caption}</div>` : ''}
            </div>
        `;

        container.html(imageHtml);
    }

    /**
     * Show widget error
     *
     * @param {jQuery} widget Widget element
     * @param {string} error Error message
     */
    function showWidgetError(widget, error) {
        widget.find('.dab-widget-data').html(`
            <div class="dab-widget-error">
                <p>${error}</p>
            </div>
        `);
    }

    // Initialize dashboard view when document is ready
    $(document).ready(function() {
        if ($('.dab-dashboard-view-container').length) {
            initDashboardView();
        }
    });
})(jQuery);
