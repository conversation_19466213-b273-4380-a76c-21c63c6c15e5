/**
 * Social Media Field Styles
 * 
 * Styles for social media fields in the Database App Builder plugin.
 */

/* Social Media Field Container */
.dab-social-media-field {
    margin-bottom: 15px;
    max-width: 100%;
}

/* Social Link Row */
.dab-social-link-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
    flex-wrap: wrap;
}

/* Social Icon */
.dab-social-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f1;
    border-radius: 50%;
    color: #555;
    font-size: 1.2em;
}

.dab-social-icon i {
    transition: color 0.3s;
}

/* Platform-specific icon colors */
.dab-social-icon i.fa-facebook {
    color: #1877f2;
}

.dab-social-icon i.fa-twitter {
    color: #1da1f2;
}

.dab-social-icon i.fa-instagram {
    color: #e1306c;
}

.dab-social-icon i.fa-linkedin {
    color: #0077b5;
}

.dab-social-icon i.fa-youtube {
    color: #ff0000;
}

.dab-social-icon i.fa-pinterest {
    color: #bd081c;
}

.dab-social-icon i.fa-tiktok {
    color: #000000;
}

.dab-social-icon i.fa-reddit {
    color: #ff4500;
}

.dab-social-icon i.fa-tumblr {
    color: #35465c;
}

.dab-social-icon i.fa-github {
    color: #333333;
}

/* Platform Wrapper */
.dab-social-platform-wrapper {
    flex: 1;
    min-width: 150px;
}

.dab-social-platform {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-social-platform:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-custom-platform {
    width: 100%;
    margin-top: 5px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-custom-platform:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* URL Wrapper */
.dab-social-url-wrapper {
    flex: 2;
    min-width: 200px;
}

.dab-social-url {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-social-url:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-social-url.dab-invalid-url {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

/* Actions */
.dab-social-actions {
    display: flex;
    align-items: center;
}

.dab-remove-social-link {
    background-color: transparent !important;
    border: none !important;
    color: #dc3545 !important;
    padding: 0 !important;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color 0.3s;
}

.dab-remove-social-link:hover {
    color: #bd2130 !important;
}

.dab-remove-social-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.dab-social-media-actions {
    margin-top: 10px;
}

.dab-add-social-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background-color: #f0f0f1 !important;
    border: 1px solid #ddd !important;
    color: #555 !important;
    transition: background-color 0.3s, color 0.3s;
}

.dab-add-social-link:hover {
    background-color: #e0e0e0 !important;
    color: #333 !important;
}

.dab-add-social-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Admin-specific styles */
.wp-admin .dab-social-media-field {
    max-width: 800px;
}

/* Frontend form-specific styles */
.dab-form .dab-social-media-field {
    margin-bottom: 20px;
}

.dab-form .dab-social-platform,
.dab-form .dab-custom-platform,
.dab-form .dab-social-url {
    padding: 10px 15px;
    border-radius: 5px;
}

/* Error state */
.dab-form .dab-social-media-field.has-error .dab-social-platform,
.dab-form .dab-social-media-field.has-error .dab-custom-platform,
.dab-form .dab-social-media-field.has-error .dab-social-url {
    border-color: #dc3545;
}

/* Disabled state */
.dab-social-media-field.disabled .dab-social-platform,
.dab-social-media-field.disabled .dab-custom-platform,
.dab-social-media-field.disabled .dab-social-url {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-social-link-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dab-social-platform-wrapper,
    .dab-social-url-wrapper {
        width: 100%;
    }
    
    .dab-social-icon {
        margin-bottom: 5px;
    }
    
    .dab-social-actions {
        position: absolute;
        right: 0;
        top: 0;
    }
    
    .dab-social-link-row {
        position: relative;
        padding-right: 40px;
    }
}

/* Social Links Display */
.dab-social-links-display {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;
}

.dab-social-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background-color: #f0f0f1;
    border-radius: 4px;
    color: #555;
    text-decoration: none;
    transition: background-color 0.3s, color 0.3s;
}

.dab-social-link:hover {
    background-color: #e0e0e0;
    color: #333;
    text-decoration: none;
}

/* Platform-specific link colors */
.dab-social-link-facebook {
    background-color: rgba(24, 119, 242, 0.1);
}

.dab-social-link-facebook:hover {
    background-color: rgba(24, 119, 242, 0.2);
}

.dab-social-link-twitter {
    background-color: rgba(29, 161, 242, 0.1);
}

.dab-social-link-twitter:hover {
    background-color: rgba(29, 161, 242, 0.2);
}

.dab-social-link-instagram {
    background-color: rgba(225, 48, 108, 0.1);
}

.dab-social-link-instagram:hover {
    background-color: rgba(225, 48, 108, 0.2);
}

.dab-social-link-linkedin {
    background-color: rgba(0, 119, 181, 0.1);
}

.dab-social-link-linkedin:hover {
    background-color: rgba(0, 119, 181, 0.2);
}
