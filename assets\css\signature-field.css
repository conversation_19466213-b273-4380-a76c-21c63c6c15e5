/**
 * Signature Field Styles
 * 
 * Styles for signature fields in the Database App Builder plugin.
 */

/* Signature Field Container */
.dab-signature-field {
    margin-bottom: 15px;
    max-width: 100%;
}

/* Name Input */
.dab-signature-name-wrapper {
    margin-bottom: 10px;
}

.dab-signature-name-wrapper label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.dab-signature-name {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-signature-name:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Canvas Wrapper */
.dab-signature-canvas-wrapper {
    position: relative;
    margin-bottom: 10px;
}

/* Signature Canvas */
.dab-signature-canvas {
    display: block;
    touch-action: none;
    cursor: crosshair;
    max-width: 100%;
    height: auto;
}

/* Signature Actions */
.dab-signature-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.dab-clear-signature {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background-color: #f0f0f1 !important;
    border: 1px solid #ddd !important;
    color: #555 !important;
    transition: background-color 0.3s, color 0.3s;
}

.dab-clear-signature:hover {
    background-color: #e0e0e0 !important;
    color: #333 !important;
}

.dab-clear-signature .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Signature Preview */
.dab-signature-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-signature-preview img {
    max-width: 100%;
    height: auto;
    display: block;
    margin-bottom: 5px;
}

.dab-signature-timestamp {
    font-size: 0.85em;
    color: #666;
    margin-top: 5px;
}

/* Admin-specific styles */
.wp-admin .dab-signature-field {
    max-width: 800px;
}

/* Frontend form-specific styles */
.dab-form .dab-signature-field {
    margin-bottom: 20px;
}

.dab-form .dab-signature-name {
    padding: 10px 15px;
    border-radius: 5px;
}

/* Error state */
.dab-form .dab-signature-field.has-error .dab-signature-name {
    border-color: #dc3545;
}

.dab-form .dab-signature-field.has-error .dab-signature-canvas {
    border-color: #dc3545 !important;
}

/* Disabled state */
.dab-signature-field.disabled .dab-signature-name {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.dab-signature-field.disabled .dab-signature-canvas {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Signature Display */
.dab-signature-display {
    margin: 10px 0;
}

.dab-signature-display .dab-signature-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin-bottom: 5px;
    border: 1px solid #ddd;
    background-color: #fff;
}

.dab-signature-display .dab-signature-name {
    font-weight: 500;
    margin-top: 5px;
}

.dab-signature-display .dab-signature-timestamp {
    font-size: 0.85em;
    color: #666;
    margin-top: 3px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-signature-canvas {
        width: 100% !important;
        height: auto !important;
    }
}
