/**
 * Conditional Logic JavaScript
 * 
 * Handles conditional field visibility and form logic
 */

(function($) {
    'use strict';

    // Conditional Logic Object
    window.DABConditionalLogic = {
        rules: [],
        
        init: function() {
            this.bindEvents();
            this.loadRules();
            this.evaluateAllRules();
        },

        bindEvents: function() {
            // Monitor form field changes
            $(document).on('change', 'input, select, textarea', this.onFieldChange.bind(this));
            
            // Monitor checkbox and radio changes
            $(document).on('click', 'input[type="checkbox"], input[type="radio"]', this.onFieldChange.bind(this));
            
            // Monitor text input changes with debounce
            $(document).on('input', 'input[type="text"], input[type="email"], input[type="number"], textarea', 
                this.debounce(this.onFieldChange.bind(this), 300));
        },

        onFieldChange: function(e) {
            const changedField = $(e.target);
            const fieldName = changedField.attr('name') || changedField.attr('id');
            
            if (fieldName) {
                this.evaluateRulesForField(fieldName);
            }
        },

        loadRules: function() {
            // Try to load rules from global variable or data attributes
            if (typeof dabConditionalRules !== 'undefined') {
                this.rules = dabConditionalRules;
            } else {
                // Look for rules in data attributes
                this.loadRulesFromDOM();
            }
        },

        loadRulesFromDOM: function() {
            const rulesElements = $('[data-conditional-rules]');
            
            rulesElements.each((index, element) => {
                try {
                    const rulesData = $(element).data('conditional-rules');
                    if (rulesData) {
                        this.rules = this.rules.concat(rulesData);
                    }
                } catch (e) {
                    console.warn('DAB Conditional Logic: Error parsing rules from DOM', e);
                }
            });
        },

        evaluateAllRules: function() {
            this.rules.forEach(rule => {
                this.evaluateRule(rule);
            });
        },

        evaluateRulesForField: function(fieldName) {
            const relevantRules = this.rules.filter(rule => 
                rule.conditions.some(condition => condition.field === fieldName)
            );
            
            relevantRules.forEach(rule => {
                this.evaluateRule(rule);
            });
        },

        evaluateRule: function(rule) {
            if (!rule || !rule.conditions || !rule.actions) {
                return;
            }

            const conditionsMet = this.evaluateConditions(rule.conditions, rule.logic || 'AND');
            
            rule.actions.forEach(action => {
                this.executeAction(action, conditionsMet);
            });
        },

        evaluateConditions: function(conditions, logic) {
            if (!conditions || conditions.length === 0) {
                return true;
            }

            const results = conditions.map(condition => this.evaluateCondition(condition));
            
            if (logic === 'OR') {
                return results.some(result => result);
            } else {
                return results.every(result => result);
            }
        },

        evaluateCondition: function(condition) {
            const field = this.getFieldElement(condition.field);
            if (!field || field.length === 0) {
                return false;
            }

            const fieldValue = this.getFieldValue(field);
            const conditionValue = condition.value;
            const operator = condition.operator || 'equals';

            return this.compareValues(fieldValue, conditionValue, operator);
        },

        getFieldElement: function(fieldName) {
            // Try multiple selectors to find the field
            let field = $(`[name="${fieldName}"]`);
            
            if (field.length === 0) {
                field = $(`#${fieldName}`);
            }
            
            if (field.length === 0) {
                field = $(`.field-${fieldName}`);
            }
            
            return field;
        },

        getFieldValue: function(field) {
            const fieldType = field.attr('type');
            const tagName = field.prop('tagName').toLowerCase();
            
            if (fieldType === 'checkbox') {
                if (field.length > 1) {
                    // Multiple checkboxes with same name
                    const values = [];
                    field.filter(':checked').each(function() {
                        values.push($(this).val());
                    });
                    return values;
                } else {
                    return field.is(':checked') ? field.val() : '';
                }
            } else if (fieldType === 'radio') {
                return field.filter(':checked').val() || '';
            } else if (tagName === 'select') {
                if (field.prop('multiple')) {
                    return field.val() || [];
                } else {
                    return field.val() || '';
                }
            } else {
                return field.val() || '';
            }
        },

        compareValues: function(fieldValue, conditionValue, operator) {
            // Convert values to strings for comparison
            const fieldStr = Array.isArray(fieldValue) ? fieldValue.join(',') : String(fieldValue);
            const conditionStr = String(conditionValue);
            
            switch (operator) {
                case 'equals':
                case '=':
                case '==':
                    return fieldStr === conditionStr;
                    
                case 'not_equals':
                case '!=':
                case '<>':
                    return fieldStr !== conditionStr;
                    
                case 'contains':
                    return fieldStr.toLowerCase().includes(conditionStr.toLowerCase());
                    
                case 'not_contains':
                    return !fieldStr.toLowerCase().includes(conditionStr.toLowerCase());
                    
                case 'starts_with':
                    return fieldStr.toLowerCase().startsWith(conditionStr.toLowerCase());
                    
                case 'ends_with':
                    return fieldStr.toLowerCase().endsWith(conditionStr.toLowerCase());
                    
                case 'greater_than':
                case '>':
                    return parseFloat(fieldStr) > parseFloat(conditionStr);
                    
                case 'less_than':
                case '<':
                    return parseFloat(fieldStr) < parseFloat(conditionStr);
                    
                case 'greater_than_or_equal':
                case '>=':
                    return parseFloat(fieldStr) >= parseFloat(conditionStr);
                    
                case 'less_than_or_equal':
                case '<=':
                    return parseFloat(fieldStr) <= parseFloat(conditionStr);
                    
                case 'is_empty':
                    return fieldStr === '' || fieldStr === null || fieldStr === undefined;
                    
                case 'is_not_empty':
                    return fieldStr !== '' && fieldStr !== null && fieldStr !== undefined;
                    
                case 'in':
                    const inValues = conditionStr.split(',').map(v => v.trim());
                    return inValues.includes(fieldStr);
                    
                case 'not_in':
                    const notInValues = conditionStr.split(',').map(v => v.trim());
                    return !notInValues.includes(fieldStr);
                    
                default:
                    return fieldStr === conditionStr;
            }
        },

        executeAction: function(action, conditionsMet) {
            const targetElement = this.getTargetElement(action.target);
            
            if (!targetElement || targetElement.length === 0) {
                return;
            }

            switch (action.type) {
                case 'show':
                    if (conditionsMet) {
                        this.showElement(targetElement);
                    } else {
                        this.hideElement(targetElement);
                    }
                    break;
                    
                case 'hide':
                    if (conditionsMet) {
                        this.hideElement(targetElement);
                    } else {
                        this.showElement(targetElement);
                    }
                    break;
                    
                case 'enable':
                    if (conditionsMet) {
                        this.enableElement(targetElement);
                    } else {
                        this.disableElement(targetElement);
                    }
                    break;
                    
                case 'disable':
                    if (conditionsMet) {
                        this.disableElement(targetElement);
                    } else {
                        this.enableElement(targetElement);
                    }
                    break;
                    
                case 'set_value':
                    if (conditionsMet) {
                        this.setElementValue(targetElement, action.value);
                    }
                    break;
                    
                case 'add_class':
                    if (conditionsMet) {
                        targetElement.addClass(action.value);
                    } else {
                        targetElement.removeClass(action.value);
                    }
                    break;
                    
                case 'remove_class':
                    if (conditionsMet) {
                        targetElement.removeClass(action.value);
                    } else {
                        targetElement.addClass(action.value);
                    }
                    break;
            }
        },

        getTargetElement: function(target) {
            // Try multiple selectors to find the target
            let element = $(`[name="${target}"]`);
            
            if (element.length === 0) {
                element = $(`#${target}`);
            }
            
            if (element.length === 0) {
                element = $(`.field-${target}`);
            }
            
            if (element.length === 0) {
                element = $(target);
            }
            
            // If it's a form field, get the field container
            if (element.is('input, select, textarea')) {
                const container = element.closest('.form-group, .field-group, .dab-field-group');
                if (container.length > 0) {
                    return container;
                }
            }
            
            return element;
        },

        showElement: function(element) {
            element.show().removeClass('dab-hidden');
            element.find('input, select, textarea').prop('disabled', false);
        },

        hideElement: function(element) {
            element.hide().addClass('dab-hidden');
            element.find('input, select, textarea').prop('disabled', true);
        },

        enableElement: function(element) {
            element.find('input, select, textarea, button').prop('disabled', false);
            element.removeClass('dab-disabled');
        },

        disableElement: function(element) {
            element.find('input, select, textarea, button').prop('disabled', true);
            element.addClass('dab-disabled');
        },

        setElementValue: function(element, value) {
            const field = element.is('input, select, textarea') ? element : element.find('input, select, textarea').first();
            
            if (field.length > 0) {
                field.val(value).trigger('change');
            }
        },

        // Utility function for debouncing
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Public method to add rules dynamically
        addRule: function(rule) {
            this.rules.push(rule);
            this.evaluateRule(rule);
        },

        // Public method to remove rules
        removeRule: function(ruleId) {
            this.rules = this.rules.filter(rule => rule.id !== ruleId);
        },

        // Public method to re-evaluate all rules
        refresh: function() {
            this.evaluateAllRules();
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        window.DABConditionalLogic.init();
    });

    // Also initialize on dynamic content load
    $(document).on('dab:content-loaded', function() {
        window.DABConditionalLogic.refresh();
    });

})(jQuery);
