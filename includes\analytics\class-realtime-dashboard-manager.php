<?php
/**
 * Real-time Analytics Dashboard Manager
 * Phase 3: Data Intelligence & Analytics
 *
 * Manages real-time data streaming, live dashboard updates,
 * and interactive analytics dashboards with drill-down capabilities.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Realtime_Dashboard_Manager {

    /**
     * Initialize the Real-time Dashboard Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_create_realtime_dashboard', array(__CLASS__, 'create_dashboard'));
        add_action('wp_ajax_dab_update_realtime_dashboard', array(__CLASS__, 'update_dashboard'));
        add_action('wp_ajax_dab_get_realtime_data', array(__CLASS__, 'get_realtime_data'));
        add_action('wp_ajax_dab_add_dashboard_widget', array(__CLASS__, 'add_widget'));
        add_action('wp_ajax_dab_update_widget_config', array(__CLASS__, 'update_widget_config'));
        add_action('wp_ajax_dab_remove_dashboard_widget', array(__CLASS__, 'remove_widget'));
        add_action('wp_ajax_dab_get_widget_data', array(__CLASS__, 'get_widget_data'));
        add_action('wp_ajax_dab_export_dashboard', array(__CLASS__, 'export_dashboard'));
        add_action('wp_ajax_dab_share_dashboard', array(__CLASS__, 'share_dashboard'));

        // Frontend AJAX for public dashboards
        add_action('wp_ajax_nopriv_dab_get_realtime_data', array(__CLASS__, 'get_realtime_data'));
        add_action('wp_ajax_nopriv_dab_get_widget_data', array(__CLASS__, 'get_widget_data'));

        // WebSocket support for real-time updates with class existence check
        add_action('wp_footer', function() {
            if (class_exists('DAB_Realtime_Dashboard_Manager') && method_exists('DAB_Realtime_Dashboard_Manager', 'add_realtime_scripts')) {
                DAB_Realtime_Dashboard_Manager::add_realtime_scripts();
            }
        });
        add_action('admin_footer', function() {
            if (class_exists('DAB_Realtime_Dashboard_Manager') && method_exists('DAB_Realtime_Dashboard_Manager', 'add_realtime_scripts')) {
                DAB_Realtime_Dashboard_Manager::add_realtime_scripts();
            }
        });

        // Data change hooks for real-time updates
        add_action('dab_data_inserted', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
        add_action('dab_data_updated', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
        add_action('dab_data_deleted', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
    }

    /**
     * Create database tables for real-time dashboards
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Real-time dashboards table
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';
        $sql = "CREATE TABLE IF NOT EXISTS $dashboards_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            layout_config LONGTEXT,
            theme_config TEXT,
            refresh_interval INT DEFAULT 30,
            auto_refresh TINYINT(1) DEFAULT 1,
            is_public TINYINT(1) DEFAULT 0,
            public_key VARCHAR(32),
            access_permissions TEXT,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_accessed DATETIME NULL,
            access_count INT DEFAULT 0,
            PRIMARY KEY (id),
            UNIQUE KEY unique_public_key (public_key),
            KEY idx_created_by (created_by),
            KEY idx_is_public (is_public)
        ) $charset_collate;";

        // Dashboard widgets table
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        $sql .= "CREATE TABLE IF NOT EXISTS $widgets_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            dashboard_id BIGINT(20) UNSIGNED NOT NULL,
            widget_type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            position_x INT DEFAULT 0,
            position_y INT DEFAULT 0,
            width INT DEFAULT 4,
            height INT DEFAULT 3,
            data_source VARCHAR(100),
            query_config LONGTEXT,
            visualization_config LONGTEXT,
            filter_config TEXT,
            refresh_interval INT DEFAULT 30,
            is_active TINYINT(1) DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_dashboard_id (dashboard_id),
            KEY idx_widget_type (widget_type),
            KEY idx_is_active (is_active),
            FOREIGN KEY (dashboard_id) REFERENCES $dashboards_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Real-time data cache table
        $cache_table = $wpdb->prefix . 'dab_realtime_cache';
        $sql .= "CREATE TABLE IF NOT EXISTS $cache_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            cache_key VARCHAR(255) NOT NULL,
            data_source VARCHAR(100) NOT NULL,
            cached_data LONGTEXT,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_cache_key (cache_key),
            KEY idx_data_source (data_source),
            KEY idx_expires_at (expires_at)
        ) $charset_collate;";

        // Dashboard alerts table
        $alerts_table = $wpdb->prefix . 'dab_dashboard_alerts';
        $sql .= "CREATE TABLE IF NOT EXISTS $alerts_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            dashboard_id BIGINT(20) UNSIGNED NOT NULL,
            widget_id BIGINT(20) UNSIGNED,
            alert_type VARCHAR(50) NOT NULL,
            condition_config TEXT,
            notification_config TEXT,
            is_active TINYINT(1) DEFAULT 1,
            last_triggered DATETIME NULL,
            trigger_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_dashboard_id (dashboard_id),
            KEY idx_widget_id (widget_id),
            KEY idx_is_active (is_active),
            FOREIGN KEY (dashboard_id) REFERENCES $dashboards_table(id) ON DELETE CASCADE,
            FOREIGN KEY (widget_id) REFERENCES $widgets_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create a new real-time dashboard
     */
    public static function create_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';

        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $layout_config = $_POST['layout_config'];
        $theme_config = $_POST['theme_config'];
        $refresh_interval = intval($_POST['refresh_interval']);
        $auto_refresh = intval($_POST['auto_refresh']);
        $is_public = intval($_POST['is_public']);
        $access_permissions = $_POST['access_permissions'];

        $public_key = $is_public ? wp_generate_password(32, false) : null;

        $data = array(
            'name' => $name,
            'description' => $description,
            'layout_config' => json_encode($layout_config),
            'theme_config' => json_encode($theme_config),
            'refresh_interval' => $refresh_interval,
            'auto_refresh' => $auto_refresh,
            'is_public' => $is_public,
            'public_key' => $public_key,
            'access_permissions' => json_encode($access_permissions),
            'created_by' => get_current_user_id()
        );

        $result = $wpdb->insert($dashboards_table, $data);

        if ($result !== false) {
            $dashboard_id = $wpdb->insert_id;

            wp_send_json_success(array(
                'message' => 'Dashboard created successfully',
                'dashboard_id' => $dashboard_id,
                'public_key' => $public_key
            ));
        } else {
            wp_send_json_error('Failed to create dashboard');
        }
    }

    /**
     * Update dashboard configuration
     */
    public static function update_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';

        $dashboard_id = intval($_POST['dashboard_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $layout_config = $_POST['layout_config'];
        $theme_config = $_POST['theme_config'];
        $refresh_interval = intval($_POST['refresh_interval']);
        $auto_refresh = intval($_POST['auto_refresh']);
        $is_public = intval($_POST['is_public']);
        $access_permissions = $_POST['access_permissions'];

        // Generate public key if making dashboard public
        $public_key = null;
        if ($is_public) {
            $existing_key = $wpdb->get_var($wpdb->prepare(
                "SELECT public_key FROM $dashboards_table WHERE id = %d",
                $dashboard_id
            ));
            $public_key = $existing_key ?: wp_generate_password(32, false);
        }

        $data = array(
            'name' => $name,
            'description' => $description,
            'layout_config' => json_encode($layout_config),
            'theme_config' => json_encode($theme_config),
            'refresh_interval' => $refresh_interval,
            'auto_refresh' => $auto_refresh,
            'is_public' => $is_public,
            'public_key' => $public_key,
            'access_permissions' => json_encode($access_permissions)
        );

        $result = $wpdb->update($dashboards_table, $data, array('id' => $dashboard_id));

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Dashboard updated successfully',
                'public_key' => $public_key
            ));
        } else {
            wp_send_json_error('Failed to update dashboard');
        }
    }

    /**
     * Get real-time data for dashboard
     */
    public static function get_realtime_data() {
        $dashboard_id = intval($_POST['dashboard_id']);
        $widget_ids = isset($_POST['widget_ids']) ? array_map('intval', $_POST['widget_ids']) : array();
        $public_key = isset($_POST['public_key']) ? sanitize_text_field($_POST['public_key']) : '';

        // Verify access permissions
        if (!self::check_dashboard_access($dashboard_id, $public_key)) {
            wp_send_json_error('Access denied');
            return;
        }

        try {
            $data = array();

            if (empty($widget_ids)) {
                // Get all widgets for dashboard
                $widget_ids = self::get_dashboard_widget_ids($dashboard_id);
            }

            foreach ($widget_ids as $widget_id) {
                $widget_data = self::get_widget_realtime_data($widget_id);
                if ($widget_data) {
                    $data[$widget_id] = $widget_data;
                }
            }

            // Update dashboard access tracking
            self::update_dashboard_access($dashboard_id);

            wp_send_json_success($data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to get real-time data: ' . $e->getMessage());
        }
    }

    /**
     * Add a widget to dashboard
     */
    public static function add_widget() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        $dashboard_id = intval($_POST['dashboard_id']);
        $widget_type = sanitize_text_field($_POST['widget_type']);
        $title = sanitize_text_field($_POST['title']);
        $position_x = intval($_POST['position_x']);
        $position_y = intval($_POST['position_y']);
        $width = intval($_POST['width']);
        $height = intval($_POST['height']);
        $data_source = sanitize_text_field($_POST['data_source']);
        $query_config = $_POST['query_config'];
        $visualization_config = $_POST['visualization_config'];
        $filter_config = $_POST['filter_config'];
        $refresh_interval = intval($_POST['refresh_interval']);

        $data = array(
            'dashboard_id' => $dashboard_id,
            'widget_type' => $widget_type,
            'title' => $title,
            'position_x' => $position_x,
            'position_y' => $position_y,
            'width' => $width,
            'height' => $height,
            'data_source' => $data_source,
            'query_config' => json_encode($query_config),
            'visualization_config' => json_encode($visualization_config),
            'filter_config' => json_encode($filter_config),
            'refresh_interval' => $refresh_interval
        );

        $result = $wpdb->insert($widgets_table, $data);

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Widget added successfully',
                'widget_id' => $wpdb->insert_id
            ));
        } else {
            wp_send_json_error('Failed to add widget');
        }
    }

    /**
     * Update widget configuration
     */
    public static function update_widget_config() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_realtime_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_realtime_widgets';

        $widget_id = intval($_POST['widget_id']);
        $title = sanitize_text_field($_POST['title']);
        $position_x = intval($_POST['position_x']);
        $position_y = intval($_POST['position_y']);
        $width = intval($_POST['width']);
        $height = intval($_POST['height']);
        $query_config = $_POST['query_config'];
        $visualization_config = $_POST['visualization_config'];
        $filter_config = $_POST['filter_config'];
        $refresh_interval = intval($_POST['refresh_interval']);

        $data = array(
            'title' => $title,
            'position_x' => $position_x,
            'position_y' => $position_y,
            'width' => $width,
            'height' => $height,
            'query_config' => json_encode($query_config),
            'visualization_config' => json_encode($visualization_config),
            'filter_config' => json_encode($filter_config),
            'refresh_interval' => $refresh_interval
        );

        $result = $wpdb->update($widgets_table, $data, array('id' => $widget_id));

        if ($result !== false) {
            wp_send_json_success('Widget updated successfully');
        } else {
            wp_send_json_error('Failed to update widget');
        }
    }

    /**
     * Remove dashboard widget
     */
    public static function remove_widget() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_realtime_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_realtime_widgets';

        $widget_id = intval($_POST['widget_id']);

        $result = $wpdb->delete($widgets_table, array('id' => $widget_id));

        if ($result !== false) {
            wp_send_json_success('Widget removed successfully');
        } else {
            wp_send_json_error('Failed to remove widget');
        }
    }

    /**
     * Get widget data
     */
    public static function get_widget_data() {
        $widget_id = intval($_POST['widget_id']);
        $public_key = isset($_POST['public_key']) ? sanitize_text_field($_POST['public_key']) : '';

        try {
            $widget_data = self::get_widget_realtime_data($widget_id);

            if ($widget_data) {
                wp_send_json_success($widget_data);
            } else {
                wp_send_json_error('Widget not found or no data available');
            }
        } catch (Exception $e) {
            wp_send_json_error('Failed to get widget data: ' . $e->getMessage());
        }
    }

    /**
     * Export dashboard
     */
    public static function export_dashboard() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_realtime_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        $dashboard_id = intval($_POST['dashboard_id']);

        try {
            $dashboard_data = self::get_dashboard_export_data($dashboard_id);

            if ($dashboard_data) {
                wp_send_json_success($dashboard_data);
            } else {
                wp_send_json_error('Dashboard not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Failed to export dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Share dashboard
     */
    public static function share_dashboard() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_realtime_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';

        $dashboard_id = intval($_POST['dashboard_id']);
        $is_public = intval($_POST['is_public']);

        $data = array('is_public' => $is_public);

        if ($is_public && empty($_POST['public_key'])) {
            $data['public_key'] = wp_generate_password(32, false);
        } elseif (!$is_public) {
            $data['public_key'] = null;
        }

        $result = $wpdb->update($dashboards_table, $data, array('id' => $dashboard_id));

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Dashboard sharing updated successfully',
                'public_key' => $data['public_key']
            ));
        } else {
            wp_send_json_error('Failed to update dashboard sharing');
        }
    }

    /**
     * Add real-time scripts to footer
     */
    public static function add_realtime_scripts() {
        // Only add scripts on pages that need real-time functionality
        if (!self::should_load_realtime_scripts()) {
            return;
        }

        ?>
        <script type="text/javascript">
        (function($) {
            'use strict';

            // Real-time dashboard functionality
            window.DABRealtimeDashboard = {
                intervals: {},
                websocket: null,

                init: function() {
                    this.setupWebSocket();
                    this.startAutoRefresh();
                },

                setupWebSocket: function() {
                    // WebSocket implementation for real-time updates
                    // This would connect to a WebSocket server for live data streaming
                    if (typeof WebSocket !== 'undefined') {
                        try {
                            // Example WebSocket connection (would need actual WebSocket server)
                            // this.websocket = new WebSocket('ws://localhost:8080/realtime');
                            // this.websocket.onmessage = this.handleWebSocketMessage.bind(this);
                        } catch (e) {
                            console.log('WebSocket not available, falling back to polling');
                        }
                    }
                },

                startAutoRefresh: function() {
                    var self = this;
                    $('.dab-realtime-widget').each(function() {
                        var $widget = $(this);
                        var widgetId = $widget.data('widget-id');
                        var refreshInterval = $widget.data('refresh-interval') || 30;

                        // Set up auto-refresh for each widget
                        self.intervals[widgetId] = setInterval(function() {
                            self.refreshWidget(widgetId);
                        }, refreshInterval * 1000);
                    });
                },

                refreshWidget: function(widgetId) {
                    var $widget = $('.dab-realtime-widget[data-widget-id="' + widgetId + '"]');

                    $.ajax({
                        url: ajaxurl || '<?php echo admin_url('admin-ajax.php'); ?>',
                        type: 'POST',
                        data: {
                            action: 'dab_get_widget_data',
                            widget_id: widgetId,
                            nonce: '<?php echo wp_create_nonce('dab_realtime_dashboard_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                self.updateWidgetData($widget, response.data);
                            }
                        },
                        error: function() {
                            console.log('Failed to refresh widget ' + widgetId);
                        }
                    });
                },

                updateWidgetData: function($widget, data) {
                    // Update widget with new data
                    var $content = $widget.find('.widget-content');
                    if ($content.length) {
                        // Trigger custom event for widget update
                        $widget.trigger('dab:widget:updated', [data]);
                    }
                },

                handleWebSocketMessage: function(event) {
                    try {
                        var data = JSON.parse(event.data);
                        if (data.type === 'widget_update') {
                            this.refreshWidget(data.widget_id);
                        }
                    } catch (e) {
                        console.log('Invalid WebSocket message');
                    }
                },

                destroy: function() {
                    // Clean up intervals
                    for (var widgetId in this.intervals) {
                        clearInterval(this.intervals[widgetId]);
                    }

                    // Close WebSocket connection
                    if (this.websocket) {
                        this.websocket.close();
                    }
                }
            };

            // Initialize when document is ready
            $(document).ready(function() {
                if ($('.dab-realtime-widget').length > 0) {
                    DABRealtimeDashboard.init();
                }
            });

            // Clean up on page unload
            $(window).on('beforeunload', function() {
                DABRealtimeDashboard.destroy();
            });

        })(jQuery);
        </script>
        <?php
    }

    /**
     * Check if real-time scripts should be loaded
     */
    private static function should_load_realtime_scripts() {
        global $pagenow;

        // Load on admin pages that use real-time dashboards
        if (is_admin()) {
            $allowed_pages = array(
                'admin.php',
                'index.php' // WordPress dashboard
            );

            if (in_array($pagenow, $allowed_pages)) {
                return true;
            }

            // Check if current page is a DAB dashboard page
            if (isset($_GET['page'])) {
                $page = $_GET['page'] !== null ? (string)$_GET['page'] : '';
                if ($page !== '' && strpos($page, 'dab') !== false) {
                    return true;
                }
            }
        }

        // Load on frontend pages with real-time dashboard shortcodes
        if (!is_admin()) {
            global $post;
            if ($post && (
                has_shortcode($post->post_content, 'dab_realtime_dashboard') ||
                has_shortcode($post->post_content, 'dab_dashboard_widget')
            )) {
                return true;
            }
        }

        return false;
    }

    /**
     * Trigger real-time update
     */
    public static function trigger_realtime_update($table_id, $record_id, $action) {
        // This would trigger WebSocket notifications or other real-time update mechanisms
        // For now, we'll just log the event
        error_log("DAB Real-time update: {$action} on table {$table_id}, record {$record_id}");

        // In a full implementation, this would:
        // 1. Identify which dashboards/widgets are affected by this data change
        // 2. Send WebSocket notifications to connected clients
        // 3. Update cached data if applicable
    }

    /**
     * Helper methods for dashboard functionality
     */

    /**
     * Check dashboard access permissions
     */
    private static function check_dashboard_access($dashboard_id, $public_key = '') {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';

        $dashboard = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $dashboards_table WHERE id = %d",
            $dashboard_id
        ));

        if (!$dashboard) {
            return false;
        }

        // Check if dashboard is public and public key matches
        if ($dashboard->is_public && !empty($public_key)) {
            return $dashboard->public_key === $public_key;
        }

        // Check if user has permission to access dashboard
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();

            // Dashboard creator always has access
            if ($dashboard->created_by == $user_id) {
                return true;
            }

            // Check access permissions
            $access_permissions = json_decode($dashboard->access_permissions, true);
            if (is_array($access_permissions)) {
                // Check user roles
                $user = wp_get_current_user();
                $user_roles = $user->roles;

                if (isset($access_permissions['roles']) && is_array($access_permissions['roles'])) {
                    foreach ($user_roles as $role) {
                        if (in_array($role, $access_permissions['roles'])) {
                            return true;
                        }
                    }
                }

                // Check specific users
                if (isset($access_permissions['users']) && is_array($access_permissions['users'])) {
                    if (in_array($user_id, $access_permissions['users'])) {
                        return true;
                    }
                }
            }

            // Check if user has manage_options capability
            if (current_user_can('manage_options')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get dashboard widget IDs
     */
    private static function get_dashboard_widget_ids($dashboard_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_realtime_widgets';

        $widget_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT id FROM $widgets_table WHERE dashboard_id = %d ORDER BY position_y, position_x",
            $dashboard_id
        ));

        return array_map('intval', $widget_ids);
    }

    /**
     * Get widget real-time data
     */
    private static function get_widget_realtime_data($widget_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_realtime_widgets';

        $widget = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $widgets_table WHERE id = %d",
            $widget_id
        ));

        if (!$widget) {
            return false;
        }

        $query_config = json_decode($widget->query_config, true);
        $data_source = $widget->data_source;

        // Execute query based on data source and configuration
        try {
            switch ($widget->widget_type) {
                case 'chart':
                    return self::get_chart_data($data_source, $query_config);
                case 'table':
                    return self::get_table_data($data_source, $query_config);
                case 'metric':
                    return self::get_metric_data($data_source, $query_config);
                case 'gauge':
                    return self::get_gauge_data($data_source, $query_config);
                default:
                    return array('error' => 'Unknown widget type');
            }
        } catch (Exception $e) {
            return array('error' => $e->getMessage());
        }
    }

    /**
     * Update dashboard access tracking
     */
    private static function update_dashboard_access($dashboard_id) {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';

        // Update last accessed time
        $wpdb->update(
            $dashboards_table,
            array('last_accessed' => current_time('mysql')),
            array('id' => $dashboard_id),
            array('%s'),
            array('%d')
        );

        // Increment access count using raw SQL
        $wpdb->query($wpdb->prepare(
            "UPDATE $dashboards_table SET access_count = access_count + 1 WHERE id = %d",
            $dashboard_id
        ));
    }

    /**
     * Get dashboard export data
     */
    private static function get_dashboard_export_data($dashboard_id) {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_realtime_widgets';

        $dashboard = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $dashboards_table WHERE id = %d",
            $dashboard_id
        ));

        if (!$dashboard) {
            return false;
        }

        $widgets = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $widgets_table WHERE dashboard_id = %d ORDER BY position_y, position_x",
            $dashboard_id
        ));

        return array(
            'dashboard' => $dashboard,
            'widgets' => $widgets,
            'export_date' => current_time('mysql'),
            'version' => DAB_VERSION
        );
    }

    /**
     * Data retrieval methods for different widget types
     */

    private static function get_chart_data($data_source, $query_config) {
        // Implementation for chart data retrieval
        return array('type' => 'chart', 'data' => array());
    }

    private static function get_table_data($data_source, $query_config) {
        // Implementation for table data retrieval
        return array('type' => 'table', 'data' => array());
    }

    private static function get_metric_data($data_source, $query_config) {
        // Implementation for metric data retrieval
        return array('type' => 'metric', 'value' => 0);
    }

    private static function get_gauge_data($data_source, $query_config) {
        // Implementation for gauge data retrieval
        return array('type' => 'gauge', 'value' => 0, 'max' => 100);
    }
}
