<?php
/**
 * PayPal Payment Gateway
 *
 * This class handles PayPal payment processing for the Database App Builder plugin.
 */
if (!defined('ABSPATH')) exit;

class DAB_PayPal_Gateway {

    /**
     * Gateway ID
     */
    private $id = 'paypal';

    /**
     * Client ID
     */
    private $client_id = '';

    /**
     * Client Secret
     */
    private $client_secret = '';

    /**
     * Test Mode
     */
    private $test_mode = true;

    /**
     * Constructor
     */
    public function __construct() {
        // Load settings
        $this->client_id = DAB_Settings_Manager::get('paypal_client_id', '');
        $this->client_secret = DAB_Settings_Manager::get('paypal_client_secret', '');
        $this->test_mode = DAB_Settings_Manager::get('paypal_test_mode', 'yes') === 'yes';
        
        // If test mode is enabled, use sandbox credentials
        if ($this->test_mode) {
            $this->client_id = DAB_Settings_Manager::get('paypal_sandbox_client_id', '');
            $this->client_secret = DAB_Settings_Manager::get('paypal_sandbox_client_secret', '');
        }
        
        // Register AJAX handlers
        add_action('wp_ajax_dab_paypal_create_order', array($this, 'ajax_create_order'));
        add_action('wp_ajax_nopriv_dab_paypal_create_order', array($this, 'ajax_create_order'));
        
        add_action('wp_ajax_dab_paypal_capture_order', array($this, 'ajax_capture_order'));
        add_action('wp_ajax_nopriv_dab_paypal_capture_order', array($this, 'ajax_capture_order'));
    }

    /**
     * Get gateway title
     */
    public function get_title() {
        return __('PayPal', 'db-app-builder');
    }

    /**
     * Get gateway description
     */
    public function get_description() {
        return __('Accept payments via PayPal.', 'db-app-builder');
    }

    /**
     * Check if gateway is configured
     */
    public function is_configured() {
        return !empty($this->client_id) && !empty($this->client_secret);
    }

    /**
     * Get settings fields
     */
    public function get_settings_fields() {
        return array(
            array(
                'name' => 'paypal_test_mode',
                'label' => __('Test Mode', 'db-app-builder'),
                'type' => 'select',
                'options' => array(
                    'yes' => __('Yes', 'db-app-builder'),
                    'no' => __('No', 'db-app-builder'),
                ),
                'default' => 'yes',
                'description' => __('Enable test mode to use PayPal sandbox.', 'db-app-builder'),
            ),
            array(
                'name' => 'paypal_sandbox_client_id',
                'label' => __('Sandbox Client ID', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your PayPal sandbox client ID.', 'db-app-builder'),
            ),
            array(
                'name' => 'paypal_sandbox_client_secret',
                'label' => __('Sandbox Client Secret', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your PayPal sandbox client secret.', 'db-app-builder'),
            ),
            array(
                'name' => 'paypal_client_id',
                'label' => __('Live Client ID', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your PayPal live client ID.', 'db-app-builder'),
            ),
            array(
                'name' => 'paypal_client_secret',
                'label' => __('Live Client Secret', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your PayPal live client secret.', 'db-app-builder'),
            ),
            array(
                'name' => 'paypal_webhook_id',
                'label' => __('Webhook ID', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your PayPal webhook ID for payment status updates.', 'db-app-builder'),
            ),
        );
    }

    /**
     * Render payment form
     */
    public function render_payment_form($field, $options) {
        // Check if PayPal is configured
        if (!$this->is_configured()) {
            return '<div class="dab-payment-error">' . __('PayPal is not properly configured. Please contact the administrator.', 'db-app-builder') . '</div>';
        }
        
        // Get amount and currency
        $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';
        $amount = isset($options['payment_amount']) ? floatval($options['payment_amount']) : 0;
        $currency = isset($options['payment_currency']) ? $options['payment_currency'] : 'USD';
        
        // Generate a unique ID for this payment form
        $form_id = 'paypal-form-' . uniqid();
        
        // Start output buffer
        ob_start();
        
        ?>
        <div class="dab-paypal-form" id="<?php echo esc_attr($form_id); ?>">
            <div class="dab-paypal-button-container"></div>
            <div class="dab-paypal-errors" role="alert"></div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Wait for PayPal SDK to load
            var paypalInterval = setInterval(function() {
                if (typeof paypal !== 'undefined') {
                    clearInterval(paypalInterval);
                    initPayPal();
                }
            }, 100);
            
            function initPayPal() {
                paypal.Buttons({
                    style: {
                        layout: 'vertical',
                        color: 'blue',
                        shape: 'rect',
                        label: 'pay'
                    },
                    createOrder: function(data, actions) {
                        return $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'dab_paypal_create_order',
                                amount: <?php echo esc_js($amount); ?>,
                                currency: '<?php echo esc_js($currency); ?>',
                                description: '<?php echo esc_js(isset($options['payment_description']) ? $options['payment_description'] : ''); ?>',
                                nonce: '<?php echo wp_create_nonce('dab_paypal_nonce'); ?>'
                            }
                        }).then(function(response) {
                            if (response.success && response.data.order_id) {
                                return response.data.order_id;
                            } else {
                                var errorMsg = response.data.message || '<?php _e('Error creating PayPal order', 'db-app-builder'); ?>';
                                $('#<?php echo esc_js($form_id); ?> .dab-paypal-errors').text(errorMsg);
                                return null;
                            }
                        });
                    },
                    onApprove: function(data, actions) {
                        // Show processing message
                        $('#<?php echo esc_js($form_id); ?> .dab-paypal-errors').text('<?php _e('Processing payment...', 'db-app-builder'); ?>');
                        
                        // Capture the order
                        return $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'dab_paypal_capture_order',
                                order_id: data.orderID,
                                nonce: '<?php echo wp_create_nonce('dab_paypal_nonce'); ?>'
                            }
                        }).then(function(response) {
                            if (response.success) {
                                // Payment succeeded
                                $('#<?php echo esc_js($form_id); ?> .dab-paypal-errors').text('<?php _e('Payment successful!', 'db-app-builder'); ?>').removeClass('error').addClass('success');
                                
                                // Update payment status
                                $('#<?php echo esc_js($field->field_slug); ?>').val(data.orderID);
                                
                                // Notify the form that payment is complete
                                $(document).trigger('dab_payment_complete', [data.orderID]);
                                
                                return true;
                            } else {
                                // Show error
                                var errorMsg = response.data.message || '<?php _e('Error processing payment', 'db-app-builder'); ?>';
                                $('#<?php echo esc_js($form_id); ?> .dab-paypal-errors').text(errorMsg).addClass('error');
                                return false;
                            }
                        });
                    },
                    onError: function(err) {
                        $('#<?php echo esc_js($form_id); ?> .dab-paypal-errors').text('<?php _e('PayPal error', 'db-app-builder'); ?>: ' + err).addClass('error');
                    }
                }).render('#<?php echo esc_js($form_id); ?> .dab-paypal-button-container');
            }
        });
        </script>
        <?php
        
        return ob_get_clean();
    }

    /**
     * AJAX handler to create a PayPal order
     */
    public function ajax_create_order() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_paypal_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }
        
        // Check if PayPal is configured
        if (!$this->is_configured()) {
            wp_send_json_error(array('message' => __('PayPal is not properly configured', 'db-app-builder')));
        }
        
        // Get amount and currency
        $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
        $currency = isset($_POST['currency']) ? sanitize_text_field($_POST['currency']) : 'USD';
        $description = isset($_POST['description']) ? sanitize_text_field($_POST['description']) : '';
        
        // Validate amount
        if ($amount <= 0) {
            wp_send_json_error(array('message' => __('Invalid payment amount', 'db-app-builder')));
        }
        
        try {
            // Get access token
            $token = $this->get_access_token();
            
            if (!$token) {
                wp_send_json_error(array('message' => __('Failed to authenticate with PayPal', 'db-app-builder')));
            }
            
            // API endpoint
            $api_url = $this->test_mode ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
            
            // Create order
            $response = wp_remote_post($api_url . '/v2/checkout/orders', array(
                'method' => 'POST',
                'timeout' => 45,
                'redirection' => 5,
                'httpversion' => '1.1',
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $token,
                ),
                'body' => json_encode(array(
                    'intent' => 'CAPTURE',
                    'purchase_units' => array(
                        array(
                            'amount' => array(
                                'currency_code' => $currency,
                                'value' => number_format($amount, 2, '.', ''),
                            ),
                            'description' => $description,
                        ),
                    ),
                    'application_context' => array(
                        'brand_name' => get_bloginfo('name'),
                        'user_action' => 'PAY_NOW',
                        'return_url' => home_url(),
                        'cancel_url' => home_url(),
                    ),
                )),
            ));
            
            if (is_wp_error($response)) {
                wp_send_json_error(array('message' => $response->get_error_message()));
            }
            
            $body = json_decode(wp_remote_retrieve_body($response), true);
            
            if (isset($body['id'])) {
                wp_send_json_success(array('order_id' => $body['id']));
            } else {
                wp_send_json_error(array('message' => __('Failed to create PayPal order', 'db-app-builder')));
            }
        } catch (\Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * AJAX handler to capture a PayPal order
     */
    public function ajax_capture_order() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_paypal_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }
        
        // Check if PayPal is configured
        if (!$this->is_configured()) {
            wp_send_json_error(array('message' => __('PayPal is not properly configured', 'db-app-builder')));
        }
        
        // Get order ID
        $order_id = isset($_POST['order_id']) ? sanitize_text_field($_POST['order_id']) : '';
        
        if (empty($order_id)) {
            wp_send_json_error(array('message' => __('Invalid order ID', 'db-app-builder')));
        }
        
        try {
            // Get access token
            $token = $this->get_access_token();
            
            if (!$token) {
                wp_send_json_error(array('message' => __('Failed to authenticate with PayPal', 'db-app-builder')));
            }
            
            // API endpoint
            $api_url = $this->test_mode ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
            
            // Capture order
            $response = wp_remote_post($api_url . '/v2/checkout/orders/' . $order_id . '/capture', array(
                'method' => 'POST',
                'timeout' => 45,
                'redirection' => 5,
                'httpversion' => '1.1',
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $token,
                ),
                'body' => json_encode(array()),
            ));
            
            if (is_wp_error($response)) {
                wp_send_json_error(array('message' => $response->get_error_message()));
            }
            
            $body = json_decode(wp_remote_retrieve_body($response), true);
            
            if (isset($body['status']) && $body['status'] === 'COMPLETED') {
                wp_send_json_success(array('transaction_id' => $body['id']));
            } else {
                wp_send_json_error(array('message' => __('Failed to capture PayPal payment', 'db-app-builder')));
            }
        } catch (\Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Get PayPal access token
     */
    private function get_access_token() {
        // API endpoint
        $api_url = $this->test_mode ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
        
        // Request token
        $response = wp_remote_post($api_url . '/v1/oauth2/token', array(
            'method' => 'POST',
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers' => array(
                'Accept' => 'application/json',
                'Accept-Language' => 'en_US',
                'Authorization' => 'Basic ' . base64_encode($this->client_id . ':' . $this->client_secret),
            ),
            'body' => 'grant_type=client_credentials',
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($body['access_token'])) {
            return $body['access_token'];
        }
        
        return false;
    }

    /**
     * Process webhook
     */
    public function process_webhook() {
        // Implementation will be added
    }
}
