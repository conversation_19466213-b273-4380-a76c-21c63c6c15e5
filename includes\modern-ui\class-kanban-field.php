<?php
/**
 * Kanban Board Field Type
 * 
 * Interactive Kanban boards for project management and workflow visualization
 * 
 * @package Database App Builder
 * @subpackage Modern UI Components
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Kanban_Field {
    
    /**
     * Initialize the Kanban field
     */
    public static function init() {
        add_filter('dab_field_types', array(__CLASS__, 'register_kanban_field_type'));
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_save_kanban_data', array(__CLASS__, 'save_kanban_data'));
        add_action('wp_ajax_nopriv_dab_save_kanban_data', array(__CLASS__, 'save_kanban_data'));
        add_action('wp_ajax_dab_get_kanban_data', array(__CLASS__, 'get_kanban_data'));
        add_action('wp_ajax_nopriv_dab_get_kanban_data', array(__CLASS__, 'get_kanban_data'));
        
        // Create database tables
        add_action('init', array(__CLASS__, 'create_tables'));
    }
    
    /**
     * Register the Kanban field type
     */
    public static function register_kanban_field_type($field_types) {
        $field_types['kanban_board'] = __('Kanban Board', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'dab-kanban-board',
            plugin_dir_url(__FILE__) . '../../assets/css/kanban-board.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'dab-kanban-board',
            plugin_dir_url(__FILE__) . '../../assets/js/kanban-board.js',
            array('jquery', 'jquery-ui-sortable'),
            '1.0.0',
            true
        );
        
        wp_localize_script('dab-kanban-board', 'dabKanbanData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_kanban_nonce'),
            'i18n' => array(
                'addCard' => __('Add Card', 'db-app-builder'),
                'editCard' => __('Edit Card', 'db-app-builder'),
                'deleteCard' => __('Delete Card', 'db-app-builder'),
                'addColumn' => __('Add Column', 'db-app-builder'),
                'editColumn' => __('Edit Column', 'db-app-builder'),
                'deleteColumn' => __('Delete Column', 'db-app-builder'),
                'confirmDelete' => __('Are you sure you want to delete this item?', 'db-app-builder'),
                'saveSuccess' => __('Kanban board saved successfully', 'db-app-builder'),
                'saveError' => __('Error saving kanban board', 'db-app-builder')
            )
        ));
    }
    
    /**
     * Create database tables for Kanban data
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Kanban boards table
        $kanban_boards_table = $wpdb->prefix . 'dab_kanban_boards';
        $sql_boards = "CREATE TABLE $kanban_boards_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            field_id bigint(20) NOT NULL,
            record_id bigint(20) NOT NULL,
            board_config longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY field_id (field_id),
            KEY record_id (record_id)
        ) $charset_collate;";
        
        // Kanban columns table
        $kanban_columns_table = $wpdb->prefix . 'dab_kanban_columns';
        $sql_columns = "CREATE TABLE $kanban_columns_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            board_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            position int(11) DEFAULT 0,
            color varchar(7) DEFAULT '#3498db',
            limit_cards int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY board_id (board_id)
        ) $charset_collate;";
        
        // Kanban cards table
        $kanban_cards_table = $wpdb->prefix . 'dab_kanban_cards';
        $sql_cards = "CREATE TABLE $kanban_cards_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            column_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            position int(11) DEFAULT 0,
            priority varchar(20) DEFAULT 'medium',
            assigned_to bigint(20) DEFAULT 0,
            due_date datetime NULL,
            labels text,
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY column_id (column_id),
            KEY assigned_to (assigned_to),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_boards);
        dbDelta($sql_columns);
        dbDelta($sql_cards);
    }
    
    /**
     * Render the Kanban field in forms
     */
    public static function render_field($field, $value = '', $record_id = 0) {
        $field_id = 'dab-kanban-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        
        // Get existing board data
        $board_data = self::get_board_data($field->id, $record_id);
        
        echo '<div class="dab-kanban-container" id="' . $field_id . '" data-field-id="' . $field->id . '" data-record-id="' . $record_id . '">';
        
        // Kanban header with controls
        echo '<div class="dab-kanban-header">';
        echo '<div class="dab-kanban-title">' . esc_html($field->field_label) . '</div>';
        echo '<div class="dab-kanban-controls">';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-add-column" title="' . __('Add Column', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-plus-alt"></span> ' . __('Add Column', 'db-app-builder');
        echo '</button>';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-kanban-settings" title="' . __('Settings', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-admin-generic"></span>';
        echo '</button>';
        echo '</div>';
        echo '</div>';
        
        // Kanban board
        echo '<div class="dab-kanban-board" id="kanban-board-' . $field->id . '">';
        
        if (!empty($board_data['columns'])) {
            foreach ($board_data['columns'] as $column) {
                self::render_kanban_column($column);
            }
        } else {
            // Default columns for new boards
            self::render_default_columns();
        }
        
        echo '</div>';
        
        // Hidden input to store board data
        echo '<input type="hidden" name="' . $field_name . '" id="' . $field_name . '" value="' . esc_attr(json_encode($board_data)) . '">';
        
        echo '</div>';
        
        // Add modal for card editing
        self::render_card_modal();
    }
    
    /**
     * Render a Kanban column
     */
    private static function render_kanban_column($column) {
        $column_id = isset($column['id']) ? $column['id'] : 0;
        $title = isset($column['title']) ? $column['title'] : '';
        $color = isset($column['color']) ? $column['color'] : '#3498db';
        $cards = isset($column['cards']) ? $column['cards'] : array();
        
        echo '<div class="dab-kanban-column" data-column-id="' . $column_id . '" style="border-top: 3px solid ' . esc_attr($color) . '">';
        
        // Column header
        echo '<div class="dab-kanban-column-header">';
        echo '<h4 class="dab-column-title" contenteditable="true">' . esc_html($title) . '</h4>';
        echo '<div class="dab-column-actions">';
        echo '<button type="button" class="dab-btn-icon dab-add-card" title="' . __('Add Card', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-plus-alt"></span>';
        echo '</button>';
        echo '<button type="button" class="dab-btn-icon dab-column-menu" title="' . __('Column Menu', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-menu"></span>';
        echo '</button>';
        echo '</div>';
        echo '</div>';
        
        // Cards container
        echo '<div class="dab-kanban-cards" data-column-id="' . $column_id . '">';
        
        foreach ($cards as $card) {
            self::render_kanban_card($card);
        }
        
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render a Kanban card
     */
    private static function render_kanban_card($card) {
        $card_id = isset($card['id']) ? $card['id'] : 0;
        $title = isset($card['title']) ? $card['title'] : '';
        $description = isset($card['description']) ? $card['description'] : '';
        $priority = isset($card['priority']) ? $card['priority'] : 'medium';
        $labels = isset($card['labels']) ? $card['labels'] : array();
        $assigned_to = isset($card['assigned_to']) ? $card['assigned_to'] : 0;
        $due_date = isset($card['due_date']) ? $card['due_date'] : '';
        
        $priority_class = 'dab-priority-' . $priority;
        
        echo '<div class="dab-kanban-card ' . $priority_class . '" data-card-id="' . $card_id . '">';
        
        // Card header
        if (!empty($labels)) {
            echo '<div class="dab-card-labels">';
            foreach ($labels as $label) {
                echo '<span class="dab-card-label" style="background-color: ' . esc_attr($label['color']) . '">' . esc_html($label['name']) . '</span>';
            }
            echo '</div>';
        }
        
        // Card title
        echo '<h5 class="dab-card-title">' . esc_html($title) . '</h5>';
        
        // Card description (truncated)
        if (!empty($description)) {
            $truncated = wp_trim_words($description, 15, '...');
            echo '<p class="dab-card-description">' . esc_html($truncated) . '</p>';
        }
        
        // Card footer
        echo '<div class="dab-card-footer">';
        
        // Due date
        if (!empty($due_date)) {
            $due_class = strtotime($due_date) < time() ? 'dab-overdue' : '';
            echo '<span class="dab-card-due-date ' . $due_class . '">';
            echo '<span class="dashicons dashicons-calendar-alt"></span>';
            echo date('M j', strtotime($due_date));
            echo '</span>';
        }
        
        // Assigned user
        if (!empty($assigned_to)) {
            $user = get_user_by('id', $assigned_to);
            if ($user) {
                echo '<span class="dab-card-assignee" title="' . esc_attr($user->display_name) . '">';
                echo get_avatar($user->ID, 24);
                echo '</span>';
            }
        }
        
        // Priority indicator
        echo '<span class="dab-card-priority dab-priority-' . $priority . '" title="' . ucfirst($priority) . ' Priority"></span>';
        
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render default columns for new boards
     */
    private static function render_default_columns() {
        $default_columns = array(
            array('id' => 1, 'title' => __('To Do', 'db-app-builder'), 'color' => '#e74c3c', 'cards' => array()),
            array('id' => 2, 'title' => __('In Progress', 'db-app-builder'), 'color' => '#f39c12', 'cards' => array()),
            array('id' => 3, 'title' => __('Done', 'db-app-builder'), 'color' => '#27ae60', 'cards' => array())
        );
        
        foreach ($default_columns as $column) {
            self::render_kanban_column($column);
        }
    }
    
    /**
     * Render card editing modal
     */
    private static function render_card_modal() {
        echo '<div id="dab-card-modal" class="dab-modal" style="display: none;">';
        echo '<div class="dab-modal-content">';
        echo '<div class="dab-modal-header">';
        echo '<h3 id="dab-card-modal-title">' . __('Edit Card', 'db-app-builder') . '</h3>';
        echo '<button type="button" class="dab-modal-close">&times;</button>';
        echo '</div>';
        echo '<div class="dab-modal-body">';
        
        // Card form fields
        echo '<div class="dab-form-field">';
        echo '<label for="dab-card-title">' . __('Title', 'db-app-builder') . '</label>';
        echo '<input type="text" id="dab-card-title" class="dab-form-control" required>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-card-description">' . __('Description', 'db-app-builder') . '</label>';
        echo '<textarea id="dab-card-description" class="dab-form-control" rows="4"></textarea>';
        echo '</div>';
        
        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-card-priority">' . __('Priority', 'db-app-builder') . '</label>';
        echo '<select id="dab-card-priority" class="dab-form-control">';
        echo '<option value="low">' . __('Low', 'db-app-builder') . '</option>';
        echo '<option value="medium" selected>' . __('Medium', 'db-app-builder') . '</option>';
        echo '<option value="high">' . __('High', 'db-app-builder') . '</option>';
        echo '</select>';
        echo '</div>';
        
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-card-due-date">' . __('Due Date', 'db-app-builder') . '</label>';
        echo '<input type="date" id="dab-card-due-date" class="dab-form-control">';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="dab-form-field">';
        echo '<label for="dab-card-assigned">' . __('Assigned To', 'db-app-builder') . '</label>';
        echo '<select id="dab-card-assigned" class="dab-form-control">';
        echo '<option value="">' . __('Unassigned', 'db-app-builder') . '</option>';
        
        // Get WordPress users
        $users = get_users(array('fields' => array('ID', 'display_name')));
        foreach ($users as $user) {
            echo '<option value="' . $user->ID . '">' . esc_html($user->display_name) . '</option>';
        }
        
        echo '</select>';
        echo '</div>';
        
        echo '</div>';
        echo '<div class="dab-modal-footer">';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-modal-close">' . __('Cancel', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-danger dab-delete-card" style="display: none;">' . __('Delete', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-save-card">' . __('Save', 'db-app-builder') . '</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Get board data for a field and record
     */
    private static function get_board_data($field_id, $record_id) {
        global $wpdb;
        
        $boards_table = $wpdb->prefix . 'dab_kanban_boards';
        $columns_table = $wpdb->prefix . 'dab_kanban_columns';
        $cards_table = $wpdb->prefix . 'dab_kanban_cards';
        
        // Get board
        $board = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $boards_table WHERE field_id = %d AND record_id = %d",
            $field_id, $record_id
        ));
        
        if (!$board) {
            return array('columns' => array());
        }
        
        // Get columns
        $columns = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $columns_table WHERE board_id = %d ORDER BY position ASC",
            $board->id
        ));
        
        $board_data = array('columns' => array());
        
        foreach ($columns as $column) {
            // Get cards for this column
            $cards = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $cards_table WHERE column_id = %d ORDER BY position ASC",
                $column->id
            ));
            
            $column_data = array(
                'id' => $column->id,
                'title' => $column->title,
                'color' => $column->color,
                'position' => $column->position,
                'cards' => array()
            );
            
            foreach ($cards as $card) {
                $card_data = array(
                    'id' => $card->id,
                    'title' => $card->title,
                    'description' => $card->description,
                    'priority' => $card->priority,
                    'assigned_to' => $card->assigned_to,
                    'due_date' => $card->due_date,
                    'labels' => !empty($card->labels) ? json_decode($card->labels, true) : array(),
                    'position' => $card->position
                );
                
                $column_data['cards'][] = $card_data;
            }
            
            $board_data['columns'][] = $column_data;
        }
        
        return $board_data;
    }
    
    /**
     * AJAX handler to save Kanban data
     */
    public static function save_kanban_data() {
        check_ajax_referer('dab_kanban_nonce', 'nonce');
        
        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        $board_data = $_POST['board_data'];
        
        if (!$field_id) {
            wp_send_json_error(__('Invalid field ID', 'db-app-builder'));
        }
        
        global $wpdb;
        
        try {
            $wpdb->query('START TRANSACTION');
            
            // Save board data logic here
            // This is a simplified version - full implementation would handle
            // creating/updating boards, columns, and cards
            
            $wpdb->query('COMMIT');
            
            wp_send_json_success(__('Kanban board saved successfully', 'db-app-builder'));
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            wp_send_json_error(__('Error saving kanban board', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to get Kanban data
     */
    public static function get_kanban_data() {
        check_ajax_referer('dab_kanban_nonce', 'nonce');
        
        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        
        $board_data = self::get_board_data($field_id, $record_id);
        
        wp_send_json_success($board_data);
    }
}
