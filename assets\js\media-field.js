/**
 * Media Field Scripts
 *
 * JavaScript for audio/video media fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all media fields
    function initMediaFields() {
        $('.dab-media-field').each(function() {
            var $field = $(this);
            
            // Skip if already initialized
            if ($field.data('initialized')) {
                return;
            }
            
            // Mark as initialized
            $field.data('initialized', true);
            
            var $value = $field.find('input[type="hidden"]').first();
            var $tabs = $field.find('.dab-media-tab');
            var $tabPanes = $field.find('.dab-media-tab-pane');
            var $uploadInput = $field.find('.dab-media-upload');
            var $embedInput = $field.find('.dab-media-embed-url');
            var $embedPreviewBtn = $field.find('.dab-media-embed-preview');
            var $embedPreviewContainer = $field.find('.dab-media-embed-preview-container');
            var $recordStartBtn = $field.find('.dab-media-record-start');
            var $recordStopBtn = $field.find('.dab-media-record-stop');
            var $recorderPreview = $field.find('.dab-media-recorder-preview');
            var $mediaPreview = $field.find('.dab-media-preview');
            
            var fieldId = $field.attr('id');
            var mediaType = $field.data('media-type') || 'both';
            var allowRecording = $field.data('allow-recording') || false;
            var allowUpload = $field.data('allow-upload') || true;
            var allowEmbed = $field.data('allow-embed') || false;
            var maxFileSize = $field.data('max-file-size') || 10; // MB
            var allowedFormats = ($field.data('allowed-formats') || 'mp3,mp4').split(',');
            var playerWidth = $field.data('player-width') || 400;
            var playerHeight = $field.data('player-height') || 300;
            
            // Media recorder variables
            var mediaRecorder = null;
            var recordedChunks = [];
            var stream = null;
            
            // Tab switching
            $tabs.on('click', function() {
                var $tab = $(this);
                var tabId = $tab.data('tab');
                
                // Activate tab
                $tabs.removeClass('active');
                $tab.addClass('active');
                
                // Show tab pane
                $tabPanes.removeClass('active');
                $field.find('[data-tab-pane="' + tabId + '"]').addClass('active');
            });
            
            // File upload handling
            $uploadInput.on('change', function() {
                var file = this.files[0];
                if (!file) return;
                
                // Validate file size
                if (file.size > maxFileSize * 1024 * 1024) {
                    alert(dabMediaField.i18n.fileTooBig);
                    this.value = '';
                    return;
                }
                
                // Validate file type
                var fileType = file.type;
                var isValidType = false;
                
                if (mediaType === 'audio' || mediaType === 'both') {
                    if (fileType.indexOf('audio/') === 0) {
                        isValidType = true;
                    }
                }
                
                if (mediaType === 'video' || mediaType === 'both') {
                    if (fileType.indexOf('video/') === 0) {
                        isValidType = true;
                    }
                }
                
                if (!isValidType) {
                    alert(dabMediaField.i18n.invalidFileType);
                    this.value = '';
                    return;
                }
                
                // Upload the file
                uploadFile(file);
            });
            
            // Embed URL handling
            $embedPreviewBtn.on('click', function() {
                var url = $embedInput.val().trim();
                if (!url) return;
                
                // Validate URL
                if (!isValidUrl(url)) {
                    alert(dabMediaField.i18n.invalidUrl);
                    return;
                }
                
                // Preview embed
                previewEmbed(url);
                
                // Update value
                updateValue({
                    type: 'embed',
                    url: url
                });
            });
            
            // Recording handling
            if (allowRecording) {
                $recordStartBtn.on('click', function() {
                    startRecording();
                });
                
                $recordStopBtn.on('click', function() {
                    stopRecording();
                });
            }
            
            // Upload file to server
            function uploadFile(file) {
                var formData = new FormData();
                formData.append('action', 'dab_upload_media');
                formData.append('nonce', dabMediaField.nonce);
                formData.append('media_file', file);
                
                $.ajax({
                    url: dabMediaField.ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function() {
                        // Show loading indicator
                        $uploadInput.prop('disabled', true);
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update preview
                            updatePreview(response.data);
                            
                            // Update value
                            updateValue(response.data);
                        } else {
                            alert(response.data || dabMediaField.i18n.uploadError);
                        }
                    },
                    error: function() {
                        alert(dabMediaField.i18n.uploadError);
                    },
                    complete: function() {
                        // Hide loading indicator
                        $uploadInput.prop('disabled', false);
                    }
                });
            }
            
            // Preview embed URL
            function previewEmbed(url) {
                var html = '';
                
                // YouTube
                if (url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/) || url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/)) {
                    var videoId = url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/) ? 
                                  url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/)[1] : 
                                  url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/)[1];
                    html = '<iframe width="' + playerWidth + '" height="' + playerHeight + '" src="https://www.youtube.com/embed/' + videoId + '" frameborder="0" allowfullscreen></iframe>';
                }
                // Vimeo
                else if (url.match(/vimeo\.com\/([0-9]+)/)) {
                    var videoId = url.match(/vimeo\.com\/([0-9]+)/)[1];
                    html = '<iframe width="' + playerWidth + '" height="' + playerHeight + '" src="https://player.vimeo.com/video/' + videoId + '" frameborder="0" allowfullscreen></iframe>';
                }
                // Default
                else {
                    html = '<a href="' + url + '" target="_blank">' + url + '</a>';
                }
                
                $embedPreviewContainer.html(html);
            }
            
            // Start recording
            function startRecording() {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    alert(dabMediaField.i18n.browserNotSupported);
                    return;
                }
                
                var constraints = {
                    audio: mediaType === 'audio' || mediaType === 'both',
                    video: mediaType === 'video' || mediaType === 'both'
                };
                
                navigator.mediaDevices.getUserMedia(constraints)
                    .then(function(mediaStream) {
                        stream = mediaStream;
                        
                        // Create video element for preview
                        if (mediaType === 'video' || mediaType === 'both') {
                            var video = document.createElement('video');
                            video.srcObject = mediaStream;
                            video.controls = false;
                            video.muted = true;
                            video.width = playerWidth;
                            video.height = playerHeight;
                            video.play();
                            $recorderPreview.html(video);
                        } else {
                            $recorderPreview.html('<div class="dab-audio-recording-indicator">Recording audio...</div>');
                        }
                        
                        // Create media recorder
                        mediaRecorder = new MediaRecorder(mediaStream);
                        recordedChunks = [];
                        
                        mediaRecorder.ondataavailable = function(e) {
                            if (e.data.size > 0) {
                                recordedChunks.push(e.data);
                            }
                        };
                        
                        mediaRecorder.onstop = function() {
                            // Create blob from recorded chunks
                            var blob = new Blob(recordedChunks, {
                                type: mediaType === 'audio' ? 'audio/webm' : 'video/webm'
                            });
                            
                            // Upload the recorded blob
                            var file = new File([blob], 'recording.' + (mediaType === 'audio' ? 'webm' : 'webm'), {
                                type: mediaType === 'audio' ? 'audio/webm' : 'video/webm'
                            });
                            
                            uploadFile(file);
                            
                            // Stop all tracks
                            stream.getTracks().forEach(function(track) {
                                track.stop();
                            });
                            
                            // Clear preview
                            $recorderPreview.empty();
                        };
                        
                        // Start recording
                        mediaRecorder.start();
                        
                        // Update UI
                        $recordStartBtn.hide();
                        $recordStopBtn.show();
                    })
                    .catch(function(err) {
                        console.error('Error accessing media devices:', err);
                        alert(dabMediaField.i18n.errorRecording);
                    });
            }
            
            // Stop recording
            function stopRecording() {
                if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                    mediaRecorder.stop();
                    
                    // Update UI
                    $recordStopBtn.hide();
                    $recordStartBtn.show();
                }
            }
            
            // Update preview
            function updatePreview(mediaData) {
                var html = '';
                
                if (mediaData.type === 'embed') {
                    // YouTube
                    if (mediaData.url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/) || mediaData.url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/)) {
                        var videoId = mediaData.url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/) ? 
                                      mediaData.url.match(/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/)[1] : 
                                      mediaData.url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/)[1];
                        html = '<iframe width="' + playerWidth + '" height="' + playerHeight + '" src="https://www.youtube.com/embed/' + videoId + '" frameborder="0" allowfullscreen></iframe>';
                    }
                    // Vimeo
                    else if (mediaData.url.match(/vimeo\.com\/([0-9]+)/)) {
                        var videoId = mediaData.url.match(/vimeo\.com\/([0-9]+)/)[1];
                        html = '<iframe width="' + playerWidth + '" height="' + playerHeight + '" src="https://player.vimeo.com/video/' + videoId + '" frameborder="0" allowfullscreen></iframe>';
                    }
                    // Default
                    else {
                        html = '<a href="' + mediaData.url + '" target="_blank">' + mediaData.url + '</a>';
                    }
                } else {
                    // Handle uploaded media
                    var mimeType = mediaData.mime_type;
                    var url = mediaData.url;
                    
                    if (mimeType.indexOf('audio/') === 0) {
                        html = '<audio controls style="width:' + playerWidth + 'px;"><source src="' + url + '" type="' + mimeType + '">Your browser does not support the audio element.</audio>';
                    } else if (mimeType.indexOf('video/') === 0) {
                        html = '<video controls width="' + playerWidth + '" height="' + playerHeight + '"><source src="' + url + '" type="' + mimeType + '">Your browser does not support the video element.</video>';
                    } else {
                        html = '<a href="' + url + '" target="_blank">' + url + '</a>';
                    }
                }
                
                $mediaPreview.html(html);
            }
            
            // Update hidden value field
            function updateValue(mediaData) {
                $value.val(JSON.stringify(mediaData));
            }
            
            // Validate URL
            function isValidUrl(url) {
                try {
                    new URL(url);
                    return true;
                } catch (e) {
                    return false;
                }
            }
            
            // Load existing value
            var existingValue = $value.val();
            if (existingValue) {
                try {
                    var mediaData = JSON.parse(existingValue);
                    updatePreview(mediaData);
                } catch (e) {
                    // Not valid JSON, try as direct URL or ID
                    console.error('Error parsing media value:', e);
                }
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initMediaFields();
        
        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            initMediaFields();
        });
    });
    
})(jQuery);
