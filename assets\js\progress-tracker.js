/**
 * Progress Tracker JavaScript
 * 
 * Handles progress tracker field functionality
 */

(function($) {
    'use strict';

    // Progress Tracker Object
    window.DABProgressTracker = {
        milestones: [],
        
        init: function() {
            this.bindEvents();
            this.loadMilestones();
        },

        bindEvents: function() {
            // Add milestone button
            $(document).on('click', '.dab-add-milestone-btn', this.showAddMilestoneModal.bind(this));
            
            // Edit milestone
            $(document).on('click', '.dab-milestone-action-btn.edit', this.editMilestone.bind(this));
            
            // Delete milestone
            $(document).on('click', '.dab-milestone-action-btn.delete', this.deleteMilestone.bind(this));
            
            // Modal events
            $(document).on('click', '.dab-milestone-modal-close', this.closeModal.bind(this));
            $(document).on('click', '.dab-milestone-modal', function(e) {
                if (e.target === this) {
                    window.DABProgressTracker.closeModal();
                }
            });
            
            // Form submission
            $(document).on('submit', '#dab-milestone-form', this.saveMilestone.bind(this));
        },

        render: function() {
            this.renderProgressOverview();
            this.renderMilestones();
        },

        renderProgressOverview: function() {
            const container = $('.dab-progress-overview');
            if (container.length === 0) return;

            const stats = this.calculateStats();
            
            // Update progress bar
            const progressBar = container.find('.dab-progress-fill');
            const progressPercentage = container.find('.dab-progress-percentage');
            
            progressBar.css('width', stats.percentage + '%');
            progressPercentage.text(stats.percentage + '%');

            // Update stats
            container.find('.dab-progress-stat.completed .dab-progress-stat-value').text(stats.completed);
            container.find('.dab-progress-stat.in-progress .dab-progress-stat-value').text(stats.inProgress);
            container.find('.dab-progress-stat.pending .dab-progress-stat-value').text(stats.pending);
        },

        renderMilestones: function() {
            const container = $('.dab-milestones-list');
            if (container.length === 0) return;

            if (this.milestones.length === 0) {
                container.html(`
                    <div class="dab-progress-empty">
                        <div class="dab-progress-empty-icon">🎯</div>
                        <h3>No Milestones</h3>
                        <p>Add your first milestone to start tracking progress.</p>
                    </div>
                `);
                return;
            }

            let html = '';
            this.milestones.forEach(milestone => {
                html += this.renderMilestone(milestone);
            });

            container.html(html);
        },

        renderMilestone: function(milestone) {
            const statusIcon = this.getStatusIcon(milestone.status);
            const date = milestone.due_date ? new Date(milestone.due_date).toLocaleDateString() : '';
            
            return `
                <div class="dab-milestone" data-milestone-id="${milestone.id}">
                    <div class="dab-milestone-status ${milestone.status}">
                        ${statusIcon}
                    </div>
                    <div class="dab-milestone-content">
                        <div class="dab-milestone-title">${milestone.title}</div>
                        ${milestone.description ? `<div class="dab-milestone-description">${milestone.description}</div>` : ''}
                        <div class="dab-milestone-meta">
                            ${date ? `<div class="dab-milestone-date"><span class="dashicons dashicons-calendar-alt"></span> ${date}</div>` : ''}
                        </div>
                    </div>
                    <div class="dab-milestone-actions">
                        <button type="button" class="dab-milestone-action-btn edit" data-milestone-id="${milestone.id}">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="dab-milestone-action-btn delete" data-milestone-id="${milestone.id}">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
            `;
        },

        getStatusIcon: function(status) {
            const icons = {
                'completed': '✓',
                'in-progress': '⏳',
                'pending': ''
            };
            return icons[status] || '';
        },

        calculateStats: function() {
            const total = this.milestones.length;
            const completed = this.milestones.filter(m => m.status === 'completed').length;
            const inProgress = this.milestones.filter(m => m.status === 'in-progress').length;
            const pending = this.milestones.filter(m => m.status === 'pending').length;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            return {
                total,
                completed,
                inProgress,
                pending,
                percentage
            };
        },

        showAddMilestoneModal: function() {
            this.showMilestoneModal();
        },

        editMilestone: function(e) {
            const milestoneId = $(e.currentTarget).data('milestone-id');
            const milestone = this.milestones.find(m => m.id == milestoneId);
            if (milestone) {
                this.showMilestoneModal(milestone);
            }
        },

        showMilestoneModal: function(milestone = null) {
            let modal = $('#dab-milestone-modal');
            
            if (modal.length === 0) {
                modal = this.createMilestoneModal();
            }

            // Reset form
            const form = modal.find('#dab-milestone-form')[0];
            if (form) form.reset();

            if (milestone) {
                // Edit mode
                modal.find('.dab-milestone-modal-title').text('Edit Milestone');
                modal.find('#milestone-title').val(milestone.title);
                modal.find('#milestone-description').val(milestone.description);
                modal.find('#milestone-due-date').val(milestone.due_date);
                modal.find('#milestone-status').val(milestone.status);
                modal.find('#milestone-id').val(milestone.id);
                modal.find('.dab-delete-milestone-btn').show();
            } else {
                // Add mode
                modal.find('.dab-milestone-modal-title').text('Add Milestone');
                modal.find('#milestone-id').val('');
                modal.find('.dab-delete-milestone-btn').hide();
            }

            modal.show();
        },

        createMilestoneModal: function() {
            const modalHtml = `
                <div id="dab-milestone-modal" class="dab-milestone-modal">
                    <div class="dab-milestone-modal-content">
                        <div class="dab-milestone-modal-header">
                            <h3 class="dab-milestone-modal-title">Add Milestone</h3>
                            <button type="button" class="dab-milestone-modal-close">&times;</button>
                        </div>
                        <div class="dab-milestone-modal-body">
                            <form id="dab-milestone-form">
                                <input type="hidden" id="milestone-id" name="milestone_id">
                                
                                <div class="dab-milestone-form-group">
                                    <label for="milestone-title">Title</label>
                                    <input type="text" id="milestone-title" name="title" class="dab-milestone-form-control" required>
                                </div>
                                
                                <div class="dab-milestone-form-group">
                                    <label for="milestone-description">Description</label>
                                    <textarea id="milestone-description" name="description" class="dab-milestone-form-control textarea"></textarea>
                                </div>
                                
                                <div class="dab-milestone-form-group">
                                    <label for="milestone-due-date">Due Date</label>
                                    <input type="date" id="milestone-due-date" name="due_date" class="dab-milestone-form-control">
                                </div>
                                
                                <div class="dab-milestone-form-group">
                                    <label for="milestone-status">Status</label>
                                    <select id="milestone-status" name="status" class="dab-milestone-form-control">
                                        <option value="pending">Not Started</option>
                                        <option value="in-progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="dab-milestone-modal-footer">
                            <button type="button" class="dab-milestone-btn dab-milestone-btn-secondary dab-milestone-modal-close">Cancel</button>
                            <button type="button" class="dab-milestone-btn dab-milestone-btn-danger dab-delete-milestone-btn" style="display: none;">Delete</button>
                            <button type="submit" form="dab-milestone-form" class="dab-milestone-btn dab-milestone-btn-primary">Save Milestone</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            
            // Bind delete button
            $('#dab-milestone-modal').on('click', '.dab-delete-milestone-btn', this.deleteMilestoneFromModal.bind(this));
            
            return $('#dab-milestone-modal');
        },

        closeModal: function() {
            $('#dab-milestone-modal').hide();
        },

        saveMilestone: function(e) {
            e.preventDefault();
            
            const form = $('#dab-milestone-form');
            const formData = new FormData(form[0]);
            
            // Add AJAX data
            formData.append('action', 'dab_save_progress_milestone');
            formData.append('nonce', dabProgressData.nonce);

            $.ajax({
                url: dabProgressData.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        window.DABProgressTracker.loadMilestones();
                        window.DABProgressTracker.closeModal();
                        window.DABProgressTracker.showNotification('Milestone saved successfully!');
                    } else {
                        window.DABProgressTracker.showNotification('Error saving milestone: ' + response.data, 'error');
                    }
                },
                error: function() {
                    window.DABProgressTracker.showNotification('Error saving milestone', 'error');
                }
            });
        },

        deleteMilestone: function(e) {
            const milestoneId = $(e.currentTarget).data('milestone-id');
            
            if (confirm('Are you sure you want to delete this milestone?')) {
                this.performDelete(milestoneId);
            }
        },

        deleteMilestoneFromModal: function() {
            const milestoneId = $('#milestone-id').val();
            
            if (confirm('Are you sure you want to delete this milestone?')) {
                this.performDelete(milestoneId);
            }
        },

        performDelete: function(milestoneId) {
            if (typeof dabProgressData === 'undefined') {
                console.warn('DAB Progress Tracker: dabProgressData not available');
                return;
            }

            $.ajax({
                url: dabProgressData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_delete_progress_milestone',
                    nonce: dabProgressData.nonce,
                    milestone_id: milestoneId
                },
                success: function(response) {
                    if (response.success) {
                        window.DABProgressTracker.loadMilestones();
                        window.DABProgressTracker.closeModal();
                        window.DABProgressTracker.showNotification('Milestone deleted successfully!');
                    } else {
                        window.DABProgressTracker.showNotification('Error deleting milestone: ' + response.data, 'error');
                    }
                },
                error: function() {
                    window.DABProgressTracker.showNotification('Error deleting milestone', 'error');
                }
            });
        },

        loadMilestones: function() {
            if (typeof dabProgressData === 'undefined') {
                console.warn('DAB Progress Tracker: dabProgressData not available');
                return;
            }

            $.ajax({
                url: dabProgressData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_progress_milestones',
                    nonce: dabProgressData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        window.DABProgressTracker.milestones = response.data || [];
                        window.DABProgressTracker.render();
                    }
                },
                error: function() {
                    console.warn('DAB Progress Tracker: Error loading milestones');
                }
            });
        },

        showNotification: function(message, type = 'success') {
            const notification = $(`
                <div class="dab-progress-notification dab-notification-${type}">
                    ${message}
                </div>
            `);

            // Add notification styles if not present
            if (!$('#dab-progress-notification-styles').length) {
                $('head').append(`
                    <style id="dab-progress-notification-styles">
                        .dab-progress-notification {
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            padding: 12px 20px;
                            border-radius: 4px;
                            z-index: 10001;
                            font-size: 14px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        }
                        .dab-notification-success {
                            background: #4CAF50;
                            color: white;
                        }
                        .dab-notification-error {
                            background: #f44336;
                            color: white;
                        }
                    </style>
                `);
            }

            $('body').append(notification);

            setTimeout(function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            }, 3000);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        if ($('.dab-progress-tracker').length > 0) {
            window.DABProgressTracker.init();
        }
    });

})(jQuery);
