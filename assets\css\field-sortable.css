/**
 * Field Sortable CSS
 * Styles for the drag-and-drop field reordering functionality
 */

.dab-sortable-fields {
    margin: 15px 0;
    padding: 0;
    list-style: none;
}

.dab-field-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dab-field-item:hover {
    background-color: #f9f9f9;
    border-color: #bbb;
}

.dab-field-handle {
    cursor: move;
    margin-right: 10px;
    color: #999;
    transition: color 0.2s ease;
}

.dab-field-item:hover .dab-field-handle {
    color: #2271b1;
}

.dab-field-item.ui-sortable-helper {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #2271b1;
    z-index: 100;
}

.dab-field-item.ui-sortable-placeholder {
    visibility: visible !important;
    background-color: #f0f7fc;
    border: 1px dashed #2271b1;
    box-shadow: none;
}

/* Save indicator styles */
.dab-save-indicator {
    padding: 8px 12px;
    margin: 10px 0;
    background-color: #f0f7fc;
    border-left: 4px solid #2271b1;
    color: #333;
    font-size: 14px;
    border-radius: 2px;
    animation: fadeIn 0.3s ease;
}

.dab-save-success {
    background-color: #f0f7e6;
    border-left-color: #46b450;
}

.dab-save-error {
    background-color: #fbeaea;
    border-left-color: #dc3232;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Two-column layout for larger screens */
@media (min-width: 768px) {
    .dab-sortable-fields {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 10px;
    }
}
