<?php
/**
 * Payment Tracking Admin Page
 *
 * This page displays all payment records and allows filtering and searching.
 */
if (!defined('ABSPATH')) exit;

// Enqueue styles
wp_enqueue_style('dab-admin-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-style.css', array(), DAB_VERSION);
wp_enqueue_style('dab-modern-admin', plugin_dir_url(dirname(__FILE__)) . 'assets/css/modern-admin.css', array(), DAB_VERSION);

// Get payment records
global $wpdb;
$payments_table = $wpdb->prefix . 'dab_payments';
$forms_table = $wpdb->prefix . 'dab_forms';
$tables_table = $wpdb->prefix . 'dab_tables';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$payments_table'") == $payments_table;

if (!$table_exists) {
    // Create the table if it doesn't exist
    if (class_exists('DAB_Payment_Gateway')) {
        DAB_Payment_Gateway::create_payment_table();
    }
}

// Handle filters
$gateway_filter = isset($_GET['gateway']) ? sanitize_text_field($_GET['gateway']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

// Build query
$query = "SELECT p.*, f.form_name, t.table_label, u.display_name as user_name 
          FROM $payments_table p
          LEFT JOIN $forms_table f ON p.form_id = f.id
          LEFT JOIN $tables_table t ON f.table_id = t.id
          LEFT JOIN {$wpdb->users} u ON p.user_id = u.ID
          WHERE 1=1";

$query_args = array();

if (!empty($gateway_filter)) {
    $query .= " AND p.gateway = %s";
    $query_args[] = $gateway_filter;
}

if (!empty($status_filter)) {
    $query .= " AND p.status = %s";
    $query_args[] = $status_filter;
}

if (!empty($date_from)) {
    $query .= " AND p.created_at >= %s";
    $query_args[] = $date_from . ' 00:00:00';
}

if (!empty($date_to)) {
    $query .= " AND p.created_at <= %s";
    $query_args[] = $date_to . ' 23:59:59';
}

$query .= " ORDER BY p.created_at DESC";

// Prepare the query if we have arguments
if (!empty($query_args)) {
    $query = $wpdb->prepare($query, $query_args);
}

// Get payments
$payments = $wpdb->get_results($query);

// Get unique gateways and statuses for filters
$gateways = $wpdb->get_col("SELECT DISTINCT gateway FROM $payments_table");
$statuses = $wpdb->get_col("SELECT DISTINCT status FROM $payments_table");

// Calculate totals
$total_payments = count($payments);
$total_amount = 0;
$currency_totals = array();

foreach ($payments as $payment) {
    $total_amount += $payment->amount;
    
    if (!isset($currency_totals[$payment->currency])) {
        $currency_totals[$payment->currency] = 0;
    }
    
    $currency_totals[$payment->currency] += $payment->amount;
}
?>

<div class="wrap dab-admin-wrap dab-animate-fade-in">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title"><?php _e('Payment Tracking', 'db-app-builder'); ?></h1>
        <div class="dab-admin-actions">
            <a href="<?php echo admin_url('admin.php?page=dab_payment_settings'); ?>" class="dab-btn dab-btn-outline-primary"><?php _e('Payment Settings', 'db-app-builder'); ?></a>
        </div>
    </div>

    <!-- Dashboard Cards -->
    <div class="dab-layout">
        <div class="dab-col dab-col-md-4">
            <div class="dab-card dab-animate-slide-up" data-delay="100">
                <div class="dab-card-body">
                    <h3 class="dab-card-title"><?php _e('Total Payments', 'db-app-builder'); ?></h3>
                    <div class="dab-card-value"><?php echo $total_payments; ?></div>
                </div>
            </div>
        </div>
        
        <?php foreach ($currency_totals as $currency => $amount): ?>
        <div class="dab-col dab-col-md-4">
            <div class="dab-card dab-animate-slide-up" data-delay="200">
                <div class="dab-card-body">
                    <h3 class="dab-card-title"><?php echo sprintf(__('Total %s', 'db-app-builder'), $currency); ?></h3>
                    <div class="dab-card-value"><?php echo DAB_Payment_Gateway::format_currency($amount, $currency); ?></div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Filters -->
    <div class="dab-card dab-animate-slide-up" data-delay="300">
        <div class="dab-card-header">
            <h3 class="dab-card-title"><?php _e('Filter Payments', 'db-app-builder'); ?></h3>
        </div>
        <div class="dab-card-body">
            <form method="get" class="dab-filter-form">
                <input type="hidden" name="page" value="dab_payment_tracking">
                
                <div class="dab-layout">
                    <div class="dab-col dab-col-md-3">
                        <div class="dab-form-group">
                            <label for="gateway"><?php _e('Gateway', 'db-app-builder'); ?></label>
                            <select name="gateway" id="gateway" class="dab-form-control">
                                <option value=""><?php _e('All Gateways', 'db-app-builder'); ?></option>
                                <?php foreach ($gateways as $gateway): ?>
                                    <option value="<?php echo esc_attr($gateway); ?>" <?php selected($gateway_filter, $gateway); ?>><?php echo ucfirst($gateway); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="dab-col dab-col-md-3">
                        <div class="dab-form-group">
                            <label for="status"><?php _e('Status', 'db-app-builder'); ?></label>
                            <select name="status" id="status" class="dab-form-control">
                                <option value=""><?php _e('All Statuses', 'db-app-builder'); ?></option>
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?php echo esc_attr($status); ?>" <?php selected($status_filter, $status); ?>><?php echo ucfirst($status); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="dab-col dab-col-md-3">
                        <div class="dab-form-group">
                            <label for="date_from"><?php _e('Date From', 'db-app-builder'); ?></label>
                            <input type="date" name="date_from" id="date_from" class="dab-form-control" value="<?php echo esc_attr($date_from); ?>">
                        </div>
                    </div>
                    
                    <div class="dab-col dab-col-md-3">
                        <div class="dab-form-group">
                            <label for="date_to"><?php _e('Date To', 'db-app-builder'); ?></label>
                            <input type="date" name="date_to" id="date_to" class="dab-form-control" value="<?php echo esc_attr($date_to); ?>">
                        </div>
                    </div>
                </div>
                
                <div class="dab-form-actions">
                    <button type="submit" class="dab-btn dab-btn-primary"><?php _e('Apply Filters', 'db-app-builder'); ?></button>
                    <a href="<?php echo admin_url('admin.php?page=dab_payment_tracking'); ?>" class="dab-btn dab-btn-outline-secondary"><?php _e('Reset', 'db-app-builder'); ?></a>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="dab-card dab-animate-slide-up" data-delay="400">
        <div class="dab-card-header">
            <h3 class="dab-card-title"><?php _e('Payment Records', 'db-app-builder'); ?></h3>
        </div>
        <div class="dab-card-body">
            <?php if (empty($payments)): ?>
                <div class="dab-empty-state">
                    <div class="dab-empty-state-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <h3 class="dab-empty-state-title"><?php _e('No payments found', 'db-app-builder'); ?></h3>
                    <p class="dab-empty-state-description"><?php _e('No payment records match your current filters.', 'db-app-builder'); ?></p>
                </div>
            <?php else: ?>
                <div class="dab-table-responsive">
                    <table class="dab-table">
                        <thead>
                            <tr>
                                <th><?php _e('ID', 'db-app-builder'); ?></th>
                                <th><?php _e('Date', 'db-app-builder'); ?></th>
                                <th><?php _e('Form', 'db-app-builder'); ?></th>
                                <th><?php _e('Table', 'db-app-builder'); ?></th>
                                <th><?php _e('User', 'db-app-builder'); ?></th>
                                <th><?php _e('Gateway', 'db-app-builder'); ?></th>
                                <th><?php _e('Amount', 'db-app-builder'); ?></th>
                                <th><?php _e('Status', 'db-app-builder'); ?></th>
                                <th><?php _e('Transaction ID', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td><?php echo $payment->id; ?></td>
                                    <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($payment->created_at)); ?></td>
                                    <td><?php echo esc_html($payment->form_name); ?></td>
                                    <td><?php echo esc_html($payment->table_label); ?></td>
                                    <td><?php echo $payment->user_name ? esc_html($payment->user_name) : __('Guest', 'db-app-builder'); ?></td>
                                    <td><?php echo ucfirst($payment->gateway); ?></td>
                                    <td><?php echo DAB_Payment_Gateway::format_currency($payment->amount, $payment->currency); ?></td>
                                    <td>
                                        <span class="dab-badge dab-badge-<?php echo $payment->status === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($payment->status); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html($payment->transaction_id); ?></td>
                                    <td>
                                        <?php if ($payment->record_id): ?>
                                            <a href="<?php echo admin_url('admin.php?page=dab_data_dashboard&table_id=' . $payment->table_id . '&action=view&id=' . $payment->record_id); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary"><?php _e('View Record', 'db-app-builder'); ?></a>
                                        <?php else: ?>
                                            <span class="dab-text-muted"><?php _e('No record', 'db-app-builder'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
