/**
 * Conditional Logic Builder CSS
 * Styles for the visual conditional logic builder
 */

.dab-logic-builder {
    margin: 20px 0;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-logic-builder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.dab-logic-builder-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.dab-logic-builder-description {
    color: #666;
    margin-bottom: 20px;
}

.dab-logic-rules {
    margin-bottom: 20px;
}

.dab-logic-rule {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
    transition: all 0.2s ease;
}

.dab-logic-rule:hover {
    border-color: #bbb;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.dab-rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dab-rule-title {
    font-weight: 600;
    margin: 0;
}

.dab-rule-actions {
    display: flex;
    gap: 5px;
}

.dab-rule-action {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.dab-rule-action:hover {
    color: #2271b1;
    background-color: #f0f0f0;
}

.dab-rule-action.dab-delete-rule {
    color: #b32d2e;
}

.dab-rule-action.dab-delete-rule:hover {
    color: #b32d2e;
    background-color: #f9e2e2;
}

.dab-rule-body {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-rule-section {
    margin-bottom: 10px;
}

.dab-rule-section-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #2271b1;
}

.dab-conditions {
    margin-bottom: 15px;
}

.dab-condition {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #eee;
}

.dab-condition:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.dab-condition-field,
.dab-condition-operator,
.dab-condition-value {
    flex: 1;
    min-width: 150px;
}

.dab-condition-field select,
.dab-condition-operator select,
.dab-condition-value input,
.dab-condition-value select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-condition-remove {
    background: none;
    border: none;
    cursor: pointer;
    color: #b32d2e;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.dab-condition-remove:hover {
    background-color: #f9e2e2;
}

.dab-add-condition {
    background: none;
    border: 1px dashed #2271b1;
    color: #2271b1;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.2s ease;
}

.dab-add-condition:hover {
    background-color: #f0f7fc;
}

.dab-logic-type {
    margin-bottom: 15px;
}

.dab-logic-type label {
    margin-right: 15px;
    font-weight: normal;
}

.dab-actions {
    margin-top: 15px;
}

.dab-action {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.dab-action-type,
.dab-action-target,
.dab-action-value {
    flex: 1;
    min-width: 150px;
}

.dab-action-type select,
.dab-action-target select,
.dab-action-value input,
.dab-action-value select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-add-rule {
    background-color: #2271b1;
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.2s ease;
}

.dab-add-rule:hover {
    background-color: #135e96;
}

.dab-rule-preview {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f7fc;
    border: 1px solid #c5d9ed;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dab-logic-rule {
    animation: fadeIn 0.3s ease;
}
