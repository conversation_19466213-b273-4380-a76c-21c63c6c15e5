document.addEventListener('DOMContentLoaded', function() {
    if (typeof dabFormulas !== 'undefined') {
        Object.keys(dabFormulas).forEach(function(fieldSlug) {
            var expr = dabFormulas[fieldSlug];

            function calculate() {
                var formula = expr;
                for (var input of document.querySelectorAll('.dab-form input')) {
                    var name = input.name.replace('[]', '');
                    var value = input.value || 0;
                    formula = formula.replaceAll(name, value);
                }
                try {
                    var result = eval(formula);
                    document.getElementById('formula_' + fieldSlug).value = result;
                } catch (e) {
                    document.getElementById('formula_' + fieldSlug).value = '';
                }
            }

            document.querySelectorAll('.dab-form input').forEach(input => {
                input.addEventListener('input', calculate);
            });

            calculate(); // Initial
        });
    }
});
