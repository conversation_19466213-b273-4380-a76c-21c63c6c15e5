<?php
/**
 * Data Insights Page
 * Phase 3: Data Intelligence & Analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('edit_posts')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

?>
<div class="wrap dab-insights-page">
    <h1 class="wp-heading-inline">
        <?php _e('Data Insights', 'db-app-builder'); ?>
        <span class="dab-phase-badge">Phase 3</span>
    </h1>
    
    <div class="dab-insights-header">
        <div class="dab-insights-summary">
            <div class="dab-summary-card">
                <div class="dab-summary-icon">
                    <span class="dashicons dashicons-lightbulb"></span>
                </div>
                <div class="dab-summary-content">
                    <div class="dab-summary-number" id="active-insights">-</div>
                    <div class="dab-summary-label"><?php _e('Active Insights', 'db-app-builder'); ?></div>
                </div>
            </div>
            
            <div class="dab-summary-card">
                <div class="dab-summary-icon">
                    <span class="dashicons dashicons-warning"></span>
                </div>
                <div class="dab-summary-content">
                    <div class="dab-summary-number" id="critical-anomalies">-</div>
                    <div class="dab-summary-label"><?php _e('Critical Anomalies', 'db-app-builder'); ?></div>
                </div>
            </div>
            
            <div class="dab-summary-card">
                <div class="dab-summary-icon">
                    <span class="dashicons dashicons-star-filled"></span>
                </div>
                <div class="dab-summary-content">
                    <div class="dab-summary-number" id="recommendations">-</div>
                    <div class="dab-summary-label"><?php _e('Recommendations', 'db-app-builder'); ?></div>
                </div>
            </div>
            
            <div class="dab-summary-card">
                <div class="dab-summary-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="dab-summary-content">
                    <div class="dab-summary-number" id="trend-changes">-</div>
                    <div class="dab-summary-label"><?php _e('Trend Changes', 'db-app-builder'); ?></div>
                </div>
            </div>
        </div>
        
        <div class="dab-insights-actions">
            <button class="button" id="generate-insights">
                <span class="dashicons dashicons-update"></span>
                <?php _e('Generate New Insights', 'db-app-builder'); ?>
            </button>
            
            <button class="button" id="export-insights">
                <span class="dashicons dashicons-download"></span>
                <?php _e('Export Insights', 'db-app-builder'); ?>
            </button>
            
            <button class="button button-primary" id="create-custom-insight">
                <span class="dashicons dashicons-plus"></span>
                <?php _e('Create Custom Insight', 'db-app-builder'); ?>
            </button>
        </div>
    </div>

    <div class="dab-insights-content">
        <div class="dab-insights-sidebar">
            <div class="dab-sidebar-section">
                <h3><?php _e('Filters', 'db-app-builder'); ?></h3>
                
                <div class="dab-filter-group">
                    <label for="insight-type-filter"><?php _e('Insight Type', 'db-app-builder'); ?></label>
                    <select id="insight-type-filter" class="dab-filter-select">
                        <option value=""><?php _e('All Types', 'db-app-builder'); ?></option>
                        <option value="trend"><?php _e('Trends', 'db-app-builder'); ?></option>
                        <option value="anomaly"><?php _e('Anomalies', 'db-app-builder'); ?></option>
                        <option value="pattern"><?php _e('Patterns', 'db-app-builder'); ?></option>
                        <option value="correlation"><?php _e('Correlations', 'db-app-builder'); ?></option>
                        <option value="forecast"><?php _e('Forecasts', 'db-app-builder'); ?></option>
                    </select>
                </div>
                
                <div class="dab-filter-group">
                    <label for="data-source-filter"><?php _e('Data Source', 'db-app-builder'); ?></label>
                    <select id="data-source-filter" class="dab-filter-select">
                        <option value=""><?php _e('All Sources', 'db-app-builder'); ?></option>
                    </select>
                </div>
                
                <div class="dab-filter-group">
                    <label for="impact-level-filter"><?php _e('Impact Level', 'db-app-builder'); ?></label>
                    <select id="impact-level-filter" class="dab-filter-select">
                        <option value=""><?php _e('All Levels', 'db-app-builder'); ?></option>
                        <option value="critical"><?php _e('Critical', 'db-app-builder'); ?></option>
                        <option value="high"><?php _e('High', 'db-app-builder'); ?></option>
                        <option value="medium"><?php _e('Medium', 'db-app-builder'); ?></option>
                        <option value="low"><?php _e('Low', 'db-app-builder'); ?></option>
                    </select>
                </div>
                
                <div class="dab-filter-group">
                    <label for="time-range-filter"><?php _e('Time Range', 'db-app-builder'); ?></label>
                    <select id="time-range-filter" class="dab-filter-select">
                        <option value="today"><?php _e('Today', 'db-app-builder'); ?></option>
                        <option value="week" selected><?php _e('This Week', 'db-app-builder'); ?></option>
                        <option value="month"><?php _e('This Month', 'db-app-builder'); ?></option>
                        <option value="quarter"><?php _e('This Quarter', 'db-app-builder'); ?></option>
                        <option value="year"><?php _e('This Year', 'db-app-builder'); ?></option>
                    </select>
                </div>
                
                <button class="button" id="apply-filters">
                    <?php _e('Apply Filters', 'db-app-builder'); ?>
                </button>
                
                <button class="button" id="clear-filters">
                    <?php _e('Clear All', 'db-app-builder'); ?>
                </button>
            </div>
            
            <div class="dab-sidebar-section">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                
                <div class="dab-quick-actions">
                    <button class="dab-quick-action" data-action="detect-anomalies">
                        <span class="dashicons dashicons-search"></span>
                        <span><?php _e('Detect Anomalies', 'db-app-builder'); ?></span>
                    </button>
                    
                    <button class="dab-quick-action" data-action="analyze-trends">
                        <span class="dashicons dashicons-chart-line"></span>
                        <span><?php _e('Analyze Trends', 'db-app-builder'); ?></span>
                    </button>
                    
                    <button class="dab-quick-action" data-action="find-correlations">
                        <span class="dashicons dashicons-networking"></span>
                        <span><?php _e('Find Correlations', 'db-app-builder'); ?></span>
                    </button>
                    
                    <button class="dab-quick-action" data-action="generate-forecast">
                        <span class="dashicons dashicons-crystal"></span>
                        <span><?php _e('Generate Forecast', 'db-app-builder'); ?></span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="dab-insights-main">
            <div class="dab-insights-tabs">
                <button class="dab-tab-btn active" data-tab="insights">
                    <span class="dashicons dashicons-lightbulb"></span>
                    <?php _e('Insights', 'db-app-builder'); ?>
                </button>
                <button class="dab-tab-btn" data-tab="anomalies">
                    <span class="dashicons dashicons-warning"></span>
                    <?php _e('Anomalies', 'db-app-builder'); ?>
                </button>
                <button class="dab-tab-btn" data-tab="recommendations">
                    <span class="dashicons dashicons-star-filled"></span>
                    <?php _e('Recommendations', 'db-app-builder'); ?>
                </button>
                <button class="dab-tab-btn" data-tab="trends">
                    <span class="dashicons dashicons-chart-line"></span>
                    <?php _e('Trends', 'db-app-builder'); ?>
                </button>
            </div>
            
            <div class="dab-tab-content active" id="tab-insights">
                <div class="dab-insights-grid" id="insights-grid">
                    <div class="dab-loading">
                        <div class="spinner is-active"></div>
                        <p><?php _e('Loading insights...', 'db-app-builder'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="dab-tab-content" id="tab-anomalies">
                <div class="dab-anomalies-list" id="anomalies-list">
                    <div class="dab-loading">
                        <div class="spinner is-active"></div>
                        <p><?php _e('Loading anomalies...', 'db-app-builder'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="dab-tab-content" id="tab-recommendations">
                <div class="dab-recommendations-list" id="recommendations-list">
                    <div class="dab-loading">
                        <div class="spinner is-active"></div>
                        <p><?php _e('Loading recommendations...', 'db-app-builder'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="dab-tab-content" id="tab-trends">
                <div class="dab-trends-dashboard" id="trends-dashboard">
                    <div class="dab-loading">
                        <div class="spinner is-active"></div>
                        <p><?php _e('Loading trend analysis...', 'db-app-builder'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Insight Modal -->
<div class="dab-modal" id="custom-insight-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h2><?php _e('Create Custom Insight', 'db-app-builder'); ?></h2>
            <button class="dab-modal-close">&times;</button>
        </div>
        <div class="dab-modal-body">
            <form id="custom-insight-form">
                <div class="dab-form-row">
                    <div class="dab-form-group">
                        <label for="insight-title"><?php _e('Insight Title', 'db-app-builder'); ?></label>
                        <input type="text" id="insight-title" class="dab-input" required>
                    </div>
                    
                    <div class="dab-form-group">
                        <label for="insight-type"><?php _e('Insight Type', 'db-app-builder'); ?></label>
                        <select id="insight-type" class="dab-select" required>
                            <option value="trend"><?php _e('Trend Analysis', 'db-app-builder'); ?></option>
                            <option value="pattern"><?php _e('Pattern Recognition', 'db-app-builder'); ?></option>
                            <option value="correlation"><?php _e('Correlation Analysis', 'db-app-builder'); ?></option>
                            <option value="custom"><?php _e('Custom Insight', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                </div>
                
                <div class="dab-form-group">
                    <label for="insight-description"><?php _e('Description', 'db-app-builder'); ?></label>
                    <textarea id="insight-description" class="dab-textarea" rows="4" required></textarea>
                </div>
                
                <div class="dab-form-row">
                    <div class="dab-form-group">
                        <label for="insight-data-source"><?php _e('Data Source', 'db-app-builder'); ?></label>
                        <select id="insight-data-source" class="dab-select" required>
                            <option value=""><?php _e('Select Data Source', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                    
                    <div class="dab-form-group">
                        <label for="insight-impact"><?php _e('Impact Level', 'db-app-builder'); ?></label>
                        <select id="insight-impact" class="dab-select" required>
                            <option value="low"><?php _e('Low', 'db-app-builder'); ?></option>
                            <option value="medium" selected><?php _e('Medium', 'db-app-builder'); ?></option>
                            <option value="high"><?php _e('High', 'db-app-builder'); ?></option>
                            <option value="critical"><?php _e('Critical', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                </div>
                
                <div class="dab-form-actions">
                    <button type="button" class="button" id="cancel-insight"><?php _e('Cancel', 'db-app-builder'); ?></button>
                    <button type="submit" class="button button-primary"><?php _e('Create Insight', 'db-app-builder'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.dab-insights-page {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

.dab-phase-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 10px;
}

.dab-insights-header {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-insights-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.dab-summary-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.dab-summary-icon {
    font-size: 2.5em;
    margin-right: 20px;
    opacity: 0.8;
}

.dab-summary-number {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.dab-summary-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.dab-insights-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.dab-insights-content {
    display: flex;
    gap: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dab-insights-sidebar {
    width: 280px;
    background: #f8f9fa;
    border-right: 1px solid #e1e1e1;
}

.dab-sidebar-section {
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
}

.dab-sidebar-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
}

.dab-filter-group {
    margin-bottom: 15px;
}

.dab-filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.dab-filter-select, .dab-input, .dab-textarea, .dab-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.dab-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-quick-action {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.dab-quick-action:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.dab-quick-action .dashicons {
    color: #667eea;
}

.dab-insights-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.dab-insights-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e1e1;
}

.dab-tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.dab-tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.dab-tab-btn.active {
    background: white;
    border-bottom-color: #667eea;
    color: #667eea;
}

.dab-tab-content {
    display: none;
    padding: 20px;
    flex: 1;
}

.dab-tab-content.active {
    display: block;
}

.dab-insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.dab-loading {
    text-align: center;
    padding: 50px;
}

.dab-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow: auto;
}

.dab-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

.dab-modal-body {
    padding: 20px;
}

.dab-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.dab-form-group {
    margin-bottom: 15px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.dab-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize the data insights page
    DAB_DataInsights.init();
});
</script>
