# Dashboard Builder Phase 1 Implementation Plan

## 1. Modernize the UI/UX with GridStack.js

### 1.1 Add GridStack.js Library
```php
// In admin/page-dashboard-builder.php
wp_enqueue_style('gridstack-css', 'https://cdn.jsdelivr.net/npm/gridstack@7.2.3/dist/gridstack.min.css', array(), '7.2.3');
wp_enqueue_script('gridstack-js', 'https://cdn.jsdelivr.net/npm/gridstack@7.2.3/dist/gridstack.all.js', array('jquery'), '7.2.3', true);
```

### 1.2 Update Dashboard Grid HTML
```html
<!-- Replace the current grid with GridStack -->
<div class="grid-stack" id="dashboard-grid" data-gs-column="12" data-gs-animate="true">
    <!-- Widgets will be added here -->
</div>
```

### 1.3 Initialize GridStack in JavaScript
```javascript
// In assets/js/simple-dashboard-builder.js
function initDashboardGrid() {
    const options = {
        column: 12,
        cellHeight: 80,
        animate: true,
        resizable: {
            handles: 'e, se, s, sw, w'
        },
        draggable: {
            handle: '.dab-widget-header'
        }
    };

    grid = GridStack.init(options, '#dashboard-grid');

    // Load existing widgets
    if (dab_dashboard.dashboard && dab_dashboard.dashboard.widgets) {
        loadExistingWidgets(dab_dashboard.dashboard.widgets);
    }

    // Setup widget add event
    grid.on('added', function(event, items) {
        items.forEach(function(item) {
            setupWidgetEvents(item.el);
        });
    });

    // Setup widget change event
    grid.on('change', function(event, items) {
        saveWidgetPositions(items);
    });
}
```

## 2. Redesign Widget Cards

### 2.1 Update Widget HTML Template
```html
<!-- Widget template in JavaScript -->
function createWidgetTemplate(widget) {
    return `
        <div class="grid-stack-item" data-widget-id="${widget.id || 0}" data-widget-type="${widget.type}">
            <div class="grid-stack-item-content">
                <div class="dab-widget">
                    <div class="dab-widget-header">
                        <h3 class="dab-widget-title">${widget.title || 'New Widget'}</h3>
                        <div class="dab-widget-actions">
                            <button type="button" class="dab-widget-settings" title="Settings">
                                <span class="dashicons dashicons-admin-generic"></span>
                            </button>
                            <button type="button" class="dab-widget-edit" title="Edit">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                            <button type="button" class="dab-widget-delete" title="Delete">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>
                    </div>
                    <div class="dab-widget-content">
                        <div class="dab-widget-placeholder">
                            <span class="dashicons dashicons-${getWidgetIcon(widget.type)}"></span>
                            <p>${widget.title || 'New Widget'}</p>
                        </div>
                    </div>
                    <div class="dab-widget-footer">
                        <div class="dab-widget-status"></div>
                        <div class="dab-widget-info">
                            <span class="dab-widget-type">${widget.type}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}
```

### 2.2 Update CSS for Modern Widget Design
```css
/* In assets/css/simple-dashboard-builder.css */
.dab-widget {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: box-shadow 0.3s ease;
    overflow: hidden;
}

.dab-widget:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.dab-widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
}

.dab-widget-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dab-widget-actions {
    display: flex;
    gap: 4px;
}

.dab-widget-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #666;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.dab-widget-actions button:hover {
    background-color: #f0f0f0;
    color: #333;
}

.dab-widget-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
    position: relative;
}

.dab-widget-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    text-align: center;
}

.dab-widget-placeholder .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
}

.dab-widget-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;
    font-size: 12px;
    color: #999;
}

.dab-widget-type {
    text-transform: capitalize;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
}
```

## 3. Enhance Widget Settings Modal

### 3.1 Create a Sliding Settings Panel
```html
<!-- Add to admin/page-dashboard-builder.php -->
<div id="dab-widget-settings-panel" class="dab-settings-panel">
    <div class="dab-settings-panel-header">
        <h2 id="dab-settings-panel-title">Widget Settings</h2>
        <button type="button" class="dab-settings-panel-close">
            <span class="dashicons dashicons-no-alt"></span>
        </button>
    </div>
    <div class="dab-settings-panel-content">
        <form id="dab-widget-settings-form">
            <input type="hidden" id="widget-id" name="widget-id" value="">
            <input type="hidden" id="widget-type" name="widget-type" value="">

            <div class="dab-form-group">
                <label for="widget-title">Title</label>
                <input type="text" id="widget-title" name="widget-title" class="regular-text" required>
            </div>

            <div id="widget-settings-fields"></div>

            <div class="dab-form-actions">
                <button type="submit" class="button button-primary">Save Settings</button>
                <button type="button" class="button dab-settings-panel-cancel">Cancel</button>
            </div>
        </form>
    </div>
</div>
```

### 3.2 Add CSS for Settings Panel
```css
/* In assets/css/simple-dashboard-builder.css */
.dab-settings-panel {
    position: fixed;
    top: 32px; /* WP admin bar height */
    right: -400px;
    width: 400px;
    height: calc(100vh - 32px);
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.dab-settings-panel.open {
    right: 0;
}

.dab-settings-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.dab-settings-panel-header h2 {
    margin: 0;
    font-size: 18px;
}

.dab-settings-panel-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
}

.dab-settings-panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.dab-settings-section {
    margin-bottom: 24px;
}

.dab-settings-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

/* Form field styles */
.dab-form-group {
    margin-bottom: 16px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
}

.dab-form-group .description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.dab-field-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.dab-field-col {
    flex: 1;
}

/* Form actions */
.dab-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}
```

## 4. Enhance Widget Types

### 4.1 Improve Table Widget

#### 4.1.1 Update Table Widget Settings Form
```javascript
// In assets/js/simple-dashboard-builder.js
function addTableWidgetFields(widget) {
    console.log('Adding table widget fields with settings:', widget.settings);
    const settings = widget.settings || {};

    // Ensure table_id is treated as a string for comparison
    const tableId = settings.table_id ? settings.table_id.toString() : '';
    console.log('Table ID from settings:', tableId);

    const fields = `
        <div class="dab-settings-section">
            <h3>Table Settings</h3>

            <div class="dab-form-group">
                <label for="widget-table-id">Table</label>
                <select id="widget-table-id" class="regular-text" required>
                    <option value="">Select a table</option>
                    ${dab_dashboard.tables.map(table => {
                        const isSelected = tableId === table.id.toString();
                        return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                            ${table.table_label}
                        </option>`;
                    }).join('')}
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-table-pagination">Enable Pagination</label>
                <input type="checkbox" id="widget-table-pagination" ${settings.pagination ? 'checked' : ''}>
                <p class="description">Show pagination controls for large datasets</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-table-rows-per-page">Rows Per Page</label>
                <select id="widget-table-rows-per-page" ${!settings.pagination ? 'disabled' : ''}>
                    <option value="10" ${settings.rows_per_page === 10 ? 'selected' : ''}>10</option>
                    <option value="25" ${settings.rows_per_page === 25 ? 'selected' : ''}>25</option>
                    <option value="50" ${settings.rows_per_page === 50 ? 'selected' : ''}>50</option>
                    <option value="100" ${settings.rows_per_page === 100 ? 'selected' : ''}>100</option>
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-table-sorting">Enable Sorting</label>
                <input type="checkbox" id="widget-table-sorting" ${settings.sorting ? 'checked' : ''}>
                <p class="description">Allow users to sort table columns</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-table-search">Enable Search</label>
                <input type="checkbox" id="widget-table-search" ${settings.search ? 'checked' : ''}>
                <p class="description">Add a search box to filter table data</p>
            </div>

            <div class="dab-form-group">
                <label>Visible Columns</label>
                <div id="widget-table-columns" class="dab-columns-selector">
                    <!-- Columns will be loaded dynamically based on selected table -->
                    <p class="description">Select a table to configure visible columns</p>
                </div>
            </div>
        </div>
    `;

    $('#widget-settings-fields').html(fields);

    // Add event listener for table selection to load columns
    $('#widget-table-id').on('change', function() {
        loadTableColumns($(this).val());
    });

    // Add event listener for pagination checkbox
    $('#widget-table-pagination').on('change', function() {
        $('#widget-table-rows-per-page').prop('disabled', !$(this).is(':checked'));
    });

    // Load columns if table is already selected
    if (tableId) {
        loadTableColumns(tableId);
    }
}

// Function to load table columns
function loadTableColumns(tableId) {
    if (!tableId) return;

    // Show loading indicator
    $('#widget-table-columns').html('<p>Loading columns...</p>');

    // Fetch table fields via AJAX
    $.ajax({
        url: dab_dashboard.ajax_url,
        type: 'POST',
        data: {
            action: 'dab_get_dashboard_table_fields',
            table_id: tableId,
            nonce: dab_dashboard.nonce
        },
        success: function(response) {
            if (response.success && response.data) {
                renderColumnSelector(response.data);
            } else {
                $('#widget-table-columns').html('<p class="error">Failed to load columns</p>');
            }
        },
        error: function() {
            $('#widget-table-columns').html('<p class="error">Failed to load columns</p>');
        }
    });
}

// Function to render column selector
function renderColumnSelector(fields) {
    if (!fields || !fields.length) {
        $('#widget-table-columns').html('<p>No columns found for this table</p>');
        return;
    }

    const currentWidget = getCurrentWidget();
    const selectedColumns = currentWidget && currentWidget.settings && currentWidget.settings.columns ?
        currentWidget.settings.columns : [];

    let html = '<div class="dab-columns-list">';

    fields.forEach(field => {
        const isChecked = selectedColumns.includes(field.field_slug) || selectedColumns.length === 0;
        html += `
            <div class="dab-column-item">
                <label>
                    <input type="checkbox" name="widget-table-columns[]" value="${field.field_slug}" ${isChecked ? 'checked' : ''}>
                    ${field.field_label}
                </label>
            </div>
        `;
    });

    html += '</div>';
    $('#widget-table-columns').html(html);
}
```

#### 4.1.2 Update Table Widget Rendering
```javascript
// In assets/js/simple-dashboard-view.js
function renderTableWidget(container, data) {
    console.log('Rendering table widget with data:', data);

    // Check if data is valid
    if (!data) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
        return;
    }

    // Check if headers and records exist
    if (!data.headers || !Array.isArray(data.headers) || !data.records || !Array.isArray(data.records)) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
        return;
    }

    // Check if there are any records
    if (data.records.length === 0) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
        return;
    }

    // Get widget settings
    const widget = container.closest('.dab-dashboard-widget');
    const widgetId = widget.data('widget-id');
    const settings = widget.data('settings') || {};

    // Build table HTML
    let tableHtml = '<div class="dab-widget-table-wrapper">';

    // Add search box if enabled
    if (settings.search) {
        tableHtml += `
            <div class="dab-table-search">
                <input type="text" placeholder="Search..." class="dab-search-input">
            </div>
        `;
    }

    tableHtml += '<div class="dab-widget-table-container"><table class="dab-widget-table">';

    // Headers
    tableHtml += '<thead><tr>';
    data.headers.forEach(header => {
        const sortClass = settings.sorting ? ' dab-sortable' : '';
        tableHtml += `<th class="${sortClass}" data-field="${header.slug}">${header.label || header.slug || 'Unknown'}</th>`;
    });
    tableHtml += '</tr></thead>';

    // Records
    tableHtml += '<tbody>';
    data.records.forEach(record => {
        tableHtml += '<tr>';
        data.headers.forEach(header => {
            const slug = header.slug || '';
            const value = record[slug] !== undefined ? record[slug] : '';
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += '</tr>';
    });
    tableHtml += '</tbody>';
    tableHtml += '</table></div>';

    // Add pagination if enabled
    if (settings.pagination) {
        const totalRecords = data.records.length;
        const rowsPerPage = settings.rows_per_page || 10;
        const totalPages = Math.ceil(totalRecords / rowsPerPage);

        if (totalPages > 1) {
            tableHtml += `
                <div class="dab-table-pagination">
                    <div class="dab-pagination-info">
                        Showing <span class="dab-page-start">1</span> to <span class="dab-page-end">${Math.min(rowsPerPage, totalRecords)}</span> of <span class="dab-total-records">${totalRecords}</span> entries
                    </div>
                    <div class="dab-pagination-controls">
                        <button class="dab-pagination-prev" disabled>&laquo; Previous</button>
                        <span class="dab-pagination-pages">Page <span class="dab-current-page">1</span> of ${totalPages}</span>
                        <button class="dab-pagination-next" ${totalPages === 1 ? 'disabled' : ''}>Next &raquo;</button>
                    </div>
                </div>
            `;
        }
    }

    tableHtml += '</div>';

    container.html(tableHtml);

    // Initialize table features
    initTableFeatures(container, data, settings);
}

// Function to initialize table features
function initTableFeatures(container, data, settings) {
    // Initialize search functionality
    if (settings.search) {
        const searchInput = container.find('.dab-search-input');
        searchInput.on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTableRows(container, searchTerm);
        });
    }

    // Initialize sorting functionality
    if (settings.sorting) {
        const sortableHeaders = container.find('th.dab-sortable');
        sortableHeaders.on('click', function() {
            const field = $(this).data('field');
            const isAsc = $(this).hasClass('asc');

            // Remove sort classes from all headers
            sortableHeaders.removeClass('asc desc');

            // Add sort class to clicked header
            $(this).addClass(isAsc ? 'desc' : 'asc');

            // Sort table rows
            sortTableRows(container, field, !isAsc);
        });
    }

    // Initialize pagination functionality
    if (settings.pagination) {
        const rowsPerPage = settings.rows_per_page || 10;
        const totalRecords = data.records.length;
        const totalPages = Math.ceil(totalRecords / rowsPerPage);

        if (totalPages > 1) {
            // Show only first page rows
            const tableRows = container.find('tbody tr');
            tableRows.hide();
            tableRows.slice(0, rowsPerPage).show();

            // Setup pagination controls
            const prevButton = container.find('.dab-pagination-prev');
            const nextButton = container.find('.dab-pagination-next');

            prevButton.on('click', function() {
                if ($(this).prop('disabled')) return;

                const currentPage = parseInt(container.find('.dab-current-page').text());
                goToPage(container, currentPage - 1, rowsPerPage, totalRecords);
            });

            nextButton.on('click', function() {
                if ($(this).prop('disabled')) return;

                const currentPage = parseInt(container.find('.dab-current-page').text());
                goToPage(container, currentPage + 1, rowsPerPage, totalRecords);
            });
        }
    }
}

// Function to filter table rows
function filterTableRows(container, searchTerm) {
    const tableRows = container.find('tbody tr');

    tableRows.each(function() {
        const row = $(this);
        const text = row.text().toLowerCase();

        if (text.indexOf(searchTerm) > -1) {
            row.show();
        } else {
            row.hide();
        }
    });

    // Reset pagination if active
    if (container.find('.dab-table-pagination').length) {
        updatePaginationInfo(container);
    }
}

// Function to sort table rows
function sortTableRows(container, field, asc) {
    const table = container.find('table');
    const tbody = table.find('tbody');
    const rows = tbody.find('tr').toArray();
    const headerIndex = table.find('th').toArray().findIndex(th => $(th).data('field') === field);

    rows.sort(function(a, b) {
        const aValue = $(a).find('td').eq(headerIndex).text();
        const bValue = $(b).find('td').eq(headerIndex).text();

        // Try to sort as numbers if possible
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return asc ? aNum - bNum : bNum - aNum;
        }

        // Otherwise sort as strings
        return asc ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // Append sorted rows
    tbody.empty();
    rows.forEach(row => tbody.append(row));

    // Reset pagination if active
    if (container.find('.dab-table-pagination').length) {
        const currentPage = parseInt(container.find('.dab-current-page').text());
        const rowsPerPage = parseInt(container.find('.dab-pagination-info').data('rows-per-page') || 10);
        const totalRecords = rows.length;

        goToPage(container, 1, rowsPerPage, totalRecords);
    }
}

// Function to go to a specific page
function goToPage(container, page, rowsPerPage, totalRecords) {
    const tableRows = container.find('tbody tr:visible');
    const totalPages = Math.ceil(tableRows.length / rowsPerPage);

    // Validate page number
    if (page < 1) page = 1;
    if (page > totalPages) page = totalPages;

    // Update current page display
    container.find('.dab-current-page').text(page);

    // Enable/disable pagination buttons
    container.find('.dab-pagination-prev').prop('disabled', page === 1);
    container.find('.dab-pagination-next').prop('disabled', page === totalPages);

    // Show only rows for current page
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    tableRows.hide();
    tableRows.slice(start, end).show();

    // Update pagination info
    container.find('.dab-page-start').text(start + 1);
    container.find('.dab-page-end').text(Math.min(end, tableRows.length));
    container.find('.dab-total-records').text(tableRows.length);
}

// Function to update pagination info after filtering
function updatePaginationInfo(container) {
    const tableRows = container.find('tbody tr:visible');
    const rowsPerPage = parseInt(container.find('.dab-pagination-info').data('rows-per-page') || 10);
    const totalRecords = tableRows.length;
    const totalPages = Math.ceil(totalRecords / rowsPerPage);

    // Reset to page 1
    goToPage(container, 1, rowsPerPage, totalRecords);

    // Update total pages
    container.find('.dab-pagination-pages').text(`Page 1 of ${totalPages}`);
}

### 4.2 Improve Chart Widget

#### 4.2.1 Update Chart Widget Settings Form
```javascript
// In assets/js/simple-dashboard-builder.js
function addChartWidgetFields(widget) {
    console.log('Adding chart widget fields with settings:', widget.settings);
    const settings = widget.settings || {};

    // Ensure table_id is treated as a string for comparison
    const tableId = settings.table_id ? settings.table_id.toString() : '';
    console.log('Chart Table ID from settings:', tableId);

    const fields = `
        <div class="dab-settings-section">
            <h3>Chart Settings</h3>

            <div class="dab-form-group">
                <label for="widget-chart-table-id">Table</label>
                <select id="widget-chart-table-id" class="regular-text" required>
                    <option value="">Select a table</option>
                    ${dab_dashboard.tables.map(table => {
                        const isSelected = tableId === table.id.toString();
                        return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                            ${table.table_label}
                        </option>`;
                    }).join('')}
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-chart-type">Chart Type</label>
                <select id="widget-chart-type" class="regular-text" required>
                    <option value="bar" ${settings.chart_type === 'bar' ? 'selected' : ''}>Bar Chart</option>
                    <option value="line" ${settings.chart_type === 'line' ? 'selected' : ''}>Line Chart</option>
                    <option value="pie" ${settings.chart_type === 'pie' ? 'selected' : ''}>Pie Chart</option>
                    <option value="doughnut" ${settings.chart_type === 'doughnut' ? 'selected' : ''}>Doughnut Chart</option>
                    <option value="polarArea" ${settings.chart_type === 'polarArea' ? 'selected' : ''}>Polar Area Chart</option>
                    <option value="radar" ${settings.chart_type === 'radar' ? 'selected' : ''}>Radar Chart</option>
                </select>
            </div>

            <div class="dab-field-row">
                <div class="dab-field-col">
                    <div class="dab-form-group">
                        <label for="widget-chart-x-axis">X-Axis Field</label>
                        <select id="widget-chart-x-axis" class="regular-text" required disabled>
                            <option value="">Select a table first</option>
                        </select>
                        <p class="description">Field to use for X-axis labels</p>
                    </div>
                </div>

                <div class="dab-field-col">
                    <div class="dab-form-group">
                        <label for="widget-chart-y-axis">Y-Axis Field</label>
                        <select id="widget-chart-y-axis" class="regular-text" required disabled>
                            <option value="">Select a table first</option>
                        </select>
                        <p class="description">Field to use for Y-axis values</p>
                    </div>
                </div>
            </div>

            <div class="dab-form-group">
                <label for="widget-chart-aggregation">Aggregation Method</label>
                <select id="widget-chart-aggregation" class="regular-text">
                    <option value="sum" ${settings.aggregation === 'sum' ? 'selected' : ''}>Sum</option>
                    <option value="average" ${settings.aggregation === 'average' ? 'selected' : ''}>Average</option>
                    <option value="count" ${settings.aggregation === 'count' ? 'selected' : ''}>Count</option>
                    <option value="min" ${settings.aggregation === 'min' ? 'selected' : ''}>Minimum</option>
                    <option value="max" ${settings.aggregation === 'max' ? 'selected' : ''}>Maximum</option>
                </select>
                <p class="description">How to aggregate Y-axis values</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-chart-limit">Data Limit</label>
                <input type="number" id="widget-chart-limit" class="regular-text" value="${settings.limit || 10}" min="1" max="100">
                <p class="description">Maximum number of data points to display</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-chart-legend">Show Legend</label>
                <input type="checkbox" id="widget-chart-legend" ${settings.show_legend ? 'checked' : ''}>
                <p class="description">Display chart legend</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-chart-colors">Custom Colors</label>
                <input type="text" id="widget-chart-colors" class="regular-text" value="${settings.colors || ''}">
                <p class="description">Comma-separated list of colors (e.g., #ff0000,#00ff00,#0000ff)</p>
            </div>
        </div>
    `;

    $('#widget-settings-fields').html(fields);

    // Add event listener for table selection to load fields
    $('#widget-chart-table-id').on('change', function() {
        loadChartFields($(this).val());
    });

    // Load fields if table is already selected
    if (tableId) {
        loadChartFields(tableId);
    }
}

// Function to load chart fields
function loadChartFields(tableId) {
    if (!tableId) return;

    // Show loading indicator
    $('#widget-chart-x-axis, #widget-chart-y-axis').html('<option value="">Loading fields...</option>').prop('disabled', true);

    // Fetch table fields via AJAX
    $.ajax({
        url: dab_dashboard.ajax_url,
        type: 'POST',
        data: {
            action: 'dab_get_dashboard_table_fields',
            table_id: tableId,
            nonce: dab_dashboard.nonce
        },
        success: function(response) {
            if (response.success && response.data) {
                populateChartFields(response.data);
            } else {
                $('#widget-chart-x-axis, #widget-chart-y-axis').html('<option value="">Failed to load fields</option>');
            }
        },
        error: function() {
            $('#widget-chart-x-axis, #widget-chart-y-axis').html('<option value="">Failed to load fields</option>');
        }
    });
}

// Function to populate chart fields
function populateChartFields(fields) {
    if (!fields || !fields.length) {
        $('#widget-chart-x-axis, #widget-chart-y-axis').html('<option value="">No fields available</option>');
        return;
    }

    const currentWidget = getCurrentWidget();
    const settings = currentWidget && currentWidget.settings ? currentWidget.settings : {};

    // Populate X-axis field dropdown
    let xAxisOptions = '<option value="">Select X-axis field</option>';
    fields.forEach(field => {
        const isSelected = settings.x_axis === field.field_slug;
        xAxisOptions += `<option value="${field.field_slug}" ${isSelected ? 'selected' : ''}>${field.field_label}</option>`;
    });
    $('#widget-chart-x-axis').html(xAxisOptions).prop('disabled', false);

    // Populate Y-axis field dropdown (only numeric fields)
    let yAxisOptions = '<option value="">Select Y-axis field</option>';
    fields.filter(field => ['number', 'integer', 'float', 'decimal'].includes(field.field_type)).forEach(field => {
        const isSelected = settings.y_axis === field.field_slug;
        yAxisOptions += `<option value="${field.field_slug}" ${isSelected ? 'selected' : ''}>${field.field_label}</option>`;
    });
    $('#widget-chart-y-axis').html(yAxisOptions).prop('disabled', false);
}

#### 4.2.2 Update Chart Widget Rendering
```javascript
// In assets/js/simple-dashboard-view.js
function renderChartWidget(widget, container, data) {
    console.log('Rendering chart widget with data:', data);
    const widgetId = widget.data('widget-id');
    const settings = widget.data('settings') || {};

    // Check if data is valid
    if (!data) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
        return;
    }

    // Check for the format returned by get_chart_data
    if (data.labels && Array.isArray(data.labels) && data.values && Array.isArray(data.values)) {
        // This is the format from get_chart_data
        // Transform it to the format expected by Chart.js
        const chartType = settings.chart_type || widget.data('chart-type') || 'bar';
        const chartLabel = data.label || 'Value';

        // Get custom colors if specified
        let backgroundColor = [];
        let borderColor = [];

        if (settings.colors) {
            const colors = settings.colors.split(',').map(color => color.trim());
            backgroundColor = colors.map(color => `${color}80`); // Add 50% opacity
            borderColor = colors;
        } else {
            // Default colors based on chart type
            if (['pie', 'doughnut', 'polarArea'].includes(chartType)) {
                // Multiple colors for pie/doughnut charts
                backgroundColor = [
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(255, 206, 86, 0.5)',
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(153, 102, 255, 0.5)',
                    'rgba(255, 159, 64, 0.5)',
                    'rgba(199, 199, 199, 0.5)',
                    'rgba(83, 102, 255, 0.5)',
                    'rgba(40, 159, 64, 0.5)',
                    'rgba(210, 199, 199, 0.5)'
                ];
                borderColor = [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(199, 199, 199, 1)',
                    'rgba(83, 102, 255, 1)',
                    'rgba(40, 159, 64, 1)',
                    'rgba(210, 199, 199, 1)'
                ];
            } else {
                // Single color for bar/line charts
                backgroundColor = 'rgba(54, 162, 235, 0.5)';
                borderColor = 'rgba(54, 162, 235, 1)';
            }
        }

        // Create datasets from values
        const datasets = [{
            label: chartLabel,
            data: data.values,
            backgroundColor: backgroundColor,
            borderColor: borderColor,
            borderWidth: 1
        }];

        // Create canvas
        container.html('<div class="dab-widget-chart-container"><canvas></canvas></div>');

        const canvas = container.find('canvas')[0];
        const ctx = canvas.getContext('2d');

        // Destroy existing chart
        if (chartInstances[widgetId]) {
            chartInstances[widgetId].destroy();
        }

        try {
            // Create chart options based on chart type
            const options = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: settings.show_legend !== false,
                        position: 'top',
                    },
                    title: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        mode: ['pie', 'doughnut', 'polarArea'].includes(chartType) ? 'nearest' : 'index',
                        intersect: false
                    }
                }
            };

            // Add specific options based on chart type
            if (chartType === 'bar') {
                options.scales = {
                    y: {
                        beginAtZero: true
                    }
                };
            } else if (chartType === 'line') {
                options.elements = {
                    line: {
                        tension: 0.4 // Smooth lines
                    }
                };
                options.scales = {
                    y: {
                        beginAtZero: true
                    }
                };
            }

            // Create chart
            chartInstances[widgetId] = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: data.labels,
                    datasets: datasets
                },
                options: options
            });
        } catch (error) {
            console.error('Error creating chart:', error);
            container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}: ${error.message}</p>`);
        }
        return;
    }

    // Handle other data formats or errors
    if (data.error) {
        container.html(`<p class="dab-no-data">${data.error}</p>`);
        return;
    }

    // If we get here, we don't have valid data
    container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
}
```

### 4.3 Improve Metric Widget

#### 4.3.1 Update Metric Widget Settings Form
```javascript
// In assets/js/simple-dashboard-builder.js
function addMetricWidgetFields(widget) {
    console.log('Adding metric widget fields with settings:', widget.settings);
    const settings = widget.settings || {};

    // Ensure table_id is treated as a string for comparison
    const tableId = settings.table_id ? settings.table_id.toString() : '';
    console.log('Metric Table ID from settings:', tableId);

    // Determine if this is a count metric
    const isCountMetric = settings.metric_type === 'count';

    const fields = `
        <div class="dab-settings-section">
            <h3>Metric Settings</h3>

            <div class="dab-form-group">
                <label for="widget-metric-table-id">Table</label>
                <select id="widget-metric-table-id" class="regular-text" required>
                    <option value="">Select a table</option>
                    ${dab_dashboard.tables.map(table => {
                        const isSelected = tableId === table.id.toString();
                        return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                            ${table.table_label}
                        </option>`;
                    }).join('')}
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-type">Metric Type</label>
                <select id="widget-metric-type" class="regular-text" required>
                    <option value="count" ${settings.metric_type === 'count' ? 'selected' : ''}>Count</option>
                    <option value="sum" ${settings.metric_type === 'sum' ? 'selected' : ''}>Sum</option>
                    <option value="average" ${settings.metric_type === 'average' ? 'selected' : ''}>Average</option>
                    <option value="min" ${settings.metric_type === 'min' ? 'selected' : ''}>Minimum</option>
                    <option value="max" ${settings.metric_type === 'max' ? 'selected' : ''}>Maximum</option>
                </select>
                <p class="description">Type of calculation to perform</p>
            </div>

            <div class="dab-form-group" id="metric-field-group" ${isCountMetric ? 'style="display:none;"' : ''}>
                <label for="widget-metric-field">Field</label>
                <select id="widget-metric-field" class="regular-text" ${isCountMetric ? '' : 'required'} disabled>
                    <option value="">Select a table first</option>
                </select>
                <p class="description">Field to use for calculation (not required for Count)</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-label">Label</label>
                <input type="text" id="widget-metric-label" class="regular-text" value="${settings.label || ''}">
                <p class="description">Label to display with the metric value</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-prefix">Prefix</label>
                <input type="text" id="widget-metric-prefix" class="regular-text" value="${settings.prefix || ''}">
                <p class="description">Text to display before the value (e.g., $)</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-suffix">Suffix</label>
                <input type="text" id="widget-metric-suffix" class="regular-text" value="${settings.suffix || ''}">
                <p class="description">Text to display after the value (e.g., %)</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-decimal-places">Decimal Places</label>
                <input type="number" id="widget-metric-decimal-places" class="regular-text" value="${settings.decimal_places || 0}" min="0" max="10">
                <p class="description">Number of decimal places to display</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-color">Color</label>
                <input type="text" id="widget-metric-color" class="regular-text" value="${settings.color || '#3498db'}">
                <p class="description">Color for the metric value (hex code)</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-size">Size</label>
                <select id="widget-metric-size" class="regular-text">
                    <option value="small" ${settings.size === 'small' ? 'selected' : ''}>Small</option>
                    <option value="medium" ${settings.size === 'medium' || !settings.size ? 'selected' : ''}>Medium</option>
                    <option value="large" ${settings.size === 'large' ? 'selected' : ''}>Large</option>
                </select>
                <p class="description">Size of the metric display</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-metric-icon">Icon</label>
                <input type="text" id="widget-metric-icon" class="regular-text" value="${settings.icon || ''}">
                <p class="description">Dashicon name (e.g., dashboard, chart-bar)</p>
            </div>
        </div>
    `;

    $('#widget-settings-fields').html(fields);

    // Add event listener for table selection to load fields
    $('#widget-metric-table-id').on('change', function() {
        loadMetricFields($(this).val());
    });

    // Add event listener for metric type
    $('#widget-metric-type').on('change', function() {
        const metricType = $(this).val();
        if (metricType === 'count') {
            $('#metric-field-group').hide();
            $('#widget-metric-field').prop('required', false);
        } else {
            $('#metric-field-group').show();
            $('#widget-metric-field').prop('required', true);
        }
    });

    // Load fields if table is already selected
    if (tableId) {
        loadMetricFields(tableId);
    }
}

// Function to load metric fields
function loadMetricFields(tableId) {
    if (!tableId) return;

    // Show loading indicator
    $('#widget-metric-field').html('<option value="">Loading fields...</option>').prop('disabled', true);

    // Fetch table fields via AJAX
    $.ajax({
        url: dab_dashboard.ajax_url,
        type: 'POST',
        data: {
            action: 'dab_get_dashboard_table_fields',
            table_id: tableId,
            nonce: dab_dashboard.nonce
        },
        success: function(response) {
            if (response.success && response.data) {
                populateMetricFields(response.data);
            } else {
                $('#widget-metric-field').html('<option value="">Failed to load fields</option>');
            }
        },
        error: function() {
            $('#widget-metric-field').html('<option value="">Failed to load fields</option>');
        }
    });
}

// Function to populate metric fields
function populateMetricFields(fields) {
    if (!fields || !fields.length) {
        $('#widget-metric-field').html('<option value="">No fields available</option>');
        return;
    }

    const currentWidget = getCurrentWidget();
    const settings = currentWidget && currentWidget.settings ? currentWidget.settings : {};

    // Populate field dropdown (only numeric fields for non-count metrics)
    let fieldOptions = '<option value="">Select field</option>';
    fields.filter(field => ['number', 'integer', 'float', 'decimal'].includes(field.field_type)).forEach(field => {
        const isSelected = settings.field === field.field_slug;
        fieldOptions += `<option value="${field.field_slug}" ${isSelected ? 'selected' : ''}>${field.field_label}</option>`;
    });
    $('#widget-metric-field').html(fieldOptions).prop('disabled', false);
}

#### 4.3.2 Update Metric Widget Rendering
```javascript
// In assets/js/simple-dashboard-view.js
function renderMetricWidget(container, data) {
    console.log('Rendering metric widget with data:', data);

    // Get widget settings
    const widget = container.closest('.dab-dashboard-widget');
    const settings = widget.data('settings') || {};

    // Check if data is valid
    if (!data) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
        return;
    }

    // Check if value exists
    if (data.value === undefined || data.value === null) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
        return;
    }

    // Format value
    let value = data.value;
    const metricType = data.type || settings.metric_type || 'count';
    const decimalPlaces = settings.decimal_places !== undefined ? parseInt(settings.decimal_places) :
        (metricType === 'average' ? 2 : 0);

    if (typeof value === 'number') {
        if (decimalPlaces > 0) {
            value = parseFloat(value).toFixed(decimalPlaces);
        } else {
            value = parseInt(value).toLocaleString();
        }
    }

    // Get prefix and suffix
    const prefix = settings.prefix || '';
    const suffix = settings.suffix || '';

    // Get size class
    const sizeClass = settings.size ? `dab-metric-${settings.size}` : 'dab-metric-medium';

    // Get color style
    const colorStyle = settings.color ? `color: ${settings.color};` : '';

    // Get icon
    const iconHtml = settings.icon ?
        `<div class="dab-widget-metric-icon"><span class="dashicons dashicons-${settings.icon}"></span></div>` : '';

    // Build metric HTML
    const metricHtml = `
        <div class="dab-widget-metric ${sizeClass}">
            ${iconHtml}
            <div class="dab-widget-metric-content">
                <div class="dab-widget-metric-value" style="${colorStyle}">
                    <span class="dab-metric-prefix">${prefix}</span>
                    <span class="dab-metric-value">${value}</span>
                    <span class="dab-metric-suffix">${suffix}</span>
                </div>
                <div class="dab-widget-metric-label">${data.label || settings.label || ''}</div>
            </div>
        </div>
    `;

    container.html(metricHtml);
}
```

### 4.4 Improve Text Widget

#### 4.4.1 Update Text Widget Settings Form
```javascript
// In assets/js/simple-dashboard-builder.js
function addTextWidgetFields(widget) {
    console.log('Adding text widget fields with settings:', widget.settings);
    const settings = widget.settings || {};

    const fields = `
        <div class="dab-settings-section">
            <h3>Text Widget Settings</h3>

            <div class="dab-form-group">
                <label for="widget-text-content">Content</label>
                <textarea id="widget-text-content" class="regular-text" rows="8">${settings.content || ''}</textarea>
                <p class="description">Enter text or HTML content. You can use markdown syntax.</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-text-format">Format</label>
                <select id="widget-text-format" class="regular-text">
                    <option value="text" ${settings.format === 'text' || !settings.format ? 'selected' : ''}>Plain Text</option>
                    <option value="html" ${settings.format === 'html' ? 'selected' : ''}>HTML</option>
                    <option value="markdown" ${settings.format === 'markdown' ? 'selected' : ''}>Markdown</option>
                </select>
                <p class="description">How to interpret the content</p>
            </div>

            <div class="dab-form-group">
                <label for="widget-text-align">Text Alignment</label>
                <select id="widget-text-align" class="regular-text">
                    <option value="left" ${settings.align === 'left' || !settings.align ? 'selected' : ''}>Left</option>
                    <option value="center" ${settings.align === 'center' ? 'selected' : ''}>Center</option>
                    <option value="right" ${settings.align === 'right' ? 'selected' : ''}>Right</option>
                    <option value="justify" ${settings.align === 'justify' ? 'selected' : ''}>Justify</option>
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-text-size">Text Size</label>
                <select id="widget-text-size" class="regular-text">
                    <option value="small" ${settings.size === 'small' ? 'selected' : ''}>Small</option>
                    <option value="medium" ${settings.size === 'medium' || !settings.size ? 'selected' : ''}>Medium</option>
                    <option value="large" ${settings.size === 'large' ? 'selected' : ''}>Large</option>
                </select>
            </div>

            <div class="dab-form-group">
                <label for="widget-text-color">Text Color</label>
                <input type="text" id="widget-text-color" class="regular-text" value="${settings.color || ''}">
                <p class="description">Hex color code (e.g., #333333)</p>
            </div>
        </div>
    `;

    $('#widget-settings-fields').html(fields);
}

#### 4.4.2 Update Text Widget Rendering
```javascript
// In assets/js/simple-dashboard-view.js
function renderTextWidget(container, data) {
    console.log('Rendering text widget with data:', data);

    // Get widget settings
    const widget = container.closest('.dab-dashboard-widget');
    const settings = widget.data('settings') || {};

    // Check if data is valid
    if (!data) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.error}</p>`);
        return;
    }

    // Get content
    let content = data.content || '';
    if (!content) {
        container.html(`<p class="dab-no-data">${dab_dashboard.i18n.no_data}</p>`);
        return;
    }

    // Get format
    const format = settings.format || 'text';

    // Process content based on format
    if (format === 'markdown') {
        // Simple markdown processing (for a full solution, include a markdown library)
        content = content
            // Headers
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            // Bold
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Italic
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            // Links
            .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>')
            // Lists
            .replace(/^\s*\*\s(.*$)/gm, '<li>$1</li>')
            // Paragraphs
            .replace(/^\s*(\n)?([^\n]+)/gm, function(m) {
                return /^<(\/)?(h\d|p|li|div|pre|table|ul|ol)/.test(m) ? m : '<p>' + m + '</p>';
            })
            // Line breaks
            .replace(/\n/g, '<br>');
    } else if (format === 'text') {
        // Escape HTML and preserve line breaks
        content = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>');
    }
    // For HTML format, use content as is

    // Get alignment
    const align = settings.align || 'left';

    // Get size
    const size = settings.size || 'medium';

    // Get color
    const color = settings.color ? `color: ${settings.color};` : '';

    // Build text widget HTML
    const textHtml = `
        <div class="dab-widget-text dab-text-${size} dab-text-${align}" style="${color}">
            ${content}
        </div>
    `;

    container.html(textHtml);
}
```

## 5. Add CSS for Enhanced Widgets

```css
/* In assets/css/simple-dashboard-view.css */

/* Table Widget Styles */
.dab-widget-table-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dab-table-search {
    margin-bottom: 10px;
}

.dab-search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-widget-table-container {
    flex: 1;
    overflow: auto;
}

.dab-widget-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.dab-widget-table th {
    background-color: #f5f5f5;
    padding: 10px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    position: sticky;
    top: 0;
    z-index: 1;
}

.dab-widget-table th.dab-sortable {
    cursor: pointer;
    position: relative;
}

.dab-widget-table th.dab-sortable:after {
    content: "⇕";
    position: absolute;
    right: 8px;
    color: #999;
    font-size: 12px;
}

.dab-widget-table th.dab-sortable.asc:after {
    content: "↑";
}

.dab-widget-table th.dab-sortable.desc:after {
    content: "↓";
}

.dab-widget-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
}

.dab-widget-table tr:hover {
    background-color: #f9f9f9;
}

.dab-table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    font-size: 13px;
}

.dab-pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dab-pagination-prev,
.dab-pagination-next {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.dab-pagination-prev:disabled,
.dab-pagination-next:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Chart Widget Styles */
.dab-widget-chart-container {
    height: 100%;
    width: 100%;
    position: relative;
}

/* Metric Widget Styles */
.dab-widget-metric {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 15px;
    text-align: center;
}

.dab-widget-metric-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.dab-widget-metric-icon {
    margin-right: 15px;
}

.dab-widget-metric-icon .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #666;
}

.dab-widget-metric-value {
    font-weight: 600;
    line-height: 1.2;
}

.dab-widget-metric-label {
    color: #666;
    margin-top: 5px;
}

.dab-metric-small .dab-widget-metric-value {
    font-size: 24px;
}

.dab-metric-medium .dab-widget-metric-value {
    font-size: 32px;
}

.dab-metric-large .dab-widget-metric-value {
    font-size: 48px;
}

/* Text Widget Styles */
.dab-widget-text {
    height: 100%;
    overflow: auto;
    padding: 15px;
}

.dab-text-left {
    text-align: left;
}

.dab-text-center {
    text-align: center;
}

.dab-text-right {
    text-align: right;
}

.dab-text-justify {
    text-align: justify;
}

.dab-text-small {
    font-size: 13px;
}

.dab-text-medium {
    font-size: 16px;
}

.dab-text-large {
    font-size: 20px;
}
```
```
