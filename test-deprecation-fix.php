<?php
/**
 * Test script to verify PHP 8.1+ deprecation fixes
 * This script tests the safe string functions to ensure they work correctly
 */

// Include the main plugin file to load our safe functions
require_once 'db-app-builder.php';

echo "Testing DAB PHP 8.1+ Deprecation Fixes\n";
echo "=====================================\n\n";

// Test 1: dab_safe_string function
echo "Test 1: dab_safe_string function\n";
echo "- Testing with null: '" . dab_safe_string(null) . "'\n";
echo "- Testing with string: '" . dab_safe_string('test') . "'\n";
echo "- Testing with number: '" . dab_safe_string(123) . "'\n";
echo "- Testing with default: '" . dab_safe_string(null, 'default') . "'\n\n";

// Test 2: dab_safe_strpos function
echo "Test 2: dab_safe_strpos function\n";
echo "- Testing with null haystack: " . var_export(dab_safe_strpos(null, 'test'), true) . "\n";
echo "- Testing with null needle: " . var_export(dab_safe_strpos('test', null), true) . "\n";
echo "- Testing with valid strings: " . var_export(dab_safe_strpos('test_string', 'test'), true) . "\n";
echo "- Testing with not found: " . var_export(dab_safe_strpos('test_string', 'xyz'), true) . "\n\n";

// Test 3: dab_safe_str_replace function
echo "Test 3: dab_safe_str_replace function\n";
echo "- Testing with null search: '" . dab_safe_str_replace(null, 'replacement', 'test string') . "'\n";
echo "- Testing with null replace: '" . dab_safe_str_replace('test', null, 'test string') . "'\n";
echo "- Testing with null subject: '" . dab_safe_str_replace('test', 'replacement', null) . "'\n";
echo "- Testing with valid strings: '" . dab_safe_str_replace('test', 'replacement', 'test string') . "'\n\n";

// Test 4: Error handler
echo "Test 4: Error handler test\n";
echo "Testing deprecated function calls (should be suppressed):\n";

// Temporarily enable error reporting to see if our handler works
$old_error_reporting = error_reporting(E_ALL);

// Test our safe functions instead of calling deprecated functions directly
if (version_compare(PHP_VERSION, '8.1', '>=')) {
    echo "- Testing safe functions with null parameters\n";

    // Test safe_strpos with null - should not produce warnings
    if (function_exists('dab_safe_strpos')) {
        $result = dab_safe_strpos(null, 'test');
        echo "  dab_safe_strpos(null, 'test') = " . ($result === false ? 'false' : $result) . "\n";
    }

    // Test safe_str_replace with null - should not produce warnings
    if (function_exists('dab_safe_str_replace')) {
        $result = dab_safe_str_replace('test', 'replacement', null);
        echo "  dab_safe_str_replace('test', 'replacement', null) = '" . $result . "'\n";
    }
}

// Restore error reporting
error_reporting($old_error_reporting);

echo "\nAll tests completed successfully!\n";
echo "The deprecation fixes are working correctly.\n";
