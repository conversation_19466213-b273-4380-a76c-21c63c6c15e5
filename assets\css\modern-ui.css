/**
 * Database App Builder Modern UI
 * 
 * Modern UI components and layouts for the Database App Builder plugin
 */

/* Import Design System */
@import url('design-system.css');

/* Layout Components */
.dab-layout {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--dab-spacing-md) * -1);
  margin-left: calc(var(--dab-spacing-md) * -1);
}

.dab-col {
  position: relative;
  width: 100%;
  padding-right: var(--dab-spacing-md);
  padding-left: var(--dab-spacing-md);
}

.dab-col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.dab-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.dab-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.dab-col-3 { flex: 0 0 25%; max-width: 25%; }
.dab-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.dab-col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.dab-col-6 { flex: 0 0 50%; max-width: 50%; }
.dab-col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.dab-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.dab-col-9 { flex: 0 0 75%; max-width: 75%; }
.dab-col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.dab-col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.dab-col-12 { flex: 0 0 100%; max-width: 100%; }

/* Responsive Columns */
@media (min-width: 576px) {
  .dab-col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .dab-col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .dab-col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .dab-col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .dab-col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .dab-col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .dab-col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .dab-col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .dab-col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .dab-col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .dab-col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .dab-col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 768px) {
  .dab-col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .dab-col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .dab-col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .dab-col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .dab-col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .dab-col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .dab-col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .dab-col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .dab-col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .dab-col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .dab-col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .dab-col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 992px) {
  .dab-col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .dab-col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .dab-col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .dab-col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .dab-col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .dab-col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .dab-col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .dab-col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .dab-col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .dab-col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .dab-col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .dab-col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Tables */
.dab-table {
  width: 100%;
  margin-bottom: var(--dab-spacing-md);
  color: var(--dab-gray-700);
  border-collapse: collapse;
}

.dab-table th,
.dab-table td {
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  vertical-align: middle;
  border-top: 1px solid var(--dab-gray-300);
}

.dab-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--dab-gray-300);
  background-color: var(--dab-gray-100);
  color: var(--dab-gray-800);
  font-weight: 600;
}

.dab-table tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

.dab-table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.dab-table-bordered {
  border: 1px solid var(--dab-gray-300);
}

.dab-table-bordered th,
.dab-table-bordered td {
  border: 1px solid var(--dab-gray-300);
}

/* Alerts */
.dab-alert {
  position: relative;
  padding: var(--dab-spacing-md);
  margin-bottom: var(--dab-spacing-md);
  border: 1px solid transparent;
  border-radius: var(--dab-border-radius-md);
}

.dab-alert-primary {
  color: var(--dab-primary-dark);
  background-color: rgba(67, 97, 238, 0.1);
  border-color: rgba(67, 97, 238, 0.2);
}

.dab-alert-success {
  color: #155724;
  background-color: rgba(46, 196, 182, 0.1);
  border-color: rgba(46, 196, 182, 0.2);
}

.dab-alert-warning {
  color: #856404;
  background-color: rgba(255, 159, 28, 0.1);
  border-color: rgba(255, 159, 28, 0.2);
}

.dab-alert-danger {
  color: #721c24;
  background-color: rgba(231, 29, 54, 0.1);
  border-color: rgba(231, 29, 54, 0.2);
}

.dab-alert-info {
  color: #0c5460;
  background-color: rgba(76, 201, 240, 0.1);
  border-color: rgba(76, 201, 240, 0.2);
}

/* Badges */
.dab-badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--dab-border-radius-full);
  transition: color var(--dab-transition-normal) ease-in-out, background-color var(--dab-transition-normal) ease-in-out;
}

.dab-badge-primary {
  color: var(--dab-white);
  background-color: var(--dab-primary);
}

.dab-badge-success {
  color: var(--dab-white);
  background-color: var(--dab-success);
}

.dab-badge-warning {
  color: var(--dab-gray-900);
  background-color: var(--dab-warning);
}

.dab-badge-danger {
  color: var(--dab-white);
  background-color: var(--dab-danger);
}

.dab-badge-info {
  color: var(--dab-white);
  background-color: var(--dab-info);
}

/* Modals */
.dab-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--dab-z-index-modal);
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.dab-modal-open {
  display: block;
}

.dab-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--dab-z-index-modal-backdrop);
  width: 100vw;
  height: 100vh;
  background-color: var(--dab-black);
  opacity: 0.5;
}

.dab-modal-dialog {
  position: relative;
  width: auto;
  margin: 1.75rem auto;
  max-width: 500px;
  pointer-events: none;
  transform: translateY(-50px);
  transition: transform var(--dab-transition-normal) ease-out;
}

.dab-modal-open .dab-modal-dialog {
  transform: translateY(0);
}

.dab-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--dab-white);
  background-clip: padding-box;
  border: 1px solid var(--dab-gray-300);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-xl);
  outline: 0;
}

.dab-modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--dab-gray-300);
  border-top-left-radius: var(--dab-border-radius-md);
  border-top-right-radius: var(--dab-border-radius-md);
}

.dab-modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: var(--dab-font-size-xl);
  font-weight: 600;
}

.dab-modal-close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
  background-color: transparent;
  border: 0;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: var(--dab-gray-600);
  cursor: pointer;
}

.dab-modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.dab-modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid var(--dab-gray-300);
  border-bottom-right-radius: var(--dab-border-radius-md);
  border-bottom-left-radius: var(--dab-border-radius-md);
}

.dab-modal-footer > * {
  margin: 0.25rem;
}

/* Responsive Utilities */
@media (max-width: 576px) {
  .dab-hide-xs {
    display: none !important;
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .dab-hide-sm {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .dab-hide-md {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .dab-hide-lg {
    display: none !important;
  }
}
