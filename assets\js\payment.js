/**
 * Payment Field Scripts
 *
 * JavaScript for payment fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize payment fields
    function initPaymentFields() {
        $('.dab-payment-field').each(function() {
            var $field = $(this);
            var fieldId = $field.attr('id');
            var gateway = $field.data('gateway');

            // Handle Pay Now button click
            $field.find('.dab-payment-button').on('click', function() {
                var $button = $(this);
                var paymentId = $button.data('payment-id');
                var $form = $button.closest('form');

                // Validate all required fields before proceeding
                var isValid = true;
                var $firstInvalidField = null;

                // Check all required fields
                $form.find('[required]').each(function() {
                    var $input = $(this);

                    // Skip the payment field itself
                    if ($input.closest('.dab-payment-field').length > 0) {
                        return;
                    }

                    // Check if field is empty
                    if (!$input.val()) {
                        isValid = false;

                        // Add error class
                        $input.addClass('dab-field-error');

                        // Add error message if not already present
                        var $fieldContainer = $input.closest('.dab-form-field');
                        if ($fieldContainer.find('.dab-field-error-message').length === 0) {
                            $fieldContainer.append('<div class="dab-field-error-message">This field is required</div>');
                        }

                        // Store first invalid field for scrolling
                        if (!$firstInvalidField) {
                            $firstInvalidField = $input;
                        }
                    } else {
                        // Remove error class and message
                        $input.removeClass('dab-field-error');
                        $input.closest('.dab-form-field').find('.dab-field-error-message').remove();
                    }
                });

                // If form is not valid, scroll to first invalid field and stop
                if (!isValid) {
                    // Scroll to first invalid field
                    if ($firstInvalidField) {
                        $('html, body').animate({
                            scrollTop: $firstInvalidField.offset().top - 100
                        }, 500);
                    }

                    // Show error message in payment field
                    $field.find('.dab-payment-error').remove();
                    $field.append('<div class="dab-payment-error">Please fill in all required fields before proceeding with payment.</div>');

                    return;
                }

                // Remove any previous error messages
                $field.find('.dab-payment-error').remove();

                // Disable button to prevent multiple clicks
                $button.prop('disabled', true).text('Loading...');

                // Collect form data for field-based amount
                var formData = {
                    action: 'dab_get_payment_form',
                    field_id: $field.data('field-id'),
                    gateway: gateway,
                    nonce: dabPayment.nonce
                };

                // Add current amount if available
                var currentAmount = $field.data('current-amount');
                if (currentAmount) {
                    formData.amount = currentAmount;
                }

                // If amount type is field, add the field value
                var amountType = $field.data('amount-type');
                var amountFieldSlug = $field.data('amount-field');
                if (amountType === 'field' && amountFieldSlug) {
                    var $sourceField = $form.find('[name="' + amountFieldSlug + '"]');
                    if ($sourceField.length) {
                        formData.field_amount = $sourceField.val();
                    }
                }

                // If amount type is user_input, add the input value
                if (amountType === 'user_input') {
                    var $amountInput = $field.find('.dab-payment-amount-input input');
                    if ($amountInput.length) {
                        formData.user_amount = $amountInput.val();
                    }
                }

                // Get payment form via AJAX
                $.ajax({
                    url: dabPayment.ajaxUrl,
                    type: 'POST',
                    dataType: 'json',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Show payment form
                            $('#' + paymentId + '-form').html(response.data.form);

                            // Hide button
                            $button.hide();
                        } else {
                            // Show error
                            $field.append('<div class="dab-payment-error">' + response.data.message + '</div>');

                            // Re-enable button
                            $button.prop('disabled', false).text('Pay Now');
                        }
                    },
                    error: function() {
                        // Show error
                        $field.append('<div class="dab-payment-error">Error loading payment form. Please try again.</div>');

                        // Re-enable button
                        $button.prop('disabled', false).text('Pay Now');
                    }
                });
            });
        });

        // Listen for payment complete event
        $(document).on('dab_payment_complete', function(event, transactionId) {
            // Get the form containing the payment field
            var $form = $('.dab-payment-field').closest('form');

            // Check if auto-submit is enabled
            var autoSubmit = true; // Set to true to enable auto-submit

            if (autoSubmit) {
                // Show success message
                $form.append('<div class="dab-payment-success" style="margin-top: 15px;">Payment successful! Submitting form...</div>');

                // Submit the form automatically after a short delay
                setTimeout(function() {
                    $form.submit();
                }, 1500); // 1.5 second delay to show the success message
            } else {
                // Just enable the submit button
                $form.find('button[type="submit"]').prop('disabled', false);

                // Show message to user
                $form.append('<div class="dab-payment-success" style="margin-top: 15px;">Payment successful! You can now submit the form.</div>');
            }
        });
    }

    // Handle user input amount changes and field-based amount changes
    function handleAmountChanges() {
        // Handle user input amount changes
        $('.dab-payment-amount-input input').on('change', function() {
            var $input = $(this);
            var amount = parseFloat($input.val());

            // Validate amount
            if (isNaN(amount) || amount <= 0) {
                $input.val('0.00');

                // Disable payment button
                $input.closest('.dab-payment-field').find('.dab-payment-button').prop('disabled', true);
            } else {
                $input.val(amount.toFixed(2));

                // Enable payment button
                $input.closest('.dab-payment-field').find('.dab-payment-button').prop('disabled', false);
            }
        });

        // Trigger change on page load for user input fields
        $('.dab-payment-amount-input input').trigger('change');

        // Handle field-based amount changes
        $('.dab-payment-field').each(function() {
            var $paymentField = $(this);
            var amountType = $paymentField.data('amount-type');
            var amountFieldSlug = $paymentField.data('amount-field');

            if (amountType === 'field' && amountFieldSlug) {
                // Find the source field in the form
                var $form = $paymentField.closest('form');
                var $sourceField = $form.find('[name="' + amountFieldSlug + '"]');

                if ($sourceField.length) {
                    // Add event listener to the source field
                    $sourceField.on('change keyup', function() {
                        updatePaymentAmountFromField($paymentField, $sourceField);
                    });

                    // Initial update
                    updatePaymentAmountFromField($paymentField, $sourceField);
                }
            }
        });
    }

    // Function to update payment amount from a source field
    function updatePaymentAmountFromField($paymentField, $sourceField) {
        var amount = parseFloat($sourceField.val());

        // Validate amount
        if (isNaN(amount) || amount <= 0) {
            amount = 0;
            // Disable payment button if amount is invalid
            $paymentField.find('.dab-payment-button').prop('disabled', true);
        } else {
            // Enable payment button
            $paymentField.find('.dab-payment-button').prop('disabled', false);
        }

        // Update the amount display
        var $amountDisplay = $paymentField.find('.dab-payment-amount');
        if ($amountDisplay.length) {
            $amountDisplay.text(amount.toFixed(2));
        }

        // Update the hidden amount input
        var $hiddenInput = $paymentField.find('input[name$="_amount"]');
        if ($hiddenInput.length) {
            $hiddenInput.val(amount.toFixed(2));
        }

        // Store the current amount as a data attribute for later use
        $paymentField.data('current-amount', amount.toFixed(2));
    }

    // Initialize on document ready
    $(document).ready(function() {
        initPaymentFields();
        handleAmountChanges();

        // Disable form submission until payment is complete
        $('.dab-form').each(function() {
            var $form = $(this);

            // Check if form has payment fields
            if ($form.find('.dab-payment-field').length > 0) {
                $form.on('submit', function(e) {
                    var $paymentFields = $form.find('.dab-payment-field');
                    var paymentComplete = true;

                    $paymentFields.each(function() {
                        var $field = $(this);
                        var $statusField = $field.find('input[type="hidden"]');

                        // Check if payment is complete
                        if ($statusField.val() === '') {
                            paymentComplete = false;

                            // Show error
                            if (!$field.find('.dab-payment-error').length) {
                                $field.append('<div class="dab-payment-error">Please complete the payment before submitting the form.</div>');
                            }

                            // Scroll to payment field
                            $('html, body').animate({
                                scrollTop: $field.offset().top - 100
                            }, 500);
                        }
                    });

                    // Prevent form submission if payment is not complete
                    if (!paymentComplete) {
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    });
})(jQuery);
