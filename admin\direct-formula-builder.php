<?php
/**
 * Direct Formula Builder
 *
 * This file provides a direct implementation of the visual formula builder
 * that doesn't rely on dynamic JavaScript loading.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

/**
 * Add the visual formula builder to the admin page
 */
function dab_add_visual_formula_builder() {
    // Check if we're on the fields page
    $current_page = isset($_GET['page']) ? $_GET['page'] : '';
    if ($current_page !== 'dab_fields') {
        return;
    }

    // Enqueue the CSS
    wp_enqueue_style(
        'dab-visual-formula-builder',
        plugin_dir_url(dirname(__FILE__)) . 'assets/css/visual-formula-builder.css',
        array(),
        DAB_VERSION
    );

    // Add the HTML for the visual formula builder
    add_action('admin_footer', 'dab_output_visual_formula_builder_html');
}
add_action('admin_init', 'dab_add_visual_formula_builder');

/**
 * Output the HTML for the visual formula builder
 */
function dab_output_visual_formula_builder_html() {
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Initialize the formula builder when the field type is changed to formula
        $('#field_type').on('change', function() {
            if ($(this).val() === 'formula') {
                initVisualFormulaBuilder();
            }
        });

        // Also initialize if the page loads with formula selected
        if ($('#field_type').val() === 'formula') {
            initVisualFormulaBuilder();
        }

        /**
         * Initialize the visual formula builder
         */
        function initVisualFormulaBuilder() {
            // Get the formula input field
            const formulaInput = $('#formula_expression');
            if (!formulaInput.length) return;

            // Check if the builder already exists
            if ($('.dab-visual-formula-builder').length) return;

            // Create the visual builder container
            const visualBuilder = $('<div class="dab-visual-formula-builder"></div>');
            visualBuilder.insertAfter(formulaInput);

            // Create the formula preview
            const formulaPreview = $('<div class="dab-formula-preview"><strong>Formula Preview:</strong> <span class="preview-text"></span></div>');
            visualBuilder.append(formulaPreview);

            // Create the field selector
            createFieldSelector(visualBuilder);

            // Create the operator buttons
            createOperatorButtons(visualBuilder);

            // Create the function buttons
            createFunctionButtons(visualBuilder);

            // Create the clear button
            const clearButton = $('<button type="button" class="button dab-clear-formula">Clear Formula</button>');
            visualBuilder.append(clearButton);

            // Add event listener to clear button
            clearButton.on('click', function() {
                formulaInput.val('');
                updateFormulaPreview();
            });

            // Update the formula preview initially
            updateFormulaPreview();

            // Add event listener to formula input for manual changes
            formulaInput.on('input', updateFormulaPreview);
        }

        /**
         * Create the field selector
         */
        function createFieldSelector(container) {
            // Create the field selector container
            const fieldSelectorContainer = $('<div class="dab-field-selector-container"></div>');
            container.append(fieldSelectorContainer);

            // Create the field selector heading
            const fieldSelectorHeading = $('<h4>Available Fields</h4>');
            fieldSelectorContainer.append(fieldSelectorHeading);

            // Create the field selector
            const fieldSelector = $('<div class="dab-field-selector"></div>');
            fieldSelectorContainer.append(fieldSelector);

            // Get all fields from the current table
            const fields = [];
            $('#field-list tr').each(function() {
                const row = $(this);
                const fieldSlug = row.data('slug');
                const fieldLabel = row.find('td:first-child').text().trim();
                const fieldType = row.find('td:nth-child(3)').text().trim().toLowerCase();

                // Only add numeric fields or fields that can be used in calculations
                if (fieldSlug && (fieldType === 'number' || fieldType === 'currency' ||
                    fieldType === 'formula' || fieldType === 'rollup' ||
                    fieldType === 'checkbox' || fieldType === 'rating')) {
                    fields.push({
                        slug: fieldSlug,
                        label: fieldLabel,
                        type: fieldType
                    });
                }
            });

            // Create field buttons
            if (fields.length > 0) {
                fields.forEach(function(field) {
                    const fieldButton = $('<button type="button" class="button dab-field-button"></button>');
                    fieldButton.text(field.label);
                    fieldButton.attr('data-field', field.slug);
                    fieldButton.attr('data-type', field.type);
                    fieldButton.attr('title', field.label + ' (' + field.type + ')');

                    // Add icon based on field type
                    const icon = getFieldTypeIcon(field.type);
                    if (icon) {
                        fieldButton.prepend($(icon));
                    }

                    fieldSelector.append(fieldButton);

                    // Add event listener to field button
                    fieldButton.on('click', function() {
                        insertFieldReference(field.slug);
                    });
                });
            } else {
                // If no fields are available, show a message
                const noFieldsMessage = $('<p class="dab-no-fields-message">No numeric fields available. Add number fields to use in formulas.</p>');
                fieldSelector.append(noFieldsMessage);
            }
        }

        /**
         * Create the operator buttons
         */
        function createOperatorButtons(container) {
            const operators = [
                { symbol: '+', label: 'Add' },
                { symbol: '-', label: 'Subtract' },
                { symbol: '*', label: 'Multiply' },
                { symbol: '/', label: 'Divide' },
                { symbol: '(', label: 'Open Parenthesis' },
                { symbol: ')', label: 'Close Parenthesis' }
            ];

            // Create the operator container
            const operatorContainer = $('<div class="dab-operator-container"></div>');
            container.append(operatorContainer);

            // Create the operator heading
            const operatorHeading = $('<h4>Operators</h4>');
            operatorContainer.append(operatorHeading);

            // Create the operator buttons
            const operatorButtons = $('<div class="dab-operator-buttons"></div>');
            operatorContainer.append(operatorButtons);

            operators.forEach(function(operator) {
                const button = $('<button type="button" class="button dab-operator-button"></button>');
                button.text(operator.symbol);
                button.attr('title', operator.label);
                operatorButtons.append(button);

                // Add event listener to operator button
                button.on('click', function() {
                    insertOperator(operator.symbol);
                });
            });
        }

        /**
         * Create the function buttons
         */
        function createFunctionButtons(container) {
            const functions = [
                { name: 'SUM', description: 'Sum of values' },
                { name: 'AVG', description: 'Average of values' },
                { name: 'MIN', description: 'Minimum value' },
                { name: 'MAX', description: 'Maximum value' },
                { name: 'ROUND', description: 'Round to nearest integer' },
                { name: 'FLOOR', description: 'Round down' },
                { name: 'CEIL', description: 'Round up' },
                { name: 'ABS', description: 'Absolute value' }
            ];

            // Create the function container
            const functionContainer = $('<div class="dab-function-container"></div>');
            container.append(functionContainer);

            // Create the function heading
            const functionHeading = $('<h4>Functions</h4>');
            functionContainer.append(functionHeading);

            // Create the function buttons
            const functionButtons = $('<div class="dab-function-buttons"></div>');
            functionContainer.append(functionButtons);

            functions.forEach(function(func) {
                const button = $('<button type="button" class="button dab-function-button"></button>');
                button.text(func.name);
                button.attr('title', func.description);
                functionButtons.append(button);

                // Add event listener to function button
                button.on('click', function() {
                    insertFunction(func.name);
                });
            });
        }

        /**
         * Insert a field reference into the formula
         */
        function insertFieldReference(fieldSlug) {
            const formulaInput = $('#formula_expression');
            const cursorPos = formulaInput[0].selectionStart;
            const currentValue = formulaInput.val();
            const fieldReference = `{${fieldSlug}}`;

            // Insert the field reference at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                fieldReference +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position
            formulaInput[0].selectionStart = cursorPos + fieldReference.length;
            formulaInput[0].selectionEnd = cursorPos + fieldReference.length;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        }

        /**
         * Insert an operator into the formula
         */
        function insertOperator(operator) {
            const formulaInput = $('#formula_expression');
            const cursorPos = formulaInput[0].selectionStart;
            const currentValue = formulaInput.val();

            // Insert the operator at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                operator +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position
            formulaInput[0].selectionStart = cursorPos + operator.length;
            formulaInput[0].selectionEnd = cursorPos + operator.length;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        }

        /**
         * Insert a function into the formula
         */
        function insertFunction(functionName) {
            const formulaInput = $('#formula_expression');
            const cursorPos = formulaInput[0].selectionStart;
            const currentValue = formulaInput.val();
            const functionText = `${functionName}()`;

            // Insert the function at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                functionText +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position to be inside the parentheses
            formulaInput[0].selectionStart = cursorPos + functionName.length + 1;
            formulaInput[0].selectionEnd = cursorPos + functionName.length + 1;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        }

        /**
         * Update the formula preview
         */
        function updateFormulaPreview() {
            const formula = $('#formula_expression').val();
            const previewText = $('.dab-formula-preview .preview-text');

            // Format the formula for display
            let formattedFormula = formula;

            // Replace field references with field labels
            formattedFormula = formattedFormula.replace(/{([^}]+)}/g, function(match, fieldSlug) {
                const fieldButton = $(`.dab-field-button[data-field="${fieldSlug}"]`);
                if (fieldButton.length) {
                    return `<span class="field-reference">${fieldButton.text()}</span>`;
                }
                return match;
            });

            // Highlight operators
            formattedFormula = formattedFormula.replace(/([+\-*/()])/g, '<span class="operator">$1</span>');

            // Highlight functions
            const functions = ['SUM', 'AVG', 'MIN', 'MAX', 'ROUND', 'FLOOR', 'CEIL', 'ABS'];
            functions.forEach(function(func) {
                const regex = new RegExp(`(${func})\\(`, 'g');
                formattedFormula = formattedFormula.replace(regex, '<span class="function">$1</span>(');
            });

            // Update the preview
            previewText.html(formattedFormula || 'No formula yet');
        }

        /**
         * Get an icon for a field type
         */
        function getFieldTypeIcon(fieldType) {
            switch (fieldType) {
                case 'number':
                    return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg> ';
                case 'currency':
                    return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg> ';
                case 'formula':
                    return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline></svg> ';
                case 'checkbox':
                    return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path></svg> ';
                default:
                    return '';
            }
        }
    });
    </script>
    <?php
}
