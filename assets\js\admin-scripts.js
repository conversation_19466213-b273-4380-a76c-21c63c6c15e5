
document.addEventListener("DOMContentLoaded", function () {
    const lookupTable = document.getElementById("lookup_table_id");
    const lookupColumn = document.getElementById("lookup_display_column");

    if (lookupTable && lookupColumn) {
        // Clear the display column when table changes
        lookupTable.addEventListener("change", function () {
            const tableId = this.value;

            // Reset the display column dropdown if no table is selected
            if (!tableId) {
                lookupColumn.innerHTML = '<option value="">-- Select Display Column --</option>';
                return;
            }

            lookupColumn.innerHTML = '<option>Loading...</option>';

            // Use GET request for simplicity and to avoid nonce issues
            fetch(dab_vars.ajaxurl + "?action=dab_get_table_fields&table_id=" + tableId)
                .then(res => res.json())
                .then(data => {
                    console.log("AJAX Response:", data); // Debug log

                    lookupColumn.innerHTML = '<option value="">-- Select Display Column --</option>';

                    // Handle different response formats
                    let fieldsData = [];

                    if (data.success && Array.isArray(data.data)) {
                        // New API format with success property
                        fieldsData = data.data;
                    } else if (Array.isArray(data)) {
                        // Legacy API format (direct array)
                        fieldsData = data;
                    } else if (typeof data === 'object' && !data.success) {
                        // Error response
                        console.error("Error response:", data); // Debug log
                        lookupColumn.innerHTML = '<option value="">No fields available: ' + (data.message || 'Unknown error') + '</option>';
                        return;
                    }

                    if (fieldsData.length > 0) {
                        // Add fields as options
                        fieldsData.forEach(f => {
                            const opt = document.createElement("option");
                            opt.value = f.field_slug;
                            opt.textContent = f.field_label;
                            lookupColumn.appendChild(opt);
                        });
                    } else {
                        lookupColumn.innerHTML = '<option value="">No fields available in this table</option>';
                    }
                })
                .catch(error => {
                    console.error("AJAX Error:", error); // Debug log
                    lookupColumn.innerHTML = '<option value="">Error loading fields</option>';
                });
        });
    }
});

