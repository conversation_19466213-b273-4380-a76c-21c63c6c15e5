<?php
/**
 * Advanced Report Builder Class
 * Phase 3: Data Intelligence & Analytics
 *
 * Enhanced visual report builder with drag-and-drop interface,
 * advanced filtering, pivot tables, and automated report generation.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Advanced_Report_Builder {

    /**
     * Initialize the Advanced Report Builder
     */
    public static function init() {
        add_action('wp_ajax_dab_save_advanced_report', array(__CLASS__, 'save_report'));
        add_action('wp_ajax_dab_load_advanced_report', array(__CLASS__, 'load_report'));
        add_action('wp_ajax_dab_execute_advanced_report', array(__CLASS__, 'execute_report'));
        add_action('wp_ajax_dab_get_report_preview', array(__CLASS__, 'get_report_preview'));
        add_action('wp_ajax_dab_export_report', array(__CLASS__, 'export_report'));
        add_action('wp_ajax_dab_duplicate_report', array(__CLASS__, 'duplicate_report'));
        add_action('wp_ajax_dab_get_table_schema', array(__CLASS__, 'get_table_schema'));
        add_action('wp_ajax_dab_validate_report_query', array(__CLASS__, 'validate_report_query'));

        // Frontend AJAX for public reports
        add_action('wp_ajax_nopriv_dab_execute_advanced_report', array(__CLASS__, 'execute_report'));
        add_action('wp_ajax_nopriv_dab_export_report', array(__CLASS__, 'export_report'));
    }

    /**
     * Create database tables for advanced reporting
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Advanced reports table
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';
        $sql = "CREATE TABLE IF NOT EXISTS $reports_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            report_type VARCHAR(50) DEFAULT 'table',
            data_sources TEXT,
            query_config LONGTEXT,
            visualization_config LONGTEXT,
            filter_config LONGTEXT,
            layout_config LONGTEXT,
            permissions_config TEXT,
            is_public TINYINT(1) DEFAULT 0,
            is_scheduled TINYINT(1) DEFAULT 0,
            schedule_config TEXT,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_executed DATETIME NULL,
            execution_count INT DEFAULT 0,
            PRIMARY KEY (id),
            KEY idx_created_by (created_by),
            KEY idx_report_type (report_type),
            KEY idx_is_public (is_public),
            KEY idx_is_scheduled (is_scheduled)
        ) $charset_collate;";

        // Report executions log table
        $executions_table = $wpdb->prefix . 'dab_report_executions';
        $sql .= "CREATE TABLE IF NOT EXISTS $executions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            report_id BIGINT(20) UNSIGNED NOT NULL,
            executed_by BIGINT(20) UNSIGNED,
            execution_time FLOAT,
            row_count INT,
            export_format VARCHAR(20),
            filters_applied TEXT,
            status VARCHAR(20) DEFAULT 'success',
            error_message TEXT,
            executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_report_id (report_id),
            KEY idx_executed_by (executed_by),
            KEY idx_executed_at (executed_at),
            FOREIGN KEY (report_id) REFERENCES $reports_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Report templates table
        $templates_table = $wpdb->prefix . 'dab_report_templates';
        $sql .= "CREATE TABLE IF NOT EXISTS $templates_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(100),
            template_config LONGTEXT,
            preview_image VARCHAR(255),
            is_system TINYINT(1) DEFAULT 0,
            usage_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_category (category),
            KEY idx_is_system (is_system)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default report templates
        self::insert_default_templates();
    }

    /**
     * Insert default report templates
     */
    private static function insert_default_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_report_templates';

        $default_templates = array(
            array(
                'name' => 'Data Summary Report',
                'description' => 'Basic data summary with counts and totals',
                'category' => 'basic',
                'template_config' => json_encode(array(
                    'report_type' => 'table',
                    'columns' => array('*'),
                    'aggregations' => array('count', 'sum'),
                    'grouping' => array(),
                    'sorting' => array('created_at' => 'DESC')
                )),
                'is_system' => 1
            ),
            array(
                'name' => 'Trend Analysis Report',
                'description' => 'Time-based trend analysis with charts',
                'category' => 'analytics',
                'template_config' => json_encode(array(
                    'report_type' => 'chart',
                    'chart_type' => 'line',
                    'time_field' => 'created_at',
                    'grouping' => array('DATE(created_at)'),
                    'aggregations' => array('count')
                )),
                'is_system' => 1
            ),
            array(
                'name' => 'Performance Dashboard',
                'description' => 'KPI dashboard with multiple metrics',
                'category' => 'dashboard',
                'template_config' => json_encode(array(
                    'report_type' => 'dashboard',
                    'widgets' => array(
                        array('type' => 'metric', 'title' => 'Total Records'),
                        array('type' => 'chart', 'title' => 'Trend Over Time'),
                        array('type' => 'table', 'title' => 'Recent Activity')
                    )
                )),
                'is_system' => 1
            )
        );

        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $templates_table WHERE name = %s AND is_system = 1",
                $template['name']
            ));

            if (!$existing) {
                $wpdb->insert($templates_table, $template);
            }
        }
    }

    /**
     * Save report configuration
     */
    public static function save_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';

        $report_id = intval($_POST['report_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $report_type = sanitize_text_field($_POST['report_type']);
        $data_sources = $_POST['data_sources'];
        $query_config = $_POST['query_config'];
        $visualization_config = $_POST['visualization_config'];
        $filter_config = $_POST['filter_config'];
        $layout_config = $_POST['layout_config'];
        $permissions_config = $_POST['permissions_config'];
        $is_public = intval($_POST['is_public']);
        $is_scheduled = intval($_POST['is_scheduled']);
        $schedule_config = $_POST['schedule_config'];

        $data = array(
            'name' => $name,
            'description' => $description,
            'report_type' => $report_type,
            'data_sources' => json_encode($data_sources),
            'query_config' => json_encode($query_config),
            'visualization_config' => json_encode($visualization_config),
            'filter_config' => json_encode($filter_config),
            'layout_config' => json_encode($layout_config),
            'permissions_config' => json_encode($permissions_config),
            'is_public' => $is_public,
            'is_scheduled' => $is_scheduled,
            'schedule_config' => json_encode($schedule_config)
        );

        if ($report_id > 0) {
            // Update existing report
            $result = $wpdb->update($reports_table, $data, array('id' => $report_id));
            $report_id = $report_id;
        } else {
            // Create new report
            $data['created_by'] = get_current_user_id();
            $result = $wpdb->insert($reports_table, $data);
            $report_id = $wpdb->insert_id;
        }

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Report saved successfully',
                'report_id' => $report_id
            ));
        } else {
            wp_send_json_error('Failed to save report');
        }
    }

    /**
     * Load report configuration
     */
    public static function load_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $report_id = intval($_POST['report_id']);

        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';

        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));

        if ($report) {
            // Decode JSON fields
            $report->data_sources = json_decode($report->data_sources, true);
            $report->query_config = json_decode($report->query_config, true);
            $report->visualization_config = json_decode($report->visualization_config, true);
            $report->filter_config = json_decode($report->filter_config, true);
            $report->layout_config = json_decode($report->layout_config, true);
            $report->permissions_config = json_decode($report->permissions_config, true);
            $report->schedule_config = json_decode($report->schedule_config, true);

            wp_send_json_success($report);
        } else {
            wp_send_json_error('Report not found');
        }
    }

    /**
     * Execute report and return results
     */
    public static function execute_report() {
        $start_time = microtime(true);

        // Verify nonce for admin requests
        if (is_admin() && (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce'))) {
            wp_send_json_error('Security check failed');
            return;
        }

        $report_id = intval($_POST['report_id']);
        $filters = isset($_POST['filters']) ? $_POST['filters'] : array();
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 50;

        try {
            $result = self::build_and_execute_report($report_id, $filters, $page, $per_page);

            $execution_time = microtime(true) - $start_time;

            // Log execution
            self::log_report_execution($report_id, $execution_time, count($result['data']), null, $filters);

            wp_send_json_success($result);
        } catch (Exception $e) {
            $execution_time = microtime(true) - $start_time;

            // Log failed execution
            self::log_report_execution($report_id, $execution_time, 0, null, $filters, 'error', $e->getMessage());

            wp_send_json_error('Report execution failed: ' . $e->getMessage());
        }
    }

    /**
     * Get report preview with limited data
     */
    public static function get_report_preview() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $report_config = $_POST['report_config'];

        try {
            // Execute with limited results for preview
            $result = self::execute_report_config($report_config, array(), 1, 10);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Preview generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Export report in various formats
     */
    public static function export_report() {
        $start_time = microtime(true);

        // Verify nonce for admin requests
        if (is_admin() && (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce'))) {
            wp_send_json_error('Security check failed');
            return;
        }

        $report_id = intval($_POST['report_id']);
        $format = sanitize_text_field($_POST['format']);
        $filters = isset($_POST['filters']) ? $_POST['filters'] : array();

        try {
            $result = self::build_and_execute_report($report_id, $filters, 1, -1); // Get all data

            $execution_time = microtime(true) - $start_time;

            // Log execution
            self::log_report_execution($report_id, $execution_time, count($result['data']), $format, $filters);

            switch ($format) {
                case 'csv':
                    self::export_to_csv($result, $report_id);
                    break;
                case 'excel':
                    self::export_to_excel($result, $report_id);
                    break;
                case 'pdf':
                    self::export_to_pdf($result, $report_id);
                    break;
                default:
                    wp_send_json_error('Unsupported export format');
            }
        } catch (Exception $e) {
            $execution_time = microtime(true) - $start_time;
            self::log_report_execution($report_id, $execution_time, 0, $format, $filters, 'error', $e->getMessage());
            wp_send_json_error('Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Duplicate an existing report
     */
    public static function duplicate_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $report_id = intval($_POST['report_id']);

        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';

        $original_report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));

        if ($original_report) {
            // Create duplicate with modified name
            $new_name = $original_report->name . ' (Copy)';

            $data = array(
                'name' => $new_name,
                'description' => $original_report->description,
                'report_type' => $original_report->report_type,
                'data_sources' => $original_report->data_sources,
                'query_config' => $original_report->query_config,
                'visualization_config' => $original_report->visualization_config,
                'filter_config' => $original_report->filter_config,
                'layout_config' => $original_report->layout_config,
                'permissions_config' => $original_report->permissions_config,
                'is_public' => 0, // Make copy private by default
                'is_scheduled' => 0, // Disable scheduling for copy
                'schedule_config' => '{}',
                'created_by' => get_current_user_id()
            );

            $result = $wpdb->insert($reports_table, $data);

            if ($result !== false) {
                wp_send_json_success(array(
                    'message' => 'Report duplicated successfully',
                    'new_report_id' => $wpdb->insert_id
                ));
            } else {
                wp_send_json_error('Failed to duplicate report');
            }
        } else {
            wp_send_json_error('Original report not found');
        }
    }

    /**
     * Get table schema for report builder
     */
    public static function get_table_schema() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $table_name = sanitize_text_field($_POST['table_name']);

        try {
            $schema = self::analyze_table_schema($table_name);
            wp_send_json_success($schema);
        } catch (Exception $e) {
            wp_send_json_error('Failed to get table schema: ' . $e->getMessage());
        }
    }

    /**
     * Validate report query before execution
     */
    public static function validate_report_query() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $query_config = $_POST['query_config'];

        try {
            $validation_result = self::validate_query_config($query_config);
            wp_send_json_success($validation_result);
        } catch (Exception $e) {
            wp_send_json_error('Query validation failed: ' . $e->getMessage());
        }
    }

    /**
     * Build and execute report based on configuration
     */
    private static function build_and_execute_report($report_id, $filters = array(), $page = 1, $per_page = 50) {
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';

        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));

        if (!$report) {
            throw new Exception('Report not found');
        }

        // Check permissions
        if (!self::check_report_permissions($report)) {
            throw new Exception('Insufficient permissions to access this report');
        }

        $query_config = json_decode($report->query_config, true);
        $visualization_config = json_decode($report->visualization_config, true);

        return self::execute_report_config($query_config, $filters, $page, $per_page, $visualization_config);
    }

    /**
     * Execute report configuration
     */
    private static function execute_report_config($query_config, $filters = array(), $page = 1, $per_page = 50, $visualization_config = array()) {
        global $wpdb;

        // Build SQL query from configuration
        $sql = self::build_sql_from_config($query_config, $filters);

        // Add pagination if needed
        if ($per_page > 0) {
            $offset = ($page - 1) * $per_page;
            $sql .= " LIMIT $offset, $per_page";
        }

        // Execute query
        $data = $wpdb->get_results($sql, ARRAY_A);

        // Get total count for pagination
        $count_sql = self::build_count_sql_from_config($query_config, $filters);
        $total_count = $wpdb->get_var($count_sql);

        // Process data based on visualization config
        if (!empty($visualization_config)) {
            $data = self::process_data_for_visualization($data, $visualization_config);
        }

        return array(
            'data' => $data,
            'total_count' => intval($total_count),
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => $per_page > 0 ? ceil($total_count / $per_page) : 1
        );
    }
}
