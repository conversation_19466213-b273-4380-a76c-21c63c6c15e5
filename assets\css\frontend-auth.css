/**
 * Frontend Authentication Styles
 * 
 * Styles for login, registration, and authentication forms
 */

/* Base Styles */
.dab-auth-container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Form Container Styles */
.dab-login-container,
.dab-register-container {
    max-width: 400px;
    margin: 40px auto;
    padding: 20px;
}

.dab-register-container {
    max-width: 500px;
}

.dab-login-form-wrapper,
.dab-register-form-wrapper {
    background: #ffffff;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    position: relative;
}

/* Title Styles */
.dab-login-title,
.dab-register-title {
    text-align: center;
    margin-bottom: 30px;
    color: #1a1a1a;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

/* Form Styles */
.dab-form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 0;
}

.dab-form-col-6 {
    flex: 1;
}

.dab-form-group {
    margin-bottom: 24px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.dab-form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background-color: #ffffff;
    box-sizing: border-box;
}

.dab-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: #ffffff;
}

.dab-form-control:invalid {
    border-color: #ef4444;
}

.dab-form-control::placeholder {
    color: #9ca3af;
}

/* Checkbox Styles */
.dab-form-checkbox {
    margin-bottom: 20px;
}

.dab-form-checkbox label {
    display: flex;
    align-items: flex-start;
    font-weight: 400;
    line-height: 1.5;
    cursor: pointer;
}

.dab-form-checkbox input[type="checkbox"] {
    margin-right: 12px;
    margin-top: 2px;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    accent-color: #3b82f6;
}

/* Button Styles */
.dab-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 24px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: none;
    position: relative;
    overflow: hidden;
}

.dab-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-color: #3b82f6;
}

.dab-btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    border-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dab-btn-secondary {
    background-color: #6b7280;
    color: white;
    border-color: #6b7280;
}

.dab-btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.dab-btn-block {
    width: 100%;
}

.dab-btn-loading {
    display: none;
}

.dab-btn.loading .dab-btn-text {
    display: none;
}

.dab-btn.loading .dab-btn-loading {
    display: inline-flex;
    align-items: center;
}

.dab-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Spinner */
.dab-spinner {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: dab-spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes dab-spin {
    to { transform: rotate(360deg); }
}

/* Alert Styles */
.dab-alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
}

.dab-alert-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.dab-alert-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

.dab-alert-warning {
    background-color: #fffbeb;
    border: 1px solid #fed7aa;
    color: #d97706;
}

/* Link Styles */
.dab-login-links,
.dab-register-links {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.dab-login-links p,
.dab-register-links p {
    margin: 12px 0;
    color: #6b7280;
}

.dab-login-links a,
.dab-register-links a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.dab-login-links a:hover,
.dab-register-links a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Required Field Indicator */
.required {
    color: #ef4444;
    font-weight: 700;
}

/* Form Help Text */
.dab-form-help {
    display: block;
    margin-top: 6px;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.4;
}

/* Modal Styles */
.dab-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-content {
    background-color: #ffffff;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: dab-modal-appear 0.2s ease-out;
}

@keyframes dab-modal-appear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dab-modal-header {
    padding: 24px 32px 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9fafb;
}

.dab-modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 700;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-close:hover {
    color: #374151;
    background-color: #e5e7eb;
}

.dab-modal-body {
    padding: 32px;
}

/* Auth Required Message */
.dab-auth-required {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    max-width: 400px;
    margin: 40px auto;
}

.dab-auth-required p {
    margin-bottom: 24px;
    color: #6b7280;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 640px) {
    .dab-login-container,
    .dab-register-container {
        padding: 16px;
        margin: 20px auto;
    }
    
    .dab-login-form-wrapper,
    .dab-register-form-wrapper {
        padding: 24px;
    }
    
    .dab-login-title,
    .dab-register-title {
        font-size: 24px;
        margin-bottom: 24px;
    }
    
    .dab-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .dab-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .dab-modal-header,
    .dab-modal-body {
        padding: 20px;
    }
    
    .dab-auth-required {
        padding: 40px 24px;
        margin: 20px auto;
    }
}

@media (max-width: 480px) {
    .dab-form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .dab-btn {
        padding: 16px 24px;
        font-size: 16px;
    }
}
