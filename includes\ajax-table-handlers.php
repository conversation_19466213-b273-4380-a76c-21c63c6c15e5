<?php
/**
 * AJAX Table Handlers
 *
 * Handles AJAX requests for table management
 */
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * AJAX handler for creating tables
 */
function dab_ajax_create_table() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_create_table_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Permission denied');
        return;
    }

    // Parse form data
    parse_str($_POST['form_data'], $form_data);

    // Validate required fields
    if (empty($form_data['table_label']) || empty($form_data['table_slug'])) {
        wp_send_json_error('Table name and slug are required');
        return;
    }

    // Sanitize input
    $label = sanitize_text_field($form_data['table_label']);
    $slug = sanitize_title($form_data['table_slug']);
    $description = isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '';

    global $wpdb;
    $tables_table = $wpdb->prefix . 'dab_tables';

    // Check if table slug already exists
    $existing_table = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM $tables_table WHERE table_slug = %s",
        $slug
    ));

    if ($existing_table) {
        wp_send_json_error('A table with this slug already exists. Please choose a different slug.');
        return;
    }

    // Create new table
    $wpdb->insert($tables_table, [
        'table_label' => $label,
        'table_slug' => $slug,
        'description' => $description,
        'created_at' => current_time('mysql'),
    ]);

    // Get the new table ID
    $table_id = $wpdb->insert_id;

    if (!$table_id) {
        wp_send_json_error('Failed to create table. Database error.');
        return;
    }

    // Automatically create data table
    DAB_DB_Manager::create_data_table($slug);

    // Automatically create default fields based on table name
    $created_fields = DAB_DB_Manager::create_default_fields($table_id, $slug);

    $field_count = count($created_fields);
    $field_message = $field_count > 0 ? " with $field_count default fields" : "";

    // Get the newly created table data
    $new_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $table_id
    ), ARRAY_A);

    // Send success response
    wp_send_json_success([
        'message' => "Table created successfully$field_message and corresponding data table was initialized.",
        'table' => $new_table
    ]);
}
add_action('wp_ajax_dab_ajax_create_table', 'dab_ajax_create_table');

/**
 * AJAX handler for updating tables
 */
function dab_ajax_update_table() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_update_table_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Permission denied');
        return;
    }

    // Parse form data
    parse_str($_POST['form_data'], $form_data);

    // Validate required fields
    if (empty($form_data['table_id']) || empty($form_data['table_label']) || empty($form_data['table_slug'])) {
        wp_send_json_error('Table ID, name and slug are required');
        return;
    }

    // Sanitize input
    $table_id = intval($form_data['table_id']);
    $label = sanitize_text_field($form_data['table_label']);
    $slug = sanitize_title($form_data['table_slug']);
    $description = isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '';

    global $wpdb;
    $tables_table = $wpdb->prefix . 'dab_tables';

    // Check if table exists
    $existing_table = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM $tables_table WHERE id = %d",
        $table_id
    ));

    if (!$existing_table) {
        wp_send_json_error('Table not found');
        return;
    }

    // Check if slug is already used by another table
    $slug_check = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM $tables_table WHERE table_slug = %s AND id != %d",
        $slug, $table_id
    ));

    if ($slug_check) {
        wp_send_json_error('A table with this slug already exists. Please choose a different slug.');
        return;
    }

    // Update table
    $wpdb->update(
        $tables_table,
        [
            'table_label' => $label,
            'table_slug' => $slug,
            'description' => $description,
        ],
        ['id' => $table_id]
    );

    // Get the updated table data
    $updated_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $table_id
    ), ARRAY_A);

    // Send success response
    wp_send_json_success([
        'message' => 'Table updated successfully',
        'table' => $updated_table
    ]);
}
add_action('wp_ajax_dab_ajax_update_table', 'dab_ajax_update_table');
