<?php
/**
 * Enhanced Data Management Dashboard
 *
 * A comprehensive, responsive dashboard for managing data from any table
 * with advanced features like inline editing, filtering, and visualization.
 */
if (!defined('ABSPATH')) exit;

global $wpdb;
$tables_table = $wpdb->prefix . 'dab_tables';

// Enqueue required scripts and styles
wp_enqueue_style('dab-data-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/data-dashboard.css', array(), DAB_VERSION);
wp_enqueue_style('dab-print-options', plugin_dir_url(dirname(__FILE__)) . 'assets/css/print-options.css', array(), DAB_VERSION);
wp_enqueue_script('dab-data-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/data-dashboard.js', array('jquery'), DAB_VERSION, true);
wp_enqueue_script('dab-table-export', plugin_dir_url(dirname(__FILE__)) . 'assets/js/table-export.js', array('jquery'), DAB_VERSION, true);

// Add DataTables for enhanced table functionality
wp_enqueue_style('datatables', 'https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css', array(), '1.13.6');
wp_enqueue_style('datatables-responsive', 'https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css', array(), '2.5.0');
wp_enqueue_style('datatables-buttons', 'https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css', array(), '2.4.1');

wp_enqueue_script('datatables', 'https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js', array('jquery'), '1.13.6', true);
wp_enqueue_script('datatables-responsive', 'https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js', array('datatables'), '2.5.0', true);
wp_enqueue_script('datatables-buttons', 'https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js', array('datatables'), '2.4.1', true);
wp_enqueue_script('datatables-buttons-html5', 'https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js', array('datatables-buttons'), '2.4.1', true);
wp_enqueue_script('jszip', 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js', array(), '3.10.1', true);
wp_enqueue_script('pdfmake', 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.70/pdfmake.min.js', array(), '0.1.70', true);
wp_enqueue_script('pdfmake-fonts', 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.70/vfs_fonts.js', array('pdfmake'), '0.1.70', true);

// Add SweetAlert2 for better dialogs
wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', array(), '11.0.0', true);

// Add Chart.js for data visualization
wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', array(), '3.9.1', true);

// Localize script with necessary variables
wp_localize_script('dab-data-dashboard', 'dab_data', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('dab_data_nonce'),
    'i18n' => array(
        'delete_confirm' => 'Are you sure you want to delete this record? This action cannot be undone.',
        'bulk_delete_confirm' => 'Are you sure you want to delete the selected records? This action cannot be undone.',
        'record_updated' => 'Record updated successfully.',
        'error' => 'An error occurred. Please try again.',
        'no_records' => 'No records found.',
        'loading' => 'Loading data...',
        'processing' => 'Processing...',
        'search' => 'Search...',
        'first' => 'First',
        'last' => 'Last',
        'next' => 'Next',
        'previous' => 'Previous',
    )
));

// Handle AJAX requests
if (wp_doing_ajax()) {
    return;
}

// Fetch Tables
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label ASC");

// Filter tables based on user permissions
$current_user_id = get_current_user_id();
$user = wp_get_current_user();
$is_admin = in_array('administrator', $user->roles);

if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
    $accessible_table_ids = DAB_Role_Permissions_Manager::get_user_accessible_tables($current_user_id);

    // Filter tables to only include those the user has access to
    $filtered_tables = array();
    foreach ($tables as $table) {
        if (in_array($table->id, $accessible_table_ids)) {
            $filtered_tables[] = $table;
        }
    }
    $tables = $filtered_tables;
}

$selected_table_id = isset($_GET['table_id']) ? intval($_GET['table_id']) : 0;

// Check if the selected table is accessible to the user
if ($selected_table_id && !$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
    $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $selected_table_id);
    if (!$can_view) {
        $selected_table_id = 0; // Reset selection if user doesn't have permission
    }
}
?>

<div class="wrap dab-data-dashboard-wrap">
    <h1 class="dab-dashboard-title">Data Management Dashboard</h1>

    <div class="dab-dashboard-description">
        Manage your data with this comprehensive dashboard. Select a table to view, edit, and analyze your data.
    </div>

    <div class="dab-dashboard-container">
        <!-- Table Selection Panel -->
        <div class="dab-table-selection-panel">
            <div class="dab-panel-header">
                <h2>Tables</h2>
                <div class="dab-panel-actions">
                    <button type="button" class="dab-refresh-tables" title="Refresh Tables List">
                        <span class="dashicons dashicons-update"></span>
                    </button>
                </div>
            </div>

            <div class="dab-panel-body">
                <div class="dab-search-box">
                    <input type="text" id="dab-table-search" placeholder="Search tables...">
                    <span class="dashicons dashicons-search"></span>
                </div>

                <ul class="dab-tables-list">
                    <?php if (empty($tables)): ?>
                    <li class="dab-no-tables-message">
                        <p>You don't have access to any tables. Please contact an administrator for assistance.</p>
                    </li>
                    <?php else: ?>
                        <?php foreach ($tables as $table): ?>
                        <li class="dab-table-item <?php echo ($selected_table_id == $table->id) ? 'active' : ''; ?>"
                            data-table-id="<?php echo esc_attr($table->id); ?>"
                            data-table-slug="<?php echo esc_attr($table->table_slug); ?>">
                            <span class="dashicons dashicons-database"></span>
                            <span class="dab-table-name"><?php echo esc_html($table->table_label); ?></span>
                        </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <!-- Data Display Panel -->
        <div class="dab-data-display-panel">
            <div class="dab-panel-header">
                <h2 id="dab-current-table-name">Select a Table</h2>
                <div class="dab-panel-actions">
                    <button type="button" class="dab-add-record" title="Add New Record">
                        <span class="dashicons dashicons-plus"></span> Add Record
                    </button>
                    <button type="button" class="dab-print-table" title="Print Table">
                        <span class="dashicons dashicons-printer"></span> Print
                    </button>
                    <button type="button" class="dab-refresh-data" title="Refresh Data">
                        <span class="dashicons dashicons-update"></span>
                    </button>
                    <button type="button" class="dab-toggle-filters" title="Toggle Filters">
                        <span class="dashicons dashicons-filter"></span>
                    </button>
                    <button type="button" class="dab-toggle-visualization" title="Toggle Visualization">
                        <span class="dashicons dashicons-chart-bar"></span>
                    </button>
                    <button type="button" class="dab-toggle-import-export" title="Import/Export">
                        <span class="dashicons dashicons-upload"></span>
                    </button>
                    <div class="dab-bulk-actions-dropdown">
                        <button type="button" class="dab-bulk-actions-toggle" title="Bulk Actions">
                            <span class="dashicons dashicons-admin-tools"></span> Bulk Actions
                        </button>
                        <div class="dab-bulk-actions-menu">
                            <button type="button" class="dab-bulk-delete" title="Delete Selected">
                                <span class="dashicons dashicons-trash"></span> Delete Selected
                            </button>
                            <button type="button" class="dab-bulk-export" title="Export Selected">
                                <span class="dashicons dashicons-download"></span> Export Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Section (Initially Hidden) -->
            <div class="dab-filters-section" style="display: none;">
                <div class="dab-filter-tabs">
                    <button type="button" class="dab-tab-button active" data-tab="basic">Basic Filters</button>
                    <button type="button" class="dab-tab-button" data-tab="advanced">Advanced Filters</button>
                    <button type="button" class="dab-tab-button" data-tab="saved">Saved Filters</button>
                </div>

                <div class="dab-tab-content" id="dab-basic-filters-tab">
                    <div class="dab-filter-controls">
                        <div class="dab-filter-group">
                            <label for="dab-filter-field">Field:</label>
                            <select id="dab-filter-field">
                                <option value="">Select Field</option>
                            </select>
                        </div>
                        <div class="dab-filter-group">
                            <label for="dab-filter-operator">Operator:</label>
                            <select id="dab-filter-operator">
                                <option value="equals">Equals</option>
                                <option value="not_equals">Not Equals</option>
                                <option value="contains">Contains</option>
                                <option value="starts_with">Starts With</option>
                                <option value="ends_with">Ends With</option>
                                <option value="greater_than">Greater Than</option>
                                <option value="less_than">Less Than</option>
                                <option value="greater_than_equal">Greater Than or Equal</option>
                                <option value="less_than_equal">Less Than or Equal</option>
                                <option value="between">Between</option>
                                <option value="in_list">In List</option>
                                <option value="not_in_list">Not In List</option>
                                <option value="is_empty">Is Empty</option>
                                <option value="is_not_empty">Is Not Empty</option>
                            </select>
                        </div>
                        <div class="dab-filter-group" id="dab-filter-value-container">
                            <label for="dab-filter-value">Value:</label>
                            <input type="text" id="dab-filter-value">
                        </div>
                        <div class="dab-filter-group dab-filter-value2-container" style="display: none;">
                            <label for="dab-filter-value2">Value 2:</label>
                            <input type="text" id="dab-filter-value2">
                        </div>
                        <div class="dab-filter-actions">
                            <button type="button" class="dab-add-filter">Add Filter</button>
                        </div>
                    </div>
                </div>

                <div class="dab-tab-content" id="dab-advanced-filters-tab" style="display: none;">
                    <div class="dab-advanced-filter-builder">
                        <div class="dab-filter-group-container">
                            <div class="dab-filter-group-header">
                                <select class="dab-filter-group-operator">
                                    <option value="AND">AND</option>
                                    <option value="OR">OR</option>
                                </select>
                                <button type="button" class="dab-add-filter-group">
                                    <span class="dashicons dashicons-plus"></span> Add Group
                                </button>
                                <button type="button" class="dab-add-filter-condition">
                                    <span class="dashicons dashicons-plus"></span> Add Condition
                                </button>
                            </div>
                            <div class="dab-filter-conditions">
                                <!-- Conditions will be added dynamically -->
                            </div>
                        </div>
                    </div>
                    <div class="dab-advanced-filter-actions">
                        <button type="button" class="dab-apply-advanced-filters">Apply Filters</button>
                        <button type="button" class="dab-save-advanced-filters">Save Filter Set</button>
                    </div>
                </div>

                <div class="dab-tab-content" id="dab-saved-filters-tab" style="display: none;">
                    <div class="dab-saved-filters-list">
                        <p class="dab-no-saved-filters">No saved filters yet. Create and save filters to see them here.</p>
                        <!-- Saved filters will be added dynamically -->
                    </div>
                </div>

                <div class="dab-active-filters">
                    <div class="dab-active-filters-header">
                        <h4>Active Filters</h4>
                        <button type="button" class="dab-clear-all-filters" title="Clear All Filters">
                            <span class="dashicons dashicons-dismiss"></span> Clear All
                        </button>
                    </div>
                    <div class="dab-filter-tags"></div>
                </div>
            </div>

            <!-- Visualization Section (Initially Hidden) -->
            <div class="dab-visualization-section" style="display: none;">
                <div class="dab-visualization-controls">
                    <div class="dab-visualization-group">
                        <label for="dab-chart-type">Chart Type:</label>
                        <select id="dab-chart-type">
                            <option value="bar">Bar Chart</option>
                            <option value="line">Line Chart</option>
                            <option value="pie">Pie Chart</option>
                            <option value="doughnut">Doughnut Chart</option>
                        </select>
                    </div>
                    <div class="dab-visualization-group">
                        <label for="dab-chart-x-axis">X-Axis:</label>
                        <select id="dab-chart-x-axis">
                            <option value="">Select Field</option>
                        </select>
                    </div>
                    <div class="dab-visualization-group">
                        <label for="dab-chart-y-axis">Y-Axis:</label>
                        <select id="dab-chart-y-axis">
                            <option value="">Select Field</option>
                        </select>
                    </div>
                    <div class="dab-visualization-actions">
                        <button type="button" class="dab-generate-chart">Generate Chart</button>
                    </div>
                </div>
                <div class="dab-chart-container">
                    <canvas id="dab-data-chart"></canvas>
                </div>
            </div>

            <!-- Import/Export Section (Initially Hidden) -->
            <div class="dab-import-export-section" style="display: none;">
                <div class="dab-import-export-tabs">
                    <button type="button" class="dab-tab-button active" data-tab="import">Import Data</button>
                    <button type="button" class="dab-tab-button" data-tab="export">Export Data</button>
                </div>

                <div class="dab-tab-content" id="dab-import-tab">
                    <div class="dab-import-instructions">
                        <p>Upload a CSV or JSON file to import data into the current table. The file should have the same column structure as the table.</p>
                        <p><strong>Note:</strong> Importing data will not overwrite existing records unless you select the option below.</p>
                    </div>

                    <div class="dab-import-options">
                        <div class="dab-import-option">
                            <input type="checkbox" id="dab-import-overwrite" name="overwrite">
                            <label for="dab-import-overwrite">Overwrite existing records (match by ID)</label>
                        </div>
                        <div class="dab-import-option">
                            <input type="checkbox" id="dab-import-skip-header" name="skip_header" checked>
                            <label for="dab-import-skip-header">Skip header row</label>
                        </div>
                    </div>

                    <div class="dab-file-upload">
                        <input type="file" id="dab-import-file" accept=".csv,.json">
                        <div class="dab-file-upload-label">
                            <span class="dashicons dashicons-upload"></span>
                            <span>Choose a file or drag it here</span>
                        </div>
                    </div>

                    <div class="dab-import-options">
                        <h4>Import Options</h4>
                        <div class="dab-import-option">
                            <input type="checkbox" id="dab-import-skip-header" name="skip_header" checked>
                            <label for="dab-import-skip-header">Skip header row (CSV only)</label>
                        </div>
                        <div class="dab-import-option">
                            <input type="checkbox" id="dab-import-overwrite" name="overwrite">
                            <label for="dab-import-overwrite">Overwrite existing records (if ID matches)</label>
                        </div>
                    </div>

                    <div class="dab-import-preview">
                        <h4>Data Preview</h4>
                        <div class="dab-preview-container">
                            <p class="dab-preview-placeholder">Upload a file to see a preview</p>
                            <table class="dab-preview-table" style="display: none;">
                                <thead>
                                    <tr></tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>

                    <div class="dab-import-actions">
                        <button type="button" class="dab-import-data" disabled>Import Data</button>
                    </div>
                </div>

                <div class="dab-tab-content" id="dab-export-tab" style="display: none;">
                    <div class="dab-export-instructions">
                        <p>Export data from the current table to a CSV or JSON file.</p>
                    </div>

                    <div class="dab-export-options">
                        <div class="dab-export-option-group">
                            <label>Export Format:</label>
                            <div class="dab-export-format-options">
                                <div class="dab-export-format-option">
                                    <input type="radio" id="dab-export-format-csv" name="export_format" value="csv" checked>
                                    <label for="dab-export-format-csv">CSV</label>
                                </div>
                                <div class="dab-export-format-option">
                                    <input type="radio" id="dab-export-format-json" name="export_format" value="json">
                                    <label for="dab-export-format-json">JSON</label>
                                </div>
                                <div class="dab-export-format-option">
                                    <input type="radio" id="dab-export-format-excel" name="export_format" value="excel">
                                    <label for="dab-export-format-excel">Excel</label>
                                </div>
                            </div>
                        </div>

                        <div class="dab-export-option">
                            <input type="checkbox" id="dab-export-selected-only" name="selected_only">
                            <label for="dab-export-selected-only">Export selected records only</label>
                        </div>

                        <div class="dab-export-option">
                            <input type="checkbox" id="dab-export-include-header" name="include_header" checked>
                            <label for="dab-export-include-header">Include header row</label>
                        </div>
                    </div>

                    <div class="dab-export-actions">
                        <button type="button" class="dab-export-data">Export Data</button>
                    </div>
                </div>
            </div>

            <!-- Data Table Section -->
            <div class="dab-data-table-section">
                <div class="dab-loading-overlay">
                    <div class="dab-spinner"></div>
                    <div class="dab-loading-text">Loading data...</div>
                </div>
                <div class="dab-data-table-container">
                    <table id="dab-data-table" class="display responsive nowrap" style="width:100%">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="dab-select-all"></th>
                                <th>ID</th>
                                <!-- Dynamic columns will be added here -->
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data rows will be added dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Record Edit Modal -->
<div id="dab-record-modal" class="dab-modal">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3 id="dab-modal-title">Edit Record</h3>
            <span class="dab-modal-close">&times;</span>
        </div>
        <div class="dab-modal-body">
            <form id="dab-record-form">
                <input type="hidden" id="dab-record-id" name="record_id" value="">
                <input type="hidden" id="dab-table-id" name="table_id" value="">
                <div class="dab-form-tabs">
                    <button type="button" class="dab-form-tab active" data-tab="basic">Basic Information</button>
                    <button type="button" class="dab-form-tab" data-tab="advanced">Advanced Fields</button>
                </div>
                <div id="dab-form-fields">
                    <!-- Form fields will be added dynamically -->
                </div>
                <div class="dab-form-validation-message"></div>
            </form>
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-modal-cancel">Cancel</button>
            <button type="button" class="dab-modal-save">Save Changes</button>
        </div>
    </div>
</div>

<!-- Print Options Modal -->
<div id="dab-print-modal" class="dab-modal">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3>Print Options</h3>
            <span class="dab-modal-close">&times;</span>
        </div>
        <div class="dab-modal-body">
            <div class="dab-print-options">
                <div class="dab-print-option">
                    <input type="radio" id="dab-print-standard" name="print_format" value="standard" checked>
                    <label for="dab-print-standard">Standard Report</label>
                    <p class="dab-option-description">A standard table format suitable for general reporting.</p>
                </div>
                <div class="dab-print-option">
                    <input type="radio" id="dab-print-invoice" name="print_format" value="invoice">
                    <label for="dab-print-invoice">Invoice Format</label>
                    <p class="dab-option-description">A professional invoice layout with company details and totals.</p>
                </div>
                <div class="dab-print-option">
                    <input type="radio" id="dab-print-thermal" name="print_format" value="thermal">
                    <label for="dab-print-thermal">Thermal Receipt</label>
                    <p class="dab-option-description">Compact format optimized for thermal receipt printers.</p>
                </div>
                <div class="dab-print-settings">
                    <h4>Additional Settings</h4>
                    <div class="dab-print-setting">
                        <input type="checkbox" id="dab-print-include-header" name="include_header" checked>
                        <label for="dab-print-include-header">Include Header/Logo</label>
                    </div>
                    <div class="dab-print-setting">
                        <input type="checkbox" id="dab-print-include-date" name="include_date" checked>
                        <label for="dab-print-include-date">Include Date/Time</label>
                    </div>
                    <div class="dab-print-setting">
                        <input type="checkbox" id="dab-print-selected-only" name="print_selected">
                        <label for="dab-print-selected-only">Print Selected Records Only</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-modal-cancel">Cancel</button>
            <button type="button" class="dab-print-proceed">Print</button>
        </div>
    </div>
</div>
