<?php
/**
 * Chat Shortcode
 *
 * Provides frontend chat interface
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register chat shortcode
 */
function dab_chat_shortcode($atts) {
    $atts = shortcode_atts(array(
        'height' => '600px',
        'show_groups' => 'true',
        'show_users' => 'true',
        'default_view' => 'conversations' // 'conversations', 'groups', 'users'
    ), $atts, 'dab_chat');

    // Check if user is logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    if (!$current_user) {
        return '<div class="dab-auth-required">
                    <p>' . __('You must be logged in to use the chat.', 'db-app-builder') . '</p>
                    <p><a href="' . home_url('/login/') . '" class="dab-btn dab-btn-primary">' . __('Login', 'db-app-builder') . '</a></p>
                </div>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-chat', plugin_dir_url(dirname(__FILE__)) . 'assets/css/chat.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-chat', plugin_dir_url(dirname(__FILE__)) . 'assets/js/chat.js', array('jquery'), DAB_VERSION, true);

    // Localize script
    wp_localize_script('dab-chat', 'dab_chat', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_chat_nonce'),
        'current_user' => array(
            'id' => $current_user->id,
            'username' => $current_user->username,
            'first_name' => $current_user->first_name,
            'last_name' => $current_user->last_name,
            'avatar_url' => $current_user->avatar_url
        ),
        'settings' => array(
            'height' => $atts['height'],
            'show_groups' => $atts['show_groups'] === 'true',
            'show_users' => $atts['show_users'] === 'true',
            'default_view' => $atts['default_view'],
            'refresh_interval' => 3000, // 3 seconds
            'typing_timeout' => 2000 // 2 seconds
        ),
        'messages' => array(
            'loading' => __('Loading...', 'db-app-builder'),
            'error' => __('An error occurred. Please try again.', 'db-app-builder'),
            'no_conversations' => __('No conversations yet. Start a new chat!', 'db-app-builder'),
            'no_groups' => __('No groups available.', 'db-app-builder'),
            'no_messages' => __('No messages yet. Send the first message!', 'db-app-builder'),
            'type_message' => __('Type a message...', 'db-app-builder'),
            'send' => __('Send', 'db-app-builder'),
            'search_users' => __('Search users...', 'db-app-builder'),
            'search_groups' => __('Search groups...', 'db-app-builder'),
            'create_group' => __('Create Group', 'db-app-builder'),
            'group_name' => __('Group Name', 'db-app-builder'),
            'group_description' => __('Group Description', 'db-app-builder'),
            'online' => __('Online', 'db-app-builder'),
            'offline' => __('Offline', 'db-app-builder'),
            'typing' => __('typing...', 'db-app-builder'),
            'confirm_delete' => __('Are you sure you want to delete this message?', 'db-app-builder'),
            'confirm_leave_group' => __('Are you sure you want to leave this group?', 'db-app-builder')
        )
    ));

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-chat-container" style="height: <?php echo esc_attr($atts['height']); ?>;">
        <!-- Chat Sidebar -->
        <div class="dab-chat-sidebar">
            <!-- Sidebar Header -->
            <div class="dab-chat-sidebar-header">
                <div class="dab-chat-user-info">
                    <div class="dab-chat-avatar">
                        <?php if (!empty($current_user->avatar_url)): ?>
                            <img src="<?php echo esc_url($current_user->avatar_url); ?>" alt="<?php echo esc_attr($current_user->username); ?>">
                        <?php else: ?>
                            <div class="dab-chat-avatar-placeholder">
                                <?php echo strtoupper(substr($current_user->username, 0, 1)); ?>
                            </div>
                        <?php endif; ?>
                        <div class="dab-chat-status-indicator" id="dab-user-status"></div>
                    </div>
                    <div class="dab-chat-user-details">
                        <div class="dab-chat-username"><?php echo esc_html($current_user->first_name . ' ' . $current_user->last_name) ?: esc_html($current_user->username); ?></div>
                        <div class="dab-chat-status-text" id="dab-status-text">Online</div>
                    </div>
                </div>
                <div class="dab-chat-actions">
                    <button type="button" class="dab-chat-btn dab-chat-btn-icon" id="dab-new-chat" title="<?php _e('New Chat', 'db-app-builder'); ?>">
                        <span class="dashicons dashicons-plus"></span>
                    </button>
                    <button type="button" class="dab-chat-btn dab-chat-btn-icon" id="dab-chat-settings" title="<?php _e('Settings', 'db-app-builder'); ?>">
                        <span class="dashicons dashicons-admin-generic"></span>
                    </button>
                </div>
            </div>

            <!-- Sidebar Navigation -->
            <div class="dab-chat-nav">
                <button type="button" class="dab-chat-nav-btn active" data-view="conversations">
                    <span class="dashicons dashicons-format-chat"></span>
                    <?php _e('Chats', 'db-app-builder'); ?>
                    <span class="dab-chat-badge" id="dab-conversations-badge" style="display: none;"></span>
                </button>
                <?php if ($atts['show_groups'] === 'true'): ?>
                <button type="button" class="dab-chat-nav-btn" data-view="groups">
                    <span class="dashicons dashicons-groups"></span>
                    <?php _e('Groups', 'db-app-builder'); ?>
                    <span class="dab-chat-badge" id="dab-groups-badge" style="display: none;"></span>
                </button>
                <?php endif; ?>
                <?php if ($atts['show_users'] === 'true'): ?>
                <button type="button" class="dab-chat-nav-btn" data-view="users">
                    <span class="dashicons dashicons-admin-users"></span>
                    <?php _e('Users', 'db-app-builder'); ?>
                </button>
                <?php endif; ?>
            </div>

            <!-- Search Bar -->
            <div class="dab-chat-search">
                <input type="text" id="dab-chat-search" placeholder="<?php _e('Search...', 'db-app-builder'); ?>" class="dab-chat-search-input">
                <span class="dab-chat-search-icon dashicons dashicons-search"></span>
            </div>

            <!-- Sidebar Content -->
            <div class="dab-chat-sidebar-content">
                <!-- Conversations List -->
                <div class="dab-chat-list" id="dab-conversations-list">
                    <div class="dab-chat-loading">
                        <span class="dab-chat-spinner"></span>
                        <?php _e('Loading conversations...', 'db-app-builder'); ?>
                    </div>
                </div>

                <!-- Groups List -->
                <?php if ($atts['show_groups'] === 'true'): ?>
                <div class="dab-chat-list" id="dab-groups-list" style="display: none;">
                    <div class="dab-chat-loading">
                        <span class="dab-chat-spinner"></span>
                        <?php _e('Loading groups...', 'db-app-builder'); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Users List -->
                <?php if ($atts['show_users'] === 'true'): ?>
                <div class="dab-chat-list" id="dab-users-list" style="display: none;">
                    <div class="dab-chat-loading">
                        <span class="dab-chat-spinner"></span>
                        <?php _e('Loading users...', 'db-app-builder'); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Chat Main Area -->
        <div class="dab-chat-main">
            <!-- Welcome Screen -->
            <div class="dab-chat-welcome" id="dab-chat-welcome">
                <div class="dab-chat-welcome-content">
                    <div class="dab-chat-welcome-icon">
                        <span class="dashicons dashicons-format-chat"></span>
                    </div>
                    <h3><?php _e('Welcome to Chat', 'db-app-builder'); ?></h3>
                    <p><?php _e('Select a conversation or start a new chat to begin messaging.', 'db-app-builder'); ?></p>
                </div>
            </div>

            <!-- Chat Area -->
            <div class="dab-chat-area" id="dab-chat-area" style="display: none;">
                <!-- Chat Header -->
                <div class="dab-chat-header">
                    <div class="dab-chat-header-info">
                        <div class="dab-chat-avatar" id="dab-chat-header-avatar"></div>
                        <div class="dab-chat-header-details">
                            <div class="dab-chat-header-name" id="dab-chat-header-name"></div>
                            <div class="dab-chat-header-status" id="dab-chat-header-status"></div>
                        </div>
                    </div>
                    <div class="dab-chat-header-actions">
                        <button type="button" class="dab-chat-btn dab-chat-btn-icon" id="dab-chat-info" title="<?php _e('Chat Info', 'db-app-builder'); ?>">
                            <span class="dashicons dashicons-info"></span>
                        </button>
                        <button type="button" class="dab-chat-btn dab-chat-btn-icon" id="dab-chat-close" title="<?php _e('Close Chat', 'db-app-builder'); ?>">
                            <span class="dashicons dashicons-no"></span>
                        </button>
                    </div>
                </div>

                <!-- Messages Container -->
                <div class="dab-chat-messages" id="dab-chat-messages">
                    <div class="dab-chat-loading">
                        <span class="dab-chat-spinner"></span>
                        <?php _e('Loading messages...', 'db-app-builder'); ?>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="dab-chat-typing" id="dab-chat-typing" style="display: none;">
                    <div class="dab-chat-typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="dab-chat-typing-text"></span>
                </div>

                <!-- Message Input -->
                <div class="dab-chat-input-container">
                    <div class="dab-chat-input-wrapper">
                        <textarea id="dab-chat-input" class="dab-chat-input" placeholder="<?php _e('Type a message...', 'db-app-builder'); ?>" rows="1"></textarea>
                        <div class="dab-chat-input-actions">
                            <button type="button" class="dab-chat-btn dab-chat-btn-icon" id="dab-chat-attach" title="<?php _e('Attach File', 'db-app-builder'); ?>">
                                <span class="dashicons dashicons-paperclip"></span>
                            </button>
                            <button type="button" class="dab-chat-btn dab-chat-btn-primary" id="dab-chat-send">
                                <span class="dashicons dashicons-arrow-right-alt2"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Chat Modal -->
    <div id="dab-new-chat-modal" class="dab-chat-modal" style="display: none;">
        <div class="dab-chat-modal-content">
            <div class="dab-chat-modal-header">
                <h3><?php _e('Start New Chat', 'db-app-builder'); ?></h3>
                <button type="button" class="dab-chat-modal-close" id="dab-close-new-chat">&times;</button>
            </div>
            <div class="dab-chat-modal-body">
                <div class="dab-chat-search">
                    <input type="text" id="dab-new-chat-search" placeholder="<?php _e('Search users...', 'db-app-builder'); ?>" class="dab-chat-search-input">
                </div>
                <div class="dab-chat-user-list" id="dab-new-chat-users">
                    <!-- Users will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Create Group Modal -->
    <?php if ($atts['show_groups'] === 'true'): ?>
    <div id="dab-create-group-modal" class="dab-chat-modal" style="display: none;">
        <div class="dab-chat-modal-content">
            <div class="dab-chat-modal-header">
                <h3><?php _e('Create New Group', 'db-app-builder'); ?></h3>
                <button type="button" class="dab-chat-modal-close" id="dab-close-create-group">&times;</button>
            </div>
            <div class="dab-chat-modal-body">
                <form id="dab-create-group-form">
                    <div class="dab-chat-form-group">
                        <label for="dab-group-name"><?php _e('Group Name', 'db-app-builder'); ?> <span class="required">*</span></label>
                        <input type="text" id="dab-group-name" name="name" class="dab-chat-form-control" required>
                    </div>
                    <div class="dab-chat-form-group">
                        <label for="dab-group-description"><?php _e('Description', 'db-app-builder'); ?></label>
                        <textarea id="dab-group-description" name="description" class="dab-chat-form-control" rows="3"></textarea>
                    </div>
                    <div class="dab-chat-form-group">
                        <label for="dab-group-type"><?php _e('Group Type', 'db-app-builder'); ?></label>
                        <select id="dab-group-type" name="group_type" class="dab-chat-form-control">
                            <option value="public"><?php _e('Public', 'db-app-builder'); ?></option>
                            <option value="private"><?php _e('Private', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                    <div class="dab-chat-form-actions">
                        <button type="button" class="dab-chat-btn dab-chat-btn-secondary" id="dab-cancel-create-group"><?php _e('Cancel', 'db-app-builder'); ?></button>
                        <button type="submit" class="dab-chat-btn dab-chat-btn-primary"><?php _e('Create Group', 'db-app-builder'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Hidden file input for attachments -->
    <input type="file" id="dab-chat-file-input" style="display: none;" accept="image/*,application/pdf,.doc,.docx,.txt">
    <?php
    return ob_get_clean();
}
add_shortcode('dab_chat', 'dab_chat_shortcode');
