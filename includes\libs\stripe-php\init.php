<?php
/**
 * Stripe PHP SDK Initialization
 *
 * This file initializes the Stripe PHP SDK. In a production environment,
 * you would include the actual Stripe PHP SDK here. For this implementation,
 * we're creating a simplified mock version for demonstration purposes.
 */

if (!defined('ABSPATH')) exit;

// Create a simple mock Stripe class for demonstration
class Stripe {
    public static $apiKey;

    public static function setApiKey($apiKey) {
        self::$apiKey = $apiKey;
    }
}

// Include the PaymentIntent mock class
require_once(__DIR__ . '/PaymentIntent.php');

// In a real implementation, you would include the actual Stripe PHP SDK:
// require_once(plugin_dir_path(__FILE__) . 'stripe-php/vendor/autoload.php');
