<?php
/**
 * Signature Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Signature_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the signature field type
        add_filter('dab_field_types', array($this, 'register_signature_field_type'));
        
        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_signature_field_options'), 10, 2);
        
        // Register field renderer
        add_action('dab_render_field_signature', array($this, 'render_signature_field'), 10, 2);
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Format signature value for display
        add_filter('dab_format_field_value', array($this, 'format_signature_value'), 10, 3);
    }
    
    /**
     * Register the signature field type
     * 
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_signature_field_type($field_types) {
        $field_types['signature'] = __('Signature Capture', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Add signature field options
     * 
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_signature_field_options($options, $field_type) {
        if ($field_type === 'signature') {
            $options = array(
                'width' => array(
                    'label' => __('Canvas Width', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 200,
                    'max' => 1200,
                    'default' => 400,
                    'description' => __('Width of the signature canvas in pixels.', 'db-app-builder'),
                ),
                'height' => array(
                    'label' => __('Canvas Height', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 100,
                    'max' => 600,
                    'default' => 200,
                    'description' => __('Height of the signature canvas in pixels.', 'db-app-builder'),
                ),
                'background_color' => array(
                    'label' => __('Background Color', 'db-app-builder'),
                    'type' => 'color',
                    'default' => '#ffffff',
                    'description' => __('Background color of the signature canvas.', 'db-app-builder'),
                ),
                'pen_color' => array(
                    'label' => __('Pen Color', 'db-app-builder'),
                    'type' => 'color',
                    'default' => '#000000',
                    'description' => __('Color of the signature pen.', 'db-app-builder'),
                ),
                'pen_size' => array(
                    'label' => __('Pen Size', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 1,
                    'max' => 10,
                    'default' => 2,
                    'description' => __('Size of the signature pen in pixels.', 'db-app-builder'),
                ),
                'border_color' => array(
                    'label' => __('Border Color', 'db-app-builder'),
                    'type' => 'color',
                    'default' => '#dddddd',
                    'description' => __('Border color of the signature canvas.', 'db-app-builder'),
                ),
                'border_size' => array(
                    'label' => __('Border Size', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 0,
                    'max' => 5,
                    'default' => 1,
                    'description' => __('Border size of the signature canvas in pixels.', 'db-app-builder'),
                ),
                'include_timestamp' => array(
                    'label' => __('Include Timestamp', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Include a timestamp with the signature data.', 'db-app-builder'),
                ),
                'require_name' => array(
                    'label' => __('Require Name', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Require the signer to enter their name.', 'db-app-builder'),
                ),
                'signature_format' => array(
                    'label' => __('Signature Format', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'png' => __('PNG Image', 'db-app-builder'),
                        'svg' => __('SVG Vector', 'db-app-builder'),
                        'json' => __('JSON Data', 'db-app-builder'),
                    ),
                    'default' => 'png',
                    'description' => __('Format to store the signature data.', 'db-app-builder'),
                ),
            );
        }
        
        return $options;
    }
    
    /**
     * Render the signature field
     * 
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_signature_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }
        
        // Get field settings with defaults
        $width = isset($options['width']) ? intval($options['width']) : 400;
        $height = isset($options['height']) ? intval($options['height']) : 200;
        $background_color = isset($options['background_color']) ? $options['background_color'] : '#ffffff';
        $pen_color = isset($options['pen_color']) ? $options['pen_color'] : '#000000';
        $pen_size = isset($options['pen_size']) ? intval($options['pen_size']) : 2;
        $border_color = isset($options['border_color']) ? $options['border_color'] : '#dddddd';
        $border_size = isset($options['border_size']) ? intval($options['border_size']) : 1;
        $include_timestamp = isset($options['include_timestamp']) ? (bool)$options['include_timestamp'] : false;
        $require_name = isset($options['require_name']) ? (bool)$options['require_name'] : false;
        $signature_format = isset($options['signature_format']) ? $options['signature_format'] : 'png';
        
        // Parse the value
        $signature_data = '';
        $signer_name = '';
        $timestamp = '';
        
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $signature_data = isset($parsed_value['data']) ? $parsed_value['data'] : '';
                    $signer_name = isset($parsed_value['name']) ? $parsed_value['name'] : '';
                    $timestamp = isset($parsed_value['timestamp']) ? $parsed_value['timestamp'] : '';
                } else {
                    $signature_data = $value;
                }
            } else {
                $signature_data = $value;
            }
        }
        
        // Generate a unique ID for the field
        $field_id = 'dab-signature-' . $field->id . '-' . uniqid();
        
        // Output the field HTML
        ?>
        <div class="dab-signature-field" id="<?php echo esc_attr($field_id); ?>" data-field-slug="<?php echo esc_attr($field->field_slug); ?>">
            <?php if ($require_name): ?>
            <div class="dab-signature-name-wrapper">
                <label for="<?php echo esc_attr($field_id . '-name'); ?>"><?php _e('Name', 'db-app-builder'); ?></label>
                <input type="text" 
                       id="<?php echo esc_attr($field_id . '-name'); ?>" 
                       class="dab-signature-name" 
                       placeholder="<?php esc_attr_e('Enter your name', 'db-app-builder'); ?>" 
                       value="<?php echo esc_attr($signer_name); ?>" 
                       <?php echo $field->required ? 'required' : ''; ?>>
            </div>
            <?php endif; ?>
            
            <div class="dab-signature-canvas-wrapper">
                <canvas id="<?php echo esc_attr($field_id . '-canvas'); ?>" 
                        class="dab-signature-canvas" 
                        width="<?php echo esc_attr($width); ?>" 
                        height="<?php echo esc_attr($height); ?>" 
                        style="border: <?php echo esc_attr($border_size); ?>px solid <?php echo esc_attr($border_color); ?>; background-color: <?php echo esc_attr($background_color); ?>;">
                    <?php _e('Your browser does not support the canvas element.', 'db-app-builder'); ?>
                </canvas>
                
                <div class="dab-signature-actions">
                    <button type="button" class="button dab-clear-signature">
                        <span class="dashicons dashicons-trash"></span> <?php _e('Clear', 'db-app-builder'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Hidden field to store the signature data -->
            <input type="hidden" 
                   name="<?php echo esc_attr($field->field_slug); ?>" 
                   id="<?php echo esc_attr($field_id . '-value'); ?>" 
                   value="<?php echo esc_attr($value); ?>" 
                   <?php echo $field->required ? 'required' : ''; ?>>
            
            <!-- Hidden field to store the signature format -->
            <input type="hidden" 
                   id="<?php echo esc_attr($field_id . '-format'); ?>" 
                   value="<?php echo esc_attr($signature_format); ?>">
            
            <?php if (!empty($signature_data)): ?>
            <div class="dab-signature-preview">
                <img src="<?php echo esc_attr($signature_data); ?>" alt="<?php esc_attr_e('Signature', 'db-app-builder'); ?>">
                <?php if (!empty($timestamp)): ?>
                <div class="dab-signature-timestamp">
                    <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($timestamp))); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var $field = $('#<?php echo esc_js($field_id); ?>');
            var $canvas = $('#<?php echo esc_js($field_id . '-canvas'); ?>');
            var $value = $('#<?php echo esc_js($field_id . '-value'); ?>');
            var $format = $('#<?php echo esc_js($field_id . '-format'); ?>');
            var $name = $('#<?php echo esc_js($field_id . '-name'); ?>');
            var canvas = $canvas[0];
            var ctx = canvas.getContext('2d');
            var isDrawing = false;
            var lastX = 0;
            var lastY = 0;
            var requireName = <?php echo $require_name ? 'true' : 'false'; ?>;
            var includeTimestamp = <?php echo $include_timestamp ? 'true' : 'false'; ?>;
            
            // Set up the canvas
            ctx.strokeStyle = '<?php echo esc_js($pen_color); ?>';
            ctx.lineWidth = <?php echo esc_js($pen_size); ?>;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // Load existing signature if available
            var existingSignature = '<?php echo esc_js($signature_data); ?>';
            if (existingSignature) {
                var img = new Image();
                img.onload = function() {
                    ctx.drawImage(img, 0, 0);
                };
                img.src = existingSignature;
            }
            
            // Drawing functions
            function startDrawing(e) {
                isDrawing = true;
                var rect = canvas.getBoundingClientRect();
                var clientX = e.clientX || (e.touches && e.touches[0].clientX);
                var clientY = e.clientY || (e.touches && e.touches[0].clientY);
                
                lastX = clientX - rect.left;
                lastY = clientY - rect.top;
            }
            
            function draw(e) {
                if (!isDrawing) return;
                
                var rect = canvas.getBoundingClientRect();
                var clientX = e.clientX || (e.touches && e.touches[0].clientX);
                var clientY = e.clientY || (e.touches && e.touches[0].clientY);
                
                var currentX = clientX - rect.left;
                var currentY = clientY - rect.top;
                
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(currentX, currentY);
                ctx.stroke();
                
                lastX = currentX;
                lastY = currentY;
                
                // Update the value
                updateValue();
            }
            
            function stopDrawing() {
                isDrawing = false;
            }
            
            // Clear the signature
            function clearSignature() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                updateValue();
            }
            
            // Update the hidden value field
            function updateValue() {
                var format = $format.val();
                var data = '';
                
                if (format === 'png') {
                    data = canvas.toDataURL('image/png');
                } else if (format === 'svg') {
                    // Convert canvas to SVG (simplified)
                    data = '<svg xmlns="http://www.w3.org/2000/svg" width="' + canvas.width + '" height="' + canvas.height + '"></svg>';
                } else if (format === 'json') {
                    // Store as JSON data (simplified)
                    data = JSON.stringify({
                        width: canvas.width,
                        height: canvas.height,
                        strokes: []
                    });
                }
                
                var signatureData = {
                    data: data
                };
                
                if (requireName) {
                    signatureData.name = $name.val();
                }
                
                if (includeTimestamp) {
                    signatureData.timestamp = new Date().toISOString();
                }
                
                $value.val(JSON.stringify(signatureData));
            }
            
            // Bind events
            $canvas.on('mousedown touchstart', startDrawing);
            $canvas.on('mousemove touchmove', draw);
            $(document).on('mouseup touchend', stopDrawing);
            
            $field.on('click', '.dab-clear-signature', clearSignature);
            
            if (requireName) {
                $name.on('input', updateValue);
            }
            
            // Initialize
            updateValue();
        });
        </script>
        <?php
    }
    
    /**
     * Format signature value for display
     * 
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_signature_value($value, $field, $context) {
        if ($field->field_type !== 'signature') {
            return $value;
        }
        
        // Parse the value
        $signature_data = '';
        $signer_name = '';
        $timestamp = '';
        
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $signature_data = isset($parsed_value['data']) ? $parsed_value['data'] : '';
                    $signer_name = isset($parsed_value['name']) ? $parsed_value['name'] : '';
                    $timestamp = isset($parsed_value['timestamp']) ? $parsed_value['timestamp'] : '';
                } else {
                    $signature_data = $value;
                }
            } else {
                $signature_data = $value;
            }
        }
        
        if (empty($signature_data)) {
            return '';
        }
        
        // Format the signature
        if ($context === 'html') {
            $output = '<div class="dab-signature-display">';
            $output .= '<img src="' . esc_attr($signature_data) . '" alt="' . esc_attr__('Signature', 'db-app-builder') . '" class="dab-signature-image">';
            
            if (!empty($signer_name)) {
                $output .= '<div class="dab-signature-name">' . esc_html($signer_name) . '</div>';
            }
            
            if (!empty($timestamp)) {
                $output .= '<div class="dab-signature-timestamp">' . esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($timestamp))) . '</div>';
            }
            
            $output .= '</div>';
            
            return $output;
        } else {
            $output = __('Signature', 'db-app-builder');
            
            if (!empty($signer_name)) {
                $output .= ': ' . $signer_name;
            }
            
            if (!empty($timestamp)) {
                $output .= ' (' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($timestamp)) . ')';
            }
            
            return $output;
        }
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue custom styles
        wp_enqueue_style(
            'dab-signature-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/signature-field.css',
            array(),
            DAB_VERSION
        );
        
        // Enqueue custom scripts
        wp_enqueue_script(
            'dab-signature-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/signature-field.js',
            array('jquery'),
            DAB_VERSION,
            true
        );
    }
}

// Initialize the class
new DAB_Signature_Field();
