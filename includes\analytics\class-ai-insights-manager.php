<?php
/**
 * AI-Powered Data Insights Manager
 * Phase 3: Data Intelligence & Analytics
 *
 * Provides automated pattern detection, trend analysis, anomaly detection,
 * and AI-powered recommendations for data insights.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_AI_Insights_Manager {

    /**
     * Initialize the AI Insights Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_generate_insights', array(__CLASS__, 'generate_insights'));
        add_action('wp_ajax_dab_get_insights_dashboard', array(__CLASS__, 'get_insights_dashboard'));
        add_action('wp_ajax_dab_save_insight', array(__CLASS__, 'save_insight'));
        add_action('wp_ajax_dab_dismiss_insight', array(__CLASS__, 'dismiss_insight'));
        add_action('wp_ajax_dab_get_trend_analysis', array(__CLASS__, 'get_trend_analysis'));
        add_action('wp_ajax_dab_detect_anomalies', array(__CLASS__, 'detect_anomalies'));
        add_action('wp_ajax_dab_get_recommendations', array(__CLASS__, 'get_recommendations'));

        // Schedule automated insights generation
        add_action('dab_daily_insights_cron', array(__CLASS__, 'generate_daily_insights'));
        add_action('dab_weekly_insights_cron', array(__CLASS__, 'generate_weekly_insights'));

        // Hook into data changes for real-time insights
        add_action('dab_data_inserted', array(__CLASS__, 'on_data_change'), 10, 3);
        add_action('dab_data_updated', array(__CLASS__, 'on_data_change'), 10, 3);
    }

    /**
     * Create database tables for AI insights
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Data insights table
        $insights_table = $wpdb->prefix . 'dab_data_insights';
        $sql = "CREATE TABLE IF NOT EXISTS $insights_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            insight_type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            data_source VARCHAR(100),
            insight_data LONGTEXT,
            confidence_score DECIMAL(3,2) DEFAULT 0.00,
            impact_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            status ENUM('active', 'dismissed', 'archived') DEFAULT 'active',
            is_automated TINYINT(1) DEFAULT 1,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            expires_at DATETIME NULL,
            PRIMARY KEY (id),
            KEY idx_insight_type (insight_type),
            KEY idx_data_source (data_source),
            KEY idx_status (status),
            KEY idx_impact_level (impact_level),
            KEY idx_created_at (created_at)
        ) $charset_collate;";

        // Trend analysis table
        $trends_table = $wpdb->prefix . 'dab_trend_analysis';
        $sql .= "CREATE TABLE IF NOT EXISTS $trends_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            data_source VARCHAR(100) NOT NULL,
            metric_name VARCHAR(100) NOT NULL,
            time_period VARCHAR(50) NOT NULL,
            trend_direction ENUM('up', 'down', 'stable', 'volatile') NOT NULL,
            trend_strength DECIMAL(3,2) DEFAULT 0.00,
            trend_data LONGTEXT,
            analysis_date DATE NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_trend (data_source, metric_name, time_period, analysis_date),
            KEY idx_analysis_date (analysis_date),
            KEY idx_trend_direction (trend_direction)
        ) $charset_collate;";

        // Anomaly detection table
        $anomalies_table = $wpdb->prefix . 'dab_anomalies';
        $sql .= "CREATE TABLE IF NOT EXISTS $anomalies_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            data_source VARCHAR(100) NOT NULL,
            metric_name VARCHAR(100) NOT NULL,
            anomaly_type ENUM('spike', 'drop', 'outlier', 'pattern_break') NOT NULL,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            expected_value DECIMAL(15,4),
            actual_value DECIMAL(15,4),
            deviation_percentage DECIMAL(5,2),
            anomaly_data LONGTEXT,
            detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            resolved_at DATETIME NULL,
            status ENUM('new', 'investigating', 'resolved', 'false_positive') DEFAULT 'new',
            PRIMARY KEY (id),
            KEY idx_data_source (data_source),
            KEY idx_anomaly_type (anomaly_type),
            KEY idx_severity (severity),
            KEY idx_detected_at (detected_at),
            KEY idx_status (status)
        ) $charset_collate;";

        // Recommendations table
        $recommendations_table = $wpdb->prefix . 'dab_recommendations';
        $sql .= "CREATE TABLE IF NOT EXISTS $recommendations_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            recommendation_type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            action_items LONGTEXT,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            category VARCHAR(100),
            data_source VARCHAR(100),
            supporting_data LONGTEXT,
            estimated_impact TEXT,
            implementation_effort ENUM('low', 'medium', 'high') DEFAULT 'medium',
            status ENUM('new', 'in_progress', 'completed', 'dismissed') DEFAULT 'new',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_recommendation_type (recommendation_type),
            KEY idx_priority (priority),
            KEY idx_category (category),
            KEY idx_status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Schedule cron jobs
        if (!wp_next_scheduled('dab_daily_insights_cron')) {
            wp_schedule_event(time(), 'daily', 'dab_daily_insights_cron');
        }

        if (!wp_next_scheduled('dab_weekly_insights_cron')) {
            wp_schedule_event(time(), 'weekly', 'dab_weekly_insights_cron');
        }
    }

    /**
     * Generate insights for a specific data source
     */
    public static function generate_insights() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $data_source = sanitize_text_field($_POST['data_source']);
        $insight_types = isset($_POST['insight_types']) ? $_POST['insight_types'] : array('all');

        try {
            $insights = array();

            foreach ($insight_types as $type) {
                switch ($type) {
                    case 'trends':
                    case 'all':
                        $insights['trends'] = self::analyze_trends($data_source);
                        if ($type !== 'all') break;

                    case 'anomalies':
                        $insights['anomalies'] = self::detect_data_anomalies($data_source);
                        if ($type !== 'all') break;

                    case 'patterns':
                        $insights['patterns'] = self::identify_patterns($data_source);
                        if ($type !== 'all') break;

                    case 'correlations':
                        $insights['correlations'] = self::find_correlations($data_source);
                        if ($type !== 'all') break;

                    case 'forecasts':
                        $insights['forecasts'] = self::generate_forecasts($data_source);
                        if ($type !== 'all') break;
                }
            }

            wp_send_json_success($insights);
        } catch (Exception $e) {
            wp_send_json_error('Insight generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get insights dashboard data
     */
    public static function get_insights_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        global $wpdb;

        try {
            // Get recent insights
            $insights_table = $wpdb->prefix . 'dab_data_insights';
            $recent_insights = $wpdb->get_results(
                "SELECT * FROM $insights_table
                WHERE status = 'active'
                ORDER BY created_at DESC
                LIMIT 10"
            );

            // Get anomalies
            $anomalies_table = $wpdb->prefix . 'dab_anomalies';
            $recent_anomalies = $wpdb->get_results(
                "SELECT * FROM $anomalies_table
                WHERE status IN ('new', 'investigating')
                ORDER BY detected_at DESC
                LIMIT 5"
            );

            // Get recommendations
            $recommendations_table = $wpdb->prefix . 'dab_recommendations';
            $active_recommendations = $wpdb->get_results(
                "SELECT * FROM $recommendations_table
                WHERE status IN ('new', 'in_progress')
                ORDER BY priority DESC, created_at DESC
                LIMIT 5"
            );

            // Get trend summary
            $trends_table = $wpdb->prefix . 'dab_trend_analysis';
            $trend_summary = $wpdb->get_results(
                "SELECT trend_direction, COUNT(*) as count
                FROM $trends_table
                WHERE analysis_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                GROUP BY trend_direction"
            );

            $dashboard_data = array(
                'insights' => $recent_insights,
                'anomalies' => $recent_anomalies,
                'recommendations' => $active_recommendations,
                'trend_summary' => $trend_summary,
                'stats' => array(
                    'total_insights' => $wpdb->get_var("SELECT COUNT(*) FROM $insights_table WHERE status = 'active'"),
                    'critical_anomalies' => $wpdb->get_var("SELECT COUNT(*) FROM $anomalies_table WHERE severity = 'critical' AND status IN ('new', 'investigating')"),
                    'high_priority_recommendations' => $wpdb->get_var("SELECT COUNT(*) FROM $recommendations_table WHERE priority IN ('high', 'urgent') AND status IN ('new', 'in_progress')")
                )
            );

            wp_send_json_success($dashboard_data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to load insights dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Save a custom insight
     */
    public static function save_insight() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $insights_table = $wpdb->prefix . 'dab_data_insights';

        $insight_type = sanitize_text_field($_POST['insight_type']);
        $title = sanitize_text_field($_POST['title']);
        $description = sanitize_textarea_field($_POST['description']);
        $data_source = sanitize_text_field($_POST['data_source']);
        $insight_data = $_POST['insight_data'];
        $impact_level = sanitize_text_field($_POST['impact_level']);

        $data = array(
            'insight_type' => $insight_type,
            'title' => $title,
            'description' => $description,
            'data_source' => $data_source,
            'insight_data' => json_encode($insight_data),
            'impact_level' => $impact_level,
            'is_automated' => 0,
            'created_by' => get_current_user_id()
        );

        $result = $wpdb->insert($insights_table, $data);

        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Insight saved successfully',
                'insight_id' => $wpdb->insert_id
            ));
        } else {
            wp_send_json_error('Failed to save insight');
        }
    }

    /**
     * Dismiss an insight
     */
    public static function dismiss_insight() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $insight_id = intval($_POST['insight_id']);

        global $wpdb;
        $insights_table = $wpdb->prefix . 'dab_data_insights';

        $result = $wpdb->update(
            $insights_table,
            array('status' => 'dismissed'),
            array('id' => $insight_id)
        );

        if ($result !== false) {
            wp_send_json_success('Insight dismissed successfully');
        } else {
            wp_send_json_error('Failed to dismiss insight');
        }
    }

    /**
     * Generate daily insights (cron job)
     */
    public static function generate_daily_insights() {
        global $wpdb;

        try {
            // Get all data sources for analysis
            $tables_table = $wpdb->prefix . 'dab_tables';
            $data_sources = $wpdb->get_results("SELECT table_slug FROM $tables_table WHERE status = 'active'");

            $insights_generated = 0;

            foreach ($data_sources as $source) {
                // Generate trend analysis
                $trend_insights = self::analyze_daily_trends($source->table_slug);
                if ($trend_insights) {
                    self::save_automated_insight('trend_analysis', $trend_insights, $source->table_slug);
                    $insights_generated++;
                }

                // Detect anomalies
                $anomalies = self::detect_daily_anomalies($source->table_slug);
                if ($anomalies) {
                    self::save_automated_insight('anomaly_detection', $anomalies, $source->table_slug);
                    $insights_generated++;
                }

                // Generate recommendations
                $recommendations = self::generate_daily_recommendations($source->table_slug);
                if ($recommendations) {
                    self::save_automated_insight('recommendations', $recommendations, $source->table_slug);
                    $insights_generated++;
                }
            }

            // Log the result
            error_log("DAB AI Insights: Generated $insights_generated daily insights");

        } catch (Exception $e) {
            error_log("DAB AI Insights Daily Generation Error: " . $e->getMessage());
        }
    }

    /**
     * Generate weekly insights (cron job)
     */
    public static function generate_weekly_insights() {
        global $wpdb;

        try {
            // Get all data sources for analysis
            $tables_table = $wpdb->prefix . 'dab_tables';
            $data_sources = $wpdb->get_results("SELECT table_slug FROM $tables_table WHERE status = 'active'");

            $insights_generated = 0;

            foreach ($data_sources as $source) {
                // Generate weekly performance summary
                $performance_insights = self::analyze_weekly_performance($source->table_slug);
                if ($performance_insights) {
                    self::save_automated_insight('weekly_performance', $performance_insights, $source->table_slug);
                    $insights_generated++;
                }

                // Generate growth analysis
                $growth_insights = self::analyze_weekly_growth($source->table_slug);
                if ($growth_insights) {
                    self::save_automated_insight('growth_analysis', $growth_insights, $source->table_slug);
                    $insights_generated++;
                }

                // Generate strategic recommendations
                $strategic_recommendations = self::generate_weekly_strategic_recommendations($source->table_slug);
                if ($strategic_recommendations) {
                    self::save_automated_insight('strategic_recommendations', $strategic_recommendations, $source->table_slug);
                    $insights_generated++;
                }
            }

            // Log the result
            error_log("DAB AI Insights: Generated $insights_generated weekly insights");

        } catch (Exception $e) {
            error_log("DAB AI Insights Weekly Generation Error: " . $e->getMessage());
        }
    }

    /**
     * Save automated insight
     */
    private static function save_automated_insight($insight_type, $insight_data, $data_source) {
        global $wpdb;

        $insights_table = $wpdb->prefix . 'dab_data_insights';

        $data = array(
            'insight_type' => $insight_type,
            'title' => $insight_data['title'],
            'description' => $insight_data['description'],
            'data_source' => $data_source,
            'insight_data' => json_encode($insight_data),
            'confidence_score' => $insight_data['confidence_score'] ?? 0.75,
            'impact_level' => $insight_data['impact_level'] ?? 'medium',
            'is_automated' => 1,
            'created_by' => 0 // System generated
        );

        return $wpdb->insert($insights_table, $data);
    }

    /**
     * Analyze daily trends
     */
    private static function analyze_daily_trends($table_slug) {
        global $wpdb;

        $data_table = $wpdb->prefix . 'dab_' . $table_slug;

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'");
        if (!$table_exists) {
            return false;
        }

        // Get today's data count
        $today_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $data_table WHERE DATE(created_at) = CURDATE()"
        );

        // Get yesterday's data count
        $yesterday_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $data_table WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)"
        );

        if ($yesterday_count > 0) {
            $change_percentage = (($today_count - $yesterday_count) / $yesterday_count) * 100;

            $trend_direction = $change_percentage > 0 ? 'increasing' : ($change_percentage < 0 ? 'decreasing' : 'stable');

            return array(
                'title' => "Daily Trend Analysis for " . ucfirst(str_replace('_', ' ', $table_slug)),
                'description' => sprintf(
                    "Data entries %s by %.1f%% compared to yesterday (%d vs %d entries)",
                    $trend_direction,
                    abs($change_percentage),
                    $today_count,
                    $yesterday_count
                ),
                'confidence_score' => 0.85,
                'impact_level' => abs($change_percentage) > 20 ? 'high' : 'medium',
                'trend_data' => array(
                    'today_count' => $today_count,
                    'yesterday_count' => $yesterday_count,
                    'change_percentage' => $change_percentage,
                    'trend_direction' => $trend_direction
                )
            );
        }

        return false;
    }

    /**
     * Detect daily anomalies
     */
    private static function detect_daily_anomalies($table_slug) {
        global $wpdb;

        $data_table = $wpdb->prefix . 'dab_' . $table_slug;

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'");
        if (!$table_exists) {
            return false;
        }

        // Get last 7 days average
        $avg_count = $wpdb->get_var(
            "SELECT AVG(daily_count) FROM (
                SELECT COUNT(*) as daily_count
                FROM $data_table
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                AND DATE(created_at) < CURDATE()
                GROUP BY DATE(created_at)
            ) as daily_counts"
        );

        // Get today's count
        $today_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $data_table WHERE DATE(created_at) = CURDATE()"
        );

        if ($avg_count > 0) {
            $deviation = (($today_count - $avg_count) / $avg_count) * 100;

            // Consider it an anomaly if deviation is more than 50%
            if (abs($deviation) > 50) {
                return array(
                    'title' => "Anomaly Detected in " . ucfirst(str_replace('_', ' ', $table_slug)),
                    'description' => sprintf(
                        "Today's data volume (%d entries) deviates by %.1f%% from the 7-day average (%.1f entries)",
                        $today_count,
                        $deviation,
                        $avg_count
                    ),
                    'confidence_score' => 0.90,
                    'impact_level' => abs($deviation) > 100 ? 'critical' : 'high',
                    'anomaly_data' => array(
                        'today_count' => $today_count,
                        'average_count' => $avg_count,
                        'deviation_percentage' => $deviation,
                        'anomaly_type' => $deviation > 0 ? 'spike' : 'drop'
                    )
                );
            }
        }

        return false;
    }

    /**
     * Generate daily recommendations
     */
    private static function generate_daily_recommendations($table_slug) {
        // This is a placeholder for more sophisticated recommendation logic
        // In a real implementation, this would analyze patterns and suggest actions

        return array(
            'title' => "Daily Recommendations for " . ucfirst(str_replace('_', ' ', $table_slug)),
            'description' => "Based on today's data patterns, consider reviewing data quality and entry processes.",
            'confidence_score' => 0.70,
            'impact_level' => 'medium',
            'recommendations' => array(
                'Monitor data entry patterns',
                'Review data validation rules',
                'Check for data quality issues'
            )
        );
    }

    /**
     * Analyze weekly performance
     */
    private static function analyze_weekly_performance($table_slug) {
        global $wpdb;

        $data_table = $wpdb->prefix . 'dab_' . $table_slug;

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'");
        if (!$table_exists) {
            return false;
        }

        // Get this week's data count
        $this_week_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $data_table WHERE YEARWEEK(created_at) = YEARWEEK(CURDATE())"
        );

        // Get last week's data count
        $last_week_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $data_table WHERE YEARWEEK(created_at) = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 1 WEEK))"
        );

        if ($last_week_count > 0) {
            $change_percentage = (($this_week_count - $last_week_count) / $last_week_count) * 100;

            return array(
                'title' => "Weekly Performance Summary for " . ucfirst(str_replace('_', ' ', $table_slug)),
                'description' => sprintf(
                    "This week's performance shows %.1f%% change compared to last week (%d vs %d entries)",
                    $change_percentage,
                    $this_week_count,
                    $last_week_count
                ),
                'confidence_score' => 0.80,
                'impact_level' => abs($change_percentage) > 30 ? 'high' : 'medium',
                'performance_data' => array(
                    'this_week_count' => $this_week_count,
                    'last_week_count' => $last_week_count,
                    'change_percentage' => $change_percentage
                )
            );
        }

        return false;
    }

    /**
     * Analyze weekly growth
     */
    private static function analyze_weekly_growth($table_slug) {
        // Placeholder for growth analysis logic
        return array(
            'title' => "Weekly Growth Analysis for " . ucfirst(str_replace('_', ' ', $table_slug)),
            'description' => "Growth patterns and trends identified for strategic planning.",
            'confidence_score' => 0.75,
            'impact_level' => 'medium'
        );
    }

    /**
     * Generate weekly strategic recommendations
     */
    private static function generate_weekly_strategic_recommendations($table_slug) {
        // Placeholder for strategic recommendations logic
        return array(
            'title' => "Weekly Strategic Recommendations for " . ucfirst(str_replace('_', ' ', $table_slug)),
            'description' => "Strategic insights and recommendations based on weekly data analysis.",
            'confidence_score' => 0.70,
            'impact_level' => 'high'
        );
    }
}
