<?php
/**
 * Wizard Steps Template
 *
 * This template displays the steps for a wizard
 */
if (!defined('ABSPATH')) exit;

/**
 * @var array $steps Array of step information
 * @var int $current_step Current step number
 * @var string $wizard_type Type of wizard
 */
?>

<div class="dab-wizard-steps">
    <ul class="dab-wizard-steps-list">
        <?php foreach ($steps as $index => $step):
            $step_number = $index + 1;
            $is_active = $step_number === $current_step;
            $is_completed = $step_number < $current_step;
            $step_class = $is_active ? 'active' : ($is_completed ? 'completed' : '');
        ?>
            <li class="dab-wizard-step <?php echo $step_class; ?>">
                <div class="dab-wizard-step-number">
                    <?php if ($is_completed): ?>
                        <span class="dashicons dashicons-yes-alt"></span>
                    <?php else: ?>
                        <?php echo $step_number; ?>
                    <?php endif; ?>
                </div>
                <div class="dab-wizard-step-content">
                    <h4 class="dab-wizard-step-title"><?php echo esc_html($step['title']); ?></h4>
                    <?php if (!empty($step['description'])): ?>
                        <p class="dab-wizard-step-description"><?php echo esc_html($step['description']); ?></p>
                    <?php endif; ?>
                </div>
            </li>
        <?php endforeach; ?>
    </ul>
</div>

<div class="dab-wizard-content">
    <div class="dab-wizard-header">
        <h2 class="dab-wizard-title"><?php echo esc_html($steps[$current_step - 1]['title']); ?></h2>
        <?php if (!empty($steps[$current_step - 1]['description'])): ?>
            <p class="dab-wizard-description"><?php echo esc_html($steps[$current_step - 1]['description']); ?></p>
        <?php endif; ?>
    </div>

    <div class="dab-wizard-body">
        <!-- Wizard step content will be loaded here -->
        <?php if (isset($content)): ?>
            <?php echo $content; ?>
        <?php else: ?>
            <div class="dab-wizard-notice dab-wizard-notice-warning">
                <p><?php _e('No content found for this step. Please go back and try again.', 'db-app-builder'); ?></p>
            </div>
        <?php endif; ?>
    </div>

    <div class="dab-wizard-footer">
        <div class="dab-wizard-actions">
            <?php if ($current_step > 1): ?>
                <button type="button" class="dab-btn dab-btn-outline-primary dab-wizard-prev-btn" data-step="<?php echo $current_step - 1; ?>">
                    <span class="dashicons dashicons-arrow-left-alt"></span>
                    <?php _e('Previous', 'db-app-builder'); ?>
                </button>
            <?php endif; ?>

            <?php if ($current_step < count($steps)): ?>
                <button type="button" class="dab-btn dab-btn-primary dab-wizard-next-btn" data-step="<?php echo $current_step + 1; ?>">
                    <?php _e('Next', 'db-app-builder'); ?>
                    <span class="dashicons dashicons-arrow-right-alt"></span>
                </button>
            <?php else: ?>
                <button type="button" class="dab-btn dab-btn-success dab-wizard-finish-btn">
                    <?php _e('Finish', 'db-app-builder'); ?>
                    <span class="dashicons dashicons-yes"></span>
                </button>
            <?php endif; ?>
        </div>

        <div class="dab-wizard-progress">
            <span class="dab-wizard-progress-text">
                <?php printf(__('Step %d of %d', 'db-app-builder'), $current_step, count($steps)); ?>
            </span>
            <div class="dab-wizard-progress-bar">
                <div class="dab-wizard-progress-bar-inner" style="width: <?php echo ($current_step / count($steps)) * 100; ?>%"></div>
            </div>
        </div>
    </div>
</div>

<div class="dab-wizard-save-indicator">
    <span class="dashicons dashicons-cloud"></span>
    <span class="dab-wizard-save-text"><?php _e('Saving...', 'db-app-builder'); ?></span>
</div>
