<?php
/**
 * <PERSON>le AJAX requests
 *
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

class DAB_AJAX_Handler {

    /**
     * Initialize the class
     */
    public function __construct() {
        $this->register_ajax_handlers();
    }

    /**
     * Register AJAX handlers
     */
    public function register_ajax_handlers() {
        // Add AJAX action for getting table fields
        add_action('wp_ajax_dab_get_table_fields', array($this, 'get_table_fields'));

        // Add AJAX handler for getting record details
        add_action('wp_ajax_dab_get_record_details', array($this, 'get_record_details'));
        add_action('wp_ajax_nopriv_dab_get_record_details', array($this, 'get_record_details'));

        // Add AJAX handler for checking new approvals
        add_action('wp_ajax_dab_check_new_approvals', array($this, 'check_new_approvals'));

        // Add AJAX handler for adding approval comments
        add_action('wp_ajax_dab_add_approval_comment', array($this, 'add_approval_comment'));

        // Add AJAX handler for lookup options
        add_action('wp_ajax_dab_get_lookup_options', array($this, 'get_lookup_options'));
        add_action('wp_ajax_nopriv_dab_get_lookup_options', array($this, 'get_lookup_options'));
    }

    /**
     * Get fields for a specific table
     * This handler has been moved to includes/functions.php
     */
    public function get_table_fields() {
        // Delegate to the main handler in functions.php
        global $wpdb;

        // Support both POST and GET requests
        $table_id = isset($_POST['table_id']) ? intval($_POST['table_id']) : (isset($_GET['table_id']) ? intval($_GET['table_id']) : 0);

        if (!$table_id) {
            wp_send_json_error(['message' => 'No table ID provided']);
            return;
        }

        // Call the main handler in functions.php
        $fields_table = $wpdb->prefix . 'dab_fields';

        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT id, field_label, field_slug, field_type FROM $fields_table
             WHERE table_id = %d
             ORDER BY field_label ASC",
            $table_id
        ));

        if (empty($fields)) {
            wp_send_json_error(['message' => 'No fields found for this table']);
            return;
        }

        wp_send_json_success($fields);
    }

    /**
     * Get lookup options for a field
     */
    public function get_lookup_options() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dropdown_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        // Check for required parameters
        if (!isset($_POST['lookup_table_id']) || !isset($_POST['display_column'])) {
            wp_send_json_error(['message' => 'Missing required parameters']);
            return;
        }

        $lookup_table_id = intval($_POST['lookup_table_id']);
        $display_column = sanitize_text_field($_POST['display_column']);

        global $wpdb;

        // Get lookup table slug
        $lookup_table_slug = $wpdb->get_var($wpdb->prepare(
            "SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $lookup_table_id
        ));

        if (!$lookup_table_slug) {
            wp_send_json_error(['message' => 'Lookup table not found']);
            return;
        }

        // Get lookup options
        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
        $options = $wpdb->get_results($wpdb->prepare(
            "SELECT id, `$display_column` as label FROM $lookup_table ORDER BY `$display_column` ASC"
        ));

        wp_send_json_success($options);
    }

    /**
     * Get record details for approval
     */
    public function get_record_details() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        // Check for required parameters
        if (!isset($_POST['record_id']) || !isset($_POST['table_id'])) {
            wp_send_json_error(['message' => 'Missing required parameters']);
            return;
        }

        $record_id = intval($_POST['record_id']);
        $table_id = intval($_POST['table_id']);

        global $wpdb;

        // Get table information
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table_info) {
            wp_send_json_error(['message' => 'Table not found']);
            return;
        }

        // Get fields for this table
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d ORDER BY field_order",
            $table_id
        ));

        if (empty($fields)) {
            wp_send_json_error(['message' => 'No fields found for this table']);
            return;
        }

        // Get record data
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) {
            wp_send_json_error(['message' => 'Record not found']);
            return;
        }

        // Build HTML for record details
        $html = '<div class="dab-record-details">';

        foreach ($fields as $field) {
            $field_value = $record->{$field->field_slug} ?? '';

            // Format field value based on type
            if ($field->field_type === 'lookup') {
                $lookup_table_id = intval($field->lookup_table_id);
                $display_column = sanitize_text_field($field->lookup_display_column);
                if ($lookup_table_id && $display_column) {
                    $lookup_table_slug = $wpdb->get_var(
                        $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                    );
                    if ($lookup_table_slug) {
                        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                        $lookup_id = intval($field_value);
                        $field_value = $wpdb->get_var(
                            $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                        );
                    }
                }
            } elseif ($field->field_type === 'date') {
                $field_value = !empty($field_value) ? date('Y-m-d', strtotime($field_value)) : '';
            } elseif ($field->field_type === 'boolean' || $field->field_type === 'checkbox') {
                $field_value = !empty($field_value) ? 'Yes' : 'No';
            }

            $html .= '<div class="dab-record-field">';
            $html .= '<div class="dab-field-label">' . esc_html($field->field_label) . '</div>';
            $html .= '<div class="dab-field-value">' . esc_html($field_value) . '</div>';
            $html .= '</div>';
        }

        // Add approval history
        $history = DAB_Approval_Manager::get_approval_history($table_id, $record_id);
        if (!empty($history)) {
            $html .= '<div class="dab-approval-history">';
            $html .= '<h5 class="dab-history-title">Approval History</h5>';
            $html .= '<ul class="dab-history-list">';

            foreach ($history as $entry) {
                $user = get_userdata($entry->user_id);
                $username = $user ? $user->display_name : 'Unknown';

                $html .= '<li class="dab-history-item">';
                $html .= '<div class="dab-history-meta">';
                $html .= '<span class="dab-history-user">' . esc_html($username) . '</span>';
                $html .= '<span class="dab-history-date">' . date('Y-m-d H:i', strtotime($entry->created_at)) . '</span>';
                $html .= '</div>';

                $html .= '<div>';
                $html .= '<span class="dab-history-status dab-status-' . strtolower($entry->status) . '">' . esc_html($entry->status) . '</span>';
                $html .= ' at level "' . esc_html($entry->level_name) . '"';
                $html .= '</div>';

                if (!empty($entry->notes)) {
                    $html .= '<div class="dab-history-notes">' . esc_html($entry->notes) . '</div>';
                }

                $html .= '</li>';
            }

            $html .= '</ul>';
            $html .= '</div>';
        }

        $html .= '</div>';

        wp_send_json_success([
            'html' => $html,
            'record' => $record,
            'fields' => $fields
        ]);
    }

    /**
     * Check for new approvals
     */
    public function check_new_approvals() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $last_check = isset($_POST['last_check']) ? intval($_POST['last_check']) : 0;
        $current_user_id = get_current_user_id();

        if (!$current_user_id) {
            wp_send_json_error(['message' => 'User not logged in']);
            return;
        }

        global $wpdb;

        // Get tables with approval workflows
        $tables_with_approval = $wpdb->get_results(
            "SELECT DISTINCT t.*
            FROM {$wpdb->prefix}dab_tables t
            INNER JOIN {$wpdb->prefix}dab_approval_levels l ON t.id = l.table_id
            ORDER BY t.table_label"
        );

        $new_approvals_count = 0;

        foreach ($tables_with_approval as $table) {
            $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

            // Get records pending approval since last check
            $records = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $data_table
                WHERE approval_status = 'Pending'
                AND current_approval_level > 0
                AND UNIX_TIMESTAMP(created_at) > %d
                ORDER BY id DESC",
                $last_check
            ));

            // Filter records that the current user can approve
            foreach ($records as $record) {
                if (DAB_Approval_Manager::can_user_approve($current_user_id, $table->id, $record->id)) {
                    $new_approvals_count++;
                }
            }
        }

        wp_send_json_success([
            'count' => $new_approvals_count,
            'timestamp' => time()
        ]);
    }

    /**
     * Add approval comment
     */
    public function add_approval_comment() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        // Check for required parameters
        if (!isset($_POST['record_id']) || !isset($_POST['table_id']) || !isset($_POST['comment'])) {
            wp_send_json_error(['message' => 'Missing required parameters']);
            return;
        }

        $record_id = intval($_POST['record_id']);
        $table_id = intval($_POST['table_id']);
        $comment = sanitize_textarea_field($_POST['comment']);
        $current_user_id = get_current_user_id();

        if (!$current_user_id) {
            wp_send_json_error(['message' => 'User not logged in']);
            return;
        }

        global $wpdb;

        // Insert comment into approval comments table
        $result = $wpdb->insert(
            $wpdb->prefix . 'dab_approval_comments',
            [
                'table_id' => $table_id,
                'record_id' => $record_id,
                'user_id' => $current_user_id,
                'comment' => $comment,
                'created_at' => current_time('mysql')
            ]
        );

        if (!$result) {
            wp_send_json_error(['message' => 'Failed to add comment']);
            return;
        }

        // Get user info
        $user = get_userdata($current_user_id);
        $username = $user ? $user->display_name : 'Unknown';

        wp_send_json_success([
            'comment' => $comment,
            'author' => $username,
            'date' => date('Y-m-d H:i')
        ]);
    }

    /**
     * Save field data
     */
    public function save_field($data) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Get table structure to determine available columns
        $table_columns = $wpdb->get_col("DESCRIBE {$fields_table}");

        // Basic field data that should always be present
        $field_data = array(
            'table_id' => intval($data['table_id']),
            'field_label' => sanitize_text_field($data['field_label']),
            'field_slug' => sanitize_key($data['field_slug']),
            'field_type' => sanitize_text_field($data['field_type']),
        );

        // Add optional fields if they exist in the database
        if (in_array('field_description', $table_columns) && isset($data['field_description'])) {
            $field_data['field_description'] = sanitize_textarea_field($data['field_description']);
        }

        if (in_array('field_required', $table_columns)) {
            $field_data['field_required'] = isset($data['field_required']) ? 1 : 0;
        } elseif (in_array('required', $table_columns)) {
            $field_data['required'] = isset($data['field_required']) ? 1 : 0;
        }

        if (in_array('placeholder', $table_columns) && isset($data['placeholder'])) {
            $field_data['placeholder'] = sanitize_text_field($data['placeholder']);
        }

        // Handle field options based on field type
        $options = array();

        if ($data['field_type'] === 'select' || $data['field_type'] === 'radio' || $data['field_type'] === 'checkbox') {
            $options['choices'] = isset($data['field_options']) ? sanitize_textarea_field($data['field_options']) : '';

            // If the database has a direct options column
            if (in_array('options', $table_columns) && isset($data['field_options'])) {
                $field_data['options'] = sanitize_textarea_field($data['field_options']);
            }
        } elseif ($data['field_type'] === 'lookup') {
            $options['lookup_table'] = isset($data['lookup_table_id']) ? intval($data['lookup_table_id']) : 0;
            $options['display_column'] = isset($data['lookup_display_column']) ? sanitize_key($data['lookup_display_column']) : '';

            // If the database has direct lookup columns
            if (in_array('lookup_table_id', $table_columns) && isset($data['lookup_table_id'])) {
                $field_data['lookup_table_id'] = intval($data['lookup_table_id']);
            }
            if (in_array('lookup_display_column', $table_columns) && isset($data['lookup_display_column'])) {
                $field_data['lookup_display_column'] = sanitize_key($data['lookup_display_column']);
            }

        } elseif ($data['field_type'] === 'formula') {
            $options['formula'] = isset($data['field_formula']) ? sanitize_textarea_field($data['field_formula']) : '';

            // If the database has a direct formula column
            if (in_array('formula_expression', $table_columns) && isset($data['field_formula'])) {
                $field_data['formula_expression'] = sanitize_textarea_field($data['field_formula']);
            }
        }

        // Add created_at if the column exists and we're creating a new field
        if (in_array('created_at', $table_columns) && (!isset($data['field_id']) || empty($data['field_id']))) {
            $field_data['created_at'] = current_time('mysql');
        }

        // Only add field_options if the column exists
        if (in_array('field_options', $table_columns)) {
            $field_data['field_options'] = serialize($options);
        }

        // Set empty values for rollup fields to avoid NULL errors
        if (in_array('rollup_related_table', $table_columns)) {
            $field_data['rollup_related_table'] = 0;
        }
        if (in_array('rollup_foreign_key', $table_columns)) {
            $field_data['rollup_foreign_key'] = '';
        }
        if (in_array('rollup_aggregation_type', $table_columns)) {
            $field_data['rollup_aggregation_type'] = '';
        }
        if (in_array('rollup_target_field', $table_columns)) {
            $field_data['rollup_target_field'] = '';
        }

        // If field ID is provided, update existing field
        if (isset($data['field_id']) && !empty($data['field_id'])) {
            $wpdb->update(
                $fields_table,
                $field_data,
                array('id' => intval($data['field_id']))
            );
            return intval($data['field_id']);
        }
        // Otherwise, insert new field
        else {
            $wpdb->insert($fields_table, $field_data);
            return $wpdb->insert_id;
        }
    }
}

// Initialize the AJAX handler
new DAB_AJAX_Handler();
