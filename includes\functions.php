

/**
 * Get fields for a specific table
 * This is the main AJAX handler for getting table fields
 */
add_action('wp_ajax_dab_get_table_fields', function () {
    global $wpdb;

    // Support both GET and POST requests
    $table_id = isset($_POST['table_id']) ? intval($_POST['table_id']) : (isset($_GET['table_id']) ? intval($_GET['table_id']) : 0);

    if (!$table_id) {
        wp_send_json_error(['message' => 'Invalid table ID']);
        return;
    }

    $fields_table = $wpdb->prefix . 'dab_fields';

    // Get all fields for the table, including all field types
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT id, field_slug, field_label, field_type FROM $fields_table WHERE table_id = %d ORDER BY field_label ASC",
        $table_id
    ));

    if (empty($fields)) {
        wp_send_json_error(['message' => 'No fields found for this table']);
        return;
    }

    wp_send_json_success($fields);
});