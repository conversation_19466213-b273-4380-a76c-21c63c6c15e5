<?php
/**
 * Workflow Executor
 * 
 * Executes workflow steps and manages workflow execution
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Workflow_Executor {
    
    /**
     * Initialize the workflow executor
     */
    public static function init() {
        add_action('dab_execute_workflow_background', array(__CLASS__, 'execute_workflow_background'));
    }
    
    /**
     * Execute workflow in background
     */
    public static function execute_workflow_background($execution_id) {
        global $wpdb;
        
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';
        $workflows_table = $wpdb->prefix . 'dab_workflows';
        
        // Get execution details
        $execution = $wpdb->get_row($wpdb->prepare(
            "SELECT e.*, w.workflow_steps 
             FROM $executions_table e 
             JOIN $workflows_table w ON e.workflow_id = w.id 
             WHERE e.id = %d",
            $execution_id
        ));
        
        if (!$execution) {
            return false;
        }
        
        // Update status to running
        $wpdb->update($executions_table, 
            array('execution_status' => 'running'),
            array('id' => $execution_id)
        );
        
        $workflow_steps = json_decode($execution->workflow_steps, true);
        $trigger_data = json_decode($execution->trigger_data, true);
        
        if (empty($workflow_steps)) {
            self::mark_execution_completed($execution_id, 'No steps to execute');
            return true;
        }
        
        $execution_log = array();
        $step_count = 0;
        $total_steps = count($workflow_steps);
        
        // Update total steps
        $wpdb->update($executions_table,
            array('total_steps' => $total_steps),
            array('id' => $execution_id)
        );
        
        try {
            foreach ($workflow_steps as $step) {
                $step_count++;
                
                $step_result = self::execute_step($step, $trigger_data, $execution_log);
                
                $execution_log[] = array(
                    'step' => $step_count,
                    'action' => $step['action'],
                    'status' => $step_result['status'],
                    'message' => $step_result['message'],
                    'timestamp' => current_time('mysql')
                );
                
                // Update progress
                $wpdb->update($executions_table,
                    array(
                        'steps_completed' => $step_count,
                        'execution_log' => json_encode($execution_log)
                    ),
                    array('id' => $execution_id)
                );
                
                if ($step_result['status'] === 'error') {
                    throw new Exception($step_result['message']);
                }
                
                // Handle conditional branching
                if ($step['action'] === 'condition' && isset($step_result['branch'])) {
                    if ($step_result['branch'] === 'false' && isset($step['config']['false_path'])) {
                        // Skip to false path steps
                        continue;
                    }
                }
                
                // Handle delays
                if ($step['action'] === 'delay') {
                    $delay_seconds = self::calculate_delay($step['config']);
                    if ($delay_seconds > 0) {
                        // Schedule continuation of workflow
                        wp_schedule_single_event(
                            time() + $delay_seconds,
                            'dab_continue_workflow_execution',
                            array($execution_id, $step_count)
                        );
                        return true; // Exit for now, will continue later
                    }
                }
            }
            
            self::mark_execution_completed($execution_id, 'Workflow completed successfully');
            
        } catch (Exception $e) {
            self::mark_execution_failed($execution_id, $e->getMessage(), $execution_log);
        }
        
        return true;
    }
    
    /**
     * Execute a single workflow step
     */
    public static function execute_step($step, $trigger_data, $execution_log) {
        $action = $step['action'];
        $config = $step['config'];
        
        switch ($action) {
            case 'send_email':
                return self::execute_send_email($config, $trigger_data);
                
            case 'create_record':
                return self::execute_create_record($config, $trigger_data);
                
            case 'update_record':
                return self::execute_update_record($config, $trigger_data);
                
            case 'delete_record':
                return self::execute_delete_record($config, $trigger_data);
                
            case 'api_call':
                return self::execute_api_call($config, $trigger_data);
                
            case 'condition':
                return self::execute_condition($config, $trigger_data);
                
            case 'calculate':
                return self::execute_calculate($config, $trigger_data);
                
            case 'delay':
                return array('status' => 'success', 'message' => 'Delay step processed');
                
            default:
                return array('status' => 'error', 'message' => 'Unknown action: ' . $action);
        }
    }
    
    /**
     * Execute send email action
     */
    public static function execute_send_email($config, $trigger_data) {
        try {
            $to = self::parse_dynamic_value($config['to'], $trigger_data);
            $subject = self::parse_dynamic_value($config['subject'], $trigger_data);
            $message = self::parse_dynamic_value($config['message'], $trigger_data);
            
            $headers = array('Content-Type: text/html; charset=UTF-8');
            
            if (isset($config['from_email']) && !empty($config['from_email'])) {
                $from_email = self::parse_dynamic_value($config['from_email'], $trigger_data);
                $from_name = isset($config['from_name']) ? self::parse_dynamic_value($config['from_name'], $trigger_data) : get_bloginfo('name');
                $headers[] = 'From: ' . $from_name . ' <' . $from_email . '>';
            }
            
            $result = wp_mail($to, $subject, $message, $headers);
            
            if ($result) {
                return array('status' => 'success', 'message' => 'Email sent to ' . $to);
            } else {
                return array('status' => 'error', 'message' => 'Failed to send email');
            }
            
        } catch (Exception $e) {
            return array('status' => 'error', 'message' => 'Email error: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute create record action
     */
    public static function execute_create_record($config, $trigger_data) {
        try {
            global $wpdb;
            
            $table_id = $config['table_id'];
            $field_mappings = $config['field_mappings'];
            
            // Get table info
            $tables_table = $wpdb->prefix . 'dab_tables';
            $table = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id = %d",
                $table_id
            ));
            
            if (!$table) {
                return array('status' => 'error', 'message' => 'Table not found');
            }
            
            $data_table = $wpdb->prefix . 'dab_data_' . $table->table_slug;
            
            // Prepare data
            $insert_data = array();
            foreach ($field_mappings as $field_slug => $value_config) {
                $value = self::parse_dynamic_value($value_config, $trigger_data);
                $insert_data[$field_slug] = $value;
            }
            
            $result = $wpdb->insert($data_table, $insert_data);
            
            if ($result !== false) {
                $record_id = $wpdb->insert_id;
                return array('status' => 'success', 'message' => 'Record created with ID: ' . $record_id);
            } else {
                return array('status' => 'error', 'message' => 'Failed to create record');
            }
            
        } catch (Exception $e) {
            return array('status' => 'error', 'message' => 'Create record error: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute update record action
     */
    public static function execute_update_record($config, $trigger_data) {
        try {
            global $wpdb;
            
            $table_id = $config['table_id'];
            $record_id = self::parse_dynamic_value($config['record_id'], $trigger_data);
            $field_mappings = $config['field_mappings'];
            
            // Get table info
            $tables_table = $wpdb->prefix . 'dab_tables';
            $table = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id = %d",
                $table_id
            ));
            
            if (!$table) {
                return array('status' => 'error', 'message' => 'Table not found');
            }
            
            $data_table = $wpdb->prefix . 'dab_data_' . $table->table_slug;
            
            // Prepare data
            $update_data = array();
            foreach ($field_mappings as $field_slug => $value_config) {
                $value = self::parse_dynamic_value($value_config, $trigger_data);
                $update_data[$field_slug] = $value;
            }
            
            $result = $wpdb->update($data_table, $update_data, array('id' => $record_id));
            
            if ($result !== false) {
                return array('status' => 'success', 'message' => 'Record updated successfully');
            } else {
                return array('status' => 'error', 'message' => 'Failed to update record');
            }
            
        } catch (Exception $e) {
            return array('status' => 'error', 'message' => 'Update record error: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute API call action
     */
    public static function execute_api_call($config, $trigger_data) {
        try {
            $url = self::parse_dynamic_value($config['url'], $trigger_data);
            $method = strtoupper($config['method']);
            $headers = isset($config['headers']) ? $config['headers'] : array();
            $body = isset($config['body']) ? self::parse_dynamic_value($config['body'], $trigger_data) : '';
            
            $args = array(
                'method' => $method,
                'timeout' => 30,
                'headers' => $headers
            );
            
            if (!empty($body) && in_array($method, array('POST', 'PUT', 'PATCH'))) {
                $args['body'] = $body;
            }
            
            $response = wp_remote_request($url, $args);
            
            if (is_wp_error($response)) {
                return array('status' => 'error', 'message' => 'API call failed: ' . $response->get_error_message());
            }
            
            $response_code = wp_remote_retrieve_response_code($response);
            
            if ($response_code >= 200 && $response_code < 300) {
                return array('status' => 'success', 'message' => 'API call successful (HTTP ' . $response_code . ')');
            } else {
                return array('status' => 'error', 'message' => 'API call failed with HTTP ' . $response_code);
            }
            
        } catch (Exception $e) {
            return array('status' => 'error', 'message' => 'API call error: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute condition action
     */
    public static function execute_condition($config, $trigger_data) {
        try {
            $condition_logic = $config['condition_logic'];
            $result = self::evaluate_condition($condition_logic, $trigger_data);
            
            return array(
                'status' => 'success',
                'message' => 'Condition evaluated to: ' . ($result ? 'true' : 'false'),
                'branch' => $result ? 'true' : 'false'
            );
            
        } catch (Exception $e) {
            return array('status' => 'error', 'message' => 'Condition error: ' . $e->getMessage());
        }
    }
    
    /**
     * Parse dynamic values from trigger data
     */
    public static function parse_dynamic_value($value, $trigger_data) {
        if (!is_string($value)) {
            return $value;
        }
        
        // Replace placeholders like {{form_data.field_name}}
        $value = preg_replace_callback('/\{\{([^}]+)\}\}/', function($matches) use ($trigger_data) {
            $path = explode('.', $matches[1]);
            $current = $trigger_data;
            
            foreach ($path as $key) {
                if (is_array($current) && isset($current[$key])) {
                    $current = $current[$key];
                } else {
                    return $matches[0]; // Return original if path not found
                }
            }
            
            return $current;
        }, $value);
        
        return $value;
    }
    
    /**
     * Mark execution as completed
     */
    public static function mark_execution_completed($execution_id, $message = '') {
        global $wpdb;
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';
        
        $wpdb->update($executions_table,
            array(
                'execution_status' => 'completed',
                'completed_at' => current_time('mysql'),
                'error_message' => $message
            ),
            array('id' => $execution_id)
        );
    }
    
    /**
     * Mark execution as failed
     */
    public static function mark_execution_failed($execution_id, $error_message, $execution_log = array()) {
        global $wpdb;
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';
        
        $wpdb->update($executions_table,
            array(
                'execution_status' => 'failed',
                'completed_at' => current_time('mysql'),
                'error_message' => $error_message,
                'execution_log' => json_encode($execution_log)
            ),
            array('id' => $execution_id)
        );
    }
}
