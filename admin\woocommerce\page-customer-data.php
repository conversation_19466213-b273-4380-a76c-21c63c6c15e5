<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Handle customer segment creation
if (isset($_POST['dab_create_segment']) && wp_verify_nonce($_POST['dab_segment_nonce'], 'dab_create_segment')) {
    $segment_data = array(
        'segment_name' => sanitize_text_field($_POST['segment_name']),
        'segment_description' => sanitize_textarea_field($_POST['segment_description']),
        'segment_color' => sanitize_hex_color($_POST['segment_color']),
        'segment_criteria' => serialize($_POST['segment_criteria']),
        'is_active' => isset($_POST['is_active']) ? 1 : 0
    );
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'dab_wc_customer_segments';
    
    $result = $wpdb->insert($table_name, $segment_data);
    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('Customer segment created successfully.', 'db-app-builder') . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>' . __('Error creating customer segment.', 'db-app-builder') . '</p></div>';
    }
}

// Get customer statistics
global $wpdb;

// Total customers
$total_customers = $wpdb->get_var("
    SELECT COUNT(DISTINCT u.ID) 
    FROM {$wpdb->users} u 
    INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id 
    WHERE um.meta_key = 'wp_capabilities' 
    AND um.meta_value LIKE '%customer%'
");

// Customers with orders
$customers_with_orders = $wpdb->get_var("
    SELECT COUNT(DISTINCT pm.meta_value) 
    FROM {$wpdb->postmeta} pm 
    INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID 
    WHERE pm.meta_key = '_customer_user' 
    AND pm.meta_value > 0 
    AND p.post_type = 'shop_order' 
    AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
");

// New customers this month
$new_customers_this_month = $wpdb->get_var("
    SELECT COUNT(DISTINCT u.ID) 
    FROM {$wpdb->users} u 
    INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id 
    WHERE um.meta_key = 'wp_capabilities' 
    AND um.meta_value LIKE '%customer%'
    AND YEAR(u.user_registered) = YEAR(CURDATE()) 
    AND MONTH(u.user_registered) = MONTH(CURDATE())
");

// Get customer segments
$segments_table = $wpdb->prefix . 'dab_wc_customer_segments';
$segments = $wpdb->get_results("SELECT * FROM $segments_table ORDER BY segment_name ASC");

// Get recent customer data
$recent_customers = $wpdb->get_results("
    SELECT u.ID, u.display_name, u.user_email, u.user_registered,
           (SELECT COUNT(*) FROM {$wpdb->posts} p 
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
            WHERE pm.meta_key = '_customer_user' 
            AND pm.meta_value = u.ID 
            AND p.post_type = 'shop_order' 
            AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')) as order_count,
           (SELECT SUM(CAST(pm2.meta_value AS DECIMAL(10,2))) 
            FROM {$wpdb->posts} p2 
            INNER JOIN {$wpdb->postmeta} pm1 ON p2.ID = pm1.post_id 
            INNER JOIN {$wpdb->postmeta} pm2 ON p2.ID = pm2.post_id 
            WHERE pm1.meta_key = '_customer_user' 
            AND pm1.meta_value = u.ID 
            AND pm2.meta_key = '_order_total'
            AND p2.post_type = 'shop_order' 
            AND p2.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')) as total_spent
    FROM {$wpdb->users} u 
    INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id 
    WHERE um.meta_key = 'wp_capabilities' 
    AND um.meta_value LIKE '%customer%'
    ORDER BY u.user_registered DESC 
    LIMIT 20
");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Customer Data', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Manage enhanced customer data, create customer segments, and analyze customer behavior.', 'db-app-builder'); ?></p>
    </div>

    <!-- Customer Statistics -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($total_customers); ?></div>
            <div class="dab-stat-label"><?php _e('Total Customers', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($customers_with_orders); ?></div>
            <div class="dab-stat-label"><?php _e('Customers with Orders', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($new_customers_this_month); ?></div>
            <div class="dab-stat-label"><?php _e('New This Month', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo count($segments); ?></div>
            <div class="dab-stat-label"><?php _e('Customer Segments', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Customer Segments -->
            <div class="dab-card">
                <h2><?php _e('Customer Segments', 'db-app-builder'); ?></h2>
                
                <?php if (empty($segments)): ?>
                    <p><?php _e('No customer segments created yet.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Segment Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Description', 'db-app-builder'); ?></th>
                                <th><?php _e('Customers', 'db-app-builder'); ?></th>
                                <th><?php _e('Status', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($segments as $segment): ?>
                                <?php
                                $assignments_table = $wpdb->prefix . 'dab_wc_customer_segment_assignments';
                                $customer_count = $wpdb->get_var($wpdb->prepare(
                                    "SELECT COUNT(*) FROM $assignments_table WHERE segment_id = %d",
                                    $segment->id
                                ));
                                ?>
                                <tr>
                                    <td>
                                        <span class="dab-segment-color" style="background-color: <?php echo esc_attr($segment->segment_color); ?>"></span>
                                        <strong><?php echo esc_html($segment->segment_name); ?></strong>
                                    </td>
                                    <td><?php echo esc_html($segment->segment_description); ?></td>
                                    <td><?php echo number_format($customer_count); ?></td>
                                    <td>
                                        <?php if ($segment->is_active): ?>
                                            <span class="dab-status-active"><?php _e('Active', 'db-app-builder'); ?></span>
                                        <?php else: ?>
                                            <span class="dab-status-inactive"><?php _e('Inactive', 'db-app-builder'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="button button-small" onclick="editSegment(<?php echo $segment->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                        <button class="button button-small button-link-delete" onclick="deleteSegment(<?php echo $segment->id; ?>)"><?php _e('Delete', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                
                <p>
                    <button class="button button-primary" onclick="showCreateSegmentForm()"><?php _e('Create New Segment', 'db-app-builder'); ?></button>
                </p>
            </div>

            <!-- Recent Customers -->
            <div class="dab-card">
                <h2><?php _e('Recent Customers', 'db-app-builder'); ?></h2>
                
                <?php if (empty($recent_customers)): ?>
                    <p><?php _e('No customers found.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Customer', 'db-app-builder'); ?></th>
                                <th><?php _e('Email', 'db-app-builder'); ?></th>
                                <th><?php _e('Registered', 'db-app-builder'); ?></th>
                                <th><?php _e('Orders', 'db-app-builder'); ?></th>
                                <th><?php _e('Total Spent', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_customers as $customer): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($customer->display_name); ?></strong>
                                    </td>
                                    <td><?php echo esc_html($customer->user_email); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format'), strtotime($customer->user_registered)); ?></td>
                                    <td><?php echo number_format($customer->order_count); ?></td>
                                    <td><?php echo wc_price($customer->total_spent ?: 0); ?></td>
                                    <td>
                                        <a href="<?php echo admin_url('user-edit.php?user_id=' . $customer->ID); ?>" class="button button-small"><?php _e('Edit', 'db-app-builder'); ?></a>
                                        <button class="button button-small" onclick="viewCustomerDetails(<?php echo $customer->ID; ?>)"><?php _e('Details', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Customer Field Configuration -->
            <div class="dab-card">
                <h3><?php _e('Customer Field Configuration', 'db-app-builder'); ?></h3>
                <p><?php _e('Configure additional fields for customer profiles.', 'db-app-builder'); ?></p>
                
                <div class="dab-field-list">
                    <?php
                    $field_configs = DAB_Customer_Data_Manager::get_customer_field_configs();
                    foreach ($field_configs as $field_config):
                    ?>
                        <div class="dab-field-item">
                            <strong><?php echo esc_html($field_config['label']); ?></strong>
                            <span class="dab-field-type">(<?php echo esc_html($field_config['type']); ?>)</span>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <p>
                    <a href="#" class="button" onclick="configureCustomerFields()"><?php _e('Configure Fields', 'db-app-builder'); ?></a>
                </p>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="#" onclick="exportCustomerData()"><?php _e('Export Customer Data', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="importCustomerData()"><?php _e('Import Customer Data', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="bulkUpdateSegments()"><?php _e('Bulk Update Segments', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('users.php?role=customer'); ?>"><?php _e('View All Customers', 'db-app-builder'); ?></a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Create Segment Modal -->
<div id="dab-create-segment-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h2><?php _e('Create Customer Segment', 'db-app-builder'); ?></h2>
            <button class="dab-modal-close" onclick="hideCreateSegmentForm()">&times;</button>
        </div>
        
        <form method="post" action="">
            <?php wp_nonce_field('dab_create_segment', 'dab_segment_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="segment_name"><?php _e('Segment Name', 'db-app-builder'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="segment_name" name="segment_name" class="regular-text" required>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="segment_description"><?php _e('Description', 'db-app-builder'); ?></label>
                    </th>
                    <td>
                        <textarea id="segment_description" name="segment_description" rows="3" class="large-text"></textarea>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="segment_color"><?php _e('Color', 'db-app-builder'); ?></label>
                    </th>
                    <td>
                        <input type="color" id="segment_color" name="segment_color" value="#007cba">
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Status', 'db-app-builder'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="is_active" value="1" checked>
                            <?php _e('Active', 'db-app-builder'); ?>
                        </label>
                    </td>
                </tr>
            </table>
            
            <input type="hidden" name="segment_criteria" value="{}">
            
            <p class="submit">
                <input type="submit" name="dab_create_segment" class="button-primary" value="<?php _e('Create Segment', 'db-app-builder'); ?>">
                <button type="button" class="button" onclick="hideCreateSegmentForm()"><?php _e('Cancel', 'db-app-builder'); ?></button>
            </p>
        </form>
    </div>
</div>

<script>
function showCreateSegmentForm() {
    document.getElementById('dab-create-segment-modal').style.display = 'block';
}

function hideCreateSegmentForm() {
    document.getElementById('dab-create-segment-modal').style.display = 'none';
}

function editSegment(segmentId) {
    // TODO: Implement segment editing
    alert('Segment editing will be implemented in the next update.');
}

function deleteSegment(segmentId) {
    if (confirm('<?php _e('Are you sure you want to delete this segment?', 'db-app-builder'); ?>')) {
        // TODO: Implement segment deletion
        alert('Segment deletion will be implemented in the next update.');
    }
}

function viewCustomerDetails(customerId) {
    // TODO: Implement customer details modal
    alert('Customer details view will be implemented in the next update.');
}

function configureCustomerFields() {
    // TODO: Implement field configuration
    alert('Customer field configuration will be implemented in the next update.');
}

function exportCustomerData() {
    // TODO: Implement data export
    alert('Customer data export will be implemented in the next update.');
}

function importCustomerData() {
    // TODO: Implement data import
    alert('Customer data import will be implemented in the next update.');
}

function bulkUpdateSegments() {
    // TODO: Implement bulk segment updates
    alert('Bulk segment updates will be implemented in the next update.');
}
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-segment-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
}

.dab-status-active {
    color: #00a32a;
    font-weight: bold;
}

.dab-status-inactive {
    color: #d63638;
    font-weight: bold;
}

.dab-field-list {
    margin: 15px 0;
}

.dab-field-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.dab-field-type {
    color: #666;
    font-size: 12px;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.dab-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.dab-modal-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-modal-header h2 {
    margin: 0;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.dab-modal-content .form-table {
    margin: 20px;
}

.dab-modal-content .submit {
    margin: 0 20px 20px;
}
</style>
