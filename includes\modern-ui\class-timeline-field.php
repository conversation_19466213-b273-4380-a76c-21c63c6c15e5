<?php
/**
 * Timeline View Field Type
 *
 * Timeline visualization for project tracking and event sequences
 *
 * @package Database App Builder
 * @subpackage Modern UI Components
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Timeline_Field {

    /**
     * Initialize the Timeline field
     */
    public static function init() {
        add_filter('dab_field_types', array(__CLASS__, 'register_timeline_field_type'));
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_save_timeline_item', array(__CLASS__, 'save_timeline_item'));
        add_action('wp_ajax_nopriv_dab_save_timeline_item', array(__CLASS__, 'save_timeline_item'));
        add_action('wp_ajax_dab_get_timeline_data', array(__CLASS__, 'ajax_get_timeline_data'));
        add_action('wp_ajax_nopriv_dab_get_timeline_data', array(__CLASS__, 'ajax_get_timeline_data'));

        // Create database tables
        add_action('init', array(__CLASS__, 'create_tables'));
    }

    /**
     * Register the Timeline field type
     */
    public static function register_timeline_field_type($field_types) {
        $field_types['timeline_view'] = __('Timeline View', 'db-app-builder');
        return $field_types;
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'dab-timeline-view',
            plugin_dir_url(__FILE__) . '../../assets/css/timeline-view.css',
            array(),
            '1.0.0'
        );

        wp_enqueue_script(
            'dab-timeline-view',
            plugin_dir_url(__FILE__) . '../../assets/js/timeline-view.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('dab-timeline-view', 'dabTimelineData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_timeline_nonce'),
            'i18n' => array(
                'addItem' => __('Add Timeline Item', 'db-app-builder'),
                'editItem' => __('Edit Timeline Item', 'db-app-builder'),
                'deleteItem' => __('Delete Timeline Item', 'db-app-builder'),
                'title' => __('Title', 'db-app-builder'),
                'description' => __('Description', 'db-app-builder'),
                'date' => __('Date', 'db-app-builder'),
                'status' => __('Status', 'db-app-builder'),
                'milestone' => __('Milestone', 'db-app-builder'),
                'confirmDelete' => __('Are you sure you want to delete this timeline item?', 'db-app-builder'),
                'saveSuccess' => __('Timeline item saved successfully', 'db-app-builder'),
                'saveError' => __('Error saving timeline item', 'db-app-builder')
            )
        ));
    }

    /**
     * Create database tables for Timeline data
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Timeline items table
        $timeline_items_table = $wpdb->prefix . 'dab_timeline_items';
        $sql_items = "CREATE TABLE $timeline_items_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            field_id bigint(20) NOT NULL,
            record_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            item_date datetime NOT NULL,
            status varchar(50) DEFAULT 'pending',
            is_milestone tinyint(1) DEFAULT 0,
            color varchar(7) DEFAULT '#3498db',
            icon varchar(50),
            position int(11) DEFAULT 0,
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY field_id (field_id),
            KEY record_id (record_id),
            KEY item_date (item_date),
            KEY status (status),
            KEY created_by (created_by)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_items);
    }

    /**
     * Render the Timeline field in forms
     */
    public static function render_field($field, $value = '', $record_id = 0) {
        $field_id = 'dab-timeline-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);

        // Get existing timeline data
        $timeline_data = self::get_timeline_data($field->id, $record_id);

        echo '<div class="dab-timeline-container" id="' . $field_id . '" data-field-id="' . $field->id . '" data-record-id="' . $record_id . '">';

        // Timeline header with controls
        echo '<div class="dab-timeline-header">';
        echo '<div class="dab-timeline-title">' . esc_html($field->field_label) . '</div>';
        echo '<div class="dab-timeline-controls">';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-add-timeline-item">';
        echo '<span class="dashicons dashicons-plus-alt"></span> ' . __('Add Item', 'db-app-builder');
        echo '</button>';
        echo '</div>';
        echo '</div>';

        // Timeline view
        echo '<div class="dab-timeline-view" id="timeline-view-' . $field->id . '">';

        if (!empty($timeline_data)) {
            foreach ($timeline_data as $item) {
                self::render_timeline_item($item);
            }
        } else {
            echo '<div class="dab-timeline-empty">';
            echo '<div class="dashicons dashicons-clock"></div>';
            echo '<p>' . __('No timeline items yet. Add your first item to get started.', 'db-app-builder') . '</p>';
            echo '</div>';
        }

        echo '</div>';

        // Hidden input to store timeline data
        echo '<input type="hidden" name="' . $field_name . '" id="' . $field_name . '" value="' . esc_attr(json_encode($timeline_data)) . '">';

        echo '</div>';

        // Add modal for item editing
        self::render_item_modal();
    }

    /**
     * Render a timeline item
     */
    private static function render_timeline_item($item) {
        $item_id = isset($item->id) ? $item->id : 0;
        $title = isset($item->title) ? $item->title : '';
        $description = isset($item->description) ? $item->description : '';
        $item_date = isset($item->item_date) ? $item->item_date : '';
        $status = isset($item->status) ? $item->status : 'pending';
        $is_milestone = isset($item->is_milestone) ? $item->is_milestone : 0;
        $color = isset($item->color) ? $item->color : '#3498db';
        $icon = isset($item->icon) ? $item->icon : 'clock';

        $status_class = 'dab-status-' . $status;
        $milestone_class = $is_milestone ? 'dab-milestone' : '';

        echo '<div class="dab-timeline-item ' . $status_class . ' ' . $milestone_class . '" data-item-id="' . $item_id . '">';

        // Timeline marker
        echo '<div class="dab-timeline-marker" style="background-color: ' . esc_attr($color) . '">';
        echo '<span class="dashicons dashicons-' . esc_attr($icon) . '"></span>';
        echo '</div>';

        // Timeline content
        echo '<div class="dab-timeline-content">';

        // Item header
        echo '<div class="dab-timeline-item-header">';
        echo '<h4 class="dab-timeline-item-title">' . esc_html($title) . '</h4>';
        echo '<div class="dab-timeline-item-meta">';
        echo '<span class="dab-timeline-date">' . date('M j, Y', strtotime($item_date)) . '</span>';
        echo '<span class="dab-timeline-status dab-status-' . $status . '">' . ucfirst($status) . '</span>';
        if ($is_milestone) {
            echo '<span class="dab-milestone-badge">' . __('Milestone', 'db-app-builder') . '</span>';
        }
        echo '</div>';
        echo '</div>';

        // Item description
        if (!empty($description)) {
            echo '<div class="dab-timeline-item-description">' . wp_kses_post($description) . '</div>';
        }

        // Item actions
        echo '<div class="dab-timeline-item-actions">';
        echo '<button type="button" class="dab-btn-icon dab-edit-timeline-item" title="' . __('Edit', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-edit"></span>';
        echo '</button>';
        echo '<button type="button" class="dab-btn-icon dab-delete-timeline-item" title="' . __('Delete', 'db-app-builder') . '">';
        echo '<span class="dashicons dashicons-trash"></span>';
        echo '</button>';
        echo '</div>';

        echo '</div>';

        echo '</div>';
    }

    /**
     * Render item editing modal
     */
    private static function render_item_modal() {
        echo '<div id="dab-timeline-item-modal" class="dab-modal" style="display: none;">';
        echo '<div class="dab-modal-content">';
        echo '<div class="dab-modal-header">';
        echo '<h3 id="dab-timeline-item-modal-title">' . __('Add Timeline Item', 'db-app-builder') . '</h3>';
        echo '<button type="button" class="dab-modal-close">&times;</button>';
        echo '</div>';
        echo '<div class="dab-modal-body">';

        // Item form fields
        echo '<div class="dab-form-field">';
        echo '<label for="dab-timeline-item-title">' . __('Title', 'db-app-builder') . ' <span class="required">*</span></label>';
        echo '<input type="text" id="dab-timeline-item-title" class="dab-form-control" required>';
        echo '</div>';

        echo '<div class="dab-form-field">';
        echo '<label for="dab-timeline-item-description">' . __('Description', 'db-app-builder') . '</label>';
        echo '<textarea id="dab-timeline-item-description" class="dab-form-control" rows="3"></textarea>';
        echo '</div>';

        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-timeline-item-date">' . __('Date', 'db-app-builder') . ' <span class="required">*</span></label>';
        echo '<input type="datetime-local" id="dab-timeline-item-date" class="dab-form-control" required>';
        echo '</div>';

        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-timeline-item-status">' . __('Status', 'db-app-builder') . '</label>';
        echo '<select id="dab-timeline-item-status" class="dab-form-control">';
        echo '<option value="pending">' . __('Pending', 'db-app-builder') . '</option>';
        echo '<option value="in-progress">' . __('In Progress', 'db-app-builder') . '</option>';
        echo '<option value="completed">' . __('Completed', 'db-app-builder') . '</option>';
        echo '<option value="cancelled">' . __('Cancelled', 'db-app-builder') . '</option>';
        echo '</select>';
        echo '</div>';
        echo '</div>';

        echo '<div class="dab-form-row">';
        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-timeline-item-icon">' . __('Icon', 'db-app-builder') . '</label>';
        echo '<select id="dab-timeline-item-icon" class="dab-form-control">';
        echo '<option value="clock">' . __('Clock', 'db-app-builder') . '</option>';
        echo '<option value="yes">' . __('Checkmark', 'db-app-builder') . '</option>';
        echo '<option value="star-filled">' . __('Star', 'db-app-builder') . '</option>';
        echo '<option value="flag">' . __('Flag', 'db-app-builder') . '</option>';
        echo '<option value="calendar-alt">' . __('Calendar', 'db-app-builder') . '</option>';
        echo '<option value="admin-users">' . __('Users', 'db-app-builder') . '</option>';
        echo '<option value="admin-tools">' . __('Tools', 'db-app-builder') . '</option>';
        echo '<option value="lightbulb">' . __('Lightbulb', 'db-app-builder') . '</option>';
        echo '</select>';
        echo '</div>';

        echo '<div class="dab-form-field dab-form-field-half">';
        echo '<label for="dab-timeline-item-color">' . __('Color', 'db-app-builder') . '</label>';
        echo '<div class="dab-color-picker">';
        $colors = array('#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22');
        foreach ($colors as $color) {
            echo '<button type="button" class="dab-color-option" data-color="' . $color . '" style="background-color: ' . $color . '"></button>';
        }
        echo '</div>';
        echo '<input type="hidden" id="dab-timeline-item-color" value="#3498db">';
        echo '</div>';
        echo '</div>';

        echo '<div class="dab-form-field">';
        echo '<label>';
        echo '<input type="checkbox" id="dab-timeline-item-milestone" class="dab-checkbox">';
        echo ' ' . __('Mark as Milestone', 'db-app-builder');
        echo '</label>';
        echo '</div>';

        echo '</div>';
        echo '<div class="dab-modal-footer">';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-modal-close">' . __('Cancel', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-danger dab-delete-timeline-item-modal" style="display: none;">' . __('Delete', 'db-app-builder') . '</button>';
        echo '<button type="button" class="dab-btn dab-btn-primary dab-save-timeline-item">' . __('Save', 'db-app-builder') . '</button>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Get timeline data for a field and record
     */
    private static function get_timeline_data($field_id, $record_id) {
        global $wpdb;

        $timeline_items_table = $wpdb->prefix . 'dab_timeline_items';

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $timeline_items_table WHERE field_id = %d AND record_id = %d ORDER BY item_date ASC, position ASC",
            $field_id, $record_id
        ));
    }

    /**
     * AJAX handler to save timeline item
     */
    public static function save_timeline_item() {
        check_ajax_referer('dab_timeline_nonce', 'nonce');

        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);
        $item_id = isset($_POST['item_id']) ? intval($_POST['item_id']) : 0;

        $item_data = array(
            'field_id' => $field_id,
            'record_id' => $record_id,
            'title' => sanitize_text_field($_POST['title']),
            'description' => sanitize_textarea_field($_POST['description']),
            'item_date' => sanitize_text_field($_POST['item_date']),
            'status' => sanitize_text_field($_POST['status']),
            'is_milestone' => isset($_POST['is_milestone']) ? 1 : 0,
            'color' => sanitize_hex_color($_POST['color']),
            'icon' => sanitize_text_field($_POST['icon'])
        );

        if (!$field_id || !$item_data['title'] || !$item_data['item_date']) {
            wp_send_json_error(__('Required fields are missing', 'db-app-builder'));
        }

        global $wpdb;
        $timeline_items_table = $wpdb->prefix . 'dab_timeline_items';

        try {
            if ($item_id > 0) {
                // Update existing item
                $result = $wpdb->update($timeline_items_table, $item_data, array('id' => $item_id));
            } else {
                // Create new item
                $item_data['created_by'] = get_current_user_id();
                $result = $wpdb->insert($timeline_items_table, $item_data);
                $item_id = $wpdb->insert_id;
            }

            if ($result !== false) {
                wp_send_json_success(array(
                    'item_id' => $item_id,
                    'message' => __('Timeline item saved successfully', 'db-app-builder')
                ));
            } else {
                wp_send_json_error(__('Failed to save timeline item', 'db-app-builder'));
            }

        } catch (Exception $e) {
            wp_send_json_error(__('Error saving timeline item', 'db-app-builder'));
        }
    }

    /**
     * AJAX handler to get timeline data
     */
    public static function ajax_get_timeline_data() {
        check_ajax_referer('dab_timeline_nonce', 'nonce');

        $field_id = intval($_POST['field_id']);
        $record_id = intval($_POST['record_id']);

        $timeline_data = self::get_timeline_data($field_id, $record_id);

        wp_send_json_success($timeline_data);
    }
}
