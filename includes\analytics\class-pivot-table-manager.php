<?php
/**
 * Pivot Table Manager Class
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles pivot table functionality for advanced data analysis including
 * row grouping, column grouping, aggregations, and interactive pivot tables.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Pivot_Table_Manager {
    
    /**
     * Initialize the Pivot Table Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_generate_pivot_table', array(__CLASS__, 'generate_pivot_table'));
        add_action('wp_ajax_dab_save_pivot_config', array(__CLASS__, 'save_pivot_config'));
        add_action('wp_ajax_dab_load_pivot_config', array(__CLASS__, 'load_pivot_config'));
        add_action('wp_ajax_nopriv_dab_generate_pivot_table', array(__CLASS__, 'generate_pivot_table'));
    }
    
    /**
     * Create database tables for pivot configurations
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Pivot configurations table
        $pivot_table = $wpdb->prefix . 'dab_pivot_configs';
        $sql = "CREATE TABLE $pivot_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            table_id int(11) NOT NULL,
            row_fields longtext,
            column_fields longtext,
            value_fields longtext,
            filter_config longtext,
            aggregation_config longtext,
            created_by int(11) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY table_id (table_id),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Generate pivot table
     */
    public static function generate_pivot_table() {
        // Verify nonce for admin requests
        if (is_admin() && (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce'))) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $table_id = intval($_POST['table_id']);
        $config = $_POST['config'];
        
        try {
            $result = self::build_pivot_table($table_id, $config);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Pivot table generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Save pivot table configuration
     */
    public static function save_pivot_config() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $pivot_table = $wpdb->prefix . 'dab_pivot_configs';
        
        $config_id = isset($_POST['config_id']) ? intval($_POST['config_id']) : 0;
        $name = sanitize_text_field($_POST['name']);
        $table_id = intval($_POST['table_id']);
        $row_fields = wp_json_encode($_POST['row_fields']);
        $column_fields = wp_json_encode($_POST['column_fields']);
        $value_fields = wp_json_encode($_POST['value_fields']);
        $filter_config = wp_json_encode($_POST['filter_config']);
        $aggregation_config = wp_json_encode($_POST['aggregation_config']);
        
        $data = array(
            'name' => $name,
            'table_id' => $table_id,
            'row_fields' => $row_fields,
            'column_fields' => $column_fields,
            'value_fields' => $value_fields,
            'filter_config' => $filter_config,
            'aggregation_config' => $aggregation_config
        );
        
        if ($config_id > 0) {
            // Update existing configuration
            $result = $wpdb->update($pivot_table, $data, array('id' => $config_id));
        } else {
            // Create new configuration
            $data['created_by'] = get_current_user_id();
            $result = $wpdb->insert($pivot_table, $data);
            $config_id = $wpdb->insert_id;
        }
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Pivot configuration saved successfully',
                'config_id' => $config_id
            ));
        } else {
            wp_send_json_error('Failed to save pivot configuration');
        }
    }
    
    /**
     * Load pivot table configuration
     */
    public static function load_pivot_config() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $config_id = intval($_GET['config_id']);
        
        global $wpdb;
        $pivot_table = $wpdb->prefix . 'dab_pivot_configs';
        
        $config = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $pivot_table WHERE id = %d",
            $config_id
        ));
        
        if (!$config) {
            wp_send_json_error('Configuration not found');
            return;
        }
        
        // Decode JSON fields
        $config->row_fields = json_decode($config->row_fields, true);
        $config->column_fields = json_decode($config->column_fields, true);
        $config->value_fields = json_decode($config->value_fields, true);
        $config->filter_config = json_decode($config->filter_config, true);
        $config->aggregation_config = json_decode($config->aggregation_config, true);
        
        wp_send_json_success($config);
    }
    
    /**
     * Build pivot table from configuration
     */
    public static function build_pivot_table($table_id, $config) {
        global $wpdb;
        
        // Get table information
        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $table_id
        ));
        
        if (!$table) {
            throw new Exception('Table not found');
        }
        
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        
        // Get all data
        $data = $wpdb->get_results("SELECT * FROM $data_table");
        
        if (empty($data)) {
            return array(
                'headers' => array(),
                'rows' => array(),
                'summary' => array('total_records' => 0)
            );
        }
        
        // Process pivot table
        $pivot_data = self::process_pivot_data($data, $config);
        
        return $pivot_data;
    }
    
    /**
     * Process data into pivot table format
     */
    private static function process_pivot_data($data, $config) {
        $row_fields = $config['row_fields'] ?? array();
        $column_fields = $config['column_fields'] ?? array();
        $value_fields = $config['value_fields'] ?? array();
        $aggregation_config = $config['aggregation_config'] ?? array();
        
        // Group data by row fields
        $grouped_data = self::group_by_fields($data, $row_fields);
        
        // Get unique column values
        $column_values = self::get_unique_column_values($data, $column_fields);
        
        // Build pivot table structure
        $pivot_table = array();
        $headers = array_merge($row_fields, $column_values);
        
        foreach ($grouped_data as $row_key => $row_data) {
            $pivot_row = array();
            
            // Add row field values
            $row_parts = explode('|', $row_key);
            foreach ($row_fields as $index => $field) {
                $pivot_row[$field] = $row_parts[$index] ?? '';
            }
            
            // Add aggregated values for each column
            foreach ($column_values as $col_value) {
                $filtered_data = self::filter_data_by_columns($row_data, $column_fields, $col_value);
                $aggregated_value = self::aggregate_data($filtered_data, $value_fields, $aggregation_config);
                $pivot_row[$col_value] = $aggregated_value;
            }
            
            $pivot_table[] = $pivot_row;
        }
        
        return array(
            'headers' => $headers,
            'rows' => $pivot_table,
            'summary' => array(
                'total_records' => count($data),
                'grouped_records' => count($pivot_table),
                'columns' => count($column_values)
            )
        );
    }
    
    /**
     * Group data by specified fields
     */
    private static function group_by_fields($data, $fields) {
        $grouped = array();
        
        foreach ($data as $row) {
            $key_parts = array();
            foreach ($fields as $field) {
                $key_parts[] = isset($row->$field) ? $row->$field : '';
            }
            $key = implode('|', $key_parts);
            
            if (!isset($grouped[$key])) {
                $grouped[$key] = array();
            }
            $grouped[$key][] = $row;
        }
        
        return $grouped;
    }
    
    /**
     * Get unique values for column fields
     */
    private static function get_unique_column_values($data, $column_fields) {
        if (empty($column_fields)) {
            return array('Total');
        }
        
        $unique_values = array();
        
        foreach ($data as $row) {
            $value_parts = array();
            foreach ($column_fields as $field) {
                $value_parts[] = isset($row->$field) ? $row->$field : '';
            }
            $value = implode(' - ', $value_parts);
            
            if (!in_array($value, $unique_values)) {
                $unique_values[] = $value;
            }
        }
        
        sort($unique_values);
        return $unique_values;
    }
    
    /**
     * Filter data by column values
     */
    private static function filter_data_by_columns($data, $column_fields, $target_value) {
        if (empty($column_fields) || $target_value === 'Total') {
            return $data;
        }
        
        $filtered = array();
        
        foreach ($data as $row) {
            $value_parts = array();
            foreach ($column_fields as $field) {
                $value_parts[] = isset($row->$field) ? $row->$field : '';
            }
            $value = implode(' - ', $value_parts);
            
            if ($value === $target_value) {
                $filtered[] = $row;
            }
        }
        
        return $filtered;
    }
    
    /**
     * Aggregate data based on configuration
     */
    private static function aggregate_data($data, $value_fields, $aggregation_config) {
        if (empty($data)) {
            return 0;
        }
        
        if (empty($value_fields)) {
            return count($data);
        }
        
        $result = array();
        
        foreach ($value_fields as $field) {
            $aggregation = isset($aggregation_config[$field]) ? $aggregation_config[$field] : 'sum';
            $values = array();
            
            foreach ($data as $row) {
                if (isset($row->$field) && is_numeric($row->$field)) {
                    $values[] = floatval($row->$field);
                }
            }
            
            if (empty($values)) {
                $result[$field] = 0;
                continue;
            }
            
            switch ($aggregation) {
                case 'sum':
                    $result[$field] = array_sum($values);
                    break;
                case 'average':
                case 'avg':
                    $result[$field] = array_sum($values) / count($values);
                    break;
                case 'count':
                    $result[$field] = count($values);
                    break;
                case 'min':
                    $result[$field] = min($values);
                    break;
                case 'max':
                    $result[$field] = max($values);
                    break;
                case 'median':
                    sort($values);
                    $count = count($values);
                    $middle = floor($count / 2);
                    if ($count % 2 === 0) {
                        $result[$field] = ($values[$middle - 1] + $values[$middle]) / 2;
                    } else {
                        $result[$field] = $values[$middle];
                    }
                    break;
                default:
                    $result[$field] = array_sum($values);
            }
        }
        
        // Return single value if only one field, otherwise return array
        if (count($value_fields) === 1) {
            return $result[$value_fields[0]];
        }
        
        return $result;
    }
    
    /**
     * Get available aggregation functions
     */
    public static function get_aggregation_functions() {
        return array(
            'sum' => 'Sum',
            'average' => 'Average',
            'count' => 'Count',
            'min' => 'Minimum',
            'max' => 'Maximum',
            'median' => 'Median'
        );
    }
    
    /**
     * Export pivot table to CSV
     */
    public static function export_pivot_to_csv($pivot_data, $filename = 'pivot_table.csv') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Write headers
        if (!empty($pivot_data['headers'])) {
            fputcsv($output, $pivot_data['headers']);
        }
        
        // Write data rows
        foreach ($pivot_data['rows'] as $row) {
            $csv_row = array();
            foreach ($pivot_data['headers'] as $header) {
                $csv_row[] = isset($row[$header]) ? $row[$header] : '';
            }
            fputcsv($output, $csv_row);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Generate pivot table HTML
     */
    public static function generate_pivot_html($pivot_data) {
        if (empty($pivot_data['rows'])) {
            return '<p>No data available for pivot table.</p>';
        }
        
        $html = '<div class="dab-pivot-table-container">';
        $html .= '<table class="dab-pivot-table">';
        
        // Headers
        $html .= '<thead><tr>';
        foreach ($pivot_data['headers'] as $header) {
            $html .= '<th>' . esc_html($header) . '</th>';
        }
        $html .= '</tr></thead>';
        
        // Data rows
        $html .= '<tbody>';
        foreach ($pivot_data['rows'] as $row) {
            $html .= '<tr>';
            foreach ($pivot_data['headers'] as $header) {
                $value = isset($row[$header]) ? $row[$header] : '';
                if (is_numeric($value)) {
                    $value = number_format($value, 2);
                }
                $html .= '<td>' . esc_html($value) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';
        
        $html .= '</table>';
        $html .= '</div>';
        
        // Summary
        if (!empty($pivot_data['summary'])) {
            $html .= '<div class="dab-pivot-summary">';
            $html .= '<p><strong>Summary:</strong> ';
            $html .= 'Total Records: ' . $pivot_data['summary']['total_records'] . ', ';
            $html .= 'Grouped Records: ' . $pivot_data['summary']['grouped_records'] . ', ';
            $html .= 'Columns: ' . $pivot_data['summary']['columns'];
            $html .= '</p>';
            $html .= '</div>';
        }
        
        return $html;
    }
}
