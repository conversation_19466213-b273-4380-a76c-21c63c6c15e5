<?php
/**
 * Field Edit Page
 *
 * Displays the field edit page with tabs for different settings.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';

// Get field ID from URL
$field_id = isset($_GET['field_id']) ? intval($_GET['field_id']) : 0;

// Get active tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'details';

// Get field data
$field = $wpdb->get_row($wpdb->prepare("SELECT * FROM $fields_table WHERE id = %d", $field_id));

if (!$field) {
    echo '<div class="notice notice-error"><p>Field not found.</p></div>';
    return;
}

// Get table data
$table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $field->table_id));

if (!$table) {
    echo '<div class="notice notice-error"><p>Table not found.</p></div>';
    return;
}

// Handle field update
if (isset($_POST['dab_update_field'])) {
    $field_label = sanitize_text_field($_POST['field_label']);
    $required = isset($_POST['required']) ? 1 : 0;
    $placeholder = sanitize_text_field($_POST['placeholder']);

    // Prepare update data
    $update_data = [
        'field_label' => $field_label,
        'required' => $required,
        'placeholder' => $placeholder
    ];

    // Handle enhanced currency field options
    if ($field->field_type === 'enhanced_currency' && isset($_POST['enhanced_currency_default'])) {
        // Get enhanced currency field options
        $currency = sanitize_text_field($_POST['enhanced_currency_default']);
        $symbol_position = sanitize_text_field($_POST['enhanced_currency_symbol_position']);
        $decimal_places = intval($_POST['enhanced_currency_decimal_places']);
        $thousand_separator = sanitize_text_field($_POST['enhanced_currency_thousand_separator']);
        $decimal_separator = sanitize_text_field($_POST['enhanced_currency_decimal_separator']);
        $allow_currency_selection = isset($_POST['enhanced_currency_allow_selection']) ? 1 : 0;

        // Store enhanced currency options in the options field as JSON
        $enhanced_currency_options = array(
            'currency' => $currency,
            'symbol_position' => $symbol_position,
            'decimal_places' => $decimal_places,
            'thousand_separator' => $thousand_separator,
            'decimal_separator' => $decimal_separator,
            'allow_currency_selection' => $allow_currency_selection
        );

        // Add options to update data
        $update_data['options'] = json_encode($enhanced_currency_options);
    }

    // Handle media field options
    if ($field->field_type === 'media' && isset($_POST['media_type'])) {
        // Get media field options
        $media_type = sanitize_text_field($_POST['media_type']);
        $allow_recording = isset($_POST['media_allow_recording']) ? 1 : 0;
        $allow_upload = isset($_POST['media_allow_upload']) ? 1 : 0;
        $allow_embed = isset($_POST['media_allow_embed']) ? 1 : 0;
        $max_file_size = intval($_POST['media_max_file_size']);
        $player_width = intval($_POST['media_player_width']);
        $player_height = intval($_POST['media_player_height']);

        // Get allowed formats (multiselect)
        $allowed_formats = isset($_POST['media_allowed_formats']) && is_array($_POST['media_allowed_formats'])
            ? array_map('sanitize_text_field', $_POST['media_allowed_formats'])
            : array('mp3', 'mp4');

        // Store media options in the options field as JSON
        $media_options = array(
            'media_type' => $media_type,
            'allow_recording' => $allow_recording,
            'allow_upload' => $allow_upload,
            'allow_embed' => $allow_embed,
            'max_file_size' => $max_file_size,
            'allowed_formats' => $allowed_formats,
            'player_width' => $player_width,
            'player_height' => $player_height
        );

        // Add options to update data
        $update_data['options'] = json_encode($media_options);
    }

    // Update field data
    $wpdb->update(
        $fields_table,
        $update_data,
        ['id' => $field_id]
    );

    // Refresh field data
    $field = $wpdb->get_row($wpdb->prepare("SELECT * FROM $fields_table WHERE id = %d", $field_id));

    echo '<div class="notice notice-success is-dismissible"><p>Field updated successfully.</p></div>';
}

// Get all tables for lookup and inline table fields
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label ASC");
?>

<div class="wrap dab-admin-wrap">
    <h1>Edit Field: <?php echo esc_html($field->field_label); ?></h1>

    <div class="dab-card dab-animate-scale-in">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Edit Field</h2>

            <!-- Tabs Navigation -->
            <div class="dab-admin-tabs">
                <a href="<?php echo admin_url('admin.php?page=dab_fields&action=edit&field_id=' . $field_id . '&tab=details'); ?>" class="dab-admin-tab-link <?php echo $active_tab === 'details' ? 'active' : ''; ?>">Details</a>
                <a href="<?php echo admin_url('admin.php?page=dab_fields&action=edit&field_id=' . $field_id . '&tab=permissions'); ?>" class="dab-admin-tab-link <?php echo $active_tab === 'permissions' ? 'active' : ''; ?>">Permissions</a>
            </div>
        </div>
        <div class="dab-card-body">
            <!-- Details Tab -->
            <?php if ($active_tab === 'details'): ?>
                <form method="post" action="" class="dab-admin-form">
                    <input type="hidden" name="dab_update_field" value="1">

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="field_label" class="dab-admin-form-label">Field Label</label>
                            <input type="text" id="field_label" name="field_label" class="dab-admin-form-control" value="<?php echo esc_attr($field->field_label); ?>" required>
                            <span class="dab-admin-form-help">The display name for your field</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="field_slug" class="dab-admin-form-label">Field Slug</label>
                            <input type="text" id="field_slug" name="field_slug" class="dab-admin-form-control" value="<?php echo esc_attr($field->field_slug); ?>" disabled>
                            <span class="dab-admin-form-help">The unique identifier for your field (cannot be changed)</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="field_type" class="dab-admin-form-label">Field Type</label>
                            <input type="text" id="field_type" name="field_type" class="dab-admin-form-control" value="<?php echo esc_attr(ucfirst($field->field_type)); ?>" disabled>
                            <span class="dab-admin-form-help">The type of field (cannot be changed)</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="required" class="dab-admin-form-label">Required</label>
                            <input type="checkbox" id="required" name="required" value="1" <?php checked($field->required, 1); ?>>
                            <span class="dab-admin-form-help">Whether this field is required</span>
                        </div>
                    </div>

                    <div class="dab-admin-form-row">
                        <div class="dab-admin-form-group">
                            <label for="placeholder" class="dab-admin-form-label">Placeholder</label>
                            <input type="text" id="placeholder" name="placeholder" class="dab-admin-form-control" value="<?php echo esc_attr($field->placeholder); ?>">
                            <span class="dab-admin-form-help">Placeholder text for this field</span>
                        </div>
                    </div>

                    <?php
                        // Parse existing options
                        $options = json_decode($field->options, true);
                        if (!is_array($options)) {
                            $options = array();
                        }
                    ?>

                    <?php if ($field->field_type === 'enhanced_currency'):
                        // Get field settings with defaults
                        $currency = isset($options['currency']) ? $options['currency'] : 'USD';
                        $symbol_position = isset($options['symbol_position']) ? $options['symbol_position'] : 'before';
                        $decimal_places = isset($options['decimal_places']) ? intval($options['decimal_places']) : 2;
                        $thousand_separator = isset($options['thousand_separator']) ? $options['thousand_separator'] : 'comma';
                        $decimal_separator = isset($options['decimal_separator']) ? $options['decimal_separator'] : 'dot';
                        $allow_currency_selection = isset($options['allow_currency_selection']) ? (bool)$options['allow_currency_selection'] : false;
                    ?>
                        <h3 class="dab-admin-form-section-title">Enhanced Currency Field Options</h3>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_default" class="dab-admin-form-label">Default Currency</label>
                                <select id="enhanced_currency_default" name="enhanced_currency_default" class="dab-admin-form-control">
                                    <option value="USD" <?php selected($currency, 'USD'); ?>>US Dollar ($)</option>
                                    <option value="EUR" <?php selected($currency, 'EUR'); ?>>Euro (€)</option>
                                    <option value="GBP" <?php selected($currency, 'GBP'); ?>>British Pound (£)</option>
                                    <option value="JPY" <?php selected($currency, 'JPY'); ?>>Japanese Yen (¥)</option>
                                    <option value="CNY" <?php selected($currency, 'CNY'); ?>>Chinese Yuan (¥)</option>
                                    <option value="INR" <?php selected($currency, 'INR'); ?>>Indian Rupee (₹)</option>
                                    <option value="CAD" <?php selected($currency, 'CAD'); ?>>Canadian Dollar (C$)</option>
                                    <option value="AUD" <?php selected($currency, 'AUD'); ?>>Australian Dollar (A$)</option>
                                    <option value="NGN" <?php selected($currency, 'NGN'); ?>>Nigerian Naira (₦)</option>
                                    <option value="ZAR" <?php selected($currency, 'ZAR'); ?>>South African Rand (R)</option>
                                    <option value="BRL" <?php selected($currency, 'BRL'); ?>>Brazilian Real (R$)</option>
                                    <option value="MXN" <?php selected($currency, 'MXN'); ?>>Mexican Peso (Mex$)</option>
                                </select>
                                <span class="dab-admin-form-help">Select the default currency for this field</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_symbol_position" class="dab-admin-form-label">Symbol Position</label>
                                <select id="enhanced_currency_symbol_position" name="enhanced_currency_symbol_position" class="dab-admin-form-control">
                                    <option value="before" <?php selected($symbol_position, 'before'); ?>>Before amount ($100)</option>
                                    <option value="after" <?php selected($symbol_position, 'after'); ?>>After amount (100$)</option>
                                </select>
                                <span class="dab-admin-form-help">Position of the currency symbol</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_decimal_places" class="dab-admin-form-label">Decimal Places</label>
                                <input type="number" id="enhanced_currency_decimal_places" name="enhanced_currency_decimal_places" class="dab-admin-form-control" value="<?php echo esc_attr($decimal_places); ?>" min="0" max="6">
                                <span class="dab-admin-form-help">Number of decimal places to display</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_thousand_separator" class="dab-admin-form-label">Thousand Separator</label>
                                <select id="enhanced_currency_thousand_separator" name="enhanced_currency_thousand_separator" class="dab-admin-form-control">
                                    <option value="comma" <?php selected($thousand_separator, 'comma'); ?>>Comma (,)</option>
                                    <option value="dot" <?php selected($thousand_separator, 'dot'); ?>>Dot (.)</option>
                                    <option value="space" <?php selected($thousand_separator, 'space'); ?>>Space</option>
                                    <option value="none" <?php selected($thousand_separator, 'none'); ?>>None</option>
                                </select>
                                <span class="dab-admin-form-help">Character to use as thousand separator</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_decimal_separator" class="dab-admin-form-label">Decimal Separator</label>
                                <select id="enhanced_currency_decimal_separator" name="enhanced_currency_decimal_separator" class="dab-admin-form-control">
                                    <option value="dot" <?php selected($decimal_separator, 'dot'); ?>>Dot (.)</option>
                                    <option value="comma" <?php selected($decimal_separator, 'comma'); ?>>Comma (,)</option>
                                </select>
                                <span class="dab-admin-form-help">Character to use as decimal separator</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="enhanced_currency_allow_selection" class="dab-admin-form-label">
                                    <input type="checkbox" id="enhanced_currency_allow_selection" name="enhanced_currency_allow_selection" value="1" <?php checked($allow_currency_selection, true); ?>>
                                    Allow Currency Selection
                                </label>
                                <span class="dab-admin-form-help">If checked, users will be able to select a different currency when entering values</span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($field->field_type === 'media'):
                        // Get field settings with defaults
                        $media_type = isset($options['media_type']) ? $options['media_type'] : 'both';
                        $allow_recording = isset($options['allow_recording']) ? (bool)$options['allow_recording'] : false;
                        $allow_upload = isset($options['allow_upload']) ? (bool)$options['allow_upload'] : true;
                        $allow_embed = isset($options['allow_embed']) ? (bool)$options['allow_embed'] : false;
                        $max_file_size = isset($options['max_file_size']) ? intval($options['max_file_size']) : 10;
                        $allowed_formats = isset($options['allowed_formats']) ? $options['allowed_formats'] : array('mp3', 'mp4');
                        $player_width = isset($options['player_width']) ? intval($options['player_width']) : 400;
                        $player_height = isset($options['player_height']) ? intval($options['player_height']) : 300;
                    ?>
                        <h3 class="dab-admin-form-section-title">Audio/Video Media Field Options</h3>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_type" class="dab-admin-form-label">Media Type</label>
                                <select id="media_type" name="media_type" class="dab-admin-form-control">
                                    <option value="both" <?php selected($media_type, 'both'); ?>>Both Audio & Video</option>
                                    <option value="audio" <?php selected($media_type, 'audio'); ?>>Audio Only</option>
                                    <option value="video" <?php selected($media_type, 'video'); ?>>Video Only</option>
                                </select>
                                <span class="dab-admin-form-help">Type of media to allow</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_allow_recording" class="dab-admin-form-label">
                                    <input type="checkbox" id="media_allow_recording" name="media_allow_recording" value="1" <?php checked($allow_recording, true); ?>>
                                    Allow Recording
                                </label>
                                <span class="dab-admin-form-help">If checked, users will be able to record audio/video directly in the browser</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_allow_upload" class="dab-admin-form-label">
                                    <input type="checkbox" id="media_allow_upload" name="media_allow_upload" value="1" <?php checked($allow_upload, true); ?>>
                                    Allow Upload
                                </label>
                                <span class="dab-admin-form-help">If checked, users will be able to upload media files</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_allow_embed" class="dab-admin-form-label">
                                    <input type="checkbox" id="media_allow_embed" name="media_allow_embed" value="1" <?php checked($allow_embed, true); ?>>
                                    Allow Embed
                                </label>
                                <span class="dab-admin-form-help">If checked, users will be able to embed media from YouTube, Vimeo, etc.</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_max_file_size" class="dab-admin-form-label">Max File Size (MB)</label>
                                <input type="number" id="media_max_file_size" name="media_max_file_size" class="dab-admin-form-control" value="<?php echo esc_attr($max_file_size); ?>" min="1" max="100">
                                <span class="dab-admin-form-help">Maximum file size in megabytes</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_allowed_formats" class="dab-admin-form-label">Allowed Formats</label>
                                <select id="media_allowed_formats" name="media_allowed_formats[]" class="dab-admin-form-control" multiple style="height: 120px;">
                                    <option value="mp3" <?php selected(in_array('mp3', $allowed_formats), true); ?>>MP3</option>
                                    <option value="wav" <?php selected(in_array('wav', $allowed_formats), true); ?>>WAV</option>
                                    <option value="ogg" <?php selected(in_array('ogg', $allowed_formats), true); ?>>OGG</option>
                                    <option value="mp4" <?php selected(in_array('mp4', $allowed_formats), true); ?>>MP4</option>
                                    <option value="webm" <?php selected(in_array('webm', $allowed_formats), true); ?>>WebM</option>
                                    <option value="mov" <?php selected(in_array('mov', $allowed_formats), true); ?>>MOV</option>
                                </select>
                                <span class="dab-admin-form-help">Allowed file formats (hold Ctrl/Cmd to select multiple)</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_player_width" class="dab-admin-form-label">Player Width</label>
                                <input type="number" id="media_player_width" name="media_player_width" class="dab-admin-form-control" value="<?php echo esc_attr($player_width); ?>" min="200" max="1200">
                                <span class="dab-admin-form-help">Width of the media player in pixels</span>
                            </div>
                        </div>

                        <div class="dab-admin-form-row">
                            <div class="dab-admin-form-group">
                                <label for="media_player_height" class="dab-admin-form-label">Player Height</label>
                                <input type="number" id="media_player_height" name="media_player_height" class="dab-admin-form-control" value="<?php echo esc_attr($player_height); ?>" min="100" max="800">
                                <span class="dab-admin-form-help">Height of the media player in pixels (for video)</span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="dab-admin-form-actions">
                        <input type="submit" name="dab_save_field" value="Update Field" class="dab-btn dab-btn-primary">
                        <a href="<?php echo admin_url('admin.php?page=dab_fields&table_id=' . $field->table_id); ?>" class="dab-btn dab-btn-outline-primary">Cancel</a>
                    </div>
                </form>
            <?php elseif ($active_tab === 'permissions'): ?>
                <!-- Permissions Tab -->
                <?php include(plugin_dir_path(__FILE__) . 'partials/field-permissions-tab.php'); ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .dab-admin-form-row {
        margin-bottom: 20px;
    }

    .dab-admin-form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .dab-admin-form-control {
        width: 100%;
        max-width: 400px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .dab-admin-form-help {
        display: block;
        margin-top: 5px;
        color: #666;
        font-size: 12px;
    }

    .dab-admin-form-section-title {
        margin-top: 30px;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        color: #23282d;
        font-size: 16px;
    }

    .dab-admin-form-actions {
        margin-top: 30px;
    }

    .dab-btn {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.2s;
    }

    .dab-btn-primary {
        background-color: #2271b1;
        color: #fff;
        border: 1px solid #2271b1;
    }

    .dab-btn-primary:hover {
        background-color: #135e96;
        border-color: #135e96;
    }

    .dab-btn-outline-primary {
        background-color: transparent;
        color: #2271b1;
        border: 1px solid #2271b1;
    }

    .dab-btn-outline-primary:hover {
        background-color: #f0f0f1;
    }

    .dab-admin-tabs {
        margin-top: 10px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 0;
    }

    .dab-admin-tab-link {
        display: inline-block;
        padding: 10px 15px;
        margin-right: 5px;
        text-decoration: none;
        color: #555;
        border: 1px solid transparent;
        border-bottom: none;
        border-radius: 4px 4px 0 0;
        background-color: #f0f0f1;
    }

    .dab-admin-tab-link.active {
        background-color: #fff;
        border-color: #ccc;
        border-bottom-color: #fff;
        margin-bottom: -1px;
        color: #2271b1;
    }

    .dab-admin-tab-link:hover {
        background-color: #fff;
        color: #2271b1;
    }

    .dab-card {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
    }

    .dab-card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .dab-card-title {
        margin: 0;
        font-size: 18px;
    }

    .dab-card-body {
        padding: 20px;
    }

    .dab-animate-scale-in {
        animation: dab-scale-in 0.3s ease-out;
    }

    @keyframes dab-scale-in {
        0% {
            opacity: 0;
            transform: scale(0.95);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
</style>
