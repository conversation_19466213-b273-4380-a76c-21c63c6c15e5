<?php
/**
 * License Tester Class
 *
 * Provides testing functionality for license API endpoints.
 *
 * @since      1.0.0
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_License_Tester {

    /**
     * The single instance of the class.
     *
     * @var DAB_License_Tester
     */
    protected static $_instance = null;

    /**
     * Main DAB_License_Tester Instance.
     *
     * Ensures only one instance of DAB_License_Tester is loaded or can be loaded.
     *
     * @return DAB_License_Tester Main instance.
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor.
     */
    public function __construct() {
        // Add AJAX handler for testing API endpoints
        add_action('wp_ajax_dab_test_license_api', array($this, 'ajax_test_license_api'));
    }

    /**
     * AJAX handler for testing license API endpoints.
     */
    public function ajax_test_license_api() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Permission denied'));
            return;
        }

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_license_action')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
            return;
        }

        // Get license key
        $license_key = isset($_POST['license_key']) ? sanitize_text_field($_POST['license_key']) : '';
        if (empty($license_key)) {
            wp_send_json_error(array('message' => 'License key is required'));
            return;
        }

        // Test various API endpoints
        $results = $this->test_api_endpoints($license_key);

        // Return results
        wp_send_json_success(array('results' => $results));
    }

    /**
     * Test various API endpoints.
     *
     * @param string $license_key License key to test.
     * @return array Test results.
     */
    public function test_api_endpoints($license_key) {
        $results = array();
        $base_urls = array(
            // Put the known working endpoint first
            'https://license.choicenetng.com/wp-json/dab-license/v1',
            // Keep other endpoints as fallbacks
            'https://license.choicenetng.com/wp-json/license-api/v1',
            'https://license.choicenetng.com/wp-json/dabls/v1',
            'https://license.choicenetng.com/wp-json/dab/v1',
            'https://license.choicenetng.com/wp-json/license/v1',
            'https://license.choicenetng.com/wp-json/api/v1',
            'https://license.choicenetng.com/wp-json/dab-api/v1',
            'https://license.choicenetng.com/wp-json/dabls-api/v1',
            'https://license.choicenetng.com/wp-json/rest/v1',
            'https://license.choicenetng.com/wp-json/wc-api/v3',
            'https://license.choicenetng.com/wp-json/wc/v3',
            'https://license.choicenetng.com/wp-json/slm/v1',
            'https://license.choicenetng.com/wp-json/slm-api/v1',
            'https://license.choicenetng.com/wp-json/lmfwc/v2',
            'https://license.choicenetng.com/wp-json/lmfw/v2'
        );

        $actions = array('activate', 'validate', 'check', 'deactivate');

        // Also test direct API endpoints (non-WordPress REST API)
        $direct_endpoints = array(
            'https://license.choicenetng.com/api/license/activate.php',
            'https://license.choicenetng.com/api/license/validate.php',
            'https://license.choicenetng.com/api/activate.php',
            'https://license.choicenetng.com/api/validate.php',
            'https://license.choicenetng.com/license/api/activate.php',
            'https://license.choicenetng.com/license/api/validate.php'
        );

        // Test direct endpoints first
        foreach ($direct_endpoints as $url) {
            $result = $this->test_endpoint($url, $license_key);
            $results[] = array(
                'url' => $url,
                'status' => $result['status'],
                'code' => $result['code'],
                'message' => $result['message'],
                'response' => $result['response']
            );

            // If we found a working endpoint, save it and return early
            if ($result['status'] === 'success') {
                // For direct endpoints, we save the exact URL
                update_option('dab_license_api_url', $url);
                update_option('dab_license_direct_endpoint', 'yes');

                // Log the successful endpoint
                if (function_exists('DAB_Debug')) {
                    DAB_Debug()->log(
                        'Found working direct API endpoint: ' . $url,
                        'info',
                        array('direct_endpoint' => true)
                    );
                }

                return $results;
            }
        }

        foreach ($base_urls as $base_url) {
            foreach ($actions as $action) {
                $url = $base_url . '/' . $action;
                $result = $this->test_endpoint($url, $license_key);
                $results[] = array(
                    'url' => $url,
                    'status' => $result['status'],
                    'code' => $result['code'],
                    'message' => $result['message'],
                    'response' => $result['response']
                );

                // If we found a working endpoint, save it and return early
                if ($result['status'] === 'success') {
                    update_option('dab_license_api_url', $base_url);

                    // Log the successful endpoint
                    if (function_exists('DAB_Debug')) {
                        DAB_Debug()->log(
                            'Found working API endpoint: ' . $base_url,
                            'info',
                            array('action' => $action)
                        );
                    }

                    return $results;
                }
            }
        }

        return $results;
    }

    /**
     * Test a specific API endpoint.
     *
     * @param string $url API endpoint URL.
     * @param string $license_key License key to test.
     * @return array Test result.
     */
    private function test_endpoint($url, $license_key) {
        // Prepare request data
        $params = array(
            'license_key' => $license_key,
            'item_id' => DAB_ITEM_ID,
            'url' => home_url(),
        );

        // Log the test
        if (function_exists('DAB_Debug')) {
            DAB_Debug()->log(
                'Testing API endpoint: ' . $url,
                'info',
                array('license_key' => DAB_Debug()->mask_license_key($license_key))
            );
        }

        // Make API request
        $response = wp_remote_post($url, array(
            'timeout' => 15,
            'sslverify' => false,
            'body' => $params,
        ));

        if (is_wp_error($response)) {
            return array(
                'status' => 'error',
                'code' => $response->get_error_code(),
                'message' => $response->get_error_message(),
                'response' => null
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body);

        // Log the response
        if (function_exists('DAB_Debug')) {
            DAB_Debug()->log(
                'API endpoint response: ' . $url,
                'info',
                array(
                    'response_code' => $response_code,
                    'response_body' => $response_body
                )
            );
        }

        if ($response_code === 200 && !empty($response_data)) {
            return array(
                'status' => 'success',
                'code' => $response_code,
                'message' => 'Endpoint working',
                'response' => $response_body
            );
        } else {
            return array(
                'status' => 'error',
                'code' => $response_code,
                'message' => 'Endpoint not working',
                'response' => $response_body
            );
        }
    }
}

// Initialize License Tester
function DAB_License_Tester() {
    return DAB_License_Tester::instance();
}

// Initialize the instance
DAB_License_Tester();
