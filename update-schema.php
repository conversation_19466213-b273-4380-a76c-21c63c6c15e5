<?php
/**
 * Database schema update script for template installation fix
 */

// Load WordPress if available, otherwise create minimal environment
if (file_exists('wp-config.php')) {
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-includes/wp-db.php';
} else {
    // Create minimal database connection for testing
    echo "WordPress not found. This script should be run in a WordPress environment.\n";
    echo "Please run this script from your WordPress admin or through WP-CLI.\n";
    exit(1);
}

// Include our DB manager
require_once 'includes/class-db-manager.php';

echo "Starting database schema update...\n";

try {
    // Ensure advanced field columns exist (including field_options)
    DAB_DB_Manager::ensure_advanced_field_columns_exist();
    echo "✓ Advanced field columns updated successfully\n";
    
    // Check if field_options column now exists
    global $wpdb;
    $fields_table = $wpdb->prefix . 'dab_fields';
    $columns = $wpdb->get_col("SHOW COLUMNS FROM $fields_table");
    
    if (in_array('field_options', $columns)) {
        echo "✓ field_options column exists\n";
    } else {
        echo "✗ field_options column still missing\n";
    }
    
    if (in_array('options', $columns)) {
        echo "✓ options column exists\n";
    } else {
        echo "✗ options column missing\n";
    }
    
    echo "\nDatabase schema update completed successfully!\n";
    echo "Template installation should now work properly.\n";
    
} catch (Exception $e) {
    echo "Error updating database schema: " . $e->getMessage() . "\n";
    exit(1);
}
