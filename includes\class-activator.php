<?php
/**
 * Fired during plugin activation
 *
 * @link       https://example.com
 * @since      1.0.0
 *
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 * <AUTHOR> Name <<EMAIL>>
 */
class DAB_Activator {

    /**
     * Database version
     */
    const DB_VERSION = '1.1';

    /**
     * Activate the plugin
     */
    public static function activate() {
        // Create database tables
        self::create_tables();

        // Update schema if needed (for upgrades)
        self::update_db_schema();

        // Set version
        update_option('dab_db_version', self::DB_VERSION);
    }

    /**
     * Create database tables
     */
    private static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Tables
        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';
        $data_table_prefix = $wpdb->prefix . 'dab_data_';

        // SQL to create tables table
        $sql_tables = "CREATE TABLE IF NOT EXISTS $tables_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            table_name varchar(100) NOT NULL,
            table_slug varchar(100) NOT NULL,
            description text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY table_slug (table_slug)
        ) $charset_collate;";

        // SQL to create fields table
        $sql_fields = "CREATE TABLE IF NOT EXISTS $fields_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            table_id mediumint(9) NOT NULL,
            field_label varchar(100) NOT NULL,
            field_slug varchar(100) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_description text,
            field_options longtext,
            field_required tinyint(1) DEFAULT 0,
            field_order int DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY table_id (table_id),
            UNIQUE KEY table_field (table_id, field_slug)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_tables);
        dbDelta($sql_fields);
    }

    /**
     * Update database schema if needed
     */
    public static function update_db_schema() {
        global $wpdb;

        // Check and add field_order column to fields table if it doesn't exist
        $fields_table = $wpdb->prefix . 'dab_fields';
        $column_exists = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT COLUMN_NAME
                 FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = %s
                 AND TABLE_NAME = %s
                 AND COLUMN_NAME = 'field_order'",
                DB_NAME,
                $fields_table
            )
        );

        if (empty($column_exists)) {
            $wpdb->query("ALTER TABLE $fields_table ADD COLUMN field_order INT DEFAULT 0");

            // Initialize field_order values based on id to maintain existing order
            $wpdb->query("UPDATE $fields_table SET field_order = id");
        }
    }
}
