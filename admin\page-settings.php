<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Handle frontend installation
if (isset($_GET['action']) && $_GET['action'] === 'install_frontend' &&
    isset($_GET['page']) && $_GET['page'] === 'dab_settings') {

    // Verify user permissions
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    // Verify nonce for security
    if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'dab_install_frontend')) {
        wp_die(__('Security check failed. Please try again.'));
    }

    // Install frontend system
    DAB_Frontend_Installer::install();

    // Redirect with success message
    $redirect_url = add_query_arg(array(
        'page' => 'dab_settings',
        'tab' => 'frontend',
        'message' => 'frontend_installed'
    ), admin_url('admin.php'));

    wp_redirect($redirect_url);
    exit;
}

// Get active tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';

// Handle form submissions based on active tab
if (isset($_POST['dab_save_settings'])) {
    if ($active_tab === 'general') {
        // Save general settings
        if (isset($_POST['dab_items_per_page'])) {
            update_option('dab_items_per_page', intval($_POST['dab_items_per_page']));
        }
        if (isset($_POST['dab_allow_frontend'])) {
            update_option('dab_allow_frontend', sanitize_text_field($_POST['dab_allow_frontend']));
        }
    } elseif ($active_tab === 'email') {
        // Save email settings
        if (isset($_POST['dab_email_from_name'])) {
            update_option('dab_email_from_name', sanitize_text_field($_POST['dab_email_from_name']));
        }
        if (isset($_POST['dab_email_from_email'])) {
            update_option('dab_email_from_email', sanitize_email($_POST['dab_email_from_email']));
        }
        if (isset($_POST['dab_email_reply_to'])) {
            update_option('dab_email_reply_to', sanitize_email($_POST['dab_email_reply_to']));
        }
    } elseif ($active_tab === 'frontend') {
        // Save frontend settings
        if (isset($_POST['dab_frontend_registration_enabled'])) {
            update_option('dab_frontend_registration_enabled', sanitize_text_field($_POST['dab_frontend_registration_enabled']));
        }
        if (isset($_POST['dab_frontend_email_verification'])) {
            update_option('dab_frontend_email_verification', sanitize_text_field($_POST['dab_frontend_email_verification']));
        }
        if (isset($_POST['dab_frontend_session_timeout'])) {
            update_option('dab_frontend_session_timeout', intval($_POST['dab_frontend_session_timeout']));
        }
        if (isset($_POST['dab_frontend_remember_me_timeout'])) {
            update_option('dab_frontend_remember_me_timeout', intval($_POST['dab_frontend_remember_me_timeout']));
        }
        if (isset($_POST['dab_frontend_password_min_length'])) {
            update_option('dab_frontend_password_min_length', intval($_POST['dab_frontend_password_min_length']));
        }
        if (isset($_POST['dab_frontend_default_role'])) {
            update_option('dab_frontend_default_role', sanitize_text_field($_POST['dab_frontend_default_role']));
        }
        if (isset($_POST['dab_frontend_delete_pages_on_uninstall'])) {
            update_option('dab_frontend_delete_pages_on_uninstall', sanitize_text_field($_POST['dab_frontend_delete_pages_on_uninstall']));
        }
    }

    echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
}

// Show installation success message
if (isset($_GET['message']) && $_GET['message'] === 'frontend_installed') {
    echo '<div class="notice notice-success is-dismissible"><p>Frontend pages have been installed successfully!</p></div>';
}

// Load current settings
$items_per_page = get_option('dab_items_per_page', 10);
$allow_frontend = get_option('dab_allow_frontend', 'yes');

// Load email settings
$email_from_name = get_option('dab_email_from_name', get_bloginfo('name'));
$email_from_email = get_option('dab_email_from_email', get_option('admin_email'));
$email_reply_to = get_option('dab_email_reply_to', get_option('admin_email'));

// Load frontend settings
$frontend_registration_enabled = get_option('dab_frontend_registration_enabled', 'yes');
$frontend_email_verification = get_option('dab_frontend_email_verification', 'yes');
$frontend_session_timeout = get_option('dab_frontend_session_timeout', 24);
$frontend_remember_me_timeout = get_option('dab_frontend_remember_me_timeout', 720);
$frontend_password_min_length = get_option('dab_frontend_password_min_length', 6);
$frontend_default_role = get_option('dab_frontend_default_role', 'user');
$frontend_delete_pages_on_uninstall = get_option('dab_frontend_delete_pages_on_uninstall', 'no');
?>

<div class="wrap dab-admin-wrap">
    <h1>Database App Builder - Settings</h1>

    <h2 class="nav-tab-wrapper">
        <a href="?page=dab_settings&tab=general" class="nav-tab <?php echo $active_tab === 'general' ? 'nav-tab-active' : ''; ?>"><?php _e('General', 'db-app-builder'); ?></a>
        <a href="?page=dab_settings&tab=email" class="nav-tab <?php echo $active_tab === 'email' ? 'nav-tab-active' : ''; ?>"><?php _e('Email', 'db-app-builder'); ?></a>
        <a href="?page=dab_settings&tab=frontend" class="nav-tab <?php echo $active_tab === 'frontend' ? 'nav-tab-active' : ''; ?>"><?php _e('Frontend', 'db-app-builder'); ?></a>
    </h2>

    <?php if ($active_tab === 'general'): ?>
    <form method="post" action="">
        <input type="hidden" name="tab" value="general">
        <table class="form-table">
            <tr valign="top">
                <th scope="row">Items Per Page (Data Manager)</th>
                <td>
                    <input type="number" name="dab_items_per_page" value="<?php echo esc_attr($items_per_page); ?>" min="1" max="100">
                    <p class="description">Set how many records to show per page in Data Manager (default: 10).</p>
                </td>
            </tr>
            <tr valign="top">
                <th scope="row">Allow Frontend Forms</th>
                <td>
                    <select name="dab_allow_frontend">
                        <option value="yes" <?php selected($allow_frontend, 'yes'); ?>>Yes</option>
                        <option value="no" <?php selected($allow_frontend, 'no'); ?>>No</option>
                    </select>
                    <p class="description">Enable or disable frontend access to created forms via shortcode.</p>
                </td>
            </tr>
        </table>

        <p><input type="submit" name="dab_save_settings" class="button-primary" value="Save Settings"></p>
    </form>

    <?php elseif ($active_tab === 'email'): ?>
    <form method="post" action="">
        <input type="hidden" name="tab" value="email">
        <table class="form-table">

            <tr valign="top">
                <th scope="row">From Name</th>
                <td>
                    <input type="text" name="dab_email_from_name" value="<?php echo esc_attr($email_from_name); ?>" class="regular-text">
                    <p class="description">The name that will appear in the From field of emails sent by the plugin.</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">From Email</th>
                <td>
                    <input type="email" name="dab_email_from_email" value="<?php echo esc_attr($email_from_email); ?>" class="regular-text">
                    <p class="description">The email address that will appear in the From field of emails sent by the plugin. This should be a valid email address from your domain.</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Reply-To Email</th>
                <td>
                    <input type="email" name="dab_email_reply_to" value="<?php echo esc_attr($email_reply_to); ?>" class="regular-text">
                    <p class="description">The email address that recipients will reply to when they receive emails from the plugin.</p>
                </td>
            </tr>

            <tr>
                <th colspan="2">
                    <h3>Email Deliverability Tips</h3>
                    <div class="dab-email-tips" style="background: #f8f9fa; padding: 15px; border-left: 4px solid #0073aa; margin-top: 10px;">
                        <p><strong>To prevent emails from going to spam folders:</strong></p>
                        <ol style="margin-left: 20px;">
                            <li>Use a valid email address from your domain (e.g., <EMAIL>)</li>
                            <li>Set up SPF and DKIM records for your domain</li>
                            <li>Consider using an SMTP plugin for WordPress</li>
                            <li>Avoid spam trigger words in your email subjects and content</li>
                            <li>Ask recipients to add your email address to their contacts</li>
                        </ol>
                        <p><strong>Recommended SMTP Plugins:</strong></p>
                        <ul style="margin-left: 20px;">
                            <li><a href="https://wordpress.org/plugins/wp-mail-smtp/" target="_blank">WP Mail SMTP</a></li>
                            <li><a href="https://wordpress.org/plugins/post-smtp/" target="_blank">Post SMTP</a></li>
                            <li><a href="https://wordpress.org/plugins/easy-wp-smtp/" target="_blank">Easy WP SMTP</a></li>
                        </ul>
                    </div>
                </th>
            </tr>
        </table>

        <p><input type="submit" name="dab_save_settings" class="button-primary" value="Save Settings"></p>
    </form>

    <div class="card" style="max-width: 600px; margin-top: 20px;">
        <h2>Test Email Configuration</h2>
        <p>Send a test email to verify your email configuration is working correctly.</p>

        <form method="post" action="">
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">Recipient Email</th>
                    <td>
                        <input type="email" name="dab_test_email_to" value="<?php echo esc_attr(get_option('admin_email')); ?>" class="regular-text" required>
                        <p class="description">Email address to send the test email to.</p>
                    </td>
                </tr>
            </table>

            <p><input type="submit" name="dab_send_test_email" class="button-secondary" value="Send Test Email"></p>
        </form>
    </div>

    <div class="card" style="max-width: 600px; margin-top: 20px;">
        <h2>Email Spam Score Checker</h2>
        <p>Check if your email content is likely to trigger spam filters.</p>

        <form method="post" action="">
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">From Email</th>
                    <td>
                        <input type="email" name="dab_spam_check_from" value="<?php echo esc_attr($email_from_email); ?>" class="regular-text" required>
                        <p class="description">The email address that will be used as the sender.</p>
                    </td>
                </tr>
                <tr valign="top">
                    <th scope="row">Subject</th>
                    <td>
                        <input type="text" name="dab_spam_check_subject" value="Test Email Subject" class="regular-text" required>
                        <p class="description">The subject line to check for spam triggers.</p>
                    </td>
                </tr>
                <tr valign="top">
                    <th scope="row">Message</th>
                    <td>
                        <textarea name="dab_spam_check_message" rows="5" class="large-text" required>This is a test message to check for spam triggers.</textarea>
                        <p class="description">The email content to check for spam triggers.</p>
                    </td>
                </tr>
            </table>

            <p><input type="submit" name="dab_check_spam_score" class="button-secondary" value="Check Spam Score"></p>
        </form>
    </div>

    <?php elseif ($active_tab === 'frontend'): ?>
    <?php
    // Get frontend installation status
    $installation_status = DAB_Frontend_Installer::get_installation_status();
    $is_installed = DAB_Frontend_Installer::is_installed();
    ?>

    <div class="card" style="max-width: 800px; margin-top: 20px;">
        <h2>Frontend System Status</h2>

        <?php if ($is_installed): ?>
            <div class="notice notice-success inline">
                <p><strong>Frontend system is installed and ready!</strong></p>
                <p>All required pages have been created: <?php echo $installation_status['pages_created']; ?>/<?php echo $installation_status['pages_total']; ?> pages</p>
            </div>
        <?php else: ?>
            <div class="notice notice-warning inline">
                <p><strong>Frontend system needs to be installed.</strong></p>
                <p>Missing pages: <?php echo count($installation_status['missing_pages']); ?>/<?php echo $installation_status['pages_total']; ?></p>
                <?php if (!empty($installation_status['missing_pages'])): ?>
                    <p><small>Missing: <?php echo implode(', ', $installation_status['missing_pages']); ?></small></p>
                <?php endif; ?>
            </div>

            <p>
                <a href="<?php echo wp_nonce_url(add_query_arg(array('page' => 'dab_settings', 'tab' => 'frontend', 'action' => 'install_frontend'), admin_url('admin.php')), 'dab_install_frontend'); ?>"
                   class="button button-primary">
                    Install Frontend Pages
                </a>
            </p>
        <?php endif; ?>

        <h3>Frontend Pages</h3>
        <table class="widefat">
            <thead>
                <tr>
                    <th>Page Type</th>
                    <th>Status</th>
                    <th>URL</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $page_types = array(
                    'login' => 'Login Page',
                    'register' => 'Registration Page',
                    'user-dashboard' => 'User Dashboard',
                    'user-profile' => 'User Profile',
                    'reset-password' => 'Reset Password',
                    'chat' => 'Chat Page'
                );

                foreach ($page_types as $page_key => $page_name):
                    $page_id = get_option('dab_frontend_page_' . $page_key);
                    $page_exists = $page_id && get_post($page_id);
                    $page_url = $page_exists ? get_permalink($page_id) : '';
                ?>
                <tr>
                    <td><?php echo esc_html($page_name); ?></td>
                    <td>
                        <?php if ($page_exists): ?>
                            <span style="color: green;">✓ Created</span>
                        <?php else: ?>
                            <span style="color: red;">✗ Missing</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($page_exists): ?>
                            <a href="<?php echo esc_url($page_url); ?>" target="_blank"><?php echo esc_url($page_url); ?></a>
                        <?php else: ?>
                            <em>Not available</em>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <form method="post" action="">
        <input type="hidden" name="tab" value="frontend">
        <table class="form-table">
            <tr valign="top">
                <th scope="row">Enable Registration</th>
                <td>
                    <select name="dab_frontend_registration_enabled">
                        <option value="yes" <?php selected($frontend_registration_enabled, 'yes'); ?>>Yes</option>
                        <option value="no" <?php selected($frontend_registration_enabled, 'no'); ?>>No</option>
                    </select>
                    <p class="description">Allow new users to register through the frontend.</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Email Verification</th>
                <td>
                    <select name="dab_frontend_email_verification">
                        <option value="yes" <?php selected($frontend_email_verification, 'yes'); ?>>Yes</option>
                        <option value="no" <?php selected($frontend_email_verification, 'no'); ?>>No</option>
                    </select>
                    <p class="description">Require email verification for new registrations.</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Session Timeout (hours)</th>
                <td>
                    <input type="number" name="dab_frontend_session_timeout" value="<?php echo esc_attr($frontend_session_timeout); ?>" min="1" max="168">
                    <p class="description">How long user sessions last (default: 24 hours).</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Remember Me Timeout (hours)</th>
                <td>
                    <input type="number" name="dab_frontend_remember_me_timeout" value="<?php echo esc_attr($frontend_remember_me_timeout); ?>" min="24" max="8760">
                    <p class="description">How long "Remember Me" sessions last (default: 720 hours / 30 days).</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Minimum Password Length</th>
                <td>
                    <input type="number" name="dab_frontend_password_min_length" value="<?php echo esc_attr($frontend_password_min_length); ?>" min="4" max="50">
                    <p class="description">Minimum number of characters required for passwords (default: 6).</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Default User Role</th>
                <td>
                    <select name="dab_frontend_default_role">
                        <option value="user" <?php selected($frontend_default_role, 'user'); ?>>User</option>
                        <option value="subscriber" <?php selected($frontend_default_role, 'subscriber'); ?>>Subscriber</option>
                        <option value="contributor" <?php selected($frontend_default_role, 'contributor'); ?>>Contributor</option>
                    </select>
                    <p class="description">Default role assigned to new frontend users.</p>
                </td>
            </tr>

            <tr valign="top">
                <th scope="row">Delete Pages on Uninstall</th>
                <td>
                    <select name="dab_frontend_delete_pages_on_uninstall">
                        <option value="no" <?php selected($frontend_delete_pages_on_uninstall, 'no'); ?>>No</option>
                        <option value="yes" <?php selected($frontend_delete_pages_on_uninstall, 'yes'); ?>>Yes</option>
                    </select>
                    <p class="description">Whether to delete frontend pages when the plugin is uninstalled.</p>
                </td>
            </tr>
        </table>

        <p><input type="submit" name="dab_save_settings" class="button-primary" value="Save Settings"></p>
    </form>

    <?php endif; ?>
</div>

<?php
// Handle test email
if (isset($_POST['dab_send_test_email']) && isset($_POST['dab_test_email_to'])) {
    $to = sanitize_email($_POST['dab_test_email_to']);

    if (!is_email($to)) {
        echo '<div class="notice notice-error is-dismissible"><p>Please enter a valid email address.</p></div>';
        return;
    }

    $subject = 'Test Email from Database App Builder';

    $message = '<h2>Email Test Successful!</h2>';
    $message .= '<p>This is a test email from the Database App Builder plugin. If you\'re reading this, your email configuration is working correctly.</p>';
    $message .= '<p><strong>Email Settings:</strong></p>';
    $message .= '<ul>';
    $message .= '<li><strong>From Name:</strong> ' . esc_html($email_from_name) . '</li>';
    $message .= '<li><strong>From Email:</strong> ' . esc_html($email_from_email) . '</li>';
    $message .= '<li><strong>Reply-To:</strong> ' . esc_html($email_reply_to) . '</li>';
    $message .= '</ul>';
    $message .= '<p>This email was sent at: ' . date_i18n(get_option('date_format') . ' ' . get_option('time_format')) . '</p>';

    $result = DAB_Enhanced_Email::send($to, $subject, $message, [
        'tracking_id' => 'test-email-' . time(),
        'priority' => 'high'
    ]);

    if ($result) {
        echo '<div class="notice notice-success is-dismissible"><p>Test email sent successfully! Please check your inbox (and spam folder) for the test email.</p></div>';
    } else {
        echo '<div class="notice notice-error is-dismissible"><p>Failed to send test email. Please check your email configuration.</p></div>';
    }
}

// Handle spam score check
if (isset($_POST['dab_check_spam_score'])) {
    $from_email = sanitize_email($_POST['dab_spam_check_from']);
    $subject = sanitize_text_field($_POST['dab_spam_check_subject']);
    $message = sanitize_textarea_field($_POST['dab_spam_check_message']);

    if (!is_email($from_email)) {
        echo '<div class="notice notice-error is-dismissible"><p>Please enter a valid email address.</p></div>';
        return;
    }

    $spam_check = DAB_Enhanced_Email::check_spam_score($subject, $message, $from_email);

    echo '<div class="notice notice-info is-dismissible">';
    echo '<h3>Spam Check Results</h3>';

    echo '<div style="margin-bottom: 15px;">';
    echo '<h4 style="margin-bottom: 5px;">Potential Issues:</h4>';
    echo '<ul style="margin-left: 20px; list-style-type: disc;">';
    foreach ($spam_check['warnings'] as $warning) {
        echo '<li>' . esc_html($warning) . '</li>';
    }
    echo '</ul>';
    echo '</div>';

    echo '<div>';
    echo '<h4 style="margin-bottom: 5px;">Suggestions to Improve Deliverability:</h4>';
    echo '<ul style="margin-left: 20px; list-style-type: disc;">';
    foreach ($spam_check['suggestions'] as $suggestion) {
        echo '<li>' . esc_html($suggestion) . '</li>';
    }
    echo '</ul>';
    echo '</div>';

    echo '</div>';
}
?>
