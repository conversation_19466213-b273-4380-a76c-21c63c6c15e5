<?php
/**
 * Visual Workflow Builder
 *
 * Enables users to create automated workflows with drag-and-drop interface
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Workflow_Builder {

    /**
     * Initialize the workflow builder
     */
    public static function init() {
        add_action('wp_ajax_dab_save_workflow', array(__CLASS__, 'ajax_save_workflow'));
        add_action('wp_ajax_dab_load_workflow', array(__CLASS__, 'ajax_load_workflow'));
        add_action('wp_ajax_dab_delete_workflow', array(__CLASS__, 'ajax_delete_workflow'));
        add_action('wp_ajax_dab_test_workflow', array(__CLASS__, 'ajax_test_workflow'));
        add_action('wp_ajax_dab_get_workflow_logs', array(__CLASS__, 'ajax_get_workflow_logs'));
        add_action('wp_ajax_dab_get_table_fields_for_workflow', array(__CLASS__, 'ajax_get_table_fields_for_workflow'));

        // Hook into form submissions to trigger workflows
        add_action('dab_form_submitted', array(__CLASS__, 'trigger_form_workflows'), 10, 3);

        // Hook into data changes to trigger workflows
        add_action('dab_data_updated', array(__CLASS__, 'trigger_data_workflows'), 10, 3);
        add_action('dab_data_created', array(__CLASS__, 'trigger_data_workflows'), 10, 3);

        // Schedule workflow execution
        add_action('dab_execute_scheduled_workflows', array(__CLASS__, 'execute_scheduled_workflows'));

        // Register scheduled event if not already scheduled
        if (!wp_next_scheduled('dab_execute_scheduled_workflows')) {
            wp_schedule_event(time(), 'hourly', 'dab_execute_scheduled_workflows');
        }
    }

    /**
     * Create workflow tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Workflows table
        $workflows_table = $wpdb->prefix . 'dab_workflows';
        $sql = "CREATE TABLE $workflows_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            trigger_type enum('form_submit', 'data_change', 'schedule', 'webhook', 'manual') NOT NULL,
            trigger_config longtext,
            workflow_steps longtext,
            is_active tinyint(1) DEFAULT 1,
            created_by int(11),
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY trigger_type (trigger_type),
            KEY is_active (is_active)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Workflow executions table
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';
        $sql = "CREATE TABLE $executions_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            workflow_id int(11) NOT NULL,
            trigger_data longtext,
            execution_status enum('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            steps_completed int(11) DEFAULT 0,
            total_steps int(11) DEFAULT 0,
            error_message text,
            execution_log longtext,
            started_at timestamp NULL,
            completed_at timestamp NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY workflow_id (workflow_id),
            KEY execution_status (execution_status),
            KEY created_at (created_at)
        ) $charset_collate;";

        dbDelta($sql);
    }

    /**
     * Get available trigger types
     */
    public static function get_trigger_types() {
        return array(
            'form_submit' => array(
                'label' => __('Form Submission', 'db-app-builder'),
                'description' => __('Triggered when a form is submitted', 'db-app-builder'),
                'icon' => 'dashicons-forms',
                'config_fields' => array('form_id')
            ),
            'data_change' => array(
                'label' => __('Data Change', 'db-app-builder'),
                'description' => __('Triggered when data is created or updated', 'db-app-builder'),
                'icon' => 'dashicons-database-view',
                'config_fields' => array('table_id', 'change_type')
            ),
            'schedule' => array(
                'label' => __('Schedule', 'db-app-builder'),
                'description' => __('Triggered at specific times or intervals', 'db-app-builder'),
                'icon' => 'dashicons-clock',
                'config_fields' => array('schedule_type', 'schedule_config')
            ),
            'webhook' => array(
                'label' => __('Webhook', 'db-app-builder'),
                'description' => __('Triggered by external webhook calls', 'db-app-builder'),
                'icon' => 'dashicons-rest-api',
                'config_fields' => array('webhook_key', 'authentication')
            ),
            'manual' => array(
                'label' => __('Manual', 'db-app-builder'),
                'description' => __('Triggered manually by users', 'db-app-builder'),
                'icon' => 'dashicons-controls-play',
                'config_fields' => array('user_roles')
            )
        );
    }

    /**
     * Get available action types
     */
    public static function get_action_types() {
        return array(
            'send_email' => array(
                'label' => __('Send Email', 'db-app-builder'),
                'description' => __('Send an email notification', 'db-app-builder'),
                'icon' => 'dashicons-email-alt',
                'config_fields' => array('to', 'subject', 'message', 'template')
            ),
            'create_record' => array(
                'label' => __('Create Record', 'db-app-builder'),
                'description' => __('Create a new record in a table', 'db-app-builder'),
                'icon' => 'dashicons-plus-alt',
                'config_fields' => array('table_id', 'field_mappings')
            ),
            'update_record' => array(
                'label' => __('Update Record', 'db-app-builder'),
                'description' => __('Update an existing record', 'db-app-builder'),
                'icon' => 'dashicons-edit',
                'config_fields' => array('table_id', 'record_id', 'field_mappings')
            ),
            'delete_record' => array(
                'label' => __('Delete Record', 'db-app-builder'),
                'description' => __('Delete a record from a table', 'db-app-builder'),
                'icon' => 'dashicons-trash',
                'config_fields' => array('table_id', 'record_id')
            ),
            'api_call' => array(
                'label' => __('API Call', 'db-app-builder'),
                'description' => __('Make an HTTP API request', 'db-app-builder'),
                'icon' => 'dashicons-cloud',
                'config_fields' => array('url', 'method', 'headers', 'body')
            ),
            'condition' => array(
                'label' => __('Condition', 'db-app-builder'),
                'description' => __('Branch workflow based on conditions', 'db-app-builder'),
                'icon' => 'dashicons-randomize',
                'config_fields' => array('condition_logic', 'true_path', 'false_path')
            ),
            'delay' => array(
                'label' => __('Delay', 'db-app-builder'),
                'description' => __('Wait for a specified time', 'db-app-builder'),
                'icon' => 'dashicons-clock',
                'config_fields' => array('delay_amount', 'delay_unit')
            ),
            'calculate' => array(
                'label' => __('Calculate', 'db-app-builder'),
                'description' => __('Perform calculations and store results', 'db-app-builder'),
                'icon' => 'dashicons-calculator',
                'config_fields' => array('formula', 'store_in')
            )
        );
    }

    /**
     * Save workflow
     */
    public static function ajax_save_workflow() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_workflow_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        global $wpdb;
        $workflows_table = $wpdb->prefix . 'dab_workflows';

        $workflow_id = isset($_POST['workflow_id']) ? intval($_POST['workflow_id']) : 0;
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $description = isset($_POST['description']) ? sanitize_textarea_field($_POST['description']) : '';
        $trigger_type = isset($_POST['trigger_type']) ? sanitize_text_field($_POST['trigger_type']) : '';
        $trigger_config = json_encode(isset($_POST['trigger_config']) ? $_POST['trigger_config'] : array());
        $workflow_steps = json_encode(isset($_POST['workflow_steps']) ? $_POST['workflow_steps'] : array());
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Validate required fields
        if (empty($name)) {
            wp_send_json_error(__('Workflow name is required', 'db-app-builder'));
            return;
        }

        if (empty($trigger_type)) {
            wp_send_json_error(__('Trigger type is required', 'db-app-builder'));
            return;
        }

        $data = array(
            'name' => $name,
            'description' => $description,
            'trigger_type' => $trigger_type,
            'trigger_config' => $trigger_config,
            'workflow_steps' => $workflow_steps,
            'is_active' => $is_active
        );

        if ($workflow_id > 0) {
            // Update existing workflow
            $result = $wpdb->update($workflows_table, $data, array('id' => $workflow_id));
            $workflow_id = $workflow_id;
        } else {
            // Create new workflow
            $data['created_by'] = get_current_user_id();
            $result = $wpdb->insert($workflows_table, $data);
            $workflow_id = $wpdb->insert_id;
        }

        if ($result !== false) {
            wp_send_json_success(array(
                'workflow_id' => $workflow_id,
                'message' => __('Workflow saved successfully', 'db-app-builder')
            ));
        } else {
            wp_send_json_error(__('Failed to save workflow', 'db-app-builder'));
        }
    }

    /**
     * Load workflow
     */
    public static function ajax_load_workflow() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!isset($_POST['workflow_id'])) {
            wp_send_json_error(__('Workflow ID is required', 'db-app-builder'));
            return;
        }

        $workflow_id = intval($_POST['workflow_id']);

        global $wpdb;
        $workflows_table = $wpdb->prefix . 'dab_workflows';

        $workflow = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $workflows_table WHERE id = %d",
            $workflow_id
        ));

        if ($workflow) {
            $workflow->trigger_config = json_decode($workflow->trigger_config, true);
            $workflow->workflow_steps = json_decode($workflow->workflow_steps, true);
            wp_send_json_success($workflow);
        } else {
            wp_send_json_error(__('Workflow not found', 'db-app-builder'));
        }
    }

    /**
     * Delete workflow
     */
    public static function ajax_delete_workflow() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_workflow_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!isset($_POST['workflow_id'])) {
            wp_send_json_error(__('Workflow ID is required', 'db-app-builder'));
            return;
        }

        $workflow_id = intval($_POST['workflow_id']);

        global $wpdb;
        $workflows_table = $wpdb->prefix . 'dab_workflows';
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';

        // Delete workflow executions first
        $wpdb->delete($executions_table, array('workflow_id' => $workflow_id));

        // Delete workflow
        $result = $wpdb->delete($workflows_table, array('id' => $workflow_id));

        if ($result !== false) {
            wp_send_json_success(__('Workflow deleted successfully', 'db-app-builder'));
        } else {
            wp_send_json_error(__('Failed to delete workflow', 'db-app-builder'));
        }
    }

    /**
     * Test workflow
     */
    public static function ajax_test_workflow() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_workflow_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!isset($_POST['workflow_id'])) {
            wp_send_json_error(__('Workflow ID is required', 'db-app-builder'));
            return;
        }

        $workflow_id = intval($_POST['workflow_id']);

        // Execute workflow with test data
        $execution_id = self::execute_workflow($workflow_id, array(
            'trigger_type' => 'manual_test',
            'test_mode' => true,
            'timestamp' => current_time('mysql')
        ));

        if ($execution_id) {
            wp_send_json_success(array(
                'execution_id' => $execution_id,
                'message' => __('Workflow test started successfully', 'db-app-builder')
            ));
        } else {
            wp_send_json_error(__('Failed to start workflow test', 'db-app-builder'));
        }
    }

    /**
     * Get workflow execution logs
     */
    public static function ajax_get_workflow_logs() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!isset($_POST['workflow_id'])) {
            wp_send_json_error(__('Workflow ID is required', 'db-app-builder'));
            return;
        }

        $workflow_id = intval($_POST['workflow_id']);
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;

        global $wpdb;
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';

        $executions = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $executions_table
             WHERE workflow_id = %d
             ORDER BY started_at DESC
             LIMIT %d",
            $workflow_id,
            $limit
        ));

        // Decode execution logs
        foreach ($executions as $execution) {
            $execution->trigger_data = json_decode($execution->trigger_data, true);
            $execution->execution_log = json_decode($execution->execution_log, true);
        }

        wp_send_json_success($executions);
    }

    /**
     * Get table fields for workflow configuration
     */
    public static function ajax_get_table_fields_for_workflow() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!isset($_POST['table_id'])) {
            wp_send_json_error(__('Table ID is required', 'db-app-builder'));
            return;
        }

        $table_id = intval($_POST['table_id']);

        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT id, field_label, field_slug, field_type, options
             FROM $fields_table
             WHERE table_id = %d
             ORDER BY field_label ASC",
            $table_id
        ));

        if (empty($fields)) {
            wp_send_json_error(__('No fields found for this table', 'db-app-builder'));
            return;
        }

        // Format fields for frontend use
        $formatted_fields = array();
        foreach ($fields as $field) {
            $formatted_fields[] = array(
                'id' => $field->id,
                'label' => $field->field_label,
                'slug' => $field->field_slug,
                'type' => $field->field_type,
                'options' => $field->options ? json_decode($field->options, true) : array()
            );
        }

        wp_send_json_success($formatted_fields);
    }

    /**
     * Trigger workflows on form submission
     */
    public static function trigger_form_workflows($form_id, $data, $record_id) {
        global $wpdb;
        $workflows_table = $wpdb->prefix . 'dab_workflows';

        // Find workflows triggered by this form
        $workflows = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $workflows_table
             WHERE trigger_type = 'form_submit'
             AND is_active = 1
             AND JSON_EXTRACT(trigger_config, '$.form_id') = %d",
            $form_id
        ));

        foreach ($workflows as $workflow) {
            self::execute_workflow($workflow->id, array(
                'trigger_type' => 'form_submit',
                'form_id' => $form_id,
                'record_id' => $record_id,
                'form_data' => $data
            ));
        }
    }

    /**
     * Execute a workflow
     */
    public static function execute_workflow($workflow_id, $trigger_data = array()) {
        global $wpdb;

        // Create execution record
        $executions_table = $wpdb->prefix . 'dab_workflow_executions';

        $execution_id = $wpdb->insert($executions_table, array(
            'workflow_id' => $workflow_id,
            'trigger_data' => json_encode($trigger_data),
            'execution_status' => 'pending',
            'started_at' => current_time('mysql')
        ));

        if (!$execution_id) {
            return false;
        }

        $execution_id = $wpdb->insert_id;

        // Schedule execution in background
        wp_schedule_single_event(time(), 'dab_execute_workflow_background', array($execution_id));

        return $execution_id;
    }
}
