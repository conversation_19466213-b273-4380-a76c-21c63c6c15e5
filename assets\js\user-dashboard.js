/**
 * User Dashboard JavaScript
 * 
 * Handles user dashboard functionality and data management
 */

jQuery(document).ready(function($) {
    'use strict';

    let currentTableId = null;
    let currentPage = 1;
    const recordsPerPage = 20;

    // Initialize dashboard
    initializeDashboard();

    function initializeDashboard() {
        loadUserStats();
        loadTableRecordCounts();
        loadRecentActivity();
    }

    // Load user statistics
    function loadUserStats() {
        const $statsContainer = $('#dab-dashboard-stats');
        
        if (!$statsContainer.length) return;

        $.post(dab_dashboard.ajax_url, {
            action: 'dab_get_user_stats',
            nonce: dab_dashboard.nonce
        })
        .done(function(response) {
            if (response.success) {
                displayStats(response.data);
            } else {
                $statsContainer.html('<p class="dab-error">' + (response.data || dab_dashboard.messages.error) + '</p>');
            }
        })
        .fail(function() {
            $statsContainer.html('<p class="dab-error">' + dab_dashboard.messages.error + '</p>');
        });
    }

    // Display statistics
    function displayStats(stats) {
        const $statsContainer = $('#dab-dashboard-stats');
        
        const statsHtml = `
            <div class="dab-stat-card">
                <div class="dab-stat-number">${stats.total_tables}</div>
                <p class="dab-stat-label">Accessible Tables</p>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number">${stats.total_records}</div>
                <p class="dab-stat-label">Total Records</p>
            </div>
            <div class="dab-stat-card">
                <div class="dab-stat-number">${stats.recent_activity.length}</div>
                <p class="dab-stat-label">Recent Activities</p>
            </div>
        `;
        
        $statsContainer.html(statsHtml);
    }

    // Load record counts for each table
    function loadTableRecordCounts() {
        $('.dab-table-record-count').each(function() {
            const $countElement = $(this);
            const tableId = $countElement.data('table-id');
            
            $.post(dab_dashboard.ajax_url, {
                action: 'dab_get_user_data',
                nonce: dab_dashboard.nonce,
                table_id: tableId,
                limit: 1,
                offset: 0
            })
            .done(function(response) {
                if (response.success) {
                    $countElement.html(`<strong>${response.data.total}</strong> records`);
                } else {
                    $countElement.html('<span class="dab-error">Error loading count</span>');
                }
            })
            .fail(function() {
                $countElement.html('<span class="dab-error">Error loading count</span>');
            });
        });
    }

    // Load recent activity
    function loadRecentActivity() {
        const $activityContainer = $('#dab-recent-activity');
        
        if (!$activityContainer.length) return;

        $.post(dab_dashboard.ajax_url, {
            action: 'dab_get_user_stats',
            nonce: dab_dashboard.nonce
        })
        .done(function(response) {
            if (response.success && response.data.recent_activity) {
                displayRecentActivity(response.data.recent_activity);
            } else {
                $activityContainer.html('<p class="dab-no-data">' + dab_dashboard.messages.no_data + '</p>');
            }
        })
        .fail(function() {
            $activityContainer.html('<p class="dab-error">' + dab_dashboard.messages.error + '</p>');
        });
    }

    // Display recent activity
    function displayRecentActivity(activities) {
        const $activityContainer = $('#dab-recent-activity');
        
        if (activities.length === 0) {
            $activityContainer.html('<p class="dab-no-data">' + dab_dashboard.messages.no_data + '</p>');
            return;
        }

        let activityHtml = '<div class="dab-activity-list">';
        
        activities.forEach(function(activity) {
            const actionIcon = getActionIcon(activity.action);
            const timeAgo = getTimeAgo(activity.created_at);
            
            activityHtml += `
                <div class="dab-activity-item">
                    <div class="dab-activity-icon">${actionIcon}</div>
                    <div class="dab-activity-content">
                        <div class="dab-activity-text">
                            <strong>${capitalizeFirst(activity.action)}</strong> record in 
                            <strong>${activity.table_label || 'Unknown Table'}</strong>
                        </div>
                        <div class="dab-activity-time">${timeAgo}</div>
                    </div>
                </div>
            `;
        });
        
        activityHtml += '</div>';
        $activityContainer.html(activityHtml);
    }

    // View table data
    $(document).on('click', '.dab-view-table-data', function() {
        const tableId = $(this).data('table-id');
        const tableName = $(this).data('table-name');
        
        currentTableId = tableId;
        currentPage = 1;
        
        $('#dab-modal-title').text(tableName + ' - Data');
        $('#dab-data-modal').show();
        
        loadTableData();
    });

    // Load table data
    function loadTableData(search = '') {
        const $dataContent = $('#dab-data-content');
        
        $dataContent.html('<div class="dab-data-loading"><span class="dab-spinner"></span> ' + dab_dashboard.messages.loading + '</div>');
        
        $.post(dab_dashboard.ajax_url, {
            action: 'dab_get_user_data',
            nonce: dab_dashboard.nonce,
            table_id: currentTableId,
            limit: recordsPerPage,
            offset: (currentPage - 1) * recordsPerPage,
            search: search
        })
        .done(function(response) {
            if (response.success) {
                displayTableData(response.data);
            } else {
                $dataContent.html('<p class="dab-error">' + (response.data || dab_dashboard.messages.error) + '</p>');
            }
        })
        .fail(function() {
            $dataContent.html('<p class="dab-error">' + dab_dashboard.messages.error + '</p>');
        });
    }

    // Display table data
    function displayTableData(data) {
        const $dataContent = $('#dab-data-content');
        const $pagination = $('#dab-data-pagination');
        
        if (!data.records || data.records.length === 0) {
            $dataContent.html('<p class="dab-no-data">' + dab_dashboard.messages.no_data + '</p>');
            $pagination.hide();
            return;
        }

        // Build table HTML
        let tableHtml = '<div class="dab-data-table-wrapper"><table class="dab-data-table">';
        
        // Table header
        tableHtml += '<thead><tr>';
        const firstRecord = data.records[0];
        Object.keys(firstRecord).forEach(function(key) {
            if (key !== 'user_id') { // Hide user_id column
                tableHtml += `<th>${capitalizeFirst(key.replace(/_/g, ' '))}</th>`;
            }
        });
        tableHtml += '<th>Actions</th></tr></thead>';
        
        // Table body
        tableHtml += '<tbody>';
        data.records.forEach(function(record) {
            tableHtml += '<tr>';
            Object.keys(record).forEach(function(key) {
                if (key !== 'user_id') {
                    let value = record[key];
                    if (value === null || value === '') {
                        value = '-';
                    } else if (typeof value === 'string' && value.length > 50) {
                        value = value.substring(0, 50) + '...';
                    }
                    tableHtml += `<td>${escapeHtml(value)}</td>`;
                }
            });
            tableHtml += `
                <td class="dab-actions">
                    <button type="button" class="dab-btn dab-btn-sm dab-btn-primary dab-edit-record" 
                            data-record-id="${record.id}">Edit</button>
                    <button type="button" class="dab-btn dab-btn-sm dab-btn-danger dab-delete-record" 
                            data-record-id="${record.id}">Delete</button>
                </td>
            </tr>`;
        });
        tableHtml += '</tbody></table></div>';
        
        $dataContent.html(tableHtml);
        
        // Show pagination if needed
        if (data.total > recordsPerPage) {
            displayPagination(data.total);
            $pagination.show();
        } else {
            $pagination.hide();
        }
    }

    // Display pagination
    function displayPagination(total) {
        const $pagination = $('#dab-data-pagination');
        const totalPages = Math.ceil(total / recordsPerPage);
        
        let paginationHtml = '<div class="dab-pagination">';
        
        // Previous button
        if (currentPage > 1) {
            paginationHtml += `<button type="button" class="dab-pagination-btn" data-page="${currentPage - 1}">Previous</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                paginationHtml += `<button type="button" class="dab-pagination-btn dab-pagination-current">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - currentPage) <= 2) {
                paginationHtml += `<button type="button" class="dab-pagination-btn" data-page="${i}">${i}</button>`;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                paginationHtml += '<span class="dab-pagination-dots">...</span>';
            }
        }
        
        // Next button
        if (currentPage < totalPages) {
            paginationHtml += `<button type="button" class="dab-pagination-btn" data-page="${currentPage + 1}">Next</button>`;
        }
        
        paginationHtml += '</div>';
        $pagination.html(paginationHtml);
    }

    // Pagination click handler
    $(document).on('click', '.dab-pagination-btn[data-page]', function() {
        currentPage = parseInt($(this).data('page'));
        loadTableData($('#dab-data-search').val());
    });

    // Search functionality
    let searchTimeout;
    $('#dab-data-search').on('input', function() {
        const searchTerm = $(this).val();
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentPage = 1;
            loadTableData(searchTerm);
        }, 500);
    });

    // Close modal handlers
    $('#dab-close-data-modal').on('click', function() {
        $('#dab-data-modal').hide();
    });

    $('#dab-data-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // Delete record handler
    $(document).on('click', '.dab-delete-record', function() {
        if (!confirm(dab_dashboard.messages.confirm_delete)) {
            return;
        }
        
        const recordId = $(this).data('record-id');
        const $btn = $(this);
        
        $btn.prop('disabled', true).text('Deleting...');
        
        $.post(dab_dashboard.ajax_url, {
            action: 'dab_delete_user_data',
            nonce: dab_dashboard.nonce,
            table_id: currentTableId,
            record_id: recordId
        })
        .done(function(response) {
            if (response.success) {
                loadTableData($('#dab-data-search').val());
                loadTableRecordCounts(); // Refresh counts
            } else {
                alert(response.data || 'Failed to delete record');
                $btn.prop('disabled', false).text('Delete');
            }
        })
        .fail(function() {
            alert('Failed to delete record');
            $btn.prop('disabled', false).text('Delete');
        });
    });

    // Helper functions
    function getActionIcon(action) {
        const icons = {
            'create': '<span class="dashicons dashicons-plus"></span>',
            'update': '<span class="dashicons dashicons-edit"></span>',
            'delete': '<span class="dashicons dashicons-trash"></span>',
            'view': '<span class="dashicons dashicons-visibility"></span>'
        };
        return icons[action] || '<span class="dashicons dashicons-admin-generic"></span>';
    }

    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        return Math.floor(diffInSeconds / 86400) + ' days ago';
    }

    function capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
});
