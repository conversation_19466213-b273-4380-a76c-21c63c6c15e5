<?php
/**
 * Dashboards List Page
 *
 * Admin page for listing all dashboards.
 */
if (!defined('ABSPATH')) exit;

// Get all dashboards
$dashboards = DAB_Simple_Dashboard_Manager::get_dashboards();

// Enqueue styles
wp_enqueue_style('dab-simple-dashboard-list', plugin_dir_url(dirname(__FILE__)) . 'assets/css/simple-dashboard-list.css', array(), DAB_VERSION);
?>

<div class="wrap dab-dashboards-wrap">
    <h1 class="wp-heading-inline"><?php _e('Dashboards', 'db-app-builder'); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=dab_dashboard_builder'); ?>" class="page-title-action"><?php _e('Add New', 'db-app-builder'); ?></a>
    
    <hr class="wp-header-end">
    
    <?php if (!empty($dashboards)): ?>
        <div class="dab-dashboards-grid">
            <?php foreach ($dashboards as $dashboard): ?>
                <div class="dab-dashboard-card">
                    <div class="dab-dashboard-card-header">
                        <h2 class="dab-dashboard-card-title"><?php echo esc_html($dashboard->title); ?></h2>
                        <?php if ($dashboard->is_public): ?>
                            <span class="dab-dashboard-card-badge"><?php _e('Public', 'db-app-builder'); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="dab-dashboard-card-body">
                        <?php if (!empty($dashboard->description)): ?>
                            <p class="dab-dashboard-card-description"><?php echo wp_kses_post($dashboard->description); ?></p>
                        <?php else: ?>
                            <p class="dab-dashboard-card-description dab-empty-description"><?php _e('No description', 'db-app-builder'); ?></p>
                        <?php endif; ?>
                        
                        <div class="dab-dashboard-card-meta">
                            <span class="dab-dashboard-card-date">
                                <?php printf(__('Created: %s', 'db-app-builder'), date_i18n(get_option('date_format'), strtotime($dashboard->created_at))); ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="dab-dashboard-card-footer">
                        <a href="<?php echo admin_url('admin.php?page=dab_dashboard_view&dashboard_id=' . $dashboard->id); ?>" class="button"><?php _e('View', 'db-app-builder'); ?></a>
                        <a href="<?php echo admin_url('admin.php?page=dab_dashboard_builder&dashboard_id=' . $dashboard->id); ?>" class="button"><?php _e('Edit', 'db-app-builder'); ?></a>
                        
                        <?php if ($dashboard->is_public): ?>
                            <div class="dab-dashboard-card-shortcode">
                                <code>[dab_dashboard id="<?php echo $dashboard->id; ?>"]</code>
                                <button type="button" class="dab-copy-shortcode" data-shortcode='[dab_dashboard id="<?php echo $dashboard->id; ?>"]'>
                                    <span class="dashicons dashicons-clipboard"></span>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="dab-no-dashboards">
            <p><?php _e('No dashboards found.', 'db-app-builder'); ?></p>
            <a href="<?php echo admin_url('admin.php?page=dab_dashboard_builder'); ?>" class="button button-primary"><?php _e('Create Your First Dashboard', 'db-app-builder'); ?></a>
        </div>
    <?php endif; ?>
</div>

<script>
    // Simple script to copy shortcode to clipboard
    document.addEventListener('DOMContentLoaded', function() {
        const copyButtons = document.querySelectorAll('.dab-copy-shortcode');
        
        copyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const shortcode = this.getAttribute('data-shortcode');
                
                // Create temporary textarea
                const textarea = document.createElement('textarea');
                textarea.value = shortcode;
                document.body.appendChild(textarea);
                
                // Select and copy
                textarea.select();
                document.execCommand('copy');
                
                // Remove textarea
                document.body.removeChild(textarea);
                
                // Show copied message
                const icon = this.querySelector('.dashicons');
                icon.classList.remove('dashicons-clipboard');
                icon.classList.add('dashicons-yes');
                
                setTimeout(function() {
                    icon.classList.remove('dashicons-yes');
                    icon.classList.add('dashicons-clipboard');
                }, 1000);
            });
        });
    });
</script>
