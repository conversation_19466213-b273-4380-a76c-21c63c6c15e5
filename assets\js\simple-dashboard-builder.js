/**
 * Simple Dashboard Builder
 *
 * JavaScript for the dashboard builder interface.
 */
(function($) {
    'use strict';

    // Dashboard grid
    let dashboardGrid;

    // Current dashboard ID
    let dashboardId = 0;

    // Current widget being edited
    let currentWidget = null;

    // Current row being edited
    let currentRow = null;

    // Current column being edited
    let currentColumn = null;

    // Dashboard layout
    let dashboardLayout = {
        rows: []
    };

    // Grid size
    const gridColumns = 12;
    const gridRows = 6;

    /**
     * Initialize the dashboard builder
     */
    function initDashboardBuilder() {
        console.log('Initializing dashboard builder...');

        // Get dashboard grid
        dashboardGrid = document.getElementById('dashboard-grid');

        // Get dashboard ID
        dashboardId = dab_dashboard.dashboard_id || 0;

        // Initialize dashboard layout
        let layoutLoaded = false;

        // First try to load from localStorage (this is our backup)
        if (dashboardId > 0) {
            try {
                const localLayout = localStorage.getItem('dab_dashboard_layout_' + dashboardId);
                if (localLayout) {
                    const parsedLocalLayout = JSON.parse(localLayout);
                    if (parsedLocalLayout && parsedLocalLayout.rows) {
                        console.log('Loading layout from localStorage backup');
                        dashboardLayout = parsedLocalLayout;
                        layoutLoaded = true;
                    }
                }
            } catch (e) {
                console.error('Error loading layout from localStorage:', e);
            }
        }

        // If not loaded from localStorage, try from server data
        if (!layoutLoaded && dab_dashboard.dashboard && dab_dashboard.dashboard.layout) {
            try {
                const layout = typeof dab_dashboard.dashboard.layout === 'string'
                    ? JSON.parse(dab_dashboard.dashboard.layout)
                    : dab_dashboard.dashboard.layout;

                if (layout && layout.rows) {
                    console.log('Loading layout from server data');
                    dashboardLayout = layout;
                    layoutLoaded = true;
                }
            } catch (e) {
                console.error('Error parsing dashboard layout from server:', e);
            }
        }

        // If we have a layout (from either source), ensure all columns have required properties
        if (layoutLoaded && dashboardLayout && dashboardLayout.rows) {
            console.log('Validating layout structure');
            dashboardLayout.rows.forEach(row => {
                if (row.columns) {
                    row.columns.forEach(column => {
                        // Ensure column has a width property
                        if (!column.hasOwnProperty('width')) {
                            column.width = 1;
                        }
                        // Ensure column has a settings property
                        if (!column.hasOwnProperty('settings')) {
                            column.settings = {
                                background: '',
                                padding: 10,
                                border: 'dashed',
                                borderColor: '#ddd',
                                borderWidth: 1,
                                borderRadius: 4
                            };
                        }
                        // Ensure column has a widgets array
                        if (!column.hasOwnProperty('widgets')) {
                            column.widgets = [];
                        }
                    });
                }
            });
        } else {
            // Create default layout with one row and one column
            console.log('Creating default layout');
            dashboardLayout = {
                rows: [
                    {
                        id: 0,
                        settings: {
                            height: 100,
                            background: '',
                            padding: 10,
                            marginBottom: 20,
                            border: 'dashed',
                            borderColor: '#ccc',
                            borderWidth: 1,
                            borderRadius: 5
                        },
                        columns: [
                            {
                                id: 0,
                                width: 12, // Full width for the first column
                                settings: {
                                    background: '',
                                    padding: 10,
                                    border: 'dashed',
                                    borderColor: '#ddd',
                                    borderWidth: 1,
                                    borderRadius: 4
                                },
                                widgets: []
                            }
                        ]
                    }
                ]
            };
        }

        // Add empty column placeholders to columns without widgets
        addEmptyColumnPlaceholders();

        // Add column resize handles
        addColumnResizeHandles();

        // Initialize drag and drop
        initDragAndDrop();

        // Initialize widget actions
        initWidgetActions();

        // Initialize row and column actions
        initRowColumnActions();

        // Initialize column resizing
        initColumnResizing();

        // Initialize dashboard actions
        initDashboardActions();

        // Initialize modal
        initModal();

        // Add responsive class to dashboard grid
        $('#dashboard-grid').addClass('dab-responsive-grid');

        // Force re-binding of event handlers
        rebindRowColumnEvents();

        // Ensure layout consistency on load
        ensureLayoutConsistency();

        // Save the layout to ensure all columns are properly saved
        if (dashboardId > 0) {
            console.log('Saving initial layout to ensure columns are preserved');
            setTimeout(function() {
                saveLayout();
            }, 500);
        }

        // Add direct event handlers to all existing buttons
        $('.dab-row-add-column').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                console.log('Direct binding: Add column button clicked for row:', rowId);
                addNewColumn(rowId);
            });
        });

        $('.dab-row-settings').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                console.log('Direct binding: Row settings button clicked for row:', rowId);
                openRowSettingsModal(rowId);
            });
        });

        $('.dab-row-delete').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                console.log('Direct binding: Row delete button clicked for row:', rowId);
                if (confirm('Are you sure you want to delete this row and all its contents?')) {
                    deleteRow(rowId);
                }
            });
        });

        $('.dab-column-settings').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const column = $(this).closest('.dab-dashboard-column');
                const rowId = column.closest('.dab-dashboard-row').data('row-id');
                const columnId = column.data('column-id');
                console.log('Direct binding: Column settings button clicked for column:', columnId, 'in row:', rowId);
                openColumnSettingsModal(rowId, columnId);
            });
        });

        $('.dab-column-delete').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const column = $(this).closest('.dab-dashboard-column');
                const rowId = column.closest('.dab-dashboard-row').data('row-id');
                const columnId = column.data('column-id');
                console.log('Direct binding: Column delete button clicked for column:', columnId, 'in row:', rowId);

                // Don't allow deleting the last column in a row
                const columnsInRow = column.closest('.dab-dashboard-row').find('.dab-dashboard-column').length;
                if (columnsInRow <= 1) {
                    alert('Cannot delete the last column in a row. Delete the row instead.');
                    return;
                }

                if (confirm('Are you sure you want to delete this column and all its contents?')) {
                    deleteColumn(rowId, columnId);
                }
            });
        });

        console.log('Dashboard builder initialized successfully');
    }

    /**
     * Rebind row and column event handlers
     * This ensures the event handlers are properly bound even if the DOM has changed
     */
    function rebindRowColumnEvents() {
        // Unbind existing handlers to prevent duplicates
        $(document).off('click', '.dab-row-add-column');
        $(document).off('click', '.dab-row-settings');
        $(document).off('click', '.dab-row-delete');
        $(document).off('click', '.dab-column-settings');
        $(document).off('click', '.dab-column-delete');

        // Rebind row controls with direct binding instead of delegated events
        $('.dab-row-add-column').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Add column button clicked');
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            console.log('Adding column to row:', rowId);
            addNewColumn(rowId);
        });

        $('.dab-row-settings').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Row settings button clicked');
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            console.log('Opening settings for row:', rowId);
            openRowSettingsModal(rowId);
        });

        $('.dab-row-delete').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Row delete button clicked');
            if (confirm('Are you sure you want to delete this row and all its contents?')) {
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                console.log('Deleting row:', rowId);
                deleteRow(rowId);
            }
        });

        // Rebind column controls with direct binding
        $('.dab-column-settings').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Column settings button clicked');
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');
            console.log('Opening settings for column:', columnId, 'in row:', rowId);
            openColumnSettingsModal(rowId, columnId);
        });

        $('.dab-column-delete').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Column delete button clicked');
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');

            // Don't allow deleting the last column in a row
            const columnsInRow = column.closest('.dab-dashboard-row').find('.dab-dashboard-column').length;
            if (columnsInRow <= 1) {
                alert('Cannot delete the last column in a row. Delete the row instead.');
                return;
            }

            if (confirm('Are you sure you want to delete this column and all its contents?')) {
                console.log('Deleting column:', columnId, 'in row:', rowId);
                deleteColumn(rowId, columnId);
            }
        });

        // Also keep the delegated events for dynamically added elements
        $(document).on('click', '.dab-row-add-column', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            addNewColumn(rowId);
        });

        $(document).on('click', '.dab-row-settings', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            openRowSettingsModal(rowId);
        });

        $(document).on('click', '.dab-row-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (confirm('Are you sure you want to delete this row and all its contents?')) {
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                deleteRow(rowId);
            }
        });

        $(document).on('click', '.dab-column-settings', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');
            openColumnSettingsModal(rowId, columnId);
        });

        $(document).on('click', '.dab-column-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');

            // Don't allow deleting the last column in a row
            const columnsInRow = column.closest('.dab-dashboard-row').find('.dab-dashboard-column').length;
            if (columnsInRow <= 1) {
                alert('Cannot delete the last column in a row. Delete the row instead.');
                return;
            }

            if (confirm('Are you sure you want to delete this column and all its contents?')) {
                deleteColumn(rowId, columnId);
            }
        });
    }

    /**
     * Add empty column placeholders to columns without widgets
     */
    function addEmptyColumnPlaceholders() {
        console.log('Updating empty column placeholders');

        $('.dab-dashboard-column').each(function() {
            // Check if column has any widgets
            if ($(this).find('.dab-dashboard-widget').length === 0) {
                // Add empty placeholder if it doesn't exist
                if ($(this).find('.dab-empty-column-placeholder').length === 0) {
                    console.log('Adding empty placeholder to column:', $(this).data('column-id'));
                    $(this).append(`
                        <div class="dab-empty-column-placeholder">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <p>Drag widgets here</p>
                        </div>
                    `);

                    // Add a fade-in effect
                    $(this).find('.dab-empty-column-placeholder').css('opacity', 0).animate({opacity: 1}, 300);
                }
            } else {
                // Remove placeholder if widgets exist
                if ($(this).find('.dab-empty-column-placeholder').length > 0) {
                    console.log('Removing empty placeholder from column:', $(this).data('column-id'));
                    // Fade out and then remove
                    $(this).find('.dab-empty-column-placeholder').animate({opacity: 0}, 200, function() {
                        $(this).remove();
                    });
                }
            }
        });

        // Make sure columns are droppable even after updating placeholders
        $('.dab-dashboard-column').each(function() {
            if (!$(this).hasClass('ui-droppable') || !$(this).data('ui-droppable')) {
                $(this).droppable({
                    accept: '.dab-dashboard-widget',
                    hoverClass: 'column-drop-hover',
                    tolerance: 'pointer',
                    drop: function(event, ui) {
                        const widget = ui.draggable;
                        const sourceRowId = widget.data('row-id');
                        const sourceColumnId = widget.data('column-id');
                        const targetRowId = $(this).closest('.dab-dashboard-row').data('row-id');
                        const targetColumnId = $(this).data('column-id');

                        // Only move if the target is different from the source
                        if (sourceRowId !== targetRowId || sourceColumnId !== targetColumnId) {
                            moveWidgetToColumn(widget, sourceRowId, sourceColumnId, targetRowId, targetColumnId);
                        } else {
                            // If dropped in the same column, reset position
                            widget.css({
                                top: 0,
                                left: 0
                            });
                        }
                    }
                });
            }
        });
    }

    /**
     * Add column resize handles
     */
    function addColumnResizeHandles() {
        $('.dab-dashboard-column').each(function() {
            // Add resize handle if it doesn't exist
            if ($(this).find('.dab-column-resize-handle').length === 0) {
                $(this).append('<div class="dab-column-resize-handle"></div>');
            }
        });
    }

    /**
     * Initialize drag and drop
     */
    function initDragAndDrop() {
        // Make widgets in sidebar draggable
        $('.dab-widget-item').on('dragstart', function(e) {
            e.originalEvent.dataTransfer.setData('widget-type', $(this).data('widget-type'));
            $(this).addClass('dragging');

            // Add visual feedback
            $('.dab-dashboard-column').addClass('drop-target-active');
        });

        $('.dab-widget-item').on('dragend', function() {
            $(this).removeClass('dragging');
            $('.dab-dashboard-column').removeClass('drop-target-active column-drop-hover');
        });

        // Make columns drop targets for new widgets
        $(document).on('dragover', '.dab-dashboard-column', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('column-drop-hover');
        });

        $(document).on('dragleave', '.dab-dashboard-column', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Only remove hover if we're actually leaving the column
            const rect = this.getBoundingClientRect();
            const x = e.originalEvent.clientX;
            const y = e.originalEvent.clientY;

            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                $(this).removeClass('column-drop-hover');
            }
        });

        $(document).on('drop', '.dab-dashboard-column', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('column-drop-hover');
            $('.dab-dashboard-column').removeClass('drop-target-active');

            const widgetType = e.originalEvent.dataTransfer.getData('widget-type');

            if (widgetType) {
                // Get the row and column IDs
                const rowId = $(this).closest('.dab-dashboard-row').data('row-id');
                const columnId = $(this).data('column-id');

                // Store drop target for later use
                window.pendingWidgetDrop = {
                    rowId: rowId,
                    columnId: columnId,
                    targetColumn: $(this)
                };

                // Open widget settings modal
                openWidgetSettingsModal({
                    type: widgetType,
                    rowId: rowId,
                    columnId: columnId,
                    width: 1,
                    height: 1
                });
            }
        });

        // Initialize widget drag between columns
        initWidgetDragBetweenColumns();

        // Make widgets resizable
        $('.dab-dashboard-widget').resizable({
            handles: 'se',
            minWidth: 100,
            minHeight: 100,
            stop: function(event, ui) {
                // Calculate new size
                const width = Math.max(1, Math.round(ui.size.width / 100));
                const height = Math.max(1, Math.round(ui.size.height / 100));

                // Update widget size
                $(this).css({
                    width: width * 100 + 'px',
                    height: height * 100 + 'px'
                });

                $(this).data('width', width);
                $(this).data('height', height);

                // Save widget size
                saveWidgetSize($(this).data('widget-id'), width, height);
            }
        });
    }

    /**
     * Initialize widget actions
     */
    function initWidgetActions() {
        // Edit widget
        $(document).on('click', '.dab-widget-edit', function() {
            const widget = $(this).closest('.dab-dashboard-widget');
            const widgetId = widget.data('widget-id');

            // First, get the widget data from the database
            $.ajax({
                url: dab_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_widget',
                    widget_id: widgetId,
                    nonce: dab_dashboard.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        console.log('Widget data retrieved:', response.data);

                        // Parse the settings
                        let settings = {};
                        if (response.data.settings) {
                            try {
                                // Try to parse the settings
                                if (typeof response.data.settings === 'string') {
                                    settings = JSON.parse(response.data.settings);
                                } else if (typeof response.data.settings === 'object') {
                                    settings = response.data.settings;
                                }
                            } catch (e) {
                                console.error('Error parsing widget settings:', e);
                                // Try to clean the string and parse again
                                try {
                                    const cleaned = response.data.settings.replace(/\\/g, '');
                                    settings = JSON.parse(cleaned);
                                } catch (e2) {
                                    console.error('Error parsing cleaned widget settings:', e2);
                                }
                            }
                        }

                        console.log('Parsed settings:', settings);

                        // Open the widget settings modal
                        openWidgetSettingsModal({
                            id: widgetId,
                            type: widget.data('widget-type'),
                            title: widget.find('.dab-widget-title').text(),
                            posX: widget.data('position-x'),
                            posY: widget.data('position-y'),
                            width: widget.data('width'),
                            height: widget.data('height'),
                            settings: settings
                        });
                    } else {
                        alert(response.data || dab_dashboard.i18n.widget_save_error);
                    }
                },
                error: function() {
                    alert(dab_dashboard.i18n.widget_save_error);
                }
            });
        });

        // Delete widget
        $(document).on('click', '.dab-widget-delete', function() {
            if (confirm(dab_dashboard.i18n.delete_widget_confirm)) {
                const widget = $(this).closest('.dab-dashboard-widget');
                const widgetId = widget.data('widget-id');

                // Delete widget
                $.ajax({
                    url: dab_dashboard.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'dab_delete_widget',
                        widget_id: widgetId,
                        nonce: dab_dashboard.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            widget.remove();
                            alert(dab_dashboard.i18n.widget_delete_success);
                        } else {
                            alert(response.data || dab_dashboard.i18n.widget_delete_error);
                        }
                    },
                    error: function() {
                        alert(dab_dashboard.i18n.widget_delete_error);
                    }
                });
            }
        });
    }

    /**
     * Initialize row and column actions
     */
    function initRowColumnActions() {
        // Add row button
        $('.dab-add-row-btn').off('click').on('click', function() {
            console.log('Add row button clicked');
            addNewRow();
        });

        // Remove any existing event handlers to prevent duplicates
        $(document).off('click', '.dab-row-add-column');
        $(document).off('click', '.dab-row-settings');
        $(document).off('click', '.dab-row-delete');
        $(document).off('click', '.dab-column-settings');
        $(document).off('click', '.dab-column-delete');

        // Add direct event handlers to all row add column buttons
        $('.dab-row-add-column').each(function() {
            $(this).off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                console.log('Direct binding: Add column button clicked for row:', rowId);
                addNewColumn(rowId);
            });
        });

        // Row controls with delegated events
        $(document).on('click', '.dab-row-add-column', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Delegated event: Add column button clicked');
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            console.log('Row ID:', rowId);
            addNewColumn(rowId);
        });

        $(document).on('click', '.dab-row-settings', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const row = $(this).closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            openRowSettingsModal(rowId);
        });

        $(document).on('click', '.dab-row-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (confirm('Are you sure you want to delete this row and all its contents?')) {
                const row = $(this).closest('.dab-dashboard-row');
                const rowId = row.data('row-id');
                deleteRow(rowId);
            }
        });

        // Column controls
        $(document).on('click', '.dab-column-settings', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');
            openColumnSettingsModal(rowId, columnId);
        });

        $(document).on('click', '.dab-column-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const column = $(this).closest('.dab-dashboard-column');
            const rowId = column.closest('.dab-dashboard-row').data('row-id');
            const columnId = column.data('column-id');

            // Don't allow deleting the last column in a row
            const columnsInRow = column.closest('.dab-dashboard-row').find('.dab-dashboard-column').length;
            if (columnsInRow <= 1) {
                alert('Cannot delete the last column in a row. Delete the row instead.');
                return;
            }

            if (confirm('Are you sure you want to delete this column and all its contents?')) {
                deleteColumn(rowId, columnId);
            }
        });

        // Make widgets draggable between columns
        initWidgetDragBetweenColumns();
    }

    /**
     * Initialize widget drag between columns
     */
    function initWidgetDragBetweenColumns() {
        // First destroy any existing draggable instances to prevent duplicates
        $('.dab-dashboard-widget').each(function() {
            if ($(this).data('ui-draggable')) {
                $(this).draggable('destroy');
            }
        });

        // Make widgets draggable
        $('.dab-dashboard-widget').draggable({
            handle: '.dab-widget-header',
            revert: 'invalid',
            zIndex: 100,
            cursor: 'move',
            opacity: 0.7, // Make the widget semi-transparent while dragging
            helper: 'original', // Use the original element instead of a clone
            containment: '#dashboard-grid', // Constrain dragging to the dashboard grid
            refreshPositions: true, // Recalculate positions on every mouse move
            scroll: true, // Enable scrolling while dragging
            start: function(event, ui) {
                $(this).addClass('dragging');
                // Store original position for potential revert
                $(this).data('originalPosition', $(this).position());
                $(this).data('originalParent', $(this).parent());

                // Hide empty column placeholders during drag
                $('.dab-empty-column-placeholder').css('visibility', 'hidden');

                // Add a class to the dashboard grid to indicate dragging is in progress
                $('#dashboard-grid').addClass('dragging-in-progress');
            },
            stop: function(event, ui) {
                $(this).removeClass('dragging');

                // Show empty column placeholders after drag
                $('.dab-empty-column-placeholder').css('visibility', 'visible');
                $('#dashboard-grid').removeClass('dragging-in-progress');

                // Update empty column placeholders
                addEmptyColumnPlaceholders();
            }
        });

        // First destroy any existing droppable instances to prevent duplicates
        $('.dab-dashboard-column').each(function() {
            if ($(this).data('ui-droppable')) {
                $(this).droppable('destroy');
            }
        });

        // Make columns droppable
        $('.dab-dashboard-column').droppable({
            accept: '.dab-dashboard-widget',
            hoverClass: 'column-drop-hover',
            tolerance: 'intersect', // Changed to intersect for better drop detection
            activeClass: 'column-drop-active',
            drop: function(event, ui) {
                const widget = ui.draggable;
                const sourceRowId = widget.data('row-id');
                const sourceColumnId = widget.data('column-id');
                const targetRowId = $(this).closest('.dab-dashboard-row').data('row-id');
                const targetColumnId = $(this).data('column-id');

                console.log('Widget dropped. Source:', sourceRowId, sourceColumnId, 'Target:', targetRowId, targetColumnId);

                // Only move if the target is different from the source
                if (sourceRowId !== targetRowId || sourceColumnId !== targetColumnId) {
                    moveWidgetToColumn(widget, sourceRowId, sourceColumnId, targetRowId, targetColumnId);
                } else {
                    // If dropped in the same column, reset position
                    widget.css({
                        top: 0,
                        left: 0
                    });
                }
            }
        });
    }

    /**
     * Move a widget to a different column
     */
    function moveWidgetToColumn(widget, sourceRowId, sourceColumnId, targetRowId, targetColumnId) {
        console.log('Moving widget between columns');
        const widgetId = parseInt(widget.data('widget-id'));

        if (isNaN(widgetId) || !widgetId) {
            console.error('Invalid widget ID:', widgetId);
            return;
        }

        console.log('Widget ID:', widgetId);
        console.log('Source Row ID:', sourceRowId, 'Source Column ID:', sourceColumnId);
        console.log('Target Row ID:', targetRowId, 'Target Column ID:', targetColumnId);

        // Get source and target elements
        const sourceColumn = $(`.dab-dashboard-row[data-row-id="${sourceRowId}"] .dab-dashboard-column[data-column-id="${sourceColumnId}"]`);
        const targetColumn = $(`.dab-dashboard-row[data-row-id="${targetRowId}"] .dab-dashboard-column[data-column-id="${targetColumnId}"]`);

        if (sourceColumn.length === 0) {
            console.error('Source column not found');
            return;
        }

        if (targetColumn.length === 0) {
            console.error('Target column not found');
            return;
        }

        // Update the layout data structure
        const sourceRowIndex = dashboardLayout.rows.findIndex(row => row.id == sourceRowId);
        if (sourceRowIndex === -1) {
            console.error('Source row not found in layout:', sourceRowId);
            return;
        }

        const sourceColumnIndex = dashboardLayout.rows[sourceRowIndex].columns.findIndex(col => col.id == sourceColumnId);
        if (sourceColumnIndex === -1) {
            console.error('Source column not found in layout:', sourceColumnId);
            return;
        }

        const targetRowIndex = dashboardLayout.rows.findIndex(row => row.id == targetRowId);
        if (targetRowIndex === -1) {
            console.error('Target row not found in layout:', targetRowId);
            return;
        }

        const targetColumnIndex = dashboardLayout.rows[targetRowIndex].columns.findIndex(col => col.id == targetColumnId);
        if (targetColumnIndex === -1) {
            console.error('Target column not found in layout:', targetColumnId);
            return;
        }

        console.log('Source row index:', sourceRowIndex, 'Source column index:', sourceColumnIndex);
        console.log('Target row index:', targetRowIndex, 'Target column index:', targetColumnIndex);

        // Ensure widgets arrays exist
        if (!dashboardLayout.rows[sourceRowIndex].columns[sourceColumnIndex].widgets) {
            dashboardLayout.rows[sourceRowIndex].columns[sourceColumnIndex].widgets = [];
        }

        if (!dashboardLayout.rows[targetRowIndex].columns[targetColumnIndex].widgets) {
            dashboardLayout.rows[targetRowIndex].columns[targetColumnIndex].widgets = [];
        }

        // Remove widget from source column in the data structure
        const widgetIndex = dashboardLayout.rows[sourceRowIndex].columns[sourceColumnIndex].widgets.indexOf(widgetId);
        if (widgetIndex !== -1) {
            dashboardLayout.rows[sourceRowIndex].columns[sourceColumnIndex].widgets.splice(widgetIndex, 1);
            console.log('Removed widget from source column in layout');
        } else {
            console.warn('Widget not found in source column widgets array:', widgetId);
            console.log('Source column widgets:', dashboardLayout.rows[sourceRowIndex].columns[sourceColumnIndex].widgets);
        }

        // Check if widget already exists in target column
        const existsInTarget = dashboardLayout.rows[targetRowIndex].columns[targetColumnIndex].widgets.includes(widgetId);
        if (!existsInTarget) {
            // Add widget to target column in the data structure
            dashboardLayout.rows[targetRowIndex].columns[targetColumnIndex].widgets.push(widgetId);
            console.log('Added widget to target column in layout');
        } else {
            console.warn('Widget already exists in target column:', widgetId);
        }

        // Reset widget position before appending to new column
        widget.css({
            top: 0,
            left: 0,
            position: 'relative',
            width: widget.data('width') * 100 + 'px',
            height: widget.data('height') * 100 + 'px'
        });

        // Detach the widget from its current position and append to the target column
        widget.detach().appendTo(targetColumn);

        // Update widget data attributes
        widget.attr('data-row-id', targetRowId);
        widget.attr('data-column-id', targetColumnId);
        widget.data('row-id', targetRowId);
        widget.data('column-id', targetColumnId);

        // Ensure the widget is properly positioned in the new column
        setTimeout(function() {
            widget.css({
                position: 'relative',
                top: 0,
                left: 0
            });
        }, 50);

        // Add a highlight effect to show the widget has been moved
        widget.css('opacity', 0.5).animate({opacity: 1}, 300);

        // Update empty column placeholders
        if (sourceColumn.find('.dab-dashboard-widget').length === 0) {
            // Add empty placeholder to source column if it's now empty
            if (sourceColumn.find('.dab-empty-column-placeholder').length === 0) {
                sourceColumn.append(`
                    <div class="dab-empty-column-placeholder">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <p>Drag widgets here</p>
                    </div>
                `);
            }
        }

        // Remove empty placeholder from target column
        targetColumn.find('.dab-empty-column-placeholder').remove();

        // Make sure the widget is draggable in its new location
        if (widget.data('ui-draggable')) {
            widget.draggable('destroy');
        }
        widget.draggable({
            handle: '.dab-widget-header',
            revert: 'invalid',
            zIndex: 100,
            cursor: 'move',
            opacity: 0.7,
            helper: 'original',
            containment: '#dashboard-grid',
            refreshPositions: true, // Recalculate positions on every mouse move
            scroll: true, // Enable scrolling while dragging
            start: function(event, ui) {
                $(this).addClass('dragging');
                $(this).data('originalPosition', $(this).position());
                $(this).data('originalParent', $(this).parent());
                $('.dab-empty-column-placeholder').css('visibility', 'hidden');
                $('#dashboard-grid').addClass('dragging-in-progress');
            },
            stop: function(event, ui) {
                $(this).removeClass('dragging');
                $('.dab-empty-column-placeholder').css('visibility', 'visible');
                $('#dashboard-grid').removeClass('dragging-in-progress');

                // Ensure widget stays in place after dropping
                $(this).css({
                    top: 0,
                    left: 0,
                    position: 'relative'
                });

                addEmptyColumnPlaceholders();
            }
        });

        // Log the updated layout for debugging
        console.log('Updated layout:', JSON.stringify(dashboardLayout));

        // Save the layout
        saveLayout();

        console.log('Widget moved successfully');
    }

    /**
     * Add a new row to the dashboard
     */
    function addNewRow() {
        console.log('Adding new row');

        // Find the highest row ID and increment by 1
        let maxRowId = -1;
        dashboardLayout.rows.forEach(row => {
            maxRowId = Math.max(maxRowId, parseInt(row.id));
        });
        const newRowId = maxRowId + 1;
        console.log('New row ID:', newRowId);

        // Create new row object
        const newRow = {
            id: newRowId,
            settings: {
                height: 100,
                background: '',
                padding: 10,
                marginBottom: 20,
                border: 'dashed',
                borderColor: '#ccc',
                borderWidth: 1,
                borderRadius: 5
            },
            columns: [
                {
                    id: 0,
                    width: 12, // Full width for the first column
                    settings: {
                        background: '',
                        padding: 10,
                        border: 'dashed',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        borderRadius: 4
                    },
                    widgets: []
                }
            ]
        };

        // Add to layout
        dashboardLayout.rows.push(newRow);

        // Create row HTML
        const rowHtml = `
            <div class="dab-dashboard-row" data-row-id="${newRowId}">
                <div class="dab-row-controls">
                    <button type="button" class="dab-row-add-column" title="Add Column">
                        <span class="dashicons dashicons-plus"></span>
                    </button>
                    <button type="button" class="dab-row-settings" title="Row Settings">
                        <span class="dashicons dashicons-admin-generic"></span>
                    </button>
                    <button type="button" class="dab-row-delete" title="Delete Row">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>

                <div class="dab-dashboard-column" data-column-id="0" data-width="12" style="flex: 12 1 0">
                    <div class="dab-column-controls">
                        <button type="button" class="dab-column-settings" title="Column Settings">
                            <span class="dashicons dashicons-admin-generic"></span>
                        </button>
                        <button type="button" class="dab-column-delete" title="Delete Column">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>

                    <div class="dab-column-width">12</div>
                    <div class="dab-column-resize-handle"></div>

                    <div class="dab-empty-column-placeholder">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <p>Drag widgets here</p>
                    </div>
                </div>
            </div>
        `;

        // Add to DOM before the add row button
        $('.dab-add-row-container').before(rowHtml);
        console.log('Row added to DOM');

        // Get the newly added row
        const newRowElement = $(`.dab-dashboard-row[data-row-id="${newRowId}"]`);

        // Bind event handlers directly to the new row's controls
        newRowElement.find('.dab-row-add-column').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New row add column button clicked');
            addNewColumn(newRowId);
        });

        newRowElement.find('.dab-row-settings').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New row settings button clicked');
            openRowSettingsModal(newRowId);
        });

        newRowElement.find('.dab-row-delete').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New row delete button clicked');
            if (confirm('Are you sure you want to delete this row and all its contents?')) {
                deleteRow(newRowId);
            }
        });

        // Also bind column control events
        newRowElement.find('.dab-column-settings').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New column settings button clicked');
            const columnId = $(this).closest('.dab-dashboard-column').data('column-id');
            openColumnSettingsModal(newRowId, columnId);
        });

        newRowElement.find('.dab-column-delete').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New column delete button clicked');
            const column = $(this).closest('.dab-dashboard-column');
            const columnId = column.data('column-id');
            const columnsInRow = newRowElement.find('.dab-dashboard-column').length;
            if (columnsInRow <= 1) {
                alert('Cannot delete the last column in a row. Delete the row instead.');
                return;
            }
            if (confirm('Are you sure you want to delete this column and all its contents?')) {
                deleteColumn(newRowId, columnId);
            }
        });

        // Add animation effect
        newRowElement.css('opacity', 0).animate({opacity: 1}, 300);

        // Scroll to the new row
        $('html, body').animate({
            scrollTop: newRowElement.offset().top - 100
        }, 500);

        // Initialize draggable for new widgets
        initWidgetDragBetweenColumns();

        // Initialize column resizing
        initColumnResizing();

        // Save the layout
        saveLayout();

        // Force rebind all event handlers
        rebindRowColumnEvents();

        console.log('New row added successfully');
    }

    /**
     * Add a new column to a row
     */
    function addNewColumn(rowId) {
        console.log('Adding new column to row:', rowId);

        // Find the row
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) {
            console.error('Row not found:', rowId);
            return;
        }

        // Find the highest column ID and increment by 1
        let maxColumnId = -1;
        dashboardLayout.rows[rowIndex].columns.forEach(column => {
            maxColumnId = Math.max(maxColumnId, parseInt(column.id));
        });
        const newColumnId = maxColumnId + 1;
        console.log('New column ID:', newColumnId);

        // Create new column object
        const newColumn = {
            id: newColumnId,
            width: 1,
            settings: {
                background: '',
                padding: 10,
                border: 'dashed',
                borderColor: '#ddd',
                borderWidth: 1,
                borderRadius: 4
            },
            widgets: []
        };

        // Add to layout
        dashboardLayout.rows[rowIndex].columns.push(newColumn);
        console.log('Added new column to layout:', newColumn);

        // Create column HTML
        const columnHtml = `
            <div class="dab-dashboard-column" data-column-id="${newColumnId}" data-width="1" style="flex: 1 1 0">
                <div class="dab-column-controls">
                    <button type="button" class="dab-column-settings" title="Column Settings">
                        <span class="dashicons dashicons-admin-generic"></span>
                    </button>
                    <button type="button" class="dab-column-delete" title="Delete Column">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>

                <div class="dab-column-width">1</div>
                <div class="dab-column-resize-handle"></div>

                <div class="dab-empty-column-placeholder">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <p>Drag widgets here</p>
                </div>
            </div>
        `;

        // Add to DOM
        const rowElement = $(`.dab-dashboard-row[data-row-id="${rowId}"]`);
        if (rowElement.length === 0) {
            console.error('Row element not found in DOM:', rowId);
            return;
        }

        rowElement.append(columnHtml);
        console.log('Column added to DOM');

        // Get the newly added column
        const newColumnElement = rowElement.find(`.dab-dashboard-column[data-column-id="${newColumnId}"]`);
        if (newColumnElement.length === 0) {
            console.error('New column element not found in DOM after append');
            return;
        }

        // Make the new column droppable
        newColumnElement.droppable({
            accept: '.dab-dashboard-widget',
            hoverClass: 'column-drop-hover',
            tolerance: 'intersect',
            activeClass: 'column-drop-active',
            drop: function(event, ui) {
                const widget = ui.draggable;
                const sourceRowId = widget.data('row-id');
                const sourceColumnId = widget.data('column-id');
                const targetRowId = $(this).closest('.dab-dashboard-row').data('row-id');
                const targetColumnId = $(this).data('column-id');

                console.log('Widget dropped in new column. Source:', sourceRowId, sourceColumnId, 'Target:', targetRowId, targetColumnId);

                // Only move if the target is different from the source
                if (sourceRowId !== targetRowId || sourceColumnId !== targetColumnId) {
                    moveWidgetToColumn(widget, sourceRowId, sourceColumnId, targetRowId, targetColumnId);
                } else {
                    // If dropped in the same column, reset position
                    widget.css({
                        top: 0,
                        left: 0,
                        position: 'relative'
                    });
                }
            }
        });

        // Bind event handlers directly to the new column's controls
        newColumnElement.find('.dab-column-settings').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New column settings button clicked');
            openColumnSettingsModal(rowId, newColumnId);
        });

        newColumnElement.find('.dab-column-delete').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('New column delete button clicked');
            const columnsInRow = rowElement.find('.dab-dashboard-column').length;
            if (columnsInRow <= 1) {
                alert('Cannot delete the last column in a row. Delete the row instead.');
                return;
            }
            if (confirm('Are you sure you want to delete this column and all its contents?')) {
                deleteColumn(rowId, newColumnId);
            }
        });

        // Redistribute column widths evenly
        redistributeColumnWidths(rowId);

        // Initialize draggable for new widgets
        initWidgetDragBetweenColumns();

        // Initialize column resizing
        initColumnResizing();

        // Add animation effect to highlight the new column
        newColumnElement.css('opacity', 0).animate({opacity: 1}, 300);

        // Save the layout with a slight delay to ensure DOM is updated
        setTimeout(function() {
            saveLayout();
            console.log('Layout saved after adding new column');

            // Force save to server to ensure persistence
            forceSaveLayout();
        }, 100);

        // Force rebind all event handlers
        rebindRowColumnEvents();

        console.log('New column added successfully');
        console.log('Updated layout:', JSON.stringify(dashboardLayout));
    }

    /**
     * Redistribute column widths evenly in a row
     */
    function redistributeColumnWidths(rowId) {
        const row = $(`.dab-dashboard-row[data-row-id="${rowId}"]`);
        const columns = row.find('.dab-dashboard-column');
        const columnCount = columns.length;

        if (columnCount === 0) return;

        // Find the row in the layout
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) return;

        // Calculate equal width - ensure total doesn't exceed 12
        const totalWidth = 12;
        const baseWidth = Math.floor(totalWidth / columnCount);
        const remainder = totalWidth % columnCount;

        console.log(`Redistributing ${columnCount} columns in row ${rowId}: base width ${baseWidth}, remainder ${remainder}`);

        // Update each column with calculated width
        columns.each(function(index) {
            const columnId = $(this).data('column-id');
            const columnIndex = dashboardLayout.rows[rowIndex].columns.findIndex(col => col.id == columnId);

            if (columnIndex !== -1) {
                // Distribute remainder to first few columns
                const columnWidth = baseWidth + (index < remainder ? 1 : 0);

                // Update layout data
                dashboardLayout.rows[rowIndex].columns[columnIndex].width = columnWidth;

                // Update DOM with consistent flex values
                $(this).css('flex', `${columnWidth} 1 0%`);
                $(this).attr('data-width', columnWidth);
                $(this).find('.dab-column-width').text(columnWidth);

                console.log(`Column ${columnId} set to width ${columnWidth}`);
            }
        });

        // Ensure layout consistency by forcing a re-render
        setTimeout(() => {
            columns.each(function() {
                const width = $(this).attr('data-width');
                $(this).css('flex', `${width} 1 0%`);
            });
        }, 50);

        // Save the updated layout to ensure column widths are persisted
        console.log('Column widths redistributed, saving layout');
        saveLayout();
    }

    /**
     * Initialize column resizing
     */
    function initColumnResizing() {
        $('.dab-column-resize-handle').off('mousedown').on('mousedown', function(e) {
            e.preventDefault();

            const column = $(this).closest('.dab-dashboard-column');
            const row = column.closest('.dab-dashboard-row');
            const rowId = row.data('row-id');
            const columnId = column.data('column-id');
            const startX = e.clientX;
            const startWidth = column.width();
            const rowWidth = row.width();

            // Find the row and column in the layout
            const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
            if (rowIndex === -1) return;

            const columnIndex = dashboardLayout.rows[rowIndex].columns.findIndex(col => col.id == columnId);
            if (columnIndex === -1) return;

            // Add resizing class
            column.addClass('resizing');

            // Add mousemove and mouseup handlers to document
            $(document).on('mousemove.columnResize', function(e) {
                const deltaX = e.clientX - startX;
                const newWidth = Math.max(100, startWidth + deltaX); // Minimum width of 100px
                const widthPercentage = newWidth / rowWidth;

                // Calculate new flex value (1-12 scale)
                const newFlexValue = Math.max(1, Math.min(12, Math.round(widthPercentage * 12)));

                // Update column width
                column.css('flex', `${newFlexValue} 1 0`);
                column.attr('data-width', newFlexValue);
                column.find('.dab-column-width').text(newFlexValue);

                // Update layout
                dashboardLayout.rows[rowIndex].columns[columnIndex].width = newFlexValue;
            });

            $(document).on('mouseup.columnResize', function() {
                // Remove resizing class
                column.removeClass('resizing');

                // Remove event handlers
                $(document).off('mousemove.columnResize mouseup.columnResize');

                // Save layout
                saveLayout();
            });
        });
    }

    /**
     * Delete a row
     */
    function deleteRow(rowId) {
        // Find the row
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) return;

        // Remove from layout
        dashboardLayout.rows.splice(rowIndex, 1);

        // Remove from DOM
        $(`.dab-dashboard-row[data-row-id="${rowId}"]`).remove();

        // Save the layout
        saveLayout();
    }

    /**
     * Delete a column
     */
    function deleteColumn(rowId, columnId) {
        // Find the row and column
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) return;

        const columnIndex = dashboardLayout.rows[rowIndex].columns.findIndex(col => col.id == columnId);
        if (columnIndex === -1) return;

        // Remove from layout
        dashboardLayout.rows[rowIndex].columns.splice(columnIndex, 1);

        // Remove from DOM
        $(`.dab-dashboard-row[data-row-id="${rowId}"] .dab-dashboard-column[data-column-id="${columnId}"]`).remove();

        // Save the layout
        saveLayout();
    }

    /**
     * Open row settings modal
     */
    function openRowSettingsModal(rowId) {
        // Find the row
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) return;

        currentRow = {
            id: rowId,
            index: rowIndex
        };

        const rowSettings = dashboardLayout.rows[rowIndex].settings || {};

        // Set form values
        $('#row-height').val(rowSettings.height || 100);
        $('#row-background').val(rowSettings.background || '');
        $('#row-padding').val(rowSettings.padding || 10);
        $('#row-margin-bottom').val(rowSettings.marginBottom || 20);
        $('#row-border').val(rowSettings.border || 'dashed');
        $('#row-border-color').val(rowSettings.borderColor || '#cccccc');
        $('#row-border-width').val(rowSettings.borderWidth || 1);
        $('#row-border-radius').val(rowSettings.borderRadius || 5);

        // Show modal
        $('#row-settings-modal').show();
    }

    /**
     * Open column settings modal
     */
    function openColumnSettingsModal(rowId, columnId) {
        // Find the row and column
        const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
        if (rowIndex === -1) return;

        const columnIndex = dashboardLayout.rows[rowIndex].columns.findIndex(col => col.id == columnId);
        if (columnIndex === -1) return;

        currentColumn = {
            rowId: rowId,
            id: columnId,
            rowIndex: rowIndex,
            columnIndex: columnIndex
        };

        const column = dashboardLayout.rows[rowIndex].columns[columnIndex];
        const columnSettings = column.settings || {};

        // Set form values
        $('#column-width').val(column.width || 1);
        $('#column-background').val(columnSettings.background || '');
        $('#column-padding').val(columnSettings.padding || 10);
        $('#column-border').val(columnSettings.border || 'dashed');
        $('#column-border-color').val(columnSettings.borderColor || '#dddddd');
        $('#column-border-width').val(columnSettings.borderWidth || 1);
        $('#column-border-radius').val(columnSettings.borderRadius || 4);

        // Show modal
        $('#column-settings-modal').show();
    }

    /**
     * Save the dashboard layout
     */
    function saveLayout() {
        // Make sure we have a valid dashboard ID
        if (!dashboardId || dashboardId <= 0) {
            console.error('Cannot save layout: Invalid dashboard ID');
            return;
        }

        console.log('Saving dashboard layout with ID:', dashboardId);

        // Also save to localStorage as a backup
        try {
            localStorage.setItem('dab_dashboard_layout_' + dashboardId, JSON.stringify(dashboardLayout));
            console.log('Layout saved to localStorage as backup');
        } catch (e) {
            console.error('Failed to save layout to localStorage:', e);
        }

        // Save layout to database
        $.ajax({
            url: dab_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_save_dashboard',
                id: dashboardId,
                layout: JSON.stringify(dashboardLayout),
                nonce: dab_dashboard.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Layout saved successfully to database');

                    // Update dashboard ID if it was returned
                    if (response.data && response.data.id) {
                        dashboardId = response.data.id;
                        console.log('Updated dashboard ID:', dashboardId);

                        // Update localStorage key if ID changed
                        try {
                            const oldLayout = localStorage.getItem('dab_dashboard_layout_' + dashboardId);
                            if (oldLayout) {
                                localStorage.setItem('dab_dashboard_layout_' + response.data.id, oldLayout);
                                localStorage.removeItem('dab_dashboard_layout_' + dashboardId);
                            }
                        } catch (e) {
                            console.error('Failed to update localStorage key:', e);
                        }
                    }
                } else {
                    console.error('Failed to save layout to database:', response.data);

                    // If the save failed, show a message to the user
                    if (response.data) {
                        alert('Failed to save dashboard layout: ' + response.data);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to save layout to database:', status, error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    /**
     * Force save the dashboard layout with full dashboard data
     * This is a more robust way to save the layout that ensures it persists
     */
    function forceSaveLayout() {
        // Make sure we have a valid dashboard ID
        if (!dashboardId || dashboardId <= 0) {
            console.error('Cannot force save layout: Invalid dashboard ID');
            return;
        }

        console.log('Force saving dashboard layout with ID:', dashboardId);

        // Get the dashboard title and description
        const title = $('#dashboard-title').val();
        if (!title) {
            console.error('Cannot force save layout: Missing dashboard title');
            return;
        }

        // Save the complete dashboard data
        $.ajax({
            url: dab_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_save_dashboard',
                id: dashboardId,
                title: title,
                description: $('#dashboard-description').val(),
                is_public: $('#dashboard-is-public').is(':checked') ? 1 : 0,
                layout: JSON.stringify(dashboardLayout),
                nonce: dab_dashboard.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Dashboard layout force saved successfully');

                    // Update dashboard ID if it was returned
                    if (response.data && response.data.id) {
                        dashboardId = response.data.id;
                        console.log('Updated dashboard ID:', dashboardId);
                    }
                } else {
                    console.error('Failed to force save dashboard layout:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to force save dashboard layout:', status, error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    /**
     * Initialize dashboard actions
     */
    function initDashboardActions() {
        // Save dashboard
        $('#save-dashboard').on('click', function() {
            const title = $('#dashboard-title').val();

            if (!title) {
                alert('Please enter a dashboard title.');
                return;
            }

            // Save dashboard
            $.ajax({
                url: dab_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_save_dashboard',
                    id: dashboardId,
                    title: title,
                    description: $('#dashboard-description').val(),
                    is_public: $('#dashboard-is-public').is(':checked') ? 1 : 0,
                    layout: JSON.stringify(dashboardLayout),
                    nonce: dab_dashboard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        dashboardId = response.data.id;

                        // Redirect to edit page if we're creating a new dashboard
                        if (!dab_dashboard.dashboard_id) {
                            window.location.href = `?page=dab_dashboard_builder&dashboard_id=${dashboardId}`;
                        } else {
                            alert(dab_dashboard.i18n.save_success);
                        }
                    } else {
                        alert(response.data || dab_dashboard.i18n.save_error);
                    }
                },
                error: function() {
                    alert(dab_dashboard.i18n.save_error);
                }
            });
        });

        // Dashboard permissions
        $('#dashboard-permissions').on('click', function() {
            $('#dashboard-permissions-modal').show();
        });

        // Save dashboard permissions
        $('#save-dashboard-permissions').on('click', function() {
            const permissions = {};

            // Collect permissions from form
            $('#dashboard-permissions-table tr').each(function() {
                const roleId = $(this).find('input[type="checkbox"]').first().attr('name');
                if (roleId) {
                    const role = roleId.match(/permissions\[(.*?)\]/)[1];
                    permissions[role] = {
                        can_view: $(this).find('input[name="permissions[' + role + '][can_view]"]').is(':checked'),
                        can_edit: $(this).find('input[name="permissions[' + role + '][can_edit]"]').is(':checked')
                    };
                }
            });

            // Save permissions
            $.ajax({
                url: dab_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_save_dashboard_permissions',
                    dashboard_id: dashboardId,
                    permissions: JSON.stringify(permissions),
                    nonce: dab_dashboard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('#dashboard-permissions-modal').hide();
                        alert('Dashboard permissions saved successfully.');
                    } else {
                        alert(response.data || 'Failed to save dashboard permissions.');
                    }
                },
                error: function() {
                    alert('Failed to save dashboard permissions.');
                }
            });
        });

        // Delete dashboard
        $('#delete-dashboard').on('click', function() {
            if (confirm(dab_dashboard.i18n.delete_confirm)) {
                // Delete dashboard
                $.ajax({
                    url: dab_dashboard.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'dab_delete_dashboard',
                        dashboard_id: dashboardId,
                        nonce: dab_dashboard.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            window.location.href = '?page=dab_dashboards';
                        } else {
                            alert(response.data || 'Failed to delete dashboard.');
                        }
                    },
                    error: function() {
                        alert('Failed to delete dashboard.');
                    }
                });
            }
        });
    }

    /**
     * Initialize modal
     */
    function initModal() {
        // Close all modals
        $('.dab-modal-close, .dab-modal-cancel').on('click', function() {
            $('.dab-modal').hide();
            currentWidget = null;
            currentRow = null;
            currentColumn = null;
        });

        // Save widget
        $('#save-widget').on('click', function() {
            const title = $('#widget-title').val();

            if (!title) {
                alert('Please enter a widget title.');
                return;
            }

            // Get widget settings based on type
            let settings = {};

            switch (currentWidget.type) {
                case 'table':
                    settings = {
                        table_id: $('#widget-table-id').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'chart':
                    settings = {
                        table_id: $('#widget-chart-table-id').val(),
                        chart_type: $('#widget-chart-type').val(),
                        x_axis: $('#widget-chart-x-axis').val(),
                        y_axis: $('#widget-chart-y-axis').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'metric':
                    settings = {
                        table_id: $('#widget-metric-table-id').val(),
                        metric_type: $('#widget-metric-type').val(),
                        field: $('#widget-metric-field').val(),
                        label: $('#widget-metric-label').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'text':
                    settings = {
                        content: $('#widget-text-content').val()
                    };
                    break;
                case 'kpi':
                    settings = {
                        table_id: $('#widget-kpi-table-id').val(),
                        metric_type: $('#widget-kpi-metric-type').val(),
                        field: $('#widget-kpi-field').val(),
                        label: $('#widget-kpi-label').val(),
                        target: $('#widget-kpi-target').val(),
                        icon: $('#widget-kpi-icon').val(),
                        color: $('#widget-kpi-color').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'list':
                    settings = {
                        table_id: $('#widget-list-table-id').val(),
                        title_field: $('#widget-list-title-field').val(),
                        description_field: $('#widget-list-description-field').val(),
                        icon_field: $('#widget-list-icon-field').val(),
                        max_items: $('#widget-list-max-items').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'progress':
                    settings = {
                        table_id: $('#widget-progress-table-id').val(),
                        value_field: $('#widget-progress-value-field').val(),
                        max_field: $('#widget-progress-max-field').val(),
                        label: $('#widget-progress-label').val(),
                        color: $('#widget-progress-color').val(),
                        filters: [] // Add filters if needed
                    };
                    break;
                case 'image':
                    settings = {
                        image_url: $('#widget-image-url').val(),
                        alt_text: $('#widget-image-alt').val(),
                        caption: $('#widget-image-caption').val()
                    };
                    break;
            }

            // Get the row and column IDs from the current widget
            const rowId = currentWidget.rowId || 0;
            const columnId = currentWidget.columnId || 0;

            // Save widget
            $.ajax({
                url: dab_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_save_widget',
                    id: currentWidget.id || 0,
                    dashboard_id: dashboardId,
                    widget_type: currentWidget.type,
                    title: title,
                    settings: JSON.stringify(settings),
                    position_x: currentWidget.posX,
                    position_y: currentWidget.posY,
                    width: currentWidget.width,
                    height: currentWidget.height,
                    nonce: dab_dashboard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Store widget info before nulling currentWidget
                        const widgetInfo = { ...currentWidget };

                        // Hide modal and clear current widget
                        $('#widget-settings-modal').hide();
                        currentWidget = null;

                        // If this is a new widget, add it to the layout and DOM
                        if (response.data && response.data.id) {
                            const widgetId = parseInt(response.data.id);
                            const rowId = widgetInfo.rowId;
                            const columnId = widgetInfo.columnId;

                            console.log('Widget saved with ID:', widgetId);
                            console.log('Target row:', rowId, 'Target column:', columnId);

                            // Find the row and column in the layout
                            const rowIndex = dashboardLayout.rows.findIndex(row => row.id == rowId);
                            if (rowIndex !== -1) {
                                const columnIndex = dashboardLayout.rows[rowIndex].columns.findIndex(col => col.id == columnId);
                                if (columnIndex !== -1) {
                                    // Check if widget already exists in the column
                                    const existingIndex = dashboardLayout.rows[rowIndex].columns[columnIndex].widgets.indexOf(widgetId);

                                    if (existingIndex === -1) {
                                        // Add widget to the column in layout
                                        dashboardLayout.rows[rowIndex].columns[columnIndex].widgets.push(widgetId);
                                        console.log('Added widget to layout at row', rowId, 'column', columnId);
                                    } else {
                                        console.log('Widget already exists in layout');
                                    }

                                    // Create and add widget to DOM immediately
                                    addWidgetToDOM(widgetId, widgetInfo, title, settings);

                                    // Save the layout
                                    saveLayout();
                                } else {
                                    console.error('Column not found in layout:', columnId);
                                }
                            } else {
                                console.error('Row not found in layout:', rowId);
                            }
                        } else if (widgetInfo.id) {
                            // This is an existing widget being updated
                            updateWidgetInDOM(widgetInfo.id, title, settings);
                        } else {
                            console.error('No widget ID returned from server');
                        }

                        // Clear pending drop data
                        window.pendingWidgetDrop = null;
                    } else {
                        alert(response.data || dab_dashboard.i18n.widget_save_error);
                    }
                },
                error: function() {
                    alert(dab_dashboard.i18n.widget_save_error);
                }
            });
        });

        // Save row settings
        $('#save-row-settings').on('click', function() {
            if (!currentRow) return;

            // Get settings from form
            const settings = {
                height: parseInt($('#row-height').val()) || 100,
                background: $('#row-background').val(),
                padding: parseInt($('#row-padding').val()) || 10,
                marginBottom: parseInt($('#row-margin-bottom').val()) || 20,
                border: $('#row-border').val(),
                borderColor: $('#row-border-color').val(),
                borderWidth: parseInt($('#row-border-width').val()) || 1,
                borderRadius: parseInt($('#row-border-radius').val()) || 5
            };

            // Update layout
            dashboardLayout.rows[currentRow.index].settings = settings;

            // Update DOM
            const row = $(`.dab-dashboard-row[data-row-id="${currentRow.id}"]`);
            row.css({
                'min-height': settings.height + 'px',
                'background-color': settings.background || 'rgba(240, 240, 240, 0.5)',
                'padding': settings.padding + 'px',
                'margin-bottom': settings.marginBottom + 'px',
                'border': `${settings.borderWidth}px ${settings.border} ${settings.borderColor}`,
                'border-radius': settings.borderRadius + 'px'
            });

            // Close modal
            $('#row-settings-modal').hide();
            currentRow = null;

            // Save layout
            saveLayout();
        });

        // Save column settings
        $('#save-column-settings').on('click', function() {
            if (!currentColumn) return;

            // Get settings from form
            const width = parseInt($('#column-width').val()) || 1;
            const settings = {
                background: $('#column-background').val(),
                padding: parseInt($('#column-padding').val()) || 10,
                border: $('#column-border').val(),
                borderColor: $('#column-border-color').val(),
                borderWidth: parseInt($('#column-border-width').val()) || 1,
                borderRadius: parseInt($('#column-border-radius').val()) || 4
            };

            // Update layout
            dashboardLayout.rows[currentColumn.rowIndex].columns[currentColumn.columnIndex].width = width;
            dashboardLayout.rows[currentColumn.rowIndex].columns[currentColumn.columnIndex].settings = settings;

            // Update DOM
            const column = $(`.dab-dashboard-row[data-row-id="${currentColumn.rowId}"] .dab-dashboard-column[data-column-id="${currentColumn.id}"]`);
            column.css({
                'flex': width,
                'background-color': settings.background || 'rgba(255, 255, 255, 0.7)',
                'padding': settings.padding + 'px',
                'border': `${settings.borderWidth}px ${settings.border} ${settings.borderColor}`,
                'border-radius': settings.borderRadius + 'px'
            });
            column.attr('data-width', width);
            column.find('.dab-column-width').text(width);

            // Close modal
            $('#column-settings-modal').hide();
            currentColumn = null;

            // Save layout
            saveLayout();
        });
    }

    /**
     * Close widget settings modal
     */
    function closeWidgetSettingsModal() {
        $('#widget-settings-modal').hide();
        currentWidget = null;
    }

    /**
     * Open widget settings modal
     *
     * @param {Object} widget Widget data
     */
    function openWidgetSettingsModal(widget) {
        console.log('Opening widget settings modal with data:', widget);

        // If this is a new widget, get the row and column from the drop position
        if (!widget.id) {
            // For new widgets, we need to determine which row and column to place it in
            if (widget.posX !== undefined && widget.posY !== undefined) {
                // Find the row and column based on the drop position
                const rowId = widget.rowId || 0;
                const columnId = widget.columnId || 0;

                // Add row and column info to the widget
                widget.rowId = rowId;
                widget.columnId = columnId;
            }
        }

        currentWidget = widget;

        // Set widget title
        $('#widget-settings-title').text(widget.id ? 'Edit Widget' : 'Add Widget');
        $('#widget-title').val(widget.title || '');

        // Clear settings fields
        $('#widget-settings-fields').empty();

        // Add settings fields based on widget type
        switch (widget.type) {
            case 'table':
                addTableWidgetFields(widget);
                break;
            case 'chart':
                addChartWidgetFields(widget);
                break;
            case 'metric':
                addMetricWidgetFields(widget);
                break;
            case 'text':
                addTextWidgetFields(widget);
                break;
            case 'kpi':
                addKpiWidgetFields(widget);
                break;
            case 'list':
                addListWidgetFields(widget);
                break;
            case 'progress':
                addProgressWidgetFields(widget);
                break;
            case 'image':
                addImageWidgetFields(widget);
                break;
            default:
                console.error('Unknown widget type:', widget.type);
        }

        // Show modal
        $('#widget-settings-modal').show();
    }

    /**
     * Close widget settings modal
     */
    function closeWidgetSettingsModal() {
        $('#widget-settings-modal').hide();
        currentWidget = null;
    }

    /**
     * Add table widget fields
     *
     * @param {Object} widget Widget data
     */
    function addTableWidgetFields(widget) {
        console.log('Adding table widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('Table ID from settings:', tableId);

        const fields = `
            <div class="dab-settings-section">
                <h3>Table Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-table-id">Table</label>
                    <select id="widget-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            console.log(`Comparing table ${table.id} (${typeof table.id}) with ${tableId} (${typeof tableId}): ${isSelected}`);
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <!-- Add filter options here if needed -->
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add chart widget fields
     *
     * @param {Object} widget Widget data
     */
    function addChartWidgetFields(widget) {
        console.log('Adding chart widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('Chart Table ID from settings:', tableId);

        const fields = `
            <div class="dab-settings-section">
                <h3>Chart Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-chart-table-id">Table</label>
                    <select id="widget-chart-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-chart-type">Chart Type</label>
                    <select id="widget-chart-type" class="regular-text" required>
                        <option value="bar" ${settings.chart_type === 'bar' ? 'selected' : ''}>Bar Chart</option>
                        <option value="line" ${settings.chart_type === 'line' ? 'selected' : ''}>Line Chart</option>
                        <option value="pie" ${settings.chart_type === 'pie' ? 'selected' : ''}>Pie Chart</option>
                    </select>
                </div>

                <div class="dab-field-row">
                    <div class="dab-field-col">
                        <div class="dab-form-group">
                            <label for="widget-chart-x-axis">X-Axis Field</label>
                            <input type="text" id="widget-chart-x-axis" class="regular-text" value="${settings.x_axis || ''}" required>
                            <p class="description">Enter the field slug for the X-axis</p>
                        </div>
                    </div>

                    <div class="dab-field-col">
                        <div class="dab-form-group">
                            <label for="widget-chart-y-axis">Y-Axis Field</label>
                            <input type="text" id="widget-chart-y-axis" class="regular-text" value="${settings.y_axis || ''}" required>
                            <p class="description">Enter the field slug for the Y-axis</p>
                        </div>
                    </div>
                </div>

                <!-- Add filter options here if needed -->
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add metric widget fields
     *
     * @param {Object} widget Widget data
     */
    function addMetricWidgetFields(widget) {
        console.log('Adding metric widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('Metric Table ID from settings:', tableId);

        // Determine if this is a count metric
        const isCountMetric = settings.metric_type === 'count';

        const fields = `
            <div class="dab-settings-section">
                <h3>Metric Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-metric-table-id">Table</label>
                    <select id="widget-metric-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-metric-type">Metric Type</label>
                    <select id="widget-metric-type" class="regular-text" required>
                        <option value="count" ${settings.metric_type === 'count' ? 'selected' : ''}>Count</option>
                        <option value="sum" ${settings.metric_type === 'sum' ? 'selected' : ''}>Sum</option>
                        <option value="average" ${settings.metric_type === 'average' ? 'selected' : ''}>Average</option>
                        <option value="min" ${settings.metric_type === 'min' ? 'selected' : ''}>Minimum</option>
                        <option value="max" ${settings.metric_type === 'max' ? 'selected' : ''}>Maximum</option>
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-metric-field">Field</label>
                    <input type="text" id="widget-metric-field" class="regular-text" value="${settings.field || ''}" ${isCountMetric ? '' : 'required'}>
                    <p class="description">Enter the field slug. Not required for Count metric type.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-metric-label">Label</label>
                    <input type="text" id="widget-metric-label" class="regular-text" value="${settings.label || ''}">
                    <p class="description">Optional label to display with the metric value.</p>
                </div>

                <!-- Add filter options here if needed -->
            </div>
        `;

        $('#widget-settings-fields').html(fields);

        // Add event listener to update field requirement based on metric type
        $('#widget-metric-type').on('change', function() {
            const metricType = $(this).val();
            const fieldInput = $('#widget-metric-field');

            if (metricType === 'count') {
                fieldInput.prop('required', false);
            } else {
                fieldInput.prop('required', true);
            }
        });
    }

    /**
     * Add text widget fields
     *
     * @param {Object} widget Widget data
     */
    function addTextWidgetFields(widget) {
        const settings = widget.settings || {};

        const fields = `
            <div class="dab-settings-section">
                <h3>Text Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-text-content">Content</label>
                    <textarea id="widget-text-content" class="large-text" rows="5">${settings.content || ''}</textarea>
                </div>
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add KPI widget fields
     *
     * @param {Object} widget Widget data
     */
    function addKpiWidgetFields(widget) {
        console.log('Adding KPI widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('KPI Table ID from settings:', tableId);

        const fields = `
            <div class="dab-settings-section">
                <h3>KPI Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-kpi-table-id">Table</label>
                    <select id="widget-kpi-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-metric-type">Metric Type</label>
                    <select id="widget-kpi-metric-type" class="regular-text" required>
                        <option value="count" ${settings.metric_type === 'count' ? 'selected' : ''}>Count</option>
                        <option value="sum" ${settings.metric_type === 'sum' ? 'selected' : ''}>Sum</option>
                        <option value="average" ${settings.metric_type === 'average' ? 'selected' : ''}>Average</option>
                        <option value="min" ${settings.metric_type === 'min' ? 'selected' : ''}>Minimum</option>
                        <option value="max" ${settings.metric_type === 'max' ? 'selected' : ''}>Maximum</option>
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-field">Field</label>
                    <input type="text" id="widget-kpi-field" class="regular-text" value="${settings.field || ''}">
                    <p class="description">Enter the field slug. Not required for Count metric type.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-label">Label</label>
                    <input type="text" id="widget-kpi-label" class="regular-text" value="${settings.label || ''}">
                    <p class="description">Label to display with the KPI value.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-target">Target Value</label>
                    <input type="number" id="widget-kpi-target" class="regular-text" value="${settings.target || ''}">
                    <p class="description">Target value for the KPI (optional).</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-icon">Icon</label>
                    <input type="text" id="widget-kpi-icon" class="regular-text" value="${settings.icon || ''}">
                    <p class="description">Dashicon name (e.g., 'chart-line', 'arrow-up'). Leave empty for no icon.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-kpi-color">Color</label>
                    <select id="widget-kpi-color" class="regular-text">
                        <option value="primary" ${settings.color === 'primary' ? 'selected' : ''}>Primary</option>
                        <option value="success" ${settings.color === 'success' ? 'selected' : ''}>Success</option>
                        <option value="warning" ${settings.color === 'warning' ? 'selected' : ''}>Warning</option>
                        <option value="danger" ${settings.color === 'danger' ? 'selected' : ''}>Danger</option>
                        <option value="info" ${settings.color === 'info' ? 'selected' : ''}>Info</option>
                    </select>
                </div>
            </div>
        `;

        $('#widget-settings-fields').html(fields);

        // Add event listener to update field requirement based on metric type
        $('#widget-kpi-metric-type').on('change', function() {
            const metricType = $(this).val();
            const fieldInput = $('#widget-kpi-field');

            if (metricType === 'count') {
                fieldInput.prop('required', false);
            } else {
                fieldInput.prop('required', true);
            }
        });
    }

    /**
     * Add list widget fields
     *
     * @param {Object} widget Widget data
     */
    function addListWidgetFields(widget) {
        console.log('Adding list widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('List Table ID from settings:', tableId);

        const fields = `
            <div class="dab-settings-section">
                <h3>List Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-list-table-id">Table</label>
                    <select id="widget-list-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-list-title-field">Title Field</label>
                    <input type="text" id="widget-list-title-field" class="regular-text" value="${settings.title_field || ''}" required>
                    <p class="description">Enter the field slug to use for list item titles.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-list-description-field">Description Field</label>
                    <input type="text" id="widget-list-description-field" class="regular-text" value="${settings.description_field || ''}">
                    <p class="description">Enter the field slug to use for list item descriptions (optional).</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-list-icon-field">Icon Field</label>
                    <input type="text" id="widget-list-icon-field" class="regular-text" value="${settings.icon_field || ''}">
                    <p class="description">Enter the field slug that contains icon names (optional).</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-list-max-items">Maximum Items</label>
                    <input type="number" id="widget-list-max-items" class="small-text" value="${settings.max_items || '5'}" min="1" max="20">
                    <p class="description">Maximum number of items to display in the list.</p>
                </div>
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add progress widget fields
     *
     * @param {Object} widget Widget data
     */
    function addProgressWidgetFields(widget) {
        console.log('Adding progress widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        // Ensure table_id is treated as a string for comparison
        const tableId = settings.table_id ? settings.table_id.toString() : '';
        console.log('Progress Table ID from settings:', tableId);

        const fields = `
            <div class="dab-settings-section">
                <h3>Progress Bar Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-progress-table-id">Table</label>
                    <select id="widget-progress-table-id" class="regular-text" required>
                        <option value="">Select a table</option>
                        ${dab_dashboard.tables.map(table => {
                            const isSelected = tableId === table.id.toString();
                            return `<option value="${table.id}" ${isSelected ? 'selected' : ''}>
                                ${table.table_label}
                            </option>`;
                        }).join('')}
                    </select>
                </div>

                <div class="dab-form-group">
                    <label for="widget-progress-value-field">Value Field</label>
                    <input type="text" id="widget-progress-value-field" class="regular-text" value="${settings.value_field || ''}" required>
                    <p class="description">Enter the field slug for the current value.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-progress-max-field">Maximum Value Field</label>
                    <input type="text" id="widget-progress-max-field" class="regular-text" value="${settings.max_field || ''}" required>
                    <p class="description">Enter the field slug for the maximum value.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-progress-label">Label</label>
                    <input type="text" id="widget-progress-label" class="regular-text" value="${settings.label || ''}">
                    <p class="description">Label to display with the progress bar.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-progress-color">Color</label>
                    <select id="widget-progress-color" class="regular-text">
                        <option value="primary" ${settings.color === 'primary' ? 'selected' : ''}>Primary</option>
                        <option value="success" ${settings.color === 'success' ? 'selected' : ''}>Success</option>
                        <option value="warning" ${settings.color === 'warning' ? 'selected' : ''}>Warning</option>
                        <option value="danger" ${settings.color === 'danger' ? 'selected' : ''}>Danger</option>
                        <option value="info" ${settings.color === 'info' ? 'selected' : ''}>Info</option>
                    </select>
                </div>
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add image widget fields
     *
     * @param {Object} widget Widget data
     */
    function addImageWidgetFields(widget) {
        console.log('Adding image widget fields with settings:', widget.settings);
        const settings = widget.settings || {};

        const fields = `
            <div class="dab-settings-section">
                <h3>Image Settings</h3>

                <div class="dab-form-group">
                    <label for="widget-image-url">Image URL</label>
                    <input type="text" id="widget-image-url" class="regular-text" value="${settings.image_url || ''}" required>
                    <p class="description">Enter the URL of the image.</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-image-alt">Alt Text</label>
                    <input type="text" id="widget-image-alt" class="regular-text" value="${settings.alt_text || ''}">
                    <p class="description">Alternative text for the image (for accessibility).</p>
                </div>

                <div class="dab-form-group">
                    <label for="widget-image-caption">Caption</label>
                    <input type="text" id="widget-image-caption" class="regular-text" value="${settings.caption || ''}">
                    <p class="description">Optional caption to display below the image.</p>
                </div>
            </div>
        `;

        $('#widget-settings-fields').html(fields);
    }

    /**
     * Add widget to DOM
     *
     * @param {number} widgetId Widget ID
     * @param {Object} widgetInfo Widget information
     * @param {string} title Widget title
     * @param {Object} settings Widget settings
     */
    function addWidgetToDOM(widgetId, widgetInfo, title, settings) {
        console.log('Adding widget to DOM:', widgetId, widgetInfo);

        // Find the target column
        let targetColumn;
        if (window.pendingWidgetDrop && window.pendingWidgetDrop.targetColumn) {
            targetColumn = window.pendingWidgetDrop.targetColumn;
        } else {
            targetColumn = $(`.dab-dashboard-row[data-row-id="${widgetInfo.rowId}"] .dab-dashboard-column[data-column-id="${widgetInfo.columnId}"]`);
        }

        if (targetColumn.length === 0) {
            console.error('Target column not found for widget placement');
            return;
        }

        // Remove empty placeholder if it exists
        targetColumn.find('.dab-empty-column-placeholder').remove();

        // Get widget icon based on type
        const getWidgetIcon = (type) => {
            const icons = {
                'table': 'list-view',
                'chart': 'chart-bar',
                'metric': 'dashboard',
                'text': 'text',
                'kpi': 'chart-line',
                'list': 'menu',
                'progress': 'performance',
                'image': 'format-image'
            };
            return icons[type] || 'admin-generic';
        };

        // Create widget HTML
        const widgetHtml = `
            <div class="dab-dashboard-widget"
                 data-widget-id="${widgetId}"
                 data-widget-type="${widgetInfo.type}"
                 data-row-id="${widgetInfo.rowId}"
                 data-column-id="${widgetInfo.columnId}"
                 data-width="${widgetInfo.width || 1}"
                 data-height="${widgetInfo.height || 1}">
                <div class="dab-widget-header">
                    <h3 class="dab-widget-title">${title}</h3>
                    <div class="dab-widget-actions">
                        <button type="button" class="dab-widget-edit" title="Edit Widget">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="dab-widget-delete" title="Delete Widget">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
                <div class="dab-widget-content">
                    <div class="dab-widget-placeholder">
                        <span class="dashicons dashicons-${getWidgetIcon(widgetInfo.type)}"></span>
                        <p>${title}</p>
                    </div>
                </div>
            </div>
        `;

        // Add widget to column
        targetColumn.append(widgetHtml);

        // Initialize drag and drop for the new widget
        const newWidget = targetColumn.find(`[data-widget-id="${widgetId}"]`);
        initWidgetDragBetweenColumns();

        // Make the new widget resizable
        newWidget.resizable({
            handles: 'se',
            minWidth: 100,
            minHeight: 100,
            stop: function(event, ui) {
                const width = Math.max(1, Math.round(ui.size.width / 100));
                const height = Math.max(1, Math.round(ui.size.height / 100));

                $(this).css({
                    width: width * 100 + 'px',
                    height: height * 100 + 'px'
                });

                $(this).data('width', width);
                $(this).data('height', height);

                saveWidgetSize($(this).data('widget-id'), width, height);
            }
        });

        console.log('Widget added to DOM successfully');
    }

    /**
     * Update widget in DOM
     *
     * @param {number} widgetId Widget ID
     * @param {string} title Widget title
     * @param {Object} settings Widget settings
     */
    function updateWidgetInDOM(widgetId, title, settings) {
        console.log('Updating widget in DOM:', widgetId);

        const widget = $(`.dab-dashboard-widget[data-widget-id="${widgetId}"]`);
        if (widget.length > 0) {
            // Update title
            widget.find('.dab-widget-title').text(title);
            widget.find('.dab-widget-placeholder p').text(title);

            console.log('Widget updated in DOM successfully');
        } else {
            console.error('Widget not found in DOM for update:', widgetId);
        }
    }

    /**
     * Save widget position
     *
     * @param {number} widgetId Widget ID
     * @param {number} posX X position
     * @param {number} posY Y position
     */
    function saveWidgetPosition(widgetId, posX, posY) {
        $.ajax({
            url: dab_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_save_widget',
                id: widgetId,
                dashboard_id: dashboardId,
                position_x: posX,
                position_y: posY,
                widget_type: 'unknown', // Required field
                title: 'Unknown', // Required field
                nonce: dab_dashboard.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Widget position saved.');
                } else {
                    console.error('Failed to save widget position:', response.data);
                }
            },
            error: function() {
                console.error('Failed to save widget position.');
            }
        });
    }

    /**
     * Save widget size
     *
     * @param {number} widgetId Widget ID
     * @param {number} width Width
     * @param {number} height Height
     */
    function saveWidgetSize(widgetId, width, height) {
        $.ajax({
            url: dab_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_save_widget',
                id: widgetId,
                dashboard_id: dashboardId,
                width: width,
                height: height,
                widget_type: 'unknown', // Required field
                title: 'Unknown', // Required field
                nonce: dab_dashboard.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Widget size saved.');
                } else {
                    console.error('Failed to save widget size:', response.data);
                }
            },
            error: function() {
                console.error('Failed to save widget size.');
            }
        });
    }

    /**
     * Ensure layout consistency
     */
    function ensureLayoutConsistency() {
        console.log('Ensuring layout consistency...');

        // Fix column flex values
        $('.dab-dashboard-column').each(function() {
            const column = $(this);
            const width = column.attr('data-width') || '1';

            // Ensure consistent flex value format
            column.css('flex', `${width} 1 0%`);

            // Update width display
            column.find('.dab-column-width').text(width);
        });

        // Ensure all columns have resize handles
        $('.dab-dashboard-column').each(function() {
            const column = $(this);
            if (column.find('.dab-column-resize-handle').length === 0) {
                column.append('<div class="dab-column-resize-handle"></div>');
            }
        });

        // Re-add empty placeholders where needed
        addEmptyColumnPlaceholders();

        console.log('Layout consistency check completed');
    }

    // Initialize dashboard builder when document is ready
    $(document).ready(function() {
        if ($('.dab-dashboard-builder-wrap').length) {
            initDashboardBuilder();
        }
    });
})(jQuery);
