/**
 * Enhanced Table Search and Export Styles
 */

/* Enhanced Search Container */
.dab-enhanced-search {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Search Form */
.dab-search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    flex: 1;
}

/* Search Input */
.dab-search-input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.dab-search-input:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Column Filter */
.dab-column-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    min-width: 150px;
}

.dab-column-filter:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Search Button */
.dab-search-button {
    padding: 8px 16px;
    background-color: #2271b1;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.dab-search-button:hover {
    background-color: #135e96;
}

/* Reset Button */
.dab-reset-button {
    padding: 8px 16px;
    background-color: #f0f0f1;
    color: #3c434a;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.dab-reset-button:hover {
    background-color: #e0e0e1;
}

/* Export Container */
.dab-export-container {
    position: relative;
    margin-left: 10px;
    z-index: 50;
}

/* Export Button */
.dab-export-button {
    padding: 8px 16px;
    background-color: #2c3338 !important;
    color: #fff !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: background-color 0.2s !important;
    text-align: center !important;
    line-height: 1.5 !important;
    display: inline-block !important;
}

.dab-export-button:hover {
    background-color: #1d2327 !important;
}

/* Export Options */
.dab-export-options {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 100;
    min-width: 150px;
    display: none;
}

/* Export Option */
.dab-export-option {
    display: block;
    padding: 8px 12px;
    color: #3c434a;
    text-decoration: none;
    transition: background-color 0.2s;
}

.dab-export-option:hover {
    background-color: #f0f0f1;
    color: #2271b1;
}

/* Responsive Adjustments */
@media (max-width: 782px) {
    .dab-search-form {
        flex-direction: column;
        align-items: stretch;
    }

    .dab-search-input,
    .dab-column-filter,
    .dab-search-button,
    .dab-reset-button {
        width: 100%;
        margin-bottom: 10px;
    }

    .dab-export-container {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .dab-export-button {
        width: 100%;
    }

    .dab-export-options {
        width: 100%;
    }
}

/* Table Styling Enhancements */
.dab-view-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dab-view-table thead th {
    background-color: #f8f9fa;
    color: #3c434a;
    font-weight: 600;
    text-align: left;
    padding: 12px;
    border: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.dab-view-table tbody td {
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    vertical-align: middle;
}

.dab-view-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.dab-view-table tbody tr:hover {
    background-color: #f0f7ff;
}

/* Empty Table Message */
.dab-view-table tbody tr td[colspan] {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

/* Pagination Styles */
.dab-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 5px;
}

.dab-pagination a,
.dab-pagination span {
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-decoration: none;
    color: #3c434a;
}

.dab-pagination a:hover {
    background-color: #f0f0f1;
}

.dab-pagination .current {
    background-color: #2271b1;
    color: #fff;
    border-color: #2271b1;
}
