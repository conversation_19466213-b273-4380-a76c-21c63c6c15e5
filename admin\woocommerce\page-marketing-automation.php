<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Get marketing statistics
global $wpdb;

$campaigns_table = $wpdb->prefix . 'dab_wc_marketing_campaigns';
$email_queue_table = $wpdb->prefix . 'dab_wc_email_queue';
$stats_table = $wpdb->prefix . 'dab_wc_campaign_stats';

// Campaign statistics
$total_campaigns = $wpdb->get_var("SELECT COUNT(*) FROM $campaigns_table WHERE is_active = 1");
$emails_sent_today = $wpdb->get_var("SELECT COUNT(*) FROM $email_queue_table WHERE DATE(sent_at) = CURDATE() AND status = 'sent'");
$pending_emails = $wpdb->get_var("SELECT COUNT(*) FROM $email_queue_table WHERE status = 'pending'");

// Email performance (last 30 days)
$email_performance = $wpdb->get_row("
    SELECT 
        COUNT(*) as total_sent,
        SUM(open_count) as total_opens,
        SUM(click_count) as total_clicks
    FROM $email_queue_table 
    WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
    AND status = 'sent'
");

$open_rate = $email_performance->total_sent > 0 ? 
    ($email_performance->total_opens / $email_performance->total_sent) * 100 : 0;
$click_rate = $email_performance->total_sent > 0 ? 
    ($email_performance->total_clicks / $email_performance->total_sent) * 100 : 0;

// Get active campaigns
$active_campaigns = $wpdb->get_results("
    SELECT c.*, t.template_name 
    FROM $campaigns_table c 
    LEFT JOIN {$wpdb->prefix}dab_wc_email_templates t ON c.email_template_id = t.id 
    WHERE c.is_active = 1 
    ORDER BY c.priority DESC, c.campaign_name ASC
");

// Get recent email activity
$recent_emails = $wpdb->get_results("
    SELECT eq.*, c.campaign_name 
    FROM $email_queue_table eq 
    LEFT JOIN $campaigns_table c ON eq.campaign_id = c.id 
    ORDER BY eq.created_at DESC 
    LIMIT 15
");

// Get email templates
$templates_table = $wpdb->prefix . 'dab_wc_email_templates';
$email_templates = $wpdb->get_results("SELECT * FROM $templates_table WHERE is_active = 1 ORDER BY template_name ASC");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Marketing Automation', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Automate your marketing with abandoned cart recovery, customer retention campaigns, and personalized email sequences.', 'db-app-builder'); ?></p>
    </div>

    <!-- Marketing Statistics -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($total_campaigns); ?></div>
            <div class="dab-stat-label"><?php _e('Active Campaigns', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($emails_sent_today); ?></div>
            <div class="dab-stat-label"><?php _e('Emails Sent Today', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($open_rate, 1); ?>%</div>
            <div class="dab-stat-label"><?php _e('Open Rate (30 days)', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($click_rate, 1); ?>%</div>
            <div class="dab-stat-label"><?php _e('Click Rate (30 days)', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Active Campaigns -->
            <div class="dab-card">
                <h2><?php _e('Active Marketing Campaigns', 'db-app-builder'); ?></h2>
                
                <?php if (empty($active_campaigns)): ?>
                    <p><?php _e('No active campaigns found.', 'db-app-builder'); ?></p>
                    <p><em><?php _e('Marketing campaigns help you automatically engage with customers at key moments in their journey.', 'db-app-builder'); ?></em></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Campaign Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Type', 'db-app-builder'); ?></th>
                                <th><?php _e('Trigger', 'db-app-builder'); ?></th>
                                <th><?php _e('Delay', 'db-app-builder'); ?></th>
                                <th><?php _e('Template', 'db-app-builder'); ?></th>
                                <th><?php _e('Priority', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($active_campaigns as $campaign): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($campaign->campaign_name); ?></strong>
                                    </td>
                                    <td>
                                        <span class="dab-campaign-type dab-type-<?php echo esc_attr($campaign->campaign_type); ?>">
                                            <?php echo esc_html(ucfirst(str_replace('_', ' ', $campaign->campaign_type))); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html(ucfirst(str_replace('_', ' ', $campaign->trigger_event))); ?></td>
                                    <td>
                                        <?php if ($campaign->delay_hours > 0): ?>
                                            <?php echo $campaign->delay_hours; ?> <?php _e('hours', 'db-app-builder'); ?>
                                        <?php else: ?>
                                            <?php _e('Immediate', 'db-app-builder'); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html($campaign->template_name ?: __('No template', 'db-app-builder')); ?></td>
                                    <td><?php echo esc_html($campaign->priority); ?></td>
                                    <td>
                                        <button class="button button-small" onclick="editCampaign(<?php echo $campaign->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                        <button class="button button-small" onclick="viewCampaignStats(<?php echo $campaign->id; ?>)"><?php _e('Stats', 'db-app-builder'); ?></button>
                                        <button class="button button-small button-link-delete" onclick="toggleCampaign(<?php echo $campaign->id; ?>)"><?php _e('Pause', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                
                <p>
                    <button class="button button-primary" onclick="showCreateCampaignForm()"><?php _e('Create New Campaign', 'db-app-builder'); ?></button>
                </p>
            </div>

            <!-- Email Templates -->
            <div class="dab-card">
                <h2><?php _e('Email Templates', 'db-app-builder'); ?></h2>
                
                <?php if (empty($email_templates)): ?>
                    <p><?php _e('No email templates found.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Template Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Type', 'db-app-builder'); ?></th>
                                <th><?php _e('Subject Line', 'db-app-builder'); ?></th>
                                <th><?php _e('Last Updated', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($email_templates as $template): ?>
                                <tr>
                                    <td><strong><?php echo esc_html($template->template_name); ?></strong></td>
                                    <td><?php echo esc_html(ucfirst(str_replace('_', ' ', $template->template_type))); ?></td>
                                    <td><?php echo esc_html($template->subject_line); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format'), strtotime($template->updated_at)); ?></td>
                                    <td>
                                        <button class="button button-small" onclick="editTemplate(<?php echo $template->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                        <button class="button button-small" onclick="previewTemplate(<?php echo $template->id; ?>)"><?php _e('Preview', 'db-app-builder'); ?></button>
                                        <button class="button button-small" onclick="sendTestEmail(<?php echo $template->id; ?>)"><?php _e('Test', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                
                <p>
                    <button class="button button-primary" onclick="showCreateTemplateForm()"><?php _e('Create New Template', 'db-app-builder'); ?></button>
                </p>
            </div>

            <!-- Recent Email Activity -->
            <div class="dab-card">
                <h2><?php _e('Recent Email Activity', 'db-app-builder'); ?></h2>
                
                <?php if (empty($recent_emails)): ?>
                    <p><?php _e('No recent email activity.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Recipient', 'db-app-builder'); ?></th>
                                <th><?php _e('Campaign', 'db-app-builder'); ?></th>
                                <th><?php _e('Subject', 'db-app-builder'); ?></th>
                                <th><?php _e('Status', 'db-app-builder'); ?></th>
                                <th><?php _e('Sent At', 'db-app-builder'); ?></th>
                                <th><?php _e('Opens', 'db-app-builder'); ?></th>
                                <th><?php _e('Clicks', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_emails as $email): ?>
                                <tr>
                                    <td>
                                        <?php echo esc_html($email->recipient_name ?: $email->recipient_email); ?>
                                        <?php if ($email->recipient_name): ?>
                                            <br><small><?php echo esc_html($email->recipient_email); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html($email->campaign_name ?: __('Manual', 'db-app-builder')); ?></td>
                                    <td><?php echo esc_html($email->subject_line); ?></td>
                                    <td>
                                        <span class="dab-email-status dab-status-<?php echo esc_attr($email->status); ?>">
                                            <?php echo esc_html(ucfirst($email->status)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($email->sent_at): ?>
                                            <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($email->sent_at)); ?>
                                        <?php else: ?>
                                            <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($email->scheduled_at)); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo number_format($email->open_count); ?></td>
                                    <td><?php echo number_format($email->click_count); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Email Queue Status -->
            <div class="dab-card">
                <h3><?php _e('Email Queue Status', 'db-app-builder'); ?></h3>
                <div class="dab-queue-status">
                    <div class="dab-queue-stat">
                        <div class="dab-queue-number"><?php echo number_format($pending_emails); ?></div>
                        <div class="dab-queue-label"><?php _e('Pending Emails', 'db-app-builder'); ?></div>
                    </div>
                    
                    <?php if ($pending_emails > 0): ?>
                        <p>
                            <button class="button button-primary" onclick="processPendingEmails()"><?php _e('Process Queue Now', 'db-app-builder'); ?></button>
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Campaign Performance -->
            <div class="dab-card">
                <h3><?php _e('Campaign Performance (30 days)', 'db-app-builder'); ?></h3>
                <div class="dab-performance-metrics">
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Emails Sent', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format($email_performance->total_sent); ?></div>
                    </div>
                    
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Total Opens', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format($email_performance->total_opens); ?></div>
                    </div>
                    
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Total Clicks', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format($email_performance->total_clicks); ?></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="#" onclick="exportCampaignReport()"><?php _e('Export Campaign Report', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="viewAbandonedCarts()"><?php _e('View Abandoned Carts', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="manageSubscribers()"><?php _e('Manage Subscribers', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="setupAutomationRules()"><?php _e('Setup Automation Rules', 'db-app-builder'); ?></a></li>
                </ul>
            </div>

            <!-- Marketing Tips -->
            <div class="dab-card">
                <h3><?php _e('Marketing Tips', 'db-app-builder'); ?></h3>
                <ul class="dab-marketing-tips">
                    <li><?php _e('Send abandoned cart emails within 1-3 hours for best results', 'db-app-builder'); ?></li>
                    <li><?php _e('Personalize subject lines to improve open rates', 'db-app-builder'); ?></li>
                    <li><?php _e('Use customer segmentation for targeted campaigns', 'db-app-builder'); ?></li>
                    <li><?php _e('Test different send times to optimize engagement', 'db-app-builder'); ?></li>
                    <li><?php _e('Include clear call-to-action buttons in emails', 'db-app-builder'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function editCampaign(campaignId) {
    // TODO: Implement campaign editing modal
    alert('<?php _e('Campaign editing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function viewCampaignStats(campaignId) {
    // TODO: Implement campaign statistics modal
    alert('<?php _e('Campaign statistics will be implemented in the next update.', 'db-app-builder'); ?>');
}

function toggleCampaign(campaignId) {
    if (confirm('<?php _e('Pause this campaign?', 'db-app-builder'); ?>')) {
        // TODO: Implement campaign toggle
        alert('<?php _e('Campaign toggle will be implemented in the next update.', 'db-app-builder'); ?>');
    }
}

function showCreateCampaignForm() {
    // TODO: Implement create campaign modal
    alert('<?php _e('Create campaign form will be implemented in the next update.', 'db-app-builder'); ?>');
}

function editTemplate(templateId) {
    // TODO: Implement template editing modal
    alert('<?php _e('Template editing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function previewTemplate(templateId) {
    // TODO: Implement template preview modal
    alert('<?php _e('Template preview will be implemented in the next update.', 'db-app-builder'); ?>');
}

function sendTestEmail(templateId) {
    var email = prompt('<?php _e('Enter email address for test:', 'db-app-builder'); ?>');
    if (email) {
        // TODO: Implement test email sending
        alert('<?php _e('Test email functionality will be implemented in the next update.', 'db-app-builder'); ?>');
    }
}

function showCreateTemplateForm() {
    // TODO: Implement create template modal
    alert('<?php _e('Create template form will be implemented in the next update.', 'db-app-builder'); ?>');
}

function processPendingEmails() {
    if (confirm('<?php _e('Process all pending emails now?', 'db-app-builder'); ?>')) {
        // TODO: Implement email queue processing
        alert('<?php _e('Email queue processing will be implemented in the next update.', 'db-app-builder'); ?>');
    }
}

function exportCampaignReport() {
    // TODO: Implement campaign report export
    alert('<?php _e('Campaign report export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function viewAbandonedCarts() {
    // TODO: Navigate to abandoned carts view
    window.location.href = '<?php echo admin_url('admin.php?page=dab_woocommerce_advanced_checkout'); ?>';
}

function manageSubscribers() {
    // TODO: Implement subscriber management
    alert('<?php _e('Subscriber management will be implemented in the next update.', 'db-app-builder'); ?>');
}

function setupAutomationRules() {
    // TODO: Implement automation rules setup
    alert('<?php _e('Automation rules setup will be implemented in the next update.', 'db-app-builder'); ?>');
}
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-campaign-type {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.dab-campaign-type.dab-type-welcome {
    background: #d4edda;
    color: #155724;
}

.dab-campaign-type.dab-type-abandoned-cart {
    background: #fff3cd;
    color: #856404;
}

.dab-campaign-type.dab-type-post-purchase {
    background: #d1ecf1;
    color: #0c5460;
}

.dab-campaign-type.dab-type-retention {
    background: #f8d7da;
    color: #721c24;
}

.dab-email-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.dab-email-status.dab-status-sent {
    background: #d4edda;
    color: #155724;
}

.dab-email-status.dab-status-pending {
    background: #fff3cd;
    color: #856404;
}

.dab-email-status.dab-status-failed {
    background: #f8d7da;
    color: #721c24;
}

.dab-queue-status {
    text-align: center;
}

.dab-queue-number {
    font-size: 24px;
    font-weight: bold;
    color: #2271b1;
    margin-bottom: 5px;
}

.dab-queue-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.dab-performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-metric {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.dab-metric-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.dab-metric-value {
    font-size: 18px;
    font-weight: bold;
    color: #2271b1;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-marketing-tips {
    list-style: disc;
    padding-left: 20px;
    margin: 15px 0;
}

.dab-marketing-tips li {
    margin-bottom: 8px;
    line-height: 1.4;
}
</style>
