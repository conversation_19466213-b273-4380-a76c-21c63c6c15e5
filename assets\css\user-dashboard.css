/**
 * User Dashboard Styles
 * 
 * Styles for the user dashboard interface
 */

/* Base Dashboard Styles */
.dab-user-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Dashboard Header */
.dab-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.dab-dashboard-welcome h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.dab-dashboard-subtitle {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.dab-dashboard-actions {
    display: flex;
    gap: 12px;
}

/* But<PERSON> Styles */
.dab-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: none;
    white-space: nowrap;
}

.dab-btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.dab-btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dab-btn-secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.dab-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.dab-btn-outline {
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.dab-btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
}

.dab-btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.dab-btn-danger {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.dab-btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Statistics Cards */
.dab-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.dab-stat-card {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dab-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.dab-stat-number {
    font-size: 48px;
    font-weight: 800;
    color: #3b82f6;
    margin-bottom: 8px;
    line-height: 1;
}

.dab-stat-label {
    color: #6b7280;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dashboard Sections */
.dab-dashboard-section {
    margin-bottom: 40px;
}

.dab-section-title {
    margin: 0 0 24px 0;
    color: #1f2937;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

/* Tables Grid */
.dab-tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

.dab-table-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dab-table-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.dab-table-card-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-table-title {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 700;
}

.dab-table-card-body {
    padding: 24px;
}

.dab-table-description {
    margin: 0 0 16px 0;
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
}

.dab-table-stats {
    color: #3b82f6;
    font-weight: 600;
    font-size: 14px;
}

/* Recent Activity */
.dab-activity-list {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.dab-activity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
}

.dab-activity-item:last-child {
    border-bottom: none;
}

.dab-activity-item:hover {
    background-color: #f9fafb;
}

.dab-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.dab-activity-content {
    flex: 1;
}

.dab-activity-text {
    color: #1f2937;
    font-size: 14px;
    margin-bottom: 4px;
}

.dab-activity-time {
    color: #6b7280;
    font-size: 12px;
}

/* Loading and Error States */
.dab-stats-loading,
.dab-activity-loading,
.dab-data-loading {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.dab-spinner, .dab-spinner-sm {
    display: inline-block;
    border: 2px solid #f3f4f6;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: dab-spin 1s ease-in-out infinite;
    margin-right: 8px;
}

.dab-spinner {
    width: 20px;
    height: 20px;
}

.dab-spinner-sm {
    width: 16px;
    height: 16px;
}

@keyframes dab-spin {
    to { transform: rotate(360deg); }
}

.dab-error {
    color: #ef4444;
    font-weight: 500;
}

.dab-no-data {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 40px;
}

/* Modal Styles */
.dab-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
}

.dab-modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: dab-modal-appear 0.2s ease-out;
}

.dab-modal-large {
    max-width: 1000px;
}

@keyframes dab-modal-appear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dab-modal-header {
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dab-modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 700;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-close:hover {
    color: #374151;
    background-color: #e5e7eb;
}

.dab-modal-body {
    padding: 32px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

/* Data Controls */
.dab-data-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
}

.dab-data-search {
    flex: 1;
    max-width: 300px;
}

.dab-form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.dab-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Data Table */
.dab-data-table-wrapper {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.dab-data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.dab-data-table th,
.dab-data-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #f3f4f6;
}

.dab-data-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-data-table td {
    color: #1f2937;
    font-size: 14px;
}

.dab-data-table tr:hover {
    background-color: #f9fafb;
}

.dab-actions {
    display: flex;
    gap: 8px;
}

/* Pagination */
.dab-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

.dab-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    background: white;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dab-pagination-btn:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.dab-pagination-current {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.dab-pagination-current:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

.dab-pagination-dots {
    color: #9ca3af;
    padding: 8px 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-user-dashboard {
        padding: 16px;
    }
    
    .dab-dashboard-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        padding: 24px;
    }
    
    .dab-dashboard-actions {
        justify-content: center;
    }
    
    .dab-dashboard-welcome h1 {
        font-size: 24px;
        text-align: center;
    }
    
    .dab-dashboard-subtitle {
        text-align: center;
    }
    
    .dab-tables-grid {
        grid-template-columns: 1fr;
    }
    
    .dab-modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .dab-modal-body {
        padding: 20px;
    }
    
    .dab-data-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .dab-data-search {
        max-width: none;
    }
    
    .dab-stat-card {
        padding: 24px;
    }
    
    .dab-stat-number {
        font-size: 36px;
    }
}

@media (max-width: 480px) {
    .dab-dashboard-header {
        padding: 20px;
    }
    
    .dab-table-card-header,
    .dab-table-card-body {
        padding: 16px;
    }
    
    .dab-activity-item {
        padding: 16px;
    }
    
    .dab-modal-header,
    .dab-modal-body {
        padding: 20px;
    }
    
    .dab-actions {
        flex-direction: column;
    }
}
