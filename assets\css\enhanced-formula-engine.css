/**
 * Enhanced Formula Engine Styles
 */

/* Formula Field Container */
.dab-formula-field-container {
    margin-bottom: 20px;
}

.dab-formula-expression {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    min-height: 60px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.dab-formula-expression:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

.dab-formula-expression.dab-formula-error {
    border-color: #dc3545;
    box-shadow: 0 0 0 1px #dc3545;
}

/* Formula Builder */
.dab-formula-builder {
    margin-top: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.dab-formula-builder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.dab-formula-builder-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.dab-formula-help-toggle {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 3px;
    color: #6c757d;
    transition: all 0.2s ease;
}

.dab-formula-help-toggle:hover {
    background: #dee2e6;
    color: #495057;
}

.dab-formula-builder-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 15px;
}

/* Function Categories */
.dab-formula-functions h5,
.dab-formula-fields h5 {
    margin: 0 0 10px 0;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-function-categories {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-function-category h6 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-function-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.dab-formula-function {
    background: #007cba;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dab-formula-function:hover {
    background: #005a87;
    transform: translateY(-1px);
}

/* Field References */
.dab-field-references {
    max-height: 200px;
    overflow-y: auto;
}

.dab-field-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dab-formula-field-ref {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 3px;
    font-size: 12px;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dab-formula-field-ref:hover {
    background: #1e7e34;
    transform: translateX(2px);
}

/* Formula Preview */
.dab-formula-preview {
    padding: 15px;
    border-top: 1px solid #dee2e6;
    background: #fff;
}

.dab-formula-preview h5 {
    margin: 0 0 10px 0;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
}

.dab-formula-result {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.dab-formula-valid {
    color: #155724;
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.dab-formula-error {
    color: #721c24;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

.dab-formula-loading {
    color: #856404;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
}

.dab-formula-empty {
    color: #6c757d;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    font-style: italic;
}

/* Formula Help */
.dab-formula-help {
    padding: 15px;
    border-top: 1px solid #dee2e6;
    background: #fff;
}

.dab-formula-help h5 {
    margin: 0 0 15px 0;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
}

.dab-help-section {
    margin-bottom: 20px;
}

.dab-help-section:last-child {
    margin-bottom: 0;
}

.dab-help-section h6 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

.dab-help-section ul {
    margin: 0;
    padding-left: 20px;
}

.dab-help-section li {
    margin-bottom: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.dab-help-section code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #495057;
}

/* Formula Field Display */
.dab-formula-field {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
    font-weight: 500;
}

.dab-formula-field:focus {
    background: #fff;
}

.dab-formula-field.dab-formula-error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-formula-builder-content {
        grid-template-columns: 1fr;
    }
    
    .dab-function-list {
        justify-content: center;
    }
    
    .dab-formula-function,
    .dab-formula-field-ref {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Animation for smooth transitions */
.dab-formula-builder {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar styling for field references */
.dab-field-references::-webkit-scrollbar {
    width: 6px;
}

.dab-field-references::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dab-field-references::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dab-field-references::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Syntax highlighting for formula expressions */
.dab-formula-expression {
    background: #fafafa;
}

.dab-formula-expression:focus {
    background: #fff;
}

/* Function tooltip styling */
.dab-formula-function[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.dab-formula-function[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #333;
    z-index: 1000;
}

/* Field reference tooltip styling */
.dab-formula-field-ref[title]:hover::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 0;
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-top: 5px;
}

.dab-formula-field-ref[title]:hover::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 10px;
    border: 4px solid transparent;
    border-bottom-color: #333;
    z-index: 1000;
}
