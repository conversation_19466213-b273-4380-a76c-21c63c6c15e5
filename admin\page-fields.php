<?php
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';

// Initialize error handling variables
$has_error = false;
$error_message = '';

// Handle Field Deletion
if (isset($_GET['delete_field'])) {
    $field_id = intval($_GET['delete_field']);
    $wpdb->delete($fields_table, ['id' => $field_id]);
    echo '<div class="notice notice-success is-dismissible"><p>Field deleted successfully.</p></div>';
}

// Handle Field Creation
if (isset($_POST['dab_save_field'])) {
    $table_id = intval($_POST['table_id']);
    $field_label = sanitize_text_field($_POST['field_label']);
    $field_slug = sanitize_title($_POST['field_slug']);
    $field_type = sanitize_text_field($_POST['field_type']);
    $required = isset($_POST['required']) ? 1 : 0;
    $placeholder = sanitize_text_field($_POST['placeholder']);
    $options = sanitize_textarea_field($_POST['options'] ?? '');
    $formula_expression = sanitize_text_field($_POST['formula_expression'] ?? '');
    $rollup_related_table = intval($_POST['rollup_related_table'] ?? 0);
    $rollup_foreign_key = sanitize_text_field($_POST['rollup_foreign_key'] ?? '');
    $rollup_aggregation_type = sanitize_text_field($_POST['rollup_aggregation_type'] ?? '');
    $rollup_target_field = sanitize_text_field($_POST['rollup_target_field'] ?? '');


    // Add validation for lookup fields
    if ($field_type === 'lookup') {
        $lookup_table_id = isset($_POST['lookup_table_id']) ? intval($_POST['lookup_table_id']) : 0;
        $lookup_display_column = isset($_POST['lookup_display_column']) ? sanitize_text_field($_POST['lookup_display_column']) : '';

        // Validate that both lookup table and display column are selected
        if (empty($lookup_table_id) || empty($lookup_display_column)) {
            echo '<div class="notice notice-error is-dismissible"><p>Lookup fields require both a table and display column.</p></div>';
            $has_error = true;
        }
    }

    // Add validation for inline table fields
    if ($field_type === 'inline_table') {
        $inline_table_id = isset($_POST['inline_table_id']) ? intval($_POST['inline_table_id']) : 0;
        $inline_table_foreign_key = isset($_POST['inline_table_foreign_key']) ? sanitize_text_field($_POST['inline_table_foreign_key']) : '';
        $inline_table_display_fields = isset($_POST['inline_table_display_fields']) ? $_POST['inline_table_display_fields'] : [];

        // Validate that table, foreign key, and at least one display field are selected
        if (empty($inline_table_id) || empty($inline_table_foreign_key) || empty($inline_table_display_fields)) {
            echo '<div class="notice notice-error is-dismissible"><p>Inline table fields require a child table, foreign key field, and at least one display field.</p></div>';
            $has_error = true;
        }
    }

    if (!$has_error) {
        // Get table columns to check what exists
        $table_columns = $wpdb->get_col("DESCRIBE {$fields_table}");

        // Prepare data for insertion with only existing columns
        $field_data = [
            'table_id' => $table_id,
            'field_label' => $field_label,
            'field_slug' => $field_slug,
            'field_type' => $field_type,
            'required' => $required
        ];

        // Only add fields that exist in the database table
        if (in_array('placeholder', $table_columns)) {
            $field_data['placeholder'] = $placeholder;
        }

        if (in_array('options', $table_columns)) {
            // For autoincrement fields, store the configuration options
            if ($field_type === 'autoincrement') {
                $autoincrement_prefix = isset($_POST['autoincrement_prefix']) ? sanitize_text_field($_POST['autoincrement_prefix']) : '';
                $autoincrement_start_number = isset($_POST['autoincrement_start_number']) ? intval($_POST['autoincrement_start_number']) : 1;
                $autoincrement_padding = isset($_POST['autoincrement_padding']) ? intval($_POST['autoincrement_padding']) : 3;

                // Store autoincrement options in the options field as JSON
                $autoincrement_options = array(
                    'prefix' => $autoincrement_prefix,
                    'start_number' => $autoincrement_start_number,
                    'padding' => $autoincrement_padding,
                    'current_number' => $autoincrement_start_number - 1 // Initialize with one less than start number
                );

                $field_data['options'] = json_encode($autoincrement_options);
            } else {
                $field_data['options'] = $options;
            }
        }

        if (in_array('formula_expression', $table_columns)) {
            $field_data['formula_expression'] = $formula_expression;
        }

        if (in_array('rollup_related_table', $table_columns)) {
            $field_data['rollup_related_table'] = $rollup_related_table;
        }

        if (in_array('rollup_foreign_key', $table_columns)) {
            $field_data['rollup_foreign_key'] = $rollup_foreign_key;
        }

        if (in_array('rollup_aggregation_type', $table_columns)) {
            $field_data['rollup_aggregation_type'] = $rollup_aggregation_type;
        }

        if (in_array('rollup_target_field', $table_columns)) {
            $field_data['rollup_target_field'] = $rollup_target_field;
        }

        if (in_array('created_at', $table_columns)) {
            $field_data['created_at'] = current_time('mysql');
        }

        // Add lookup field data if applicable and columns exist
        if ($field_type === 'lookup') {
            if (in_array('lookup_table_id', $table_columns)) {
                $field_data['lookup_table_id'] = $lookup_table_id;
            }

            if (in_array('lookup_display_column', $table_columns)) {
                $field_data['lookup_display_column'] = $lookup_display_column;
            }
        }

        // Add WordPress user field data if applicable
        if ($field_type === 'wp_user') {
            // Get the WordPress user field options
            $wp_user_role_filter = isset($_POST['wp_user_role_filter']) ? 1 : 0;
            $wp_user_role_field = isset($_POST['wp_user_role_field']) ? sanitize_text_field($_POST['wp_user_role_field']) : '';

            if (in_array('wp_user_role_filter', $table_columns)) {
                $field_data['wp_user_role_filter'] = $wp_user_role_filter;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `wp_user_role_filter` TINYINT(1) DEFAULT 0");
                $field_data['wp_user_role_filter'] = $wp_user_role_filter;
            }

            if (in_array('wp_user_role_field', $table_columns)) {
                $field_data['wp_user_role_field'] = $wp_user_role_field;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `wp_user_role_field` VARCHAR(255) NULL");
                $field_data['wp_user_role_field'] = $wp_user_role_field;
            }
        }

        // Add inline table field data if applicable and columns exist
        if ($field_type === 'inline_table') {
            // Get the inline table options
            $inline_table_allow_add = isset($_POST['inline_table_allow_add']) ? 1 : 0;
            $inline_table_allow_edit = isset($_POST['inline_table_allow_edit']) ? 1 : 0;
            $inline_table_allow_delete = isset($_POST['inline_table_allow_delete']) ? 1 : 0;

            if (in_array('inline_table_id', $table_columns)) {
                $field_data['inline_table_id'] = $inline_table_id;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_id` INT NULL");
                $field_data['inline_table_id'] = $inline_table_id;
            }

            if (in_array('inline_table_foreign_key', $table_columns)) {
                $field_data['inline_table_foreign_key'] = $inline_table_foreign_key;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_foreign_key` VARCHAR(255) NULL");
                $field_data['inline_table_foreign_key'] = $inline_table_foreign_key;
            }

            if (in_array('inline_table_display_fields', $table_columns)) {
                $field_data['inline_table_display_fields'] = is_array($inline_table_display_fields) ?
                    json_encode($inline_table_display_fields) : '';
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_display_fields` TEXT NULL");
                $field_data['inline_table_display_fields'] = is_array($inline_table_display_fields) ?
                    json_encode($inline_table_display_fields) : '';
            }

            // Add the inline table options
            if (in_array('inline_table_allow_add', $table_columns)) {
                $field_data['inline_table_allow_add'] = $inline_table_allow_add;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_allow_add` TINYINT(1) DEFAULT 1");
                $field_data['inline_table_allow_add'] = $inline_table_allow_add;
            }

            if (in_array('inline_table_allow_edit', $table_columns)) {
                $field_data['inline_table_allow_edit'] = $inline_table_allow_edit;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_allow_edit` TINYINT(1) DEFAULT 1");
                $field_data['inline_table_allow_edit'] = $inline_table_allow_edit;
            }

            if (in_array('inline_table_allow_delete', $table_columns)) {
                $field_data['inline_table_allow_delete'] = $inline_table_allow_delete;
            } else {
                // Add the column if it doesn't exist
                $wpdb->query("ALTER TABLE `$fields_table` ADD `inline_table_allow_delete` TINYINT(1) DEFAULT 1");
                $field_data['inline_table_allow_delete'] = $inline_table_allow_delete;
            }
        }

        // Add payment field data if applicable
        if ($field_type === 'payment') {
            // Get payment field options
            $payment_gateway = isset($_POST['payment_gateway']) ? sanitize_text_field($_POST['payment_gateway']) : 'stripe';
            $payment_amount_type = isset($_POST['payment_amount_type']) ? sanitize_text_field($_POST['payment_amount_type']) : 'fixed';
            $payment_amount = isset($_POST['payment_amount']) ? floatval($_POST['payment_amount']) : 0;
            $payment_amount_field = isset($_POST['payment_amount_field']) ? sanitize_text_field($_POST['payment_amount_field']) : '';
            $payment_currency = isset($_POST['payment_currency']) ? sanitize_text_field($_POST['payment_currency']) : 'USD';
            $payment_description = isset($_POST['payment_description']) ? sanitize_text_field($_POST['payment_description']) : '';

            // Store payment options in the options field as JSON
            $payment_options = array(
                'payment_gateway' => $payment_gateway,
                'payment_amount_type' => $payment_amount_type,
                'payment_amount' => $payment_amount,
                'payment_amount_field' => $payment_amount_field,
                'payment_currency' => $payment_currency,
                'payment_description' => $payment_description
            );

            // Update the options field with the payment options
            $field_data['options'] = json_encode($payment_options);
        }

        // Add enhanced currency field data if applicable
        if ($field_type === 'enhanced_currency') {
            // Get enhanced currency field options
            $currency = isset($_POST['enhanced_currency_default']) ? sanitize_text_field($_POST['enhanced_currency_default']) : 'USD';
            $symbol_position = isset($_POST['enhanced_currency_symbol_position']) ? sanitize_text_field($_POST['enhanced_currency_symbol_position']) : 'before';
            $decimal_places = isset($_POST['enhanced_currency_decimal_places']) ? intval($_POST['enhanced_currency_decimal_places']) : 2;
            $thousand_separator = isset($_POST['enhanced_currency_thousand_separator']) ? sanitize_text_field($_POST['enhanced_currency_thousand_separator']) : 'comma';
            $decimal_separator = isset($_POST['enhanced_currency_decimal_separator']) ? sanitize_text_field($_POST['enhanced_currency_decimal_separator']) : 'dot';
            $allow_currency_selection = isset($_POST['enhanced_currency_allow_selection']) ? 1 : 0;

            // Store enhanced currency options in the options field as JSON
            $enhanced_currency_options = array(
                'currency' => $currency,
                'symbol_position' => $symbol_position,
                'decimal_places' => $decimal_places,
                'thousand_separator' => $thousand_separator,
                'decimal_separator' => $decimal_separator,
                'allow_currency_selection' => $allow_currency_selection
            );

            // Update the options field with the enhanced currency options
            $field_data['options'] = json_encode($enhanced_currency_options);
        }

        // Add media field data if applicable
        if ($field_type === 'media') {
            // Get media field options
            $media_type = isset($_POST['media_type']) ? sanitize_text_field($_POST['media_type']) : 'both';
            $allow_recording = isset($_POST['media_allow_recording']) ? 1 : 0;
            $allow_upload = isset($_POST['media_allow_upload']) ? 1 : 0;
            $allow_embed = isset($_POST['media_allow_embed']) ? 1 : 0;
            $max_file_size = isset($_POST['media_max_file_size']) ? intval($_POST['media_max_file_size']) : 10;
            $player_width = isset($_POST['media_player_width']) ? intval($_POST['media_player_width']) : 400;
            $player_height = isset($_POST['media_player_height']) ? intval($_POST['media_player_height']) : 300;

            // Get allowed formats (multiselect)
            $allowed_formats = isset($_POST['media_allowed_formats']) && is_array($_POST['media_allowed_formats'])
                ? array_map('sanitize_text_field', $_POST['media_allowed_formats'])
                : array('mp3', 'mp4');

            // Store media options in the options field as JSON
            $media_options = array(
                'media_type' => $media_type,
                'allow_recording' => $allow_recording,
                'allow_upload' => $allow_upload,
                'allow_embed' => $allow_embed,
                'max_file_size' => $max_file_size,
                'allowed_formats' => $allowed_formats,
                'player_width' => $player_width,
                'player_height' => $player_height
            );

            // Update the options field with the media options
            $field_data['options'] = json_encode($media_options);
        }


        $wpdb->insert($fields_table, $field_data);

        echo '<div class="notice notice-success is-dismissible"><p>Field created successfully!</p></div>';

        // Auto-create column in data table
        $table_slug = $wpdb->get_var($wpdb->prepare("SELECT table_slug FROM $tables_table WHERE id = %d", $table_id));
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        $column_type = 'TEXT';
        switch ($field_type) {
            case 'number': $column_type = 'BIGINT(20)'; break;
            case 'email':
            case 'text':
            case 'radio':
            case 'select':
            case 'checkbox':
            case 'textarea': $column_type = 'TEXT'; break;
            case 'date': $column_type = 'DATE'; break;
            case 'currency':
            case 'formula':
            case 'rollup': $column_type = 'DECIMAL(20,2)'; break;
            case 'boolean': $column_type = 'TINYINT(1)'; break;
            case 'json': $column_type = 'LONGTEXT'; break;
            case 'rating': $column_type = 'TINYINT(1)'; break;
            case 'lookup': $column_type = 'BIGINT(20)'; break;
            case 'wp_role': $column_type = 'VARCHAR(100)'; break; // Store role key
            case 'wp_user': $column_type = 'BIGINT(20)'; break; // Store user ID
            case 'inline_table': $column_type = 'LONGTEXT'; break; // Store JSON data for inline table
            case 'payment': $column_type = 'TEXT'; break; // Store payment transaction data
            case 'autoincrement': $column_type = 'VARCHAR(255)'; break; // Store auto-generated IDs
            case 'signature': $column_type = 'LONGTEXT'; break; // Store signature data as JSON
            case 'media': $column_type = 'LONGTEXT'; break; // Store media data as JSON
            case 'conditional_logic': $column_type = 'LONGTEXT'; break; // Store conditional logic data as JSON
            case 'social_media': $column_type = 'LONGTEXT'; break; // Store social media links as JSON
            case 'multiselect': $column_type = 'LONGTEXT'; break; // Store multiple selections as JSON
            case 'enhanced_currency': $column_type = 'LONGTEXT'; break; // Store enhanced currency data as JSON
        }

        $exists = $wpdb->get_results("SHOW COLUMNS FROM `$data_table` LIKE '$field_slug'");
        if (empty($exists)) {
            $wpdb->query("ALTER TABLE `$data_table` ADD `$field_slug` $column_type NULL");
        }


    }
}

$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY id DESC");
$selected_table_id = isset($_GET['table_id']) ? intval($_GET['table_id']) : 0;
$fields = $selected_table_id ? $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $selected_table_id)) : [];
?>

<!-- The rest of your existing UI HTML code remains unchanged -->
<!-- To keep the output focused, that portion is assumed already intact in your implementation -->
<div class="wrap">
    <h1>Manage Fields</h1>

    <form method="get">
        <input type="hidden" name="page" value="dab_fields">
        <select name="table_id" onchange="this.form.submit();" style="min-width: 300px;">
            <option value="">-- Select Table --</option>
            <?php foreach ($tables as $tbl): ?>
                <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($selected_table_id, $tbl->id); ?>>
                    <?php echo esc_html($tbl->table_label); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </form>

    <?php if (!$selected_table_id): ?>
        <div class="notice notice-info">
            <p>Please select a table to manage its fields.</p>
        </div>
    <?php else: ?>
        <hr>
        <h2>Add New Field</h2>
        <form method="post">
            <input type="hidden" name="dab_save_field" value="1">
            <input type="hidden" name="table_id" value="<?php echo esc_attr($selected_table_id); ?>">
            <table class="form-table">
                <tr>
                    <th><label for="field_label">Field Label</label></th>
                    <td><input type="text" name="field_label" id="field_label" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="field_slug">Field Slug</label></th>
                    <td><input type="text" name="field_slug" id="field_slug" class="regular-text" required></td>
                </tr>
                <tr>
                    <th><label for="field_type">Field Type</label></th>
                    <td>
                        <select name="field_type" id="field_type" required>
                            <option value="">-- Select Type --</option>
                            <?php
                            // Get all registered field types
                            $field_types_obj = new DAB_Field_Types();
                            $field_types = $field_types_obj->get_field_types();

                            // Sort field types alphabetically by label
                            asort($field_types);

                            // Output options
                            foreach ($field_types as $type => $label) {
                                echo '<option value="' . esc_attr($type) . '">' . esc_html($label) . '</option>';
                            }
                            ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th><label for="required">Required</label></th>
                    <td><input type="checkbox" name="required" id="required" value="1"></td>
                </tr>
                <tr>
                    <th><label for="placeholder">Placeholder</label></th>
                    <td><input type="text" name="placeholder" id="placeholder" class="regular-text"></td>
                </tr>

                <!-- Options for select, radio, checkbox -->
                <tr id="options_row" style="display:none;">
                    <th><label for="options">Options</label></th>
                    <td>
                        <textarea name="options" id="options" rows="5" class="large-text"></textarea>
                        <p class="description">Enter one option per line. For key-value pairs, use format: key:value</p>
                        <!-- The dropdown builder will be inserted here via JavaScript -->
                        <div class="dab-options-help" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-left: 4px solid #2271b1;">
                            <p><strong>Example format:</strong></p>
                            <pre style="margin: 0; padding: 5px; background: #fff; border: 1px solid #ddd;">Option 1
Option 2
Option 3
Red:red
Green:green
Blue:blue</pre>
                            <p style="margin-top: 5px; font-size: 12px;">The format above will create 6 options. The first three will have the same value and label. The last three will have different values and labels.</p>
                        </div>
                    </td>
                </tr>

                <!-- Lookup field settings -->
                <tr id="lookup_settings_row" style="display:none;">
                    <th><label for="lookup_table_id">Reference Table</label></th>
                    <td>
                        <select name="lookup_table_id" id="lookup_table_id" class="regular-text">
                            <option value="">-- Select Table --</option>
                            <?php foreach ($tables as $table): ?>
                                <option value="<?php echo esc_attr($table->id); ?>"><?php echo esc_html($table->table_label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr id="lookup_display_column_row" style="display:none;">
                    <th><label for="lookup_display_column">Display Column</label></th>
                    <td>
                        <select name="lookup_display_column" id="lookup_display_column" class="regular-text">
                            <option value="">-- Select Reference Table First --</option>
                        </select>
                        <p class="description">Field from reference table to display in the dropdown</p>
                    </td>
                </tr>



                <!-- Inline Table field settings -->
                <tr id="inline_table_settings_row" style="display:none;">
                    <th><label for="inline_table_id">Child Table</label></th>
                    <td>
                        <select name="inline_table_id" id="inline_table_id" class="regular-text">
                            <option value="">-- Select Table --</option>
                            <?php foreach ($tables as $table): ?>
                                <option value="<?php echo esc_attr($table->id); ?>"><?php echo esc_html($table->table_label); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">Select the table that will be displayed inline</p>
                    </td>
                </tr>
                <tr id="inline_table_foreign_key_row" style="display:none;">
                    <th><label for="inline_table_foreign_key">Foreign Key Field</label></th>
                    <td>
                        <select name="inline_table_foreign_key" id="inline_table_foreign_key" class="regular-text">
                            <option value="">-- Select Child Table First --</option>
                        </select>
                        <p class="description">Field in the child table that references this table's record ID</p>
                    </td>
                </tr>
                <tr id="inline_table_display_fields_row" style="display:none;">
                    <th><label for="inline_table_display_fields">Display Fields</label></th>
                    <td>
                        <select name="inline_table_display_fields[]" id="inline_table_display_fields" class="regular-text" multiple style="height: 120px;">
                            <option value="">-- Select Child Table First --</option>
                        </select>
                        <p class="description">Fields from the child table to display in the inline table (hold Ctrl/Cmd to select multiple)</p>
                    </td>
                </tr>
                <tr id="inline_table_options_row" style="display:none;">
                    <th><label>Inline Table Options</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Inline Table Options</span></legend>
                            <label for="inline_table_allow_add">
                                <input name="inline_table_allow_add" type="checkbox" id="inline_table_allow_add" value="1" checked="checked">
                                Allow Add
                            </label><br>
                            <label for="inline_table_allow_edit">
                                <input name="inline_table_allow_edit" type="checkbox" id="inline_table_allow_edit" value="1" checked="checked">
                                Allow Edit
                            </label><br>
                            <label for="inline_table_allow_delete">
                                <input name="inline_table_allow_delete" type="checkbox" id="inline_table_allow_delete" value="1" checked="checked">
                                Allow Delete
                            </label>
                            <p class="description">Control what operations are allowed in the inline table</p>
                        </fieldset>
                    </td>
                </tr>

                <!-- Autoincrement field settings -->
                <tr id="autoincrement_settings_row" style="display:none;">
                    <th><label for="autoincrement_prefix">Prefix</label></th>
                    <td>
                        <input type="text" name="autoincrement_prefix" id="autoincrement_prefix" class="regular-text" placeholder="e.g., STU">
                        <p class="description">Text to prepend to the number (e.g., "STU" for "STU001")</p>
                    </td>
                </tr>
                <tr id="autoincrement_start_number_row" style="display:none;">
                    <th><label for="autoincrement_start_number">Starting Number</label></th>
                    <td>
                        <input type="number" name="autoincrement_start_number" id="autoincrement_start_number" class="regular-text" value="1" min="1">
                        <p class="description">The number to start from (default: 1)</p>
                    </td>
                </tr>
                <tr id="autoincrement_padding_row" style="display:none;">
                    <th><label for="autoincrement_padding">Zero Padding</label></th>
                    <td>
                        <input type="number" name="autoincrement_padding" id="autoincrement_padding" class="regular-text" value="3" min="1" max="10">
                        <p class="description">Number of digits to pad with zeros (e.g., 3 for "001")</p>
                    </td>
                </tr>

                <!-- Payment field settings -->
                <tr id="payment_settings_row" style="display:none;">
                    <th><label for="payment_gateway">Payment Gateway</label></th>
                    <td>
                        <select name="payment_gateway" id="payment_gateway" class="regular-text">
                            <option value="stripe">Stripe</option>
                            <option value="paypal">PayPal</option>
                            <option value="paystack">Paystack</option>
                        </select>
                        <p class="description">Select which payment gateway to use for this field.</p>
                    </td>
                </tr>
                <tr id="payment_amount_type_row" style="display:none;">
                    <th><label for="payment_amount_type">Amount Type</label></th>
                    <td>
                        <select name="payment_amount_type" id="payment_amount_type" class="regular-text">
                            <option value="fixed">Fixed Amount</option>
                            <option value="field">From Field</option>
                            <option value="user_input">User Input</option>
                        </select>
                        <p class="description">Determine how the payment amount is set.</p>
                    </td>
                </tr>
                <tr id="payment_amount_row" style="display:none;">
                    <th><label for="payment_amount">Fixed Amount</label></th>
                    <td>
                        <input type="number" name="payment_amount" id="payment_amount" class="regular-text" step="0.01" min="0" placeholder="0.00">
                        <p class="description">Set a fixed amount for payment (if Fixed Amount is selected).</p>
                    </td>
                </tr>
                <tr id="payment_amount_field_row" style="display:none;">
                    <th><label for="payment_amount_field">Amount Field</label></th>
                    <td>
                        <select name="payment_amount_field" id="payment_amount_field" class="regular-text">
                            <option value="">-- Select Field --</option>
                            <?php
                            // Get numeric fields from this table
                            if (!empty($fields)) {
                                foreach ($fields as $field) {
                                    if (in_array($field->field_type, array('number', 'currency', 'formula'))) {
                                        echo '<option value="' . esc_attr($field->field_slug) . '">' . esc_html($field->field_label) . ' (' . esc_html($field->field_type) . ')</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <p class="description">Select which field contains the payment amount (if From Field is selected). The payment amount will automatically update when this field changes.</p>
                    </td>
                </tr>
                <tr id="payment_currency_row" style="display:none;">
                    <th><label for="payment_currency">Currency</label></th>
                    <td>
                        <select name="payment_currency" id="payment_currency" class="regular-text">
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="GBP">GBP - British Pound</option>
                            <option value="CAD">CAD - Canadian Dollar</option>
                            <option value="AUD">AUD - Australian Dollar</option>
                            <option value="NGN">NGN - Nigerian Naira</option>
                        </select>
                        <p class="description">Select the currency for this payment.</p>
                    </td>
                </tr>
                <tr id="payment_description_row" style="display:none;">
                    <th><label for="payment_description">Payment Description</label></th>
                    <td>
                        <input type="text" name="payment_description" id="payment_description" class="regular-text">
                        <p class="description">Description that will appear on the payment form and receipt.</p>
                    </td>
                </tr>

                <!-- Media field settings -->
                <tr id="media_type_row" style="display:none;">
                    <th><label for="media_type">Media Type</label></th>
                    <td>
                        <select name="media_type" id="media_type" class="regular-text">
                            <option value="both">Both Audio & Video</option>
                            <option value="audio">Audio Only</option>
                            <option value="video">Video Only</option>
                        </select>
                        <p class="description">Type of media to allow.</p>
                    </td>
                </tr>
                <tr id="media_allow_recording_row" style="display:none;">
                    <th><label for="media_allow_recording">Allow Recording</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Allow Recording</span></legend>
                            <label for="media_allow_recording">
                                <input name="media_allow_recording" type="checkbox" id="media_allow_recording" value="1">
                                Allow users to record audio/video directly
                            </label>
                            <p class="description">If checked, users will be able to record audio/video directly in the browser.</p>
                        </fieldset>
                    </td>
                </tr>
                <tr id="media_allow_upload_row" style="display:none;">
                    <th><label for="media_allow_upload">Allow Upload</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Allow Upload</span></legend>
                            <label for="media_allow_upload">
                                <input name="media_allow_upload" type="checkbox" id="media_allow_upload" value="1" checked="checked">
                                Allow users to upload media files
                            </label>
                            <p class="description">If checked, users will be able to upload media files.</p>
                        </fieldset>
                    </td>
                </tr>
                <tr id="media_allow_embed_row" style="display:none;">
                    <th><label for="media_allow_embed">Allow Embed</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Allow Embed</span></legend>
                            <label for="media_allow_embed">
                                <input name="media_allow_embed" type="checkbox" id="media_allow_embed" value="1">
                                Allow users to embed media from external sources
                            </label>
                            <p class="description">If checked, users will be able to embed media from YouTube, Vimeo, etc.</p>
                        </fieldset>
                    </td>
                </tr>
                <tr id="media_max_file_size_row" style="display:none;">
                    <th><label for="media_max_file_size">Max File Size (MB)</label></th>
                    <td>
                        <input type="number" name="media_max_file_size" id="media_max_file_size" class="regular-text" value="10" min="1" max="100">
                        <p class="description">Maximum file size in megabytes.</p>
                    </td>
                </tr>
                <tr id="media_allowed_formats_row" style="display:none;">
                    <th><label for="media_allowed_formats">Allowed Formats</label></th>
                    <td>
                        <select name="media_allowed_formats[]" id="media_allowed_formats" class="regular-text" multiple style="height: 120px;">
                            <option value="mp3" selected>MP3</option>
                            <option value="wav">WAV</option>
                            <option value="ogg">OGG</option>
                            <option value="mp4" selected>MP4</option>
                            <option value="webm">WebM</option>
                            <option value="mov">MOV</option>
                        </select>
                        <p class="description">Allowed file formats (hold Ctrl/Cmd to select multiple).</p>
                    </td>
                </tr>
                <tr id="media_player_width_row" style="display:none;">
                    <th><label for="media_player_width">Player Width</label></th>
                    <td>
                        <input type="number" name="media_player_width" id="media_player_width" class="regular-text" value="400" min="200" max="1200">
                        <p class="description">Width of the media player in pixels.</p>
                    </td>
                </tr>
                <tr id="media_player_height_row" style="display:none;">
                    <th><label for="media_player_height">Player Height</label></th>
                    <td>
                        <input type="number" name="media_player_height" id="media_player_height" class="regular-text" value="300" min="100" max="800">
                        <p class="description">Height of the media player in pixels (for video).</p>
                    </td>
                </tr>

                <!-- Enhanced Currency field settings -->
                <tr id="enhanced_currency_default_row" style="display:none;">
                    <th><label for="enhanced_currency_default">Default Currency</label></th>
                    <td>
                        <select name="enhanced_currency_default" id="enhanced_currency_default" class="regular-text">
                            <option value="USD">US Dollar ($)</option>
                            <option value="EUR">Euro (€)</option>
                            <option value="GBP">British Pound (£)</option>
                            <option value="JPY">Japanese Yen (¥)</option>
                            <option value="CNY">Chinese Yuan (¥)</option>
                            <option value="INR">Indian Rupee (₹)</option>
                            <option value="CAD">Canadian Dollar (C$)</option>
                            <option value="AUD">Australian Dollar (A$)</option>
                            <option value="NGN">Nigerian Naira (₦)</option>
                            <option value="ZAR">South African Rand (R)</option>
                            <option value="BRL">Brazilian Real (R$)</option>
                            <option value="MXN">Mexican Peso (Mex$)</option>
                        </select>
                        <p class="description">Select the default currency for this field.</p>
                    </td>
                </tr>
                <tr id="enhanced_currency_symbol_position_row" style="display:none;">
                    <th><label for="enhanced_currency_symbol_position">Symbol Position</label></th>
                    <td>
                        <select name="enhanced_currency_symbol_position" id="enhanced_currency_symbol_position" class="regular-text">
                            <option value="before">Before amount ($100)</option>
                            <option value="after">After amount (100$)</option>
                        </select>
                        <p class="description">Position of the currency symbol.</p>
                    </td>
                </tr>
                <tr id="enhanced_currency_decimal_places_row" style="display:none;">
                    <th><label for="enhanced_currency_decimal_places">Decimal Places</label></th>
                    <td>
                        <input type="number" name="enhanced_currency_decimal_places" id="enhanced_currency_decimal_places" class="regular-text" value="2" min="0" max="6">
                        <p class="description">Number of decimal places to display.</p>
                    </td>
                </tr>
                <tr id="enhanced_currency_thousand_separator_row" style="display:none;">
                    <th><label for="enhanced_currency_thousand_separator">Thousand Separator</label></th>
                    <td>
                        <select name="enhanced_currency_thousand_separator" id="enhanced_currency_thousand_separator" class="regular-text">
                            <option value="comma">Comma (,)</option>
                            <option value="dot">Dot (.)</option>
                            <option value="space">Space</option>
                            <option value="none">None</option>
                        </select>
                        <p class="description">Character to use as thousand separator.</p>
                    </td>
                </tr>
                <tr id="enhanced_currency_decimal_separator_row" style="display:none;">
                    <th><label for="enhanced_currency_decimal_separator">Decimal Separator</label></th>
                    <td>
                        <select name="enhanced_currency_decimal_separator" id="enhanced_currency_decimal_separator" class="regular-text">
                            <option value="dot">Dot (.)</option>
                            <option value="comma">Comma (,)</option>
                        </select>
                        <p class="description">Character to use as decimal separator.</p>
                    </td>
                </tr>
                <tr id="enhanced_currency_allow_selection_row" style="display:none;">
                    <th><label for="enhanced_currency_allow_selection">Allow Currency Selection</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Allow Currency Selection</span></legend>
                            <label for="enhanced_currency_allow_selection">
                                <input name="enhanced_currency_allow_selection" type="checkbox" id="enhanced_currency_allow_selection" value="1">
                                Allow users to select a different currency when entering values
                            </label>
                            <p class="description">If checked, users will be able to select a different currency when entering values.</p>
                        </fieldset>
                    </td>
                </tr>

                <!-- WordPress User field settings -->
                <tr id="wp_user_settings_row" style="display:none;">
                    <th><label for="wp_user_role_filter">Filter by Role</label></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><span>Filter by Role</span></legend>
                            <label for="wp_user_role_filter">
                                <input name="wp_user_role_filter" type="checkbox" id="wp_user_role_filter" value="1">
                                Filter users by role
                            </label>
                            <p class="description">If checked, users will be filtered by the selected role in another field</p>
                        </fieldset>
                    </td>
                </tr>
                <tr id="wp_user_role_field_row" style="display:none;">
                    <th><label for="wp_user_role_field">Role Field</label></th>
                    <td>
                        <select name="wp_user_role_field" id="wp_user_role_field" class="regular-text">
                            <option value="">-- Select Role Field --</option>
                            <?php
                            // Get WordPress role fields from this table
                            if (!empty($fields)) {
                                foreach ($fields as $field) {
                                    if ($field->field_type === 'wp_role') {
                                        echo '<option value="' . esc_attr($field->field_slug) . '">' . esc_html($field->field_label) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <p class="description">Select a WordPress Role field to filter users by</p>
                    </td>
                </tr>

                <!-- Formula field settings -->
                <tr id="formula_builder_row" style="display:none;">
                    <th><label for="formula_expression">Formula</label></th>
                    <td>
                        <div class="formula-field-container">
                            <input type="text" name="formula_expression" id="formula_expression" class="large-text" placeholder="Build your formula using the visual builder below">
                            <p class="description">Use the visual builder below to create your formula by clicking on fields, operators, and functions.</p>

                            <div class="dab-formula-help">
                                <h4>How to Build a Formula:</h4>
                                <ol>
                                    <li>Click on fields from the "Available Fields" section</li>
                                    <li>Add operators (+, -, *, /) between fields</li>
                                    <li>Use functions like SUM, AVG, etc. for more complex calculations</li>
                                    <li>Use parentheses to control the order of operations</li>
                                </ol>
                                <p><strong>Example:</strong> To calculate total price, click on "Price" field, then "*" operator, then "Quantity" field.</p>
                            </div>

                            <!-- Visual Formula Builder -->
                            <div class="dab-visual-formula-builder">
                                <div class="dab-formula-preview">
                                    <strong>Formula Preview:</strong> <span class="preview-text">No formula yet</span>
                                </div>

                                <!-- Field Selector -->
                                <div class="dab-field-selector-container">
                                    <h4>Available Fields</h4>
                                    <div class="dab-field-selector">
                                        <?php
                                        // Get all fields from the current table
                                        $numeric_fields = array();
                                        if (!empty($fields)) {
                                            foreach ($fields as $field) {
                                                // Only add numeric fields or fields that can be used in calculations
                                                if (in_array($field->field_type, array('number', 'currency', 'formula', 'rollup', 'checkbox', 'rating'))) {
                                                    $numeric_fields[] = $field;
                                                }
                                            }
                                        }

                                        if (!empty($numeric_fields)) {
                                            foreach ($numeric_fields as $field) {
                                                $icon = '';
                                                switch ($field->field_type) {
                                                    case 'number':
                                                    case 'currency':
                                                        $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg> ';
                                                        break;
                                                    case 'formula':
                                                        $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline></svg> ';
                                                        break;
                                                    case 'checkbox':
                                                        $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path></svg> ';
                                                        break;
                                                }

                                                echo '<button type="button" class="button dab-field-button" data-field="' . esc_attr($field->field_slug) . '" data-type="' . esc_attr($field->field_type) . '" title="' . esc_attr($field->field_label . ' (' . $field->field_type . ')') . '">' . $icon . esc_html($field->field_label) . '</button>';
                                            }
                                        } else {
                                            echo '<p class="dab-no-fields-message">No numeric fields available. Add number fields to use in formulas.</p>';
                                        }
                                        ?>
                                    </div>
                                </div>

                                <!-- Operator Buttons -->
                                <div class="dab-operator-container">
                                    <h4>Operators</h4>
                                    <div class="dab-operator-buttons">
                                        <button type="button" class="button dab-operator-button" title="Add">+</button>
                                        <button type="button" class="button dab-operator-button" title="Subtract">-</button>
                                        <button type="button" class="button dab-operator-button" title="Multiply">*</button>
                                        <button type="button" class="button dab-operator-button" title="Divide">/</button>
                                        <button type="button" class="button dab-operator-button" title="Open Parenthesis">(</button>
                                        <button type="button" class="button dab-operator-button" title="Close Parenthesis">)</button>
                                    </div>
                                </div>

                                <!-- Function Buttons -->
                                <div class="dab-function-container">
                                    <h4>Functions</h4>
                                    <div class="dab-function-buttons">
                                        <button type="button" class="button dab-function-button" title="Sum of values">SUM</button>
                                        <button type="button" class="button dab-function-button" title="Average of values">AVG</button>
                                        <button type="button" class="button dab-function-button" title="Minimum value">MIN</button>
                                        <button type="button" class="button dab-function-button" title="Maximum value">MAX</button>
                                        <button type="button" class="button dab-function-button" title="Round to nearest integer">ROUND</button>
                                        <button type="button" class="button dab-function-button" title="Round down">FLOOR</button>
                                        <button type="button" class="button dab-function-button" title="Round up">CEIL</button>
                                        <button type="button" class="button dab-function-button" title="Absolute value">ABS</button>
                                    </div>
                                </div>

                                <!-- Clear Button -->
                                <button type="button" class="button dab-clear-formula">Clear Formula</button>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="Add Field">
            </p>
        </form>

        <hr>
        <h2>Existing Fields</h2>
        <?php if (!empty($fields)): ?>
            <table class="widefat striped" id="field-list">
                <thead>
                    <tr>
                        <th>Label</th>
                        <th>Slug</th>
                        <th>Type</th>
                        <th>Required</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fields as $field): ?>
                        <tr data-slug="<?php echo esc_attr($field->field_slug); ?>" data-type="<?php echo esc_attr($field->field_type); ?>">
                            <td><?php echo esc_html($field->field_label); ?></td>
                            <td><?php echo esc_html($field->field_slug); ?></td>
                            <td><?php echo esc_html(ucfirst($field->field_type)); ?></td>
                            <td><?php echo $field->required ? 'Yes' : 'No'; ?></td>
                            <td><?php echo esc_html($field->created_at); ?></td>
                            <td>
                                <a href="<?php echo admin_url("admin.php?page=dab_fields&action=edit&field_id={$field->id}"); ?>" class="button button-small">Edit</a>
                                <a href="<?php echo admin_url("admin.php?page=dab_fields&table_id=$selected_table_id&delete_field={$field->id}"); ?>"
                                   onclick="return confirm('Are you sure you want to delete this field?');" class="button button-small button-link-delete">Delete</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>No fields defined for this table yet.</p>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Add plugin URL to the global dab_vars object for use in our scripts
    if (typeof dab_vars === 'undefined') {
        window.dab_vars = {};
    }
    dab_vars.plugin_url = '<?php echo plugin_dir_url(dirname(__FILE__)); ?>';

    // Function to handle field type changes
    function toggleFieldSettings() {
        var fieldType = $('#field_type').val();

        // Hide all specific settings first
        $('#options_row, #lookup_settings_row, #lookup_display_column_row, #formula_builder_row, #inline_table_settings_row, #inline_table_foreign_key_row, #inline_table_display_fields_row, #inline_table_options_row, #wp_user_settings_row, #wp_user_role_field_row, #payment_settings_row, #payment_amount_type_row, #payment_amount_row, #payment_amount_field_row, #payment_currency_row, #payment_description_row, #autoincrement_settings_row, #autoincrement_start_number_row, #autoincrement_padding_row, #enhanced_currency_default_row, #enhanced_currency_symbol_position_row, #enhanced_currency_decimal_places_row, #enhanced_currency_thousand_separator_row, #enhanced_currency_decimal_separator_row, #enhanced_currency_allow_selection_row').hide();

        // Show settings based on field type
        if (fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox' || fieldType === 'multiselect') {
            $('#options_row').show();
        } else if (fieldType === 'lookup') {
            $('#lookup_settings_row, #lookup_display_column_row').show();
        } else if (fieldType === 'formula') {
            $('#formula_builder_row').show();
        } else if (fieldType === 'inline_table') {
            $('#inline_table_settings_row, #inline_table_foreign_key_row, #inline_table_display_fields_row, #inline_table_options_row').show();
        } else if (fieldType === 'wp_user') {
            $('#wp_user_settings_row').show();
            // Show role field selector if filter by role is checked
            if ($('#wp_user_role_filter').is(':checked')) {
                $('#wp_user_role_field_row').show();
            }
        } else if (fieldType === 'payment') {
            $('#payment_settings_row, #payment_amount_type_row, #payment_currency_row, #payment_description_row').show();

            // Show additional fields based on amount type
            var amountType = $('#payment_amount_type').val();
            if (amountType === 'fixed') {
                $('#payment_amount_row').show();
                $('#payment_amount_field_row').hide();
            } else if (amountType === 'field') {
                $('#payment_amount_field_row').show();
                $('#payment_amount_row').hide();
            } else {
                $('#payment_amount_row, #payment_amount_field_row').hide();
            }
        } else if (fieldType === 'autoincrement') {
            $('#autoincrement_settings_row, #autoincrement_start_number_row, #autoincrement_padding_row').show();
        } else if (fieldType === 'enhanced_currency') {
            $('#enhanced_currency_default_row, #enhanced_currency_symbol_position_row, #enhanced_currency_decimal_places_row, #enhanced_currency_thousand_separator_row, #enhanced_currency_decimal_separator_row, #enhanced_currency_allow_selection_row').show();
        } else if (fieldType === 'media') {
            $('#media_type_row, #media_allow_recording_row, #media_allow_upload_row, #media_allow_embed_row, #media_max_file_size_row, #media_allowed_formats_row, #media_player_width_row, #media_player_height_row').show();
        }

        // Our new field types don't need special settings in the admin UI
        // They have their own UI in the form builder
        // But we could add specific settings here if needed in the future
    }

    // Call the function when field type changes
    $('#field_type').on('change', toggleFieldSettings);

    // Handle "Filter by Role" checkbox change
    $('#wp_user_role_filter').on('change', function() {
        if ($(this).is(':checked')) {
            $('#wp_user_role_field_row').show();
        } else {
            $('#wp_user_role_field_row').hide();
        }
    });

    // Handle payment amount type change
    $('#payment_amount_type').on('change', function() {
        var amountType = $(this).val();
        if (amountType === 'fixed') {
            $('#payment_amount_row').show();
            $('#payment_amount_field_row').hide();
        } else if (amountType === 'field') {
            $('#payment_amount_field_row').show();
            $('#payment_amount_row').hide();
        } else {
            $('#payment_amount_row, #payment_amount_field_row').hide();
        }
    });

    // Initialize on page load
    toggleFieldSettings();

    // Handle lookup table selection
    $('#lookup_table_id').on('change', function() {
        var tableId = $(this).val();
        var displayColumnSelect = $('#lookup_display_column');

        if (!tableId) {
            displayColumnSelect.html('<option value="">-- Select Reference Table First --</option>');
            return;
        }

        displayColumnSelect.html('<option value="">Loading...</option>');

        // Use GET request for simplicity and to avoid nonce issues
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            dataType: 'json',
            data: {
                action: 'dab_get_table_fields',
                table_id: tableId
            },
            success: function(response) {


                displayColumnSelect.html('<option value="">-- Select Display Column --</option>');

                // Handle different response formats
                var fieldsData = [];

                if (response.success && response.data && Array.isArray(response.data)) {
                    // New API format with success property
                    fieldsData = response.data;
                } else if (Array.isArray(response)) {
                    // Legacy API format (direct array)
                    fieldsData = response;
                } else if (typeof response === 'object' && !response.success) {
                    // Error response
                    displayColumnSelect.html('<option value="">No fields available: ' + (response.message || 'Unknown error') + '</option>');
                    return;
                }

                if (fieldsData.length > 0) {
                    // Add fields as options
                    $.each(fieldsData, function(i, field) {
                        displayColumnSelect.append('<option value="' + field.field_slug + '">' + field.field_label + '</option>');
                    });
                } else {
                    displayColumnSelect.html('<option value="">No fields available in this table</option>');
                }
            },
            error: function(xhr, status, error) {

                displayColumnSelect.html('<option value="">Error loading fields: ' + error + '</option>');
            }
        });
    });



    // Auto-generate slug from label
    $('#field_label').on('blur', function() {
        var label = $(this).val();
        var slugField = $('#field_slug');

        if (label && !slugField.val()) {
            // Convert to lowercase, replace spaces with underscores, remove special chars
            var slug = label.toLowerCase()
                .replace(/\s+/g, '_')
                .replace(/[^a-z0-9_]/g, '');

            slugField.val(slug);
        }
    });

    // Handle inline table selection
    $('#inline_table_id').on('change', function() {
        var tableId = $(this).val();
        var foreignKeySelect = $('#inline_table_foreign_key');
        var displayFieldsSelect = $('#inline_table_display_fields');

        if (!tableId) {
            foreignKeySelect.html('<option value="">-- Select Child Table First --</option>');
            displayFieldsSelect.html('<option value="">-- Select Child Table First --</option>');
            return;
        }

        foreignKeySelect.html('<option value="">Loading...</option>');
        displayFieldsSelect.html('<option value="">Loading...</option>');

        // Use AJAX to get fields from the selected table
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            dataType: 'json',
            data: {
                action: 'dab_get_table_fields',
                table_id: tableId
            },
            success: function(response) {
                foreignKeySelect.html('<option value="">-- Select Foreign Key Field --</option>');
                displayFieldsSelect.html('');

                // Handle different response formats
                var fieldsData = [];

                if (response.success && response.data && Array.isArray(response.data)) {
                    fieldsData = response.data;
                } else if (Array.isArray(response)) {
                    fieldsData = response;
                } else if (typeof response === 'object' && !response.success) {
                    foreignKeySelect.html('<option value="">No fields available: ' + (response.message || 'Unknown error') + '</option>');
                    displayFieldsSelect.html('<option value="">No fields available</option>');
                    return;
                }

                if (fieldsData.length > 0) {
                    // Add fields as options
                    $.each(fieldsData, function(i, field) {
                        // Add to foreign key dropdown (only number fields)
                        if (field.field_type === 'number' || field.field_type === 'lookup') {
                            foreignKeySelect.append('<option value="' + field.field_slug + '">' + field.field_label + '</option>');
                        }

                        // Add to display fields multiselect (all fields)
                        displayFieldsSelect.append('<option value="' + field.field_slug + '">' + field.field_label + '</option>');
                    });
                } else {
                    foreignKeySelect.html('<option value="">No fields available in this table</option>');
                    displayFieldsSelect.html('<option value="">No fields available</option>');
                }
            },
            error: function(xhr, status, error) {
                foreignKeySelect.html('<option value="">Error loading fields: ' + error + '</option>');
                displayFieldsSelect.html('<option value="">Error loading fields</option>');
            }
        });
    });

    // Formula Builder Functionality
    if ($('#formula_builder_row').length > 0) {
        // Load the CSS
        $('<link>')
            .appendTo('head')
            .attr({
                type: 'text/css',
                rel: 'stylesheet',
                href: '<?php echo plugin_dir_url(dirname(__FILE__)) . "assets/css/visual-formula-builder.css"; ?>'
            });

        // Field buttons click handler
        $('.dab-field-button').on('click', function() {
            var fieldSlug = $(this).data('field');
            var formulaInput = $('#formula_expression');
            var cursorPos = formulaInput[0].selectionStart;
            var currentValue = formulaInput.val();
            var fieldReference = '{' + fieldSlug + '}';

            // Insert the field reference at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                fieldReference +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position
            formulaInput[0].selectionStart = cursorPos + fieldReference.length;
            formulaInput[0].selectionEnd = cursorPos + fieldReference.length;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        });

        // Operator buttons click handler
        $('.dab-operator-button').on('click', function() {
            var operator = $(this).text();
            var formulaInput = $('#formula_expression');
            var cursorPos = formulaInput[0].selectionStart;
            var currentValue = formulaInput.val();

            // Insert the operator at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                operator +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position
            formulaInput[0].selectionStart = cursorPos + operator.length;
            formulaInput[0].selectionEnd = cursorPos + operator.length;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        });

        // Function buttons click handler
        $('.dab-function-button').on('click', function() {
            var functionName = $(this).text();
            var formulaInput = $('#formula_expression');
            var cursorPos = formulaInput[0].selectionStart;
            var currentValue = formulaInput.val();
            var functionText = functionName + '()';

            // Insert the function at the cursor position
            formulaInput.val(
                currentValue.substring(0, cursorPos) +
                functionText +
                currentValue.substring(cursorPos)
            );

            // Update the cursor position to be inside the parentheses
            formulaInput[0].selectionStart = cursorPos + functionName.length + 1;
            formulaInput[0].selectionEnd = cursorPos + functionName.length + 1;

            // Focus the input
            formulaInput.focus();

            // Update the formula preview
            updateFormulaPreview();
        });

        // Clear button click handler
        $('.dab-clear-formula').on('click', function() {
            $('#formula_expression').val('');
            updateFormulaPreview();
        });

        // Formula input change handler
        $('#formula_expression').on('input', updateFormulaPreview);

        // Initial preview update
        updateFormulaPreview();

        // Function to update the formula preview
        function updateFormulaPreview() {
            var formula = $('#formula_expression').val();
            var previewText = $('.dab-formula-preview .preview-text');

            // Format the formula for display
            var formattedFormula = formula;

            // Replace field references with field labels
            formattedFormula = formattedFormula.replace(/{([^}]+)}/g, function(match, fieldSlug) {
                var fieldButton = $('.dab-field-button[data-field="' + fieldSlug + '"]');
                if (fieldButton.length) {
                    return '<span class="field-reference">' + fieldButton.text() + '</span>';
                }
                return match;
            });

            // Highlight operators
            formattedFormula = formattedFormula.replace(/([+\-*/()])/g, '<span class="operator">$1</span>');

            // Highlight functions
            var functions = ['SUM', 'AVG', 'MIN', 'MAX', 'ROUND', 'FLOOR', 'CEIL', 'ABS'];
            functions.forEach(function(func) {
                var regex = new RegExp('(' + func + ')\\(', 'g');
                formattedFormula = formattedFormula.replace(regex, '<span class="function">$1</span>(');
            });

            // Update the preview
            previewText.html(formattedFormula || 'No formula yet');
        }
    }
});
</script>







