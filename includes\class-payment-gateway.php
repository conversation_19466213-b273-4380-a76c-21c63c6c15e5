<?php
/**
 * Payment Gateway Framework
 *
 * This class provides a framework for integrating payment gateways with the Database App Builder plugin.
 */
if (!defined('ABSPATH')) exit;

class DAB_Payment_Gateway {

    /**
     * Available payment gateways
     */
    private static $gateways = array();

    /**
     * Initialize the payment gateway framework
     */
    public static function init() {
        // Register the payment field type
        add_filter('dab_field_types', array(__CLASS__, 'register_payment_field_type'));

        // Register payment field renderer
        add_action('dab_render_field_payment', array(__CLASS__, 'render_payment_field'), 10, 2);

        // Register payment field settings
        add_filter('dab_field_type_options', array(__CLASS__, 'add_payment_field_options'), 10, 2);

        // Register payment processing hooks
        add_action('dab_before_form_submission', array(__CLASS__, 'process_payment'), 10, 3);

        // Register AJAX handlers
        add_action('wp_ajax_dab_get_payment_form', array(__CLASS__, 'ajax_get_payment_form'));
        add_action('wp_ajax_nopriv_dab_get_payment_form', array(__CLASS__, 'ajax_get_payment_form'));

        // Register default gateways
        self::register_default_gateways();

        // Create payment records table on activation
        add_action('dab_activate', array(__CLASS__, 'create_payment_table'));
    }

    /**
     * Register the payment field type
     */
    public static function register_payment_field_type($field_types) {
        $field_types['payment'] = __('Payment', 'db-app-builder');
        return $field_types;
    }

    /**
     * Add payment field options
     */
    public static function add_payment_field_options($options, $field_type) {
        if ($field_type === 'payment') {
            $options = array(
                'payment_gateway' => array(
                    'label' => __('Payment Gateway', 'db-app-builder'),
                    'type' => 'select',
                    'options' => self::get_gateway_options(),
                    'description' => __('Select which payment gateway to use for this field.', 'db-app-builder'),
                ),
                'payment_amount_type' => array(
                    'label' => __('Amount Type', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'fixed' => __('Fixed Amount', 'db-app-builder'),
                        'field' => __('From Field', 'db-app-builder'),
                        'user_input' => __('User Input', 'db-app-builder'),
                    ),
                    'description' => __('Determine how the payment amount is set.', 'db-app-builder'),
                ),
                'payment_amount' => array(
                    'label' => __('Fixed Amount', 'db-app-builder'),
                    'type' => 'number',
                    'description' => __('Set a fixed amount for payment (if Fixed Amount is selected).', 'db-app-builder'),
                    'placeholder' => '0.00',
                    'step' => '0.01',
                ),
                'payment_amount_field' => array(
                    'label' => __('Amount Field', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(), // Will be populated via AJAX
                    'description' => __('Select which field contains the payment amount (if From Field is selected).', 'db-app-builder'),
                ),
                'payment_currency' => array(
                    'label' => __('Currency', 'db-app-builder'),
                    'type' => 'select',
                    'options' => self::get_currency_options(),
                    'description' => __('Select the currency for this payment.', 'db-app-builder'),
                ),
                'payment_description' => array(
                    'label' => __('Payment Description', 'db-app-builder'),
                    'type' => 'text',
                    'description' => __('Description that will appear on the payment form and receipt.', 'db-app-builder'),
                ),
            );
        }

        return $options;
    }

    /**
     * Render the payment field
     */
    public static function render_payment_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = maybe_unserialize($field->options);
        }
        $gateway = isset($options['payment_gateway']) ? $options['payment_gateway'] : 'stripe';
        $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';
        $amount = isset($options['payment_amount']) ? floatval($options['payment_amount']) : 0;
        $currency = isset($options['payment_currency']) ? $options['payment_currency'] : 'USD';
        $description = isset($options['payment_description']) ? $options['payment_description'] : '';

        // Generate a unique ID for this payment
        $payment_id = 'dab-payment-' . uniqid();

        // Get amount field slug if amount type is field
        $amount_field_slug = '';
        if ($amount_type === 'field') {
            $amount_field_slug = isset($options['payment_amount_field']) ? $options['payment_amount_field'] : '';
        }

        // Output the payment field container with data attributes for JS
        echo '<div class="dab-payment-field" id="' . esc_attr($payment_id) . '"
            data-field-id="' . esc_attr($field->id) . '"
            data-gateway="' . esc_attr($gateway) . '"
            data-amount-type="' . esc_attr($amount_type) . '"
            data-amount-field="' . esc_attr($amount_field_slug) . '">';

        // If user input amount type, show amount input
        if ($amount_type === 'user_input') {
            echo '<div class="dab-payment-amount-input">';
            echo '<label for="' . esc_attr($payment_id . '-amount') . '">' . __('Amount', 'db-app-builder') . '</label>';
            echo '<input type="number" id="' . esc_attr($payment_id . '-amount') . '" name="' . esc_attr($field->field_slug . '_amount') . '" step="0.01" min="0" value="' . esc_attr($amount) . '" required>';
            echo '<span class="dab-currency-symbol">' . esc_html(self::get_currency_symbol($currency)) . '</span>';
            echo '</div>';
        } else if ($amount_type === 'fixed') {
            // For fixed amount, show the amount
            echo '<div class="dab-payment-amount-display">';
            echo '<span class="dab-payment-amount-label">' . __('Amount', 'db-app-builder') . ': </span>';
            echo '<span class="dab-payment-amount">' . esc_html(self::format_currency($amount, $currency)) . '</span>';
            echo '</div>';
        } else if ($amount_type === 'field') {
            // For field-based amount, show a dynamic amount display
            echo '<div class="dab-payment-amount-display">';
            echo '<span class="dab-payment-amount-label">' . __('Amount', 'db-app-builder') . ': </span>';
            echo '<span class="dab-payment-amount">0.00</span>';
            echo '<span class="dab-currency-symbol">' . esc_html(self::get_currency_symbol($currency)) . '</span>';
            echo '</div>';

            // Add a hidden input to store the amount
            echo '<input type="hidden" name="' . esc_attr($field->field_slug . '_amount') . '" value="0.00">';
        }

        // Add description if provided
        if (!empty($description)) {
            echo '<div class="dab-payment-description">' . esc_html($description) . '</div>';
        }

        // Payment button and container for payment form
        echo '<div class="dab-payment-button-container">';
        echo '<button type="button" class="dab-payment-button" data-payment-id="' . esc_attr($payment_id) . '">' . __('Pay Now', 'db-app-builder') . '</button>';
        echo '</div>';

        // Container for payment form (will be populated via AJAX)
        echo '<div class="dab-payment-form-container" id="' . esc_attr($payment_id . '-form') . '"></div>';

        // Hidden field to store payment status
        echo '<input type="hidden" name="' . esc_attr($field->field_slug) . '" id="' . esc_attr($payment_id . '-status') . '" value="' . esc_attr($value) . '">';

        echo '</div>';

        // Enqueue payment scripts
        self::enqueue_payment_scripts($gateway);

        // Add payment nonce
        wp_localize_script('dab-payment', 'dabPayment', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_payment_nonce'),
            'paystackNonce' => wp_create_nonce('dab_paystack_nonce'),
            'paypalNonce' => wp_create_nonce('dab_paypal_nonce'),
            'stripeNonce' => wp_create_nonce('dab_stripe_nonce')
        ));
    }

    /**
     * Process payment before form submission
     */
    public static function process_payment($form_id, $data, &$errors) {
        global $wpdb;

        // Check if there are payment fields in the form
        $fields_table = $wpdb->prefix . 'dab_fields';
        $form_table = $wpdb->prefix . 'dab_forms';

        // Get form info
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $form_table WHERE id = %d", $form_id));
        if (!$form) {
            return;
        }

        // Get table ID from form
        $table_id = $form->table_id;

        // Get payment fields for this table
        $payment_fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d AND field_type = 'payment'",
            $table_id
        ));

        if (empty($payment_fields)) {
            return; // No payment fields in this form
        }

        // Check each payment field
        foreach ($payment_fields as $field) {
            $field_slug = $field->field_slug;

            // If payment field has a transaction ID, it means payment was completed
            if (isset($data[$field_slug]) && !empty($data[$field_slug])) {
                $transaction_id = sanitize_text_field($data[$field_slug]);

                // Get field options
                $options = json_decode($field->options, true);
                if (!is_array($options)) {
                    $options = maybe_unserialize($field->options);
                }

                // Get payment details
                $gateway = isset($options['payment_gateway']) ? $options['payment_gateway'] : 'stripe';
                $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';
                $currency = isset($options['payment_currency']) ? $options['payment_currency'] : 'USD';

                // Determine the amount based on the amount type
                if ($amount_type === 'fixed') {
                    // Use the fixed amount from options
                    $amount = isset($options['payment_amount']) ? floatval($options['payment_amount']) : 0;
                } elseif ($amount_type === 'field') {
                    // Get the amount from the referenced field
                    $amount_field_slug = isset($options['payment_amount_field']) ? $options['payment_amount_field'] : '';
                    if (!empty($amount_field_slug) && isset($data[$amount_field_slug])) {
                        $amount = floatval($data[$amount_field_slug]);
                    } else {
                        $amount = 0;
                    }
                } elseif ($amount_type === 'user_input') {
                    // Get the amount from the user input field
                    $amount_field_name = $field_slug . '_amount';
                    if (isset($data[$amount_field_name])) {
                        $amount = floatval($data[$amount_field_name]);
                    } else {
                        $amount = 0;
                    }
                } else {
                    $amount = 0;
                }

                // Save payment record
                self::save_payment_record(
                    $form_id,
                    0, // record_id will be updated after form submission
                    $gateway,
                    $amount,
                    $currency,
                    'completed',
                    $transaction_id,
                    array(
                        'field_id' => $field->id,
                        'field_slug' => $field_slug
                    )
                );
            } else {
                // Payment not completed
                $errors[] = sprintf(__('Payment for %s is required.', 'db-app-builder'), $field->field_label);
            }
        }
    }

    /**
     * Save payment record to database
     */
    public static function save_payment_record($form_id, $record_id, $gateway, $amount, $currency, $status, $transaction_id, $extra_data = array()) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_payments';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            self::create_payment_table();
        }

        // Get current user ID
        $user_id = get_current_user_id();

        // Prepare payment data
        $payment_data = array(
            'form_id' => $form_id,
            'record_id' => $record_id,
            'user_id' => $user_id,
            'gateway' => $gateway,
            'amount' => $amount,
            'currency' => $currency,
            'status' => $status,
            'transaction_id' => $transaction_id,
            'payment_data' => json_encode($extra_data),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );

        // Insert payment record
        $result = $wpdb->insert($table_name, $payment_data);

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Update payment record with record ID
     */
    public static function update_payment_record_id($transaction_id, $record_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_payments';

        // Update payment record with record ID
        $wpdb->update(
            $table_name,
            array('record_id' => $record_id),
            array('transaction_id' => $transaction_id)
        );
    }

    /**
     * AJAX handler to get payment form
     */
    public static function ajax_get_payment_form() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_payment_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }

        // Get field ID and gateway
        $field_id = isset($_POST['field_id']) ? intval($_POST['field_id']) : 0;
        $gateway_id = isset($_POST['gateway']) ? sanitize_text_field($_POST['gateway']) : '';

        if (empty($field_id) || empty($gateway_id)) {
            wp_send_json_error(array('message' => __('Invalid request parameters', 'db-app-builder')));
        }

        // Get field data
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        $field = $wpdb->get_row($wpdb->prepare("SELECT * FROM $fields_table WHERE id = %d", $field_id));

        if (!$field) {
            wp_send_json_error(array('message' => __('Field not found', 'db-app-builder')));
        }

        // Get gateway class
        $gateways = self::get_gateways();
        if (!isset($gateways[$gateway_id]) || !class_exists($gateways[$gateway_id])) {
            wp_send_json_error(array('message' => __('Payment gateway not found', 'db-app-builder')));
        }

        // Initialize gateway
        $gateway_class = $gateways[$gateway_id];
        $gateway = new $gateway_class();

        // Check if gateway is configured
        if (!$gateway->is_configured()) {
            wp_send_json_error(array('message' => __('Payment gateway is not properly configured', 'db-app-builder')));
        }

        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = maybe_unserialize($field->options);
        }

        // Handle dynamic amount based on amount type
        $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';

        // Override amount if provided in the request (for field-based or user input amounts)
        if ($amount_type === 'field' && isset($_POST['field_amount'])) {
            $options['payment_amount'] = floatval($_POST['field_amount']);
        } elseif ($amount_type === 'user_input' && isset($_POST['user_amount'])) {
            $options['payment_amount'] = floatval($_POST['user_amount']);
        } elseif (isset($_POST['amount'])) {
            // Direct amount override (from data attribute)
            $options['payment_amount'] = floatval($_POST['amount']);
        }

        // Render payment form
        $form = $gateway->render_payment_form($field, $options);

        wp_send_json_success(array('form' => $form));
    }

    /**
     * Register a payment gateway
     */
    public static function register_gateway($gateway_id, $gateway_class) {
        self::$gateways[$gateway_id] = $gateway_class;
    }

    /**
     * Get registered gateways
     */
    public static function get_gateways() {
        return self::$gateways;
    }

    /**
     * Get gateway options for select fields
     */
    public static function get_gateway_options() {
        $options = array();
        foreach (self::$gateways as $id => $class) {
            if (class_exists($class)) {
                $gateway = new $class();
                $options[$id] = $gateway->get_title();
            }
        }
        return $options;
    }

    /**
     * Register default gateways
     */
    private static function register_default_gateways() {
        // Register Stripe
        self::register_gateway('stripe', 'DAB_Stripe_Gateway');

        // Register PayPal
        self::register_gateway('paypal', 'DAB_PayPal_Gateway');

        // Register Paystack
        self::register_gateway('paystack', 'DAB_Paystack_Gateway');
    }

    /**
     * Create payment records table
     */
    public static function create_payment_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $table_name = $wpdb->prefix . 'dab_payments';

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            form_id BIGINT(20) UNSIGNED NOT NULL,
            record_id BIGINT(20) UNSIGNED NOT NULL,
            user_id BIGINT(20) UNSIGNED NULL,
            gateway VARCHAR(50) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) NOT NULL,
            status VARCHAR(20) NOT NULL,
            transaction_id VARCHAR(255) NULL,
            payment_data LONGTEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get currency options
     */
    public static function get_currency_options() {
        return array(
            'USD' => __('US Dollar ($)', 'db-app-builder'),
            'EUR' => __('Euro (€)', 'db-app-builder'),
            'GBP' => __('British Pound (£)', 'db-app-builder'),
            'NGN' => __('Nigerian Naira (₦)', 'db-app-builder'),
            'CAD' => __('Canadian Dollar (C$)', 'db-app-builder'),
            'AUD' => __('Australian Dollar (A$)', 'db-app-builder'),
            'INR' => __('Indian Rupee (₹)', 'db-app-builder'),
            'JPY' => __('Japanese Yen (¥)', 'db-app-builder'),
            'CNY' => __('Chinese Yuan (¥)', 'db-app-builder'),
            'ZAR' => __('South African Rand (R)', 'db-app-builder'),
        );
    }

    /**
     * Get currency symbol
     */
    public static function get_currency_symbol($currency) {
        $symbols = array(
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'NGN' => '₦',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'INR' => '₹',
            'JPY' => '¥',
            'CNY' => '¥',
            'ZAR' => 'R',
        );

        return isset($symbols[$currency]) ? $symbols[$currency] : $currency;
    }

    /**
     * Format currency
     */
    public static function format_currency($amount, $currency) {
        $symbol = self::get_currency_symbol($currency);
        return $symbol . number_format($amount, 2);
    }

    /**
     * Enqueue payment scripts
     */
    private static function enqueue_payment_scripts($gateway) {
        // Enqueue common payment scripts
        wp_enqueue_style('dab-payment', plugin_dir_url(dirname(__FILE__)) . 'assets/css/payment.css', array(), DAB_VERSION);
        wp_enqueue_script('dab-payment', plugin_dir_url(dirname(__FILE__)) . 'assets/js/payment.js', array('jquery'), DAB_VERSION, true);

        // Enqueue gateway-specific scripts
        if ($gateway === 'stripe') {
            wp_enqueue_script('stripe-js', 'https://js.stripe.com/v3/', array(), null, true);
        } elseif ($gateway === 'paypal') {
            wp_enqueue_script('paypal-js', 'https://www.paypal.com/sdk/js?client-id=' . urlencode(DAB_Settings_Manager::get('paypal_client_id')), array(), null, true);
        } elseif ($gateway === 'paystack') {
            wp_enqueue_script('paystack-js', 'https://js.paystack.co/v1/inline.js', array(), null, true);
        }
    }
}

// Initialize the payment gateway framework
add_action('init', array('DAB_Payment_Gateway', 'init'));
