<?php
/**
 * Field Permissions Tab
 *
 * Displays the permissions tab in the field settings.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Get all WordPress roles
$wp_roles = wp_roles();
$all_roles = $wp_roles->get_names();

// Get current permissions for this field
$current_permissions = DAB_Role_Permissions_Manager::get_field_permissions($field_id);

// Convert to a more usable format
$role_permissions = [];
foreach ($current_permissions as $perm) {
    $role_permissions[$perm['role']] = [
        'can_view' => $perm['can_view'],
        'can_edit' => $perm['can_edit']
    ];
}
?>

<style>
    .tooltip {
        position: relative;
        display: inline-block;
        margin-left: 5px;
        width: 16px;
        height: 16px;
        background-color: #f0f0f0;
        color: #666;
        border-radius: 50%;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        cursor: help;
    }
    
    .tooltip:hover::after {
        content: attr(title);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 100%;
        background-color: #333;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        white-space: nowrap;
        z-index: 1000;
        font-size: 12px;
        font-weight: normal;
    }
</style>

<div class="dab-tab-content" id="dab-tab-field-permissions">
    <h2>Field-Level Permissions</h2>
    <p class="description">Configure which user roles can view and edit this field. Hover over the question marks for more information.</p>
    
    <form id="dab-field-permissions-form" method="post">
        <input type="hidden" name="field_id" value="<?php echo esc_attr($field_id); ?>">
        <?php wp_nonce_field('dab_admin_nonce', 'dab_admin_nonce'); ?>
        
        <table class="widefat striped">
            <thead>
                <tr>
                    <th>Role</th>
                    <th>View Field</th>
                    <th>Edit Field</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($all_roles as $role_id => $role_name): ?>
                    <?php 
                    // Skip administrator role as they always have full access
                    if ($role_id === 'administrator') continue;
                    
                    // Get current permissions for this role
                    $can_view = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_view'] ? true : false;
                    $can_edit = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_edit'] ? true : false;
                    ?>
                    <tr>
                        <td><?php echo esc_html($role_name); ?></td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_view]" value="1" <?php checked($can_view); ?>>
                            <span class="tooltip" title="Allow users with this role to view this field">?</span>
                        </td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_edit]" value="1" <?php checked($can_edit); ?>>
                            <span class="tooltip" title="Allow users with this role to edit this field">?</span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <p class="description">Note: Administrators always have full access to all fields.</p>
        <p class="description">Users can always view and edit fields in their own records regardless of role permissions.</p>
        
        <p>
            <button type="submit" class="button button-primary" id="dab-save-field-permissions">Save Field Permissions</button>
            <span class="spinner" style="float: none; margin-top: 0;"></span>
        </p>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle form submission
    $('#dab-field-permissions-form').on('submit', function(e) {
        e.preventDefault();
        
        // Show spinner
        $(this).find('.spinner').addClass('is-active');
        
        // Get form data
        var formData = $(this).serialize();
        formData += '&action=dab_save_field_permissions';
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Field permissions saved successfully.');
                } else {
                    // Show error message
                    alert('Error: ' + (response.data || 'Failed to save field permissions.'));
                }
            },
            error: function() {
                alert('An error occurred while saving field permissions.');
            },
            complete: function() {
                // Hide spinner
                $('#dab-field-permissions-form').find('.spinner').removeClass('is-active');
            }
        });
    });
});
