<?php
/**
 * License Validator
 *
 * Handles Envato purchase code validation and domain restriction.
 *
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_License_Validator {

    /**
     * The single instance of the class.
     *
     * @var DAB_License_Validator
     */
    protected static $_instance = null;

    /**
     * Main License Validator Instance.
     *
     * Ensures only one instance of License Validator is loaded or can be loaded.
     *
     * @return DAB_License_Validator - Main instance.
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor.
     */
    public function __construct() {
        // Add hooks to restrict plugin functionality
        add_action('admin_init', array($this, 'check_license_restrictions'));

        // Add filter to check if license is valid
        add_filter('dab_is_license_valid', array($this, 'is_license_valid'));

        // Add hooks to restrict AJAX functionality
        add_action('wp_ajax_dab_create_table', array($this, 'check_ajax_license'), 5);
        add_action('wp_ajax_dab_create_field', array($this, 'check_ajax_license'), 5);
        add_action('wp_ajax_dab_create_form', array($this, 'check_ajax_license'), 5);
        add_action('wp_ajax_dab_create_view', array($this, 'check_ajax_license'), 5);
        add_action('wp_ajax_dab_create_dashboard', array($this, 'check_ajax_license'), 5);

        // Add hooks to restrict shortcode functionality
        add_filter('dab_shortcode_form_output', array($this, 'check_shortcode_license'), 5, 2);
        add_filter('dab_shortcode_view_output', array($this, 'check_shortcode_license'), 5, 2);
        add_filter('dab_shortcode_dashboard_output', array($this, 'check_shortcode_license'), 5, 2);
    }

    /**
     * Check if license is valid.
     *
     * @return bool Whether the license is valid.
     */
    public function is_license_valid() {
        // If we're in the admin area and on a license page, always return true
        // to allow access to license management
        if (is_admin() && isset($_GET['page']) && in_array($_GET['page'], array('dab_license', 'dab_license_manager'))) {
            return true;
        }

        // Check if license is valid using the License Manager
        if (function_exists('DAB_License')) {
            return DAB_License()->is_license_valid();
        }

        return false;
    }

    /**
     * Check license restrictions.
     */
    public function check_license_restrictions() {
        // If we're in the admin area and on a license page, don't restrict
        if (isset($_GET['page']) && in_array($_GET['page'], array('dab_license', 'dab_license_manager'))) {
            return;
        }

        // Check if license is valid
        if (!$this->is_license_valid()) {
            // Add notice about invalid license
            add_action('admin_notices', array($this, 'invalid_license_notice'));

            // Restrict access to certain admin pages
            if (isset($_GET['page']) && $this->is_restricted_page($_GET['page'])) {
                wp_redirect(admin_url('admin.php?page=dab_license'));
                exit;
            }
        }
    }

    /**
     * Check if a page is restricted.
     *
     * @param string $page Page slug.
     * @return bool Whether the page is restricted.
     */
    private function is_restricted_page($page) {
        // Define restricted pages
        $restricted_pages = array(
            'dab_tables',
            'dab_fields',
            'dab_forms',
            'dab_views',
            'dab_dashboards',
            'dab_data_dashboard',
            'dab_approval_config',
            'dab_approvals',
            'dab_permissions_dashboard',
            'dab_payment_settings',
            'dab_payment_tracking',
            'dab_integrations',
            'dab_dashboard_builder',
            'dab_dashboard_view',
            'dab_wizard_app_creation',
            'dab_wizard_workflow_setup',
        );

        return in_array($page, $restricted_pages);
    }

    /**
     * Display invalid license notice.
     */
    public function invalid_license_notice() {
        $license_data = DAB_License()->get_license_data();
        $current_domain = str_replace(array('http://', 'https://'), '', home_url());

        if (!empty($license_data['purchase_code']) && !empty($license_data['domain']) && $license_data['domain'] !== $current_domain) {
            // Domain mismatch error
            echo '<div class="notice notice-error"><p>';
            echo sprintf(
                __('Domain mismatch detected! Your purchase code is registered to %s but you are using it on %s. Please <a href="%s">update your license</a>.', 'db-app-builder'),
                '<strong>' . esc_html($license_data['domain']) . '</strong>',
                '<strong>' . esc_html($current_domain) . '</strong>',
                admin_url('admin.php?page=dab_license')
            );
            echo '</p></div>';
        } else {
            // General license error
            echo '<div class="notice notice-error"><p>';
            echo sprintf(
                __('Your Database App Builder is not activated. Please <a href="%s">activate your Envato purchase code</a> to continue using all features.', 'db-app-builder'),
                admin_url('admin.php?page=dab_license')
            );
            echo '</p></div>';
        }
    }

    /**
     * Check license for AJAX requests.
     */
    public function check_ajax_license() {
        // Check if license is valid
        if (!$this->is_license_valid()) {
            wp_send_json_error(array(
                'message' => __('Your license is invalid or has expired. Please activate your license to use this feature.', 'db-app-builder'),
                'license_error' => true,
            ));
            exit;
        }
    }

    /**
     * Check license for shortcode output.
     *
     * @param string $output Shortcode output.
     * @param array $atts Shortcode attributes.
     * @return string Modified shortcode output.
     */
    public function check_shortcode_license($output, $atts) {
        // Check if license is valid
        if (!$this->is_license_valid()) {
            return '<div class="dab-license-error">' .
                __('This feature requires a valid Database App Builder license.', 'db-app-builder') .
                '</div>';
        }

        return $output;
    }

    /**
     * Get license status message.
     *
     * @return string License status message.
     */
    public function get_license_status_message() {
        // Check if license is valid
        if ($this->is_license_valid()) {
            return __('Your Envato purchase code is valid and active on this domain.', 'db-app-builder');
        }

        // Get license data
        $license_data = DAB_License()->get_license_data();
        $current_domain = str_replace(array('http://', 'https://'), '', home_url());

        // Check for domain mismatch
        if (!empty($license_data['purchase_code']) && !empty($license_data['domain']) && $license_data['domain'] !== $current_domain) {
            return sprintf(
                __('Domain mismatch detected! Your purchase code is registered to %s but you are using it on %s.', 'db-app-builder'),
                $license_data['domain'],
                $current_domain
            );
        }

        // Check license status
        switch ($license_data['status']) {
            case 'invalid':
                return __('Your Envato purchase code is invalid. Please check your purchase code and try again.', 'db-app-builder');
            default:
                return __('Your Envato purchase code is inactive. Please activate your purchase code to use all features.', 'db-app-builder');
        }
    }
}

// Initialize License Validator
function DAB_License_Validator() {
    return DAB_License_Validator::instance();
}

// Global for backwards compatibility
$GLOBALS['dab_license_validator'] = DAB_License_Validator();
