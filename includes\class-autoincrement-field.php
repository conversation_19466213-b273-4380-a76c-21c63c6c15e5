<?php
/**
 * Autoincrement Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Autoincrement_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Hook into form submission to generate autoincrement values
        add_action('dab_before_form_submission', array($this, 'process_autoincrement_fields'), 10, 2);
        
        // Hook into direct record creation via API
        add_filter('dab_before_insert_record', array($this, 'filter_record_data_before_insert'), 10, 2);
        
        // Add the autoincrement field type to the list of available field types
        add_filter('dab_field_types', array($this, 'add_autoincrement_field_type'));
    }
    
    /**
     * Add autoincrement field type to the list of available field types
     * 
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function add_autoincrement_field_type($field_types) {
        $field_types['autoincrement'] = __('Auto Increment', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Process autoincrement fields before form submission
     * 
     * @param array $form_data Form data
     * @param int $table_id Table ID
     * @return array Modified form data
     */
    public function process_autoincrement_fields($form_data, $table_id) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        // Get all autoincrement fields for this table
        $autoincrement_fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d AND field_type = 'autoincrement'",
            $table_id
        ));
        
        if (empty($autoincrement_fields)) {
            return $form_data;
        }
        
        // Process each autoincrement field
        foreach ($autoincrement_fields as $field) {
            // Generate the next ID for this field
            $next_id = $this->generate_next_id($field);
            
            // Add the generated ID to the form data
            $form_data[$field->field_slug] = $next_id;
        }
        
        return $form_data;
    }
    
    /**
     * Filter record data before insert to add autoincrement values
     * 
     * @param array $record_data Record data
     * @param int $table_id Table ID
     * @return array Modified record data
     */
    public function filter_record_data_before_insert($record_data, $table_id) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        // Get all autoincrement fields for this table
        $autoincrement_fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d AND field_type = 'autoincrement'",
            $table_id
        ));
        
        if (empty($autoincrement_fields)) {
            return $record_data;
        }
        
        // Process each autoincrement field
        foreach ($autoincrement_fields as $field) {
            // Skip if the field already has a value
            if (!empty($record_data[$field->field_slug])) {
                continue;
            }
            
            // Generate the next ID for this field
            $next_id = $this->generate_next_id($field);
            
            // Add the generated ID to the record data
            $record_data[$field->field_slug] = $next_id;
        }
        
        return $record_data;
    }
    
    /**
     * Generate the next ID for an autoincrement field
     * 
     * @param object $field Field object
     * @return string Generated ID
     */
    public function generate_next_id($field) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        // Get the field options
        $options = json_decode($field->options, true);
        if (empty($options)) {
            $options = array(
                'prefix' => '',
                'start_number' => 1,
                'padding' => 3,
                'current_number' => 0
            );
        }
        
        // Get the next number
        $next_number = isset($options['current_number']) ? intval($options['current_number']) + 1 : intval($options['start_number']);
        
        // Update the current number in the field options
        $options['current_number'] = $next_number;
        $wpdb->update(
            $fields_table,
            array('options' => json_encode($options)),
            array('id' => $field->id)
        );
        
        // Format the ID with prefix and padding
        $prefix = isset($options['prefix']) ? $options['prefix'] : '';
        $padding = isset($options['padding']) ? intval($options['padding']) : 3;
        
        // Generate the ID
        $id = $prefix . str_pad($next_number, $padding, '0', STR_PAD_LEFT);
        
        return $id;
    }
}

// Initialize the class
new DAB_Autoincrement_Field();
