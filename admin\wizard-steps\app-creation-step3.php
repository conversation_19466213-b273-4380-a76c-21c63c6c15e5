<?php
/**
 * Application Creation Wizard - Step 3: Fields Configuration
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get saved data
$table_name = isset($progress['data']['table_name']) ? $progress['data']['table_name'] : '';
$table_slug = isset($progress['data']['table_slug']) ? $progress['data']['table_slug'] : sanitize_title($table_name);

// Check if table exists or create it
$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';

$table_id = 0;
$existing_table = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM $tables_table WHERE table_slug = %s",
    $table_slug
));

if (!$existing_table && !empty($table_name)) {
    // Create the table if it doesn't exist
    $wpdb->insert($tables_table, [
        'table_label' => $table_name,
        'table_slug' => $table_slug,
        'description' => isset($progress['data']['table_description']) ? $progress['data']['table_description'] : '',
        'created_at' => current_time('mysql'),
    ]);

    $table_id = $wpdb->insert_id;

    // Create the data table
    DAB_DB_Manager::create_data_table($table_slug);

    // Store the table ID in the progress data
    $progress['data']['table_id'] = $table_id;
    update_option('dab_wizard_progress', [$wizard_type => $progress]);
} else {
    $table_id = $existing_table ? $existing_table->id : 0;
    $progress['data']['table_id'] = $table_id;
    update_option('dab_wizard_progress', [$wizard_type => $progress]);
}

// Get existing fields for this table
$fields = [];
if ($table_id) {
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
        $table_id
    ));
}

// Get field types
$field_types_obj = new DAB_Field_Types();
$field_types = $field_types_obj->get_field_types();

// Sort field types alphabetically by label
asort($field_types);

// Process field deletion if requested
if (isset($_POST['dab_delete_field']) && $_POST['dab_delete_field'] === '1' && isset($_POST['field_id'])) {
    $field_id = intval($_POST['field_id']);

    // Get the field to delete
    $field_to_delete = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE id = %d AND table_id = %d",
        $field_id, $table_id
    ));

    if ($field_to_delete) {
        // Delete the field
        $wpdb->delete($fields_table, ['id' => $field_id], ['%d']);

        // Return success response for AJAX
        if (wp_doing_ajax() || (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest')) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => [
                    'field_id' => $field_id,
                    'message' => __('Field deleted successfully', 'db-app-builder')
                ]
            ]);
            exit;
        }

        // Refresh fields for non-AJAX requests
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
            $table_id
        ));
    } else {
        // Return error response for AJAX
        if (wp_doing_ajax() || (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest')) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'data' => [
                    'message' => __('Field not found', 'db-app-builder')
                ]
            ]);
            exit;
        }
    }
}

// Process field creation if form was submitted via AJAX
if (isset($_POST['dab_add_field']) && $_POST['dab_add_field'] === '1') {
    $field_label = sanitize_text_field($_POST['field_label']);
    $field_slug = sanitize_title($_POST['field_slug']);
    $field_type = sanitize_text_field($_POST['field_type']);
    $required = isset($_POST['required']) ? 1 : 0;
    $placeholder = sanitize_text_field($_POST['placeholder'] ?? '');
    $options = sanitize_textarea_field($_POST['options'] ?? '');

    // Insert the field
    $wpdb->insert($fields_table, [
        'table_id' => $table_id,
        'field_label' => $field_label,
        'field_slug' => $field_slug,
        'field_type' => $field_type,
        'required' => $required,
        'placeholder' => $placeholder,
        'options' => $options,
        'field_order' => count($fields) + 1,
        'created_at' => current_time('mysql')
    ]);

    $new_field_id = $wpdb->insert_id;

    // Add column to data table
    $data_table = $wpdb->prefix . 'dab_' . $table_slug;
    DAB_DB_Manager::add_column_to_table($data_table, $field_slug, $field_type);

    // If this is an AJAX request, return the new field data
    if (wp_doing_ajax() || (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest')) {
        $field_type_label = isset($field_types[$field_type]) ? $field_types[$field_type] : $field_type;
        $required_icon = $required ? '<span class="dashicons dashicons-yes"></span>' : '<span class="dashicons dashicons-no"></span>';

        // Return a proper JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => [
                'field_id' => $new_field_id,
                'field_label' => $field_label,
                'field_slug' => $field_slug,
                'field_type' => $field_type,
                'field_type_label' => $field_type_label,
                'required' => $required,
                'required_icon' => $required_icon,
                'html' => '<tr data-field-id="' . esc_attr($new_field_id) . '">
                    <td>' . esc_html($field_label) . '</td>
                    <td><code>' . esc_html($field_slug) . '</code></td>
                    <td>' . esc_html($field_type_label) . '</td>
                    <td>' . $required_icon . '</td>
                    <td>
                        <button type="button" class="dab-btn dab-btn-sm dab-btn-danger delete-field" data-field-id="' . esc_attr($new_field_id) . '">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </td>
                </tr>'
            ]
        ]);
        exit;
    }

    // Refresh fields for non-AJAX requests
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
        $table_id
    ));
}
?>

<div class="dab-wizard-form">
    <?php if (!$table_id): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Please go back and create a table first.', 'db-app-builder'); ?></p>
        </div>
    <?php else: ?>
        <div class="dab-wizard-section">
            <h3><?php _e('Table Information', 'db-app-builder'); ?></h3>
            <div class="dab-wizard-table-info">
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Table Name:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo esc_html($table_name); ?></span>
                </div>
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Table Slug:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo esc_html($table_slug); ?></span>
                </div>
            </div>
        </div>

        <div class="dab-wizard-section">
            <h3><?php _e('Existing Fields', 'db-app-builder'); ?></h3>
            <?php if (empty($fields)): ?>
                <p><?php _e('No fields have been added yet.', 'db-app-builder'); ?></p>
            <?php else: ?>
                <div class="dab-wizard-fields-list">
                    <table class="dab-wizard-table">
                        <thead>
                            <tr>
                                <th><?php _e('Label', 'db-app-builder'); ?></th>
                                <th><?php _e('Slug', 'db-app-builder'); ?></th>
                                <th><?php _e('Type', 'db-app-builder'); ?></th>
                                <th><?php _e('Required', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($fields as $field): ?>
                                <tr data-field-id="<?php echo esc_attr($field->id); ?>">
                                    <td><?php echo esc_html($field->field_label); ?></td>
                                    <td><code><?php echo esc_html($field->field_slug); ?></code></td>
                                    <td><?php echo esc_html(isset($field_types[$field->field_type]) ? $field_types[$field->field_type] : $field->field_type); ?></td>
                                    <td><?php echo $field->required ? '<span class="dashicons dashicons-yes"></span>' : '<span class="dashicons dashicons-no"></span>'; ?></td>
                                    <td>
                                        <button type="button" class="dab-btn dab-btn-sm dab-btn-danger delete-field" data-field-id="<?php echo esc_attr($field->id); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <div class="dab-wizard-section">
            <h3><?php _e('Add New Field', 'db-app-builder'); ?></h3>
            <form id="dab-add-field-form" method="post" class="dab-wizard-form-inline">
                <input type="hidden" name="dab_add_field" value="1">
                <input type="hidden" name="table_id" value="<?php echo esc_attr($table_id); ?>">

                <div class="dab-wizard-form-row">
                    <div class="dab-wizard-form-group">
                        <label for="field_label" class="dab-wizard-form-label"><?php _e('Field Label', 'db-app-builder'); ?> <span class="required">*</span></label>
                        <input type="text" id="field_label" name="field_label" class="dab-wizard-form-input" required>
                    </div>

                    <div class="dab-wizard-form-group">
                        <label for="field_slug" class="dab-wizard-form-label"><?php _e('Field Slug', 'db-app-builder'); ?> <span class="required">*</span></label>
                        <input type="text" id="field_slug" name="field_slug" class="dab-wizard-form-input" required>
                    </div>
                </div>

                <div class="dab-wizard-form-row">
                    <div class="dab-wizard-form-group">
                        <label for="field_type" class="dab-wizard-form-label"><?php _e('Field Type', 'db-app-builder'); ?> <span class="required">*</span></label>
                        <select id="field_type" name="field_type" class="dab-wizard-form-select" required>
                            <option value=""><?php _e('-- Select Field Type --', 'db-app-builder'); ?></option>
                            <?php foreach ($field_types as $value => $label): ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="dab-wizard-form-group">
                        <label for="placeholder" class="dab-wizard-form-label"><?php _e('Placeholder', 'db-app-builder'); ?></label>
                        <input type="text" id="placeholder" name="placeholder" class="dab-wizard-form-input">
                    </div>
                </div>

                <div class="dab-wizard-form-row">
                    <div class="dab-wizard-form-group dab-wizard-form-checkbox">
                        <input type="checkbox" id="required" name="required" value="1">
                        <label for="required" class="dab-wizard-form-label"><?php _e('Required Field', 'db-app-builder'); ?></label>
                    </div>
                </div>

                <div id="options-container" class="dab-wizard-form-row" style="display: none;">
                    <div class="dab-wizard-form-group dab-wizard-form-full">
                        <label for="options" class="dab-wizard-form-label"><?php _e('Options (one per line)', 'db-app-builder'); ?></label>
                        <textarea id="options" name="options" class="dab-wizard-form-textarea" rows="4"></textarea>
                        <p class="dab-wizard-form-help"><?php _e('Enter one option per line. For key-value pairs, use format: key|value', 'db-app-builder'); ?></p>
                    </div>
                </div>

                <div class="dab-wizard-form-actions">
                    <button type="submit" class="dab-btn dab-btn-primary"><?php _e('Add Field', 'db-app-builder'); ?></button>
                </div>
            </form>
        </div>

        <div class="dab-wizard-notice dab-wizard-notice-info">
            <p><?php _e('Once you have added all the fields you need, click "Next" to continue to the form creation step.', 'db-app-builder'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.dab-wizard-notice-info {
    background-color: #e5f5fa;
    border-left: 4px solid #00a0d2;
}

.dab-wizard-notice-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
}

.dab-wizard-section {
    margin-bottom: 30px;
}

.dab-wizard-table-info {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.dab-wizard-table-detail {
    margin-bottom: 10px;
}

.dab-wizard-table-detail-label {
    font-weight: 600;
    margin-right: 10px;
}

.dab-wizard-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.dab-wizard-table th,
.dab-wizard-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dab-wizard-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.dab-wizard-form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 15px;
}

.dab-wizard-form-group {
    flex: 1;
    min-width: 200px;
}

.dab-wizard-form-full {
    flex: 0 0 100%;
}

.dab-wizard-form-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dab-wizard-form-checkbox input {
    margin: 0;
}

.dab-wizard-form-actions {
    margin-top: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from label
    $('#field_label').on('input', function() {
        const label = $(this).val();
        const slug = label.toLowerCase()
            .replace(/[^a-z0-9]+/g, '_')
            .replace(/^_+|_+$/g, '');
        $('#field_slug').val(slug);
    });

    // Show/hide options field based on field type
    $('#field_type').on('change', function() {
        const fieldType = $(this).val();
        if (fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox' || fieldType === 'multiselect') {
            $('#options-container').show();
        } else {
            $('#options-container').hide();
        }
    });

    // Handle form submission via AJAX
    $('#dab-add-field-form').on('submit', function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const $form = $(this);
        const $submitButton = $form.find('button[type="submit"]');

        // Disable submit button and show loading state
        $submitButton.prop('disabled', true).html('<span class="dashicons dashicons-update-alt dab-spin"></span> Adding...');

        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    // Add the new field to the table
                    const $fieldsTable = $('.dab-wizard-fields-list table tbody');
                    const $noFieldsMessage = $('.dab-wizard-section:nth-child(2) > p');

                    if ($noFieldsMessage.length && $noFieldsMessage.text().includes('No fields')) {
                        // Replace the "No fields" message with a table
                        $noFieldsMessage.replaceWith(`
                            <div class="dab-wizard-fields-list">
                                <table class="dab-wizard-table">
                                    <thead>
                                        <tr>
                                            <th>Label</th>
                                            <th>Slug</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${response.data.html}
                                    </tbody>
                                </table>
                            </div>
                        `);

                        // Initialize delete buttons for the new table
                        initDeleteButtons();
                    } else if ($fieldsTable.length) {
                        // Add the new row to the existing table
                        $fieldsTable.append(response.data.html);

                        // Initialize delete button for the new row
                        initDeleteButtons();
                    }

                    // Show success message
                    const $successMessage = $('<div class="dab-wizard-notice dab-wizard-notice-success">' +
                        '<p>Field added successfully!</p>' +
                        '</div>');

                    // Add the success message after the form
                    $form.after($successMessage);

                    // Fade out the success message after 3 seconds
                    setTimeout(function() {
                        $successMessage.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 3000);

                    // Clear the form
                    $form.trigger('reset');
                    $('#options-container').hide();

                    // Trigger custom event for form submission
                    $(document).trigger('dab:form:submitted', ['dab-add-field-form']);

                    // Focus on the field label input
                    $('#field_label').focus();
                } else {
                    // Show error message
                    alert('Error adding field. Please try again.');
                }

                // Re-enable submit button
                $submitButton.prop('disabled', false).text('Add Field');
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.log('Response:', xhr.responseText);

                // Try to parse the response
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success && response.data) {
                        // If the response is actually successful despite the error status

                        // Add the new field to the table
                        const $fieldsTable = $('.dab-wizard-fields-list table tbody');
                        const $noFieldsMessage = $('.dab-wizard-section:nth-child(2) > p');

                        if ($noFieldsMessage.length && $noFieldsMessage.text().includes('No fields')) {
                            // Replace the "No fields" message with a table
                            $noFieldsMessage.replaceWith(`
                                <div class="dab-wizard-fields-list">
                                    <table class="dab-wizard-table">
                                        <thead>
                                            <tr>
                                                <th>Label</th>
                                                <th>Slug</th>
                                                <th>Type</th>
                                                <th>Required</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${response.data.html}
                                        </tbody>
                                    </table>
                                </div>
                            `);

                            // Initialize delete buttons for the new table
                            initDeleteButtons();
                        } else if ($fieldsTable.length) {
                            // Add the new row to the existing table
                            $fieldsTable.append(response.data.html);

                            // Initialize delete button for the new row
                            initDeleteButtons();
                        }

                        // Show success message
                        const $successMessage = $('<div class="dab-wizard-notice dab-wizard-notice-success">' +
                            '<p>Field added successfully!</p>' +
                            '</div>');

                        // Add the success message after the form
                        $form.after($successMessage);

                        // Fade out the success message after 3 seconds
                        setTimeout(function() {
                            $successMessage.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 3000);

                        // Clear the form
                        $form.trigger('reset');
                        $('#options-container').hide();

                        // Trigger custom event for form submission
                        $(document).trigger('dab:form:submitted', ['dab-add-field-form']);

                        // Focus on the field label input
                        $('#field_label').focus();

                        // Re-enable submit button
                        $submitButton.prop('disabled', false).text('Add Field');
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                // Show error message
                alert('Error adding field. Please try again.');

                // Re-enable submit button
                $submitButton.prop('disabled', false).text('Add Field');
            }
        });
    });

    // Function to initialize delete buttons
    function initDeleteButtons() {
        $('.delete-field').off('click').on('click', function() {
            const fieldId = $(this).data('field-id');
            const $row = $(this).closest('tr');

            if (confirm('Are you sure you want to delete this field? This action cannot be undone.')) {
                // Disable the button and show loading state
                $(this).prop('disabled', true).html('<span class="dashicons dashicons-update-alt dab-spin"></span>');

                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        dab_delete_field: 1,
                        field_id: fieldId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Remove the row with animation
                            $row.fadeOut(300, function() {
                                $(this).remove();

                                // If no fields left, show the "No fields" message
                                if ($('.dab-wizard-fields-list table tbody tr').length === 0) {
                                    $('.dab-wizard-fields-list').replaceWith('<p>No fields have been added yet.</p>');
                                }
                            });

                            // Show success message
                            const $successMessage = $('<div class="dab-wizard-notice dab-wizard-notice-success">' +
                                '<p>Field deleted successfully!</p>' +
                                '</div>');

                            // Add the success message after the table
                            $('.dab-wizard-fields-list').after($successMessage);

                            // Fade out the success message after 3 seconds
                            setTimeout(function() {
                                $successMessage.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 3000);
                        } else {
                            // Show error message
                            alert('Error deleting field. Please try again.');
                            // Re-enable the button
                            $(this).prop('disabled', false).html('<span class="dashicons dashicons-trash"></span>');
                        }
                    },
                    error: function() {
                        // Show error message
                        alert('Error deleting field. Please try again.');
                        // Re-enable the button
                        $(this).prop('disabled', false).html('<span class="dashicons dashicons-trash"></span>');
                    }
                });
            }
        });
    }

    // Initialize delete buttons on page load
    initDeleteButtons();

    // Add CSS for the spinner and buttons
    $('<style>')
        .text(`
            .dab-spin {
                animation: dab-spin 2s infinite linear;
            }
            @keyframes dab-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .dab-wizard-notice-success {
                background-color: #ecf9ec;
                border-left: 4px solid #46b450;
            }
            .dab-btn-sm {
                padding: 2px 8px;
                font-size: 12px;
            }
            .dab-btn-danger {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            .dab-btn-danger:hover {
                background-color: #c82333;
            }
            .dab-btn-danger .dashicons {
                font-size: 16px;
                width: 16px;
                height: 16px;
                vertical-align: middle;
            }
        `)
        .appendTo('head');
});
</script>
