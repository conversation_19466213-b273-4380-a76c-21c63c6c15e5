# Copyright (C) 2023 Database App Builder
# This file is distributed under the same license as the Database App Builder package.
msgid ""
msgstr ""
"Project-Id-Version: Database App Builder 1.0.4\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/db-app-builder\n"
"POT-Creation-Date: 2023-10-15 12:00:00+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2023-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: admin/class-admin-menu.php:14 admin/class-admin-menu.php:15
msgid "Database App Builder"
msgstr ""

#: admin/class-admin-menu.php:26 admin/class-admin-menu.php:27
msgid "Dashboard"
msgstr ""

#: admin/class-admin-menu.php:49 admin/class-admin-menu.php:50
msgid "Tables"
msgstr ""

#: admin/class-admin-menu.php:58 admin/class-admin-menu.php:59
msgid "Fields"
msgstr ""

#: admin/class-admin-menu.php:81 admin/class-admin-menu.php:82
msgid "Forms"
msgstr ""

#: admin/class-admin-menu.php:90 admin/class-admin-menu.php:91
msgid "Views"
msgstr ""

#: admin/class-admin-menu.php:99 admin/class-admin-menu.php:100
msgid "Dashboards"
msgstr ""

#: admin/class-admin-menu.php:122 admin/class-admin-menu.php:123
msgid "Data Management"
msgstr ""

#: admin/class-admin-menu.php:131 admin/class-admin-menu.php:132
msgid "Approval Workflows"
msgstr ""

#: admin/class-admin-menu.php:140 admin/class-admin-menu.php:141
msgid "Pending Approvals"
msgstr ""

#: admin/class-admin-menu.php:163 admin/class-admin-menu.php:164
msgid "Permissions Dashboard"
msgstr ""

#: admin/class-admin-menu.php:172 admin/class-admin-menu.php:173
msgid "Documentation"
msgstr ""

#: admin/class-admin-menu.php:181 admin/class-admin-menu.php:182
msgid "Permissions Docs"
msgstr ""

#: admin/class-admin-menu.php:190 admin/class-admin-menu.php:191
msgid "Settings"
msgstr ""

#: admin/class-admin-menu.php:199 admin/class-admin-menu.php:200
msgid "License"
msgstr ""

#: admin/class-admin-menu.php:208 admin/class-admin-menu.php:209
msgid "Uninstall Settings"
msgstr ""

#: admin/page-uninstall-settings.php:21
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: admin/page-uninstall-settings.php:31
msgid "Uninstall settings saved successfully."
msgstr ""

#: admin/page-uninstall-settings.php:38
msgid "Data Management"
msgstr ""

#: admin/page-uninstall-settings.php:39
msgid "Configure how your data should be handled when the plugin is uninstalled."
msgstr ""

#: admin/page-uninstall-settings.php:46
msgid "Data Deletion"
msgstr ""

#: admin/page-uninstall-settings.php:50
msgid "Delete all plugin data when uninstalling"
msgstr ""

#: admin/page-uninstall-settings.php:52
msgid "When checked, all plugin data (tables, settings, etc.) will be permanently deleted when the plugin is uninstalled. If unchecked, data will be preserved."
msgstr ""

#: admin/page-uninstall-settings.php:58
msgid "Export Settings"
msgstr ""

#: admin/page-uninstall-settings.php:59
msgid "You can export your settings to preserve them outside of WordPress."
msgstr ""

#: admin/page-uninstall-settings.php:60
msgid "Export Settings"
msgstr ""

#: admin/page-uninstall-settings.php:66
msgid "Save Settings"
msgstr ""

#: admin/page-uninstall-settings.php:94
msgid "Error exporting settings."
msgstr ""

#: includes/class-license-manager.php:390
msgid "Your Database App Builder license is not active. Please <a href=\"%s\">activate your license</a> to receive updates and support."
msgstr ""

#: includes/shortcode-form.php:160
msgid "Error: Form ID is required"
msgstr ""

#: includes/shortcode-form.php:168
msgid "Error: Form not found"
msgstr ""

#: includes/shortcode-form.php:213
msgid "Error: No fields found for this form. Please create fields for the table first."
msgstr ""

#: includes/shortcode-form.php:229
msgid "Success!"
msgstr ""

#: includes/shortcode-form.php:230
msgid "Form submitted successfully!"
msgstr ""

#: includes/shortcode-form.php:233
msgid "Submit another response"
msgstr ""

#: includes/shortcode-form.php:275
msgid "Submit"
msgstr ""

#: includes/class-form-builder.php:334
msgid "Select an option"
msgstr ""
