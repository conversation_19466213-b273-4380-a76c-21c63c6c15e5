<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Handle form submissions
if (isset($_POST['dab_save_order_field']) && wp_verify_nonce($_POST['dab_order_field_nonce'], 'dab_save_order_field')) {
    $field_data = array(
        'field_name' => sanitize_key($_POST['field_name']),
        'field_label' => sanitize_text_field($_POST['field_label']),
        'field_type' => sanitize_text_field($_POST['field_type']),
        'field_section' => sanitize_text_field($_POST['field_section']),
        'field_order' => intval($_POST['field_order']),
        'required' => isset($_POST['required']) ? 1 : 0,
        'show_in_checkout' => isset($_POST['show_in_checkout']) ? 1 : 0,
        'show_in_admin' => isset($_POST['show_in_admin']) ? 1 : 0,
        'show_in_email' => isset($_POST['show_in_email']) ? 1 : 0,
        'show_in_frontend' => isset($_POST['show_in_frontend']) ? 1 : 0,
    );
    
    // Handle field options for select fields
    if ($_POST['field_type'] === 'select' && !empty($_POST['field_options'])) {
        $options = array();
        $option_lines = explode("\n", $_POST['field_options']);
        foreach ($option_lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                if (strpos($line, '|') !== false) {
                    list($value, $label) = explode('|', $line, 2);
                    $options[trim($value)] = trim($label);
                } else {
                    $options[trim($line)] = trim($line);
                }
            }
        }
        $field_data['field_options'] = serialize(array('options' => $options));
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'dab_wc_order_fields';
    
    if (isset($_POST['field_id']) && !empty($_POST['field_id'])) {
        // Update existing field
        $wpdb->update(
            $table_name,
            $field_data,
            array('id' => intval($_POST['field_id'])),
            array('%s', '%s', '%s', '%s', '%d', '%d', '%d', '%d', '%d', '%d'),
            array('%d')
        );
        echo '<div class="notice notice-success"><p>' . __('Order field updated successfully.', 'db-app-builder') . '</p></div>';
    } else {
        // Insert new field
        $result = $wpdb->insert($table_name, $field_data);
        if ($result) {
            echo '<div class="notice notice-success"><p>' . __('Order field created successfully.', 'db-app-builder') . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('Error creating order field.', 'db-app-builder') . '</p></div>';
        }
    }
}

// Handle field deletion
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['field_id']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_order_field')) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'dab_wc_order_fields';
    $field_id = intval($_GET['field_id']);
    
    $wpdb->delete($table_name, array('id' => $field_id), array('%d'));
    echo '<div class="notice notice-success"><p>' . __('Order field deleted successfully.', 'db-app-builder') . '</p></div>';
}

// Get existing fields
global $wpdb;
$table_name = $wpdb->prefix . 'dab_wc_order_fields';
$fields = $wpdb->get_results("SELECT * FROM $table_name ORDER BY field_section ASC, field_order ASC, id ASC");

// Get field for editing
$editing_field = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['field_id'])) {
    $field_id = intval($_GET['field_id']);
    $editing_field = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $field_id));
}

// Group fields by section
$fields_by_section = array();
foreach ($fields as $field) {
    $fields_by_section[$field->field_section][] = $field;
}
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Order Fields', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Create custom fields for WooCommerce orders. These fields will appear during checkout and in order management.', 'db-app-builder'); ?></p>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Add/Edit Field Form -->
            <div class="dab-card">
                <h2><?php echo $editing_field ? __('Edit Order Field', 'db-app-builder') : __('Add New Order Field', 'db-app-builder'); ?></h2>
                
                <form method="post" action="">
                    <?php wp_nonce_field('dab_save_order_field', 'dab_order_field_nonce'); ?>
                    
                    <?php if ($editing_field): ?>
                        <input type="hidden" name="field_id" value="<?php echo esc_attr($editing_field->id); ?>">
                    <?php endif; ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="field_name"><?php _e('Field Name', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="field_name" name="field_name" 
                                       value="<?php echo $editing_field ? esc_attr($editing_field->field_name) : ''; ?>" 
                                       class="regular-text" required 
                                       <?php echo $editing_field ? 'readonly' : ''; ?>>
                                <p class="description"><?php _e('Unique identifier for the field (lowercase, no spaces).', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="field_label"><?php _e('Field Label', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="field_label" name="field_label" 
                                       value="<?php echo $editing_field ? esc_attr($editing_field->field_label) : ''; ?>" 
                                       class="regular-text" required>
                                <p class="description"><?php _e('Label displayed to customers and in admin.', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="field_type"><?php _e('Field Type', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <select id="field_type" name="field_type" required>
                                    <option value=""><?php _e('Select field type', 'db-app-builder'); ?></option>
                                    <option value="text" <?php selected($editing_field ? $editing_field->field_type : '', 'text'); ?>><?php _e('Text', 'db-app-builder'); ?></option>
                                    <option value="textarea" <?php selected($editing_field ? $editing_field->field_type : '', 'textarea'); ?>><?php _e('Textarea', 'db-app-builder'); ?></option>
                                    <option value="select" <?php selected($editing_field ? $editing_field->field_type : '', 'select'); ?>><?php _e('Select Dropdown', 'db-app-builder'); ?></option>
                                    <option value="checkbox" <?php selected($editing_field ? $editing_field->field_type : '', 'checkbox'); ?>><?php _e('Checkbox', 'db-app-builder'); ?></option>
                                    <option value="date" <?php selected($editing_field ? $editing_field->field_type : '', 'date'); ?>><?php _e('Date', 'db-app-builder'); ?></option>
                                    <option value="time" <?php selected($editing_field ? $editing_field->field_type : '', 'time'); ?>><?php _e('Time', 'db-app-builder'); ?></option>
                                </select>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="field_section"><?php _e('Field Section', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <select id="field_section" name="field_section" required>
                                    <option value="billing" <?php selected($editing_field ? $editing_field->field_section : '', 'billing'); ?>><?php _e('Billing', 'db-app-builder'); ?></option>
                                    <option value="shipping" <?php selected($editing_field ? $editing_field->field_section : '', 'shipping'); ?>><?php _e('Shipping', 'db-app-builder'); ?></option>
                                    <option value="order" <?php selected($editing_field ? $editing_field->field_section : '', 'order'); ?>><?php _e('Order Notes', 'db-app-builder'); ?></option>
                                </select>
                                <p class="description"><?php _e('Where the field appears in the checkout form.', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr id="field_options_row" style="display: none;">
                            <th scope="row">
                                <label for="field_options"><?php _e('Field Options', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <textarea id="field_options" name="field_options" rows="5" class="large-text"><?php 
                                    if ($editing_field && $editing_field->field_type === 'select') {
                                        $options = maybe_unserialize($editing_field->field_options);
                                        if (!empty($options['options'])) {
                                            foreach ($options['options'] as $value => $label) {
                                                echo esc_textarea($value . '|' . $label) . "\n";
                                            }
                                        }
                                    }
                                ?></textarea>
                                <p class="description"><?php _e('For select fields, enter one option per line. Format: value|label or just value', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="field_order"><?php _e('Field Order', 'db-app-builder'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="field_order" name="field_order" 
                                       value="<?php echo $editing_field ? esc_attr($editing_field->field_order) : '0'; ?>" 
                                       class="small-text">
                                <p class="description"><?php _e('Order in which the field appears (lower numbers appear first).', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Field Settings', 'db-app-builder'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="required" value="1" 
                                               <?php checked($editing_field ? $editing_field->required : 0, 1); ?>>
                                        <?php _e('Required field', 'db-app-builder'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="show_in_checkout" value="1" 
                                               <?php checked($editing_field ? $editing_field->show_in_checkout : 1, 1); ?>>
                                        <?php _e('Show in checkout', 'db-app-builder'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="show_in_admin" value="1" 
                                               <?php checked($editing_field ? $editing_field->show_in_admin : 1, 1); ?>>
                                        <?php _e('Show in admin order view', 'db-app-builder'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="show_in_email" value="1" 
                                               <?php checked($editing_field ? $editing_field->show_in_email : 1, 1); ?>>
                                        <?php _e('Show in order emails', 'db-app-builder'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="show_in_frontend" value="1" 
                                               <?php checked($editing_field ? $editing_field->show_in_frontend : 1, 1); ?>>
                                        <?php _e('Show in customer order view', 'db-app-builder'); ?>
                                    </label>
                                </fieldset>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="dab_save_order_field" class="button-primary" 
                               value="<?php echo $editing_field ? __('Update Field', 'db-app-builder') : __('Add Field', 'db-app-builder'); ?>">
                        <?php if ($editing_field): ?>
                            <a href="<?php echo admin_url('admin.php?page=dab_woocommerce_order_fields'); ?>" class="button"><?php _e('Cancel', 'db-app-builder'); ?></a>
                        <?php endif; ?>
                    </p>
                </form>
            </div>

            <!-- Existing Fields List -->
            <div class="dab-card">
                <h2><?php _e('Existing Order Fields', 'db-app-builder'); ?></h2>
                
                <?php if (empty($fields)): ?>
                    <p><?php _e('No order fields created yet.', 'db-app-builder'); ?></p>
                    <p><em><?php _e('Order fields allow you to collect additional information during checkout and display it in order management.', 'db-app-builder'); ?></em></p>
                <?php else: ?>
                    <?php foreach ($fields_by_section as $section => $section_fields): ?>
                        <h3><?php echo esc_html(ucfirst($section)); ?> <?php _e('Fields', 'db-app-builder'); ?></h3>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Field Name', 'db-app-builder'); ?></th>
                                    <th><?php _e('Label', 'db-app-builder'); ?></th>
                                    <th><?php _e('Type', 'db-app-builder'); ?></th>
                                    <th><?php _e('Required', 'db-app-builder'); ?></th>
                                    <th><?php _e('Checkout', 'db-app-builder'); ?></th>
                                    <th><?php _e('Admin', 'db-app-builder'); ?></th>
                                    <th><?php _e('Email', 'db-app-builder'); ?></th>
                                    <th><?php _e('Actions', 'db-app-builder'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($section_fields as $field): ?>
                                    <tr>
                                        <td><code><?php echo esc_html($field->field_name); ?></code></td>
                                        <td><?php echo esc_html($field->field_label); ?></td>
                                        <td><?php echo esc_html(ucfirst($field->field_type)); ?></td>
                                        <td><?php echo $field->required ? '✓' : '—'; ?></td>
                                        <td><?php echo $field->show_in_checkout ? '✓' : '—'; ?></td>
                                        <td><?php echo $field->show_in_admin ? '✓' : '—'; ?></td>
                                        <td><?php echo $field->show_in_email ? '✓' : '—'; ?></td>
                                        <td>
                                            <a href="<?php echo admin_url('admin.php?page=dab_woocommerce_order_fields&action=edit&field_id=' . $field->id); ?>" class="button button-small"><?php _e('Edit', 'db-app-builder'); ?></a>
                                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=dab_woocommerce_order_fields&action=delete&field_id=' . $field->id), 'delete_order_field'); ?>" 
                                               class="button button-small button-link-delete" 
                                               onclick="return confirm('<?php _e('Are you sure you want to delete this field?', 'db-app-builder'); ?>')"><?php _e('Delete', 'db-app-builder'); ?></a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <br>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Field Usage Guide -->
            <div class="dab-card">
                <h3><?php _e('Field Usage Guide', 'db-app-builder'); ?></h3>
                <div class="dab-usage-guide">
                    <h4><?php _e('Field Sections:', 'db-app-builder'); ?></h4>
                    <ul>
                        <li><strong><?php _e('Billing:', 'db-app-builder'); ?></strong> <?php _e('Appears in billing address section', 'db-app-builder'); ?></li>
                        <li><strong><?php _e('Shipping:', 'db-app-builder'); ?></strong> <?php _e('Appears in shipping address section', 'db-app-builder'); ?></li>
                        <li><strong><?php _e('Order:', 'db-app-builder'); ?></strong> <?php _e('Appears in order notes section', 'db-app-builder'); ?></li>
                    </ul>
                    
                    <h4><?php _e('Display Options:', 'db-app-builder'); ?></h4>
                    <ul>
                        <li><strong><?php _e('Checkout:', 'db-app-builder'); ?></strong> <?php _e('Shows during checkout process', 'db-app-builder'); ?></li>
                        <li><strong><?php _e('Admin:', 'db-app-builder'); ?></strong> <?php _e('Shows in admin order view', 'db-app-builder'); ?></li>
                        <li><strong><?php _e('Email:', 'db-app-builder'); ?></strong> <?php _e('Includes in order emails', 'db-app-builder'); ?></li>
                        <li><strong><?php _e('Frontend:', 'db-app-builder'); ?></strong> <?php _e('Shows in customer order view', 'db-app-builder'); ?></li>
                    </ul>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="<?php echo admin_url('edit.php?post_type=shop_order'); ?>"><?php _e('View Orders', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout'); ?>"><?php _e('Checkout Settings', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo admin_url('admin.php?page=wc-settings&tab=email'); ?>"><?php _e('Email Settings', 'db-app-builder'); ?></a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/hide field options based on field type
    $('#field_type').change(function() {
        if ($(this).val() === 'select') {
            $('#field_options_row').show();
        } else {
            $('#field_options_row').hide();
        }
    }).trigger('change');
    
    // Auto-generate field name from label
    $('#field_label').on('input', function() {
        if ($('#field_name').val() === '' && !$('#field_name').prop('readonly')) {
            var fieldName = $(this).val()
                .toLowerCase()
                .replace(/[^a-z0-9]/g, '_')
                .replace(/_+/g, '_')
                .replace(/^_|_$/g, '');
            $('#field_name').val(fieldName);
        }
    });
});
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-usage-guide ul {
    margin: 10px 0;
    padding-left: 20px;
}

.dab-usage-guide li {
    margin-bottom: 5px;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}
</style>
