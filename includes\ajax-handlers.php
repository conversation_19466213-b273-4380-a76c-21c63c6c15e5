<?php
if (!defined('ABSPATH')) exit;

/**
 * AJAX handler for filtering dropdown options
 */
add_action('wp_ajax_dab_filter_dropdown', 'dab_ajax_filter_dropdown');
add_action('wp_ajax_nopriv_dab_filter_dropdown', 'dab_ajax_filter_dropdown');

function dab_ajax_filter_dropdown() {
    global $wpdb;

    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dropdown_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        exit;
    }

    $parent_value = isset($_POST['parent_value']) ? sanitize_text_field($_POST['parent_value']) : '';
    $target_table_id = isset($_POST['target_table']) ? intval($_POST['target_table']) : 0;

    if (empty($target_table_id)) {
        wp_send_json_error(['message' => 'Invalid table ID']);
        exit;
    }

    // Get target table info
    $table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
        $target_table_id
    ));

    if (!$table) {
        wp_send_json_error(['message' => 'Table not found']);
        exit;
    }

    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

    // Get display column (default to id if not specified)
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d",
        $target_table_id
    ));

    $display_column = 'id';
    foreach ($fields as $field) {
        if (!empty($field->is_display_field) && $field->is_display_field == 1) {
            $display_column = $field->field_slug;
            break;
        }
    }

    // Build query based on parent value
    $query = "SELECT id, $display_column AS label FROM $data_table WHERE 1=1";
    $query_args = [];

    if (!empty($parent_value)) {
        // Find the parent field in the target table
        $parent_field = '';
        foreach ($fields as $field) {
            if ($field->field_type === 'relationship' && !empty($field->parent_field)) {
                $parent_field = $field->field_slug;
                break;
            }
        }

        if (!empty($parent_field)) {
            $query .= " AND $parent_field = %s";
            $query_args[] = $parent_value;
        }
    }

    $query .= " ORDER BY $display_column ASC";

    // Prepare the query if we have arguments
    if (!empty($query_args)) {
        $query = $wpdb->prepare($query, $query_args);
    }

    // Get filtered options
    $options = $wpdb->get_results($query);

    // Format options for response
    $formatted_options = [];
    foreach ($options as $option) {
        $formatted_options[] = [
            'value' => $option->id,
            'label' => $option->label
        ];
    }

    wp_send_json_success(['options' => $formatted_options]);
    exit;
}

/**
 * Get fields for a specific table (used by lookup fields)
 * This handler has been moved to includes/functions.php
 */

