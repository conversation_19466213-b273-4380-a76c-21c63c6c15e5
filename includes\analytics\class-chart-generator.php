<?php
/**
 * Chart Generator Class
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles chart generation for reports including bar charts, line charts,
 * pie charts, gauge charts, and other visualization types.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Chart_Generator {
    
    /**
     * Initialize the Chart Generator
     */
    public static function init() {
        add_action('wp_ajax_dab_generate_chart', array(__CLASS__, 'generate_chart'));
        add_action('wp_ajax_dab_get_chart_data', array(__CLASS__, 'get_chart_data'));
        add_action('wp_ajax_nopriv_dab_get_chart_data', array(__CLASS__, 'get_chart_data'));
    }
    
    /**
     * Generate chart configuration
     */
    public static function generate_chart() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $chart_type = sanitize_text_field($_POST['chart_type']);
        $data = $_POST['data'];
        $config = $_POST['config'];
        
        try {
            $chart_config = self::build_chart_config($chart_type, $data, $config);
            wp_send_json_success($chart_config);
        } catch (Exception $e) {
            wp_send_json_error('Chart generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get chart data for frontend display
     */
    public static function get_chart_data() {
        $chart_id = sanitize_text_field($_GET['chart_id']);
        $report_id = intval($_GET['report_id']);
        
        // Load report and generate chart data
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));
        
        if (!$report) {
            wp_send_json_error('Report not found');
            return;
        }
        
        // Check permissions for public reports
        if (!$report->is_public && !current_user_can('read')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        try {
            $result = DAB_Report_Builder::execute_report($report);
            $chart_config = json_decode($report->chart_config, true);
            
            if (isset($chart_config[$chart_id])) {
                $chart_data = self::process_chart_data($result['data'], $chart_config[$chart_id]);
                wp_send_json_success($chart_data);
            } else {
                wp_send_json_error('Chart configuration not found');
            }
        } catch (Exception $e) {
            wp_send_json_error('Failed to generate chart data: ' . $e->getMessage());
        }
    }
    
    /**
     * Build chart configuration based on type and data
     */
    public static function build_chart_config($chart_type, $data, $config) {
        $chart_config = array(
            'type' => $chart_type,
            'data' => array(),
            'options' => array()
        );
        
        switch ($chart_type) {
            case 'bar':
            case 'column':
                $chart_config = self::build_bar_chart($data, $config);
                break;
                
            case 'line':
                $chart_config = self::build_line_chart($data, $config);
                break;
                
            case 'pie':
            case 'doughnut':
                $chart_config = self::build_pie_chart($data, $config);
                break;
                
            case 'area':
                $chart_config = self::build_area_chart($data, $config);
                break;
                
            case 'scatter':
                $chart_config = self::build_scatter_chart($data, $config);
                break;
                
            case 'gauge':
                $chart_config = self::build_gauge_chart($data, $config);
                break;
                
            case 'funnel':
                $chart_config = self::build_funnel_chart($data, $config);
                break;
                
            case 'heatmap':
                $chart_config = self::build_heatmap_chart($data, $config);
                break;
                
            default:
                throw new Exception('Unsupported chart type: ' . $chart_type);
        }
        
        return $chart_config;
    }
    
    /**
     * Build bar/column chart configuration
     */
    private static function build_bar_chart($data, $config) {
        $labels = array();
        $datasets = array();
        
        $x_field = $config['x_field'];
        $y_field = $config['y_field'];
        $group_field = isset($config['group_field']) ? $config['group_field'] : null;
        
        if ($group_field) {
            // Grouped bar chart
            $grouped_data = self::group_data($data, $group_field);
            $colors = self::generate_colors(count($grouped_data));
            $color_index = 0;
            
            foreach ($grouped_data as $group => $group_data) {
                $dataset = array(
                    'label' => $group,
                    'data' => array(),
                    'backgroundColor' => $colors[$color_index],
                    'borderColor' => $colors[$color_index],
                    'borderWidth' => 1
                );
                
                foreach ($group_data as $row) {
                    if (!in_array($row->$x_field, $labels)) {
                        $labels[] = $row->$x_field;
                    }
                    $dataset['data'][] = floatval($row->$y_field);
                }
                
                $datasets[] = $dataset;
                $color_index++;
            }
        } else {
            // Simple bar chart
            $chart_data = array();
            foreach ($data as $row) {
                $labels[] = $row->$x_field;
                $chart_data[] = floatval($row->$y_field);
            }
            
            $datasets[] = array(
                'label' => $config['label'] ?? $y_field,
                'data' => $chart_data,
                'backgroundColor' => self::generate_colors(1)[0],
                'borderColor' => self::generate_colors(1)[0],
                'borderWidth' => 1
            );
        }
        
        return array(
            'type' => 'bar',
            'data' => array(
                'labels' => $labels,
                'datasets' => $datasets
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Bar Chart'
                    ),
                    'legend' => array(
                        'display' => !empty($group_field)
                    )
                ),
                'scales' => array(
                    'y' => array(
                        'beginAtZero' => true
                    )
                )
            )
        );
    }
    
    /**
     * Build line chart configuration
     */
    private static function build_line_chart($data, $config) {
        $labels = array();
        $datasets = array();
        
        $x_field = $config['x_field'];
        $y_field = $config['y_field'];
        $group_field = isset($config['group_field']) ? $config['group_field'] : null;
        
        if ($group_field) {
            // Multi-line chart
            $grouped_data = self::group_data($data, $group_field);
            $colors = self::generate_colors(count($grouped_data));
            $color_index = 0;
            
            foreach ($grouped_data as $group => $group_data) {
                $dataset = array(
                    'label' => $group,
                    'data' => array(),
                    'borderColor' => $colors[$color_index],
                    'backgroundColor' => $colors[$color_index] . '20',
                    'fill' => false,
                    'tension' => 0.1
                );
                
                foreach ($group_data as $row) {
                    if (!in_array($row->$x_field, $labels)) {
                        $labels[] = $row->$x_field;
                    }
                    $dataset['data'][] = floatval($row->$y_field);
                }
                
                $datasets[] = $dataset;
                $color_index++;
            }
        } else {
            // Simple line chart
            $chart_data = array();
            foreach ($data as $row) {
                $labels[] = $row->$x_field;
                $chart_data[] = floatval($row->$y_field);
            }
            
            $datasets[] = array(
                'label' => $config['label'] ?? $y_field,
                'data' => $chart_data,
                'borderColor' => self::generate_colors(1)[0],
                'backgroundColor' => self::generate_colors(1)[0] . '20',
                'fill' => false,
                'tension' => 0.1
            );
        }
        
        return array(
            'type' => 'line',
            'data' => array(
                'labels' => $labels,
                'datasets' => $datasets
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Line Chart'
                    )
                ),
                'scales' => array(
                    'y' => array(
                        'beginAtZero' => true
                    )
                )
            )
        );
    }
    
    /**
     * Build pie chart configuration
     */
    private static function build_pie_chart($data, $config) {
        $labels = array();
        $chart_data = array();
        $colors = array();
        
        $label_field = $config['label_field'];
        $value_field = $config['value_field'];
        
        $color_palette = self::generate_colors(count($data));
        
        foreach ($data as $index => $row) {
            $labels[] = $row->$label_field;
            $chart_data[] = floatval($row->$value_field);
            $colors[] = $color_palette[$index % count($color_palette)];
        }
        
        return array(
            'type' => 'pie',
            'data' => array(
                'labels' => $labels,
                'datasets' => array(
                    array(
                        'data' => $chart_data,
                        'backgroundColor' => $colors,
                        'borderWidth' => 1
                    )
                )
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Pie Chart'
                    ),
                    'legend' => array(
                        'position' => 'bottom'
                    )
                )
            )
        );
    }
    
    /**
     * Build gauge chart configuration
     */
    private static function build_gauge_chart($data, $config) {
        $value_field = $config['value_field'];
        $max_value = $config['max_value'] ?? 100;
        $min_value = $config['min_value'] ?? 0;
        
        // Calculate average or use first value
        $value = 0;
        if (count($data) > 0) {
            if (count($data) === 1) {
                $value = floatval($data[0]->$value_field);
            } else {
                $total = 0;
                foreach ($data as $row) {
                    $total += floatval($row->$value_field);
                }
                $value = $total / count($data);
            }
        }
        
        return array(
            'type' => 'gauge',
            'data' => array(
                'datasets' => array(
                    array(
                        'value' => $value,
                        'minValue' => $min_value,
                        'maxValue' => $max_value,
                        'backgroundColor' => array('#ff6384', '#36a2eb', '#ffce56'),
                        'borderWidth' => 2
                    )
                )
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Gauge Chart'
                    )
                )
            )
        );
    }
    
    /**
     * Process chart data for frontend display
     */
    private static function process_chart_data($data, $chart_config) {
        // Apply any data transformations needed for the specific chart
        return self::build_chart_config($chart_config['type'], $data, $chart_config);
    }
    
    /**
     * Group data by a specific field
     */
    private static function group_data($data, $group_field) {
        $grouped = array();
        
        foreach ($data as $row) {
            $group_value = $row->$group_field;
            if (!isset($grouped[$group_value])) {
                $grouped[$group_value] = array();
            }
            $grouped[$group_value][] = $row;
        }
        
        return $grouped;
    }
    
    /**
     * Generate color palette for charts
     */
    private static function generate_colors($count) {
        $base_colors = array(
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        );
        
        $colors = array();
        for ($i = 0; $i < $count; $i++) {
            $colors[] = $base_colors[$i % count($base_colors)];
        }
        
        return $colors;
    }
    
    /**
     * Build area chart configuration
     */
    private static function build_area_chart($data, $config) {
        $line_config = self::build_line_chart($data, $config);
        $line_config['type'] = 'line';
        
        // Make it filled
        foreach ($line_config['data']['datasets'] as &$dataset) {
            $dataset['fill'] = true;
        }
        
        return $line_config;
    }
    
    /**
     * Build scatter chart configuration
     */
    private static function build_scatter_chart($data, $config) {
        $x_field = $config['x_field'];
        $y_field = $config['y_field'];
        
        $chart_data = array();
        foreach ($data as $row) {
            $chart_data[] = array(
                'x' => floatval($row->$x_field),
                'y' => floatval($row->$y_field)
            );
        }
        
        return array(
            'type' => 'scatter',
            'data' => array(
                'datasets' => array(
                    array(
                        'label' => $config['label'] ?? 'Scatter Plot',
                        'data' => $chart_data,
                        'backgroundColor' => self::generate_colors(1)[0]
                    )
                )
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Scatter Chart'
                    )
                ),
                'scales' => array(
                    'x' => array(
                        'type' => 'linear',
                        'position' => 'bottom'
                    )
                )
            )
        );
    }
    
    /**
     * Build funnel chart configuration
     */
    private static function build_funnel_chart($data, $config) {
        $label_field = $config['label_field'];
        $value_field = $config['value_field'];
        
        $labels = array();
        $values = array();
        
        foreach ($data as $row) {
            $labels[] = $row->$label_field;
            $values[] = floatval($row->$value_field);
        }
        
        return array(
            'type' => 'funnel',
            'data' => array(
                'labels' => $labels,
                'datasets' => array(
                    array(
                        'data' => $values,
                        'backgroundColor' => self::generate_colors(count($values))
                    )
                )
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Funnel Chart'
                    )
                )
            )
        );
    }
    
    /**
     * Build heatmap chart configuration
     */
    private static function build_heatmap_chart($data, $config) {
        $x_field = $config['x_field'];
        $y_field = $config['y_field'];
        $value_field = $config['value_field'];
        
        $chart_data = array();
        foreach ($data as $row) {
            $chart_data[] = array(
                'x' => $row->$x_field,
                'y' => $row->$y_field,
                'v' => floatval($row->$value_field)
            );
        }
        
        return array(
            'type' => 'heatmap',
            'data' => array(
                'datasets' => array(
                    array(
                        'data' => $chart_data,
                        'backgroundColor' => function($context) {
                            $value = $context['parsed']['v'];
                            $alpha = $value / 100; // Normalize to 0-1
                            return "rgba(255, 99, 132, $alpha)";
                        }
                    )
                )
            ),
            'options' => array(
                'responsive' => true,
                'plugins' => array(
                    'title' => array(
                        'display' => true,
                        'text' => $config['title'] ?? 'Heatmap Chart'
                    )
                )
            )
        );
    }
}
