<?php
/**
 * Test script for autoincrement field functionality
 * 
 * This script simulates creating a record with an autoincrement field
 * to verify that the autoincrement field works correctly.
 */

// Load WordPress
require_once(dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to run this test.');
}

// Create a test table if it doesn't exist
function create_test_table() {
    global $wpdb;
    $tables_table = $wpdb->prefix . 'dab_tables';
    $fields_table = $wpdb->prefix . 'dab_fields';
    
    // Check if test table exists
    $test_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE table_slug = %s",
        'autoincrement_test'
    ));
    
    if (!$test_table) {
        // Create test table
        $wpdb->insert($tables_table, array(
            'table_label' => 'Autoincrement Test',
            'table_slug' => 'autoincrement_test',
            'description' => 'Test table for autoincrement field',
            'created_at' => current_time('mysql')
        ));
        
        $table_id = $wpdb->insert_id;
        
        // Create data table
        DAB_DB_Manager::create_data_table('autoincrement_test');
        
        // Create autoincrement field
        $wpdb->insert($fields_table, array(
            'table_id' => $table_id,
            'field_label' => 'Student ID',
            'field_slug' => 'student_id',
            'field_type' => 'autoincrement',
            'required' => 1,
            'options' => json_encode(array(
                'prefix' => 'STU',
                'start_number' => 1,
                'padding' => 3,
                'current_number' => 0
            )),
            'field_order' => 1,
            'created_at' => current_time('mysql')
        ));
        
        // Add column to data table
        $data_table = $wpdb->prefix . 'dab_autoincrement_test';
        DAB_DB_Manager::add_column_to_table($data_table, 'student_id', 'autoincrement');
        
        // Create name field
        $wpdb->insert($fields_table, array(
            'table_id' => $table_id,
            'field_label' => 'Name',
            'field_slug' => 'name',
            'field_type' => 'text',
            'required' => 1,
            'field_order' => 2,
            'created_at' => current_time('mysql')
        ));
        
        // Add column to data table
        DAB_DB_Manager::add_column_to_table($data_table, 'name', 'text');
        
        return $table_id;
    }
    
    return $test_table->id;
}

// Create a test record
function create_test_record($table_id, $name) {
    global $wpdb;
    $tables_table = $wpdb->prefix . 'dab_tables';
    
    // Get table slug
    $table_slug = $wpdb->get_var($wpdb->prepare(
        "SELECT table_slug FROM $tables_table WHERE id = %d",
        $table_id
    ));
    
    // Get data table name
    $data_table = $wpdb->prefix . 'dab_' . $table_slug;
    
    // Create record data
    $record_data = array(
        'name' => $name,
        'created_at' => current_time('mysql')
    );
    
    // Apply autoincrement field processing
    $autoincrement_handler = new DAB_Autoincrement_Field();
    $record_data = $autoincrement_handler->filter_record_data_before_insert($record_data, $table_id);
    
    // Insert record
    $wpdb->insert($data_table, $record_data);
    
    return $wpdb->insert_id;
}

// Get all test records
function get_test_records($table_id) {
    global $wpdb;
    $tables_table = $wpdb->prefix . 'dab_tables';
    
    // Get table slug
    $table_slug = $wpdb->get_var($wpdb->prepare(
        "SELECT table_slug FROM $tables_table WHERE id = %d",
        $table_id
    ));
    
    // Get data table name
    $data_table = $wpdb->prefix . 'dab_' . $table_slug;
    
    // Get records
    $records = $wpdb->get_results("SELECT * FROM $data_table ORDER BY id ASC");
    
    return $records;
}

// Run the test
echo '<h1>Autoincrement Field Test</h1>';

// Create test table
$table_id = create_test_table();
echo '<p>Test table created with ID: ' . $table_id . '</p>';

// Create test records
$record1_id = create_test_record($table_id, 'John Doe');
echo '<p>Test record 1 created with ID: ' . $record1_id . '</p>';

$record2_id = create_test_record($table_id, 'Jane Smith');
echo '<p>Test record 2 created with ID: ' . $record2_id . '</p>';

$record3_id = create_test_record($table_id, 'Bob Johnson');
echo '<p>Test record 3 created with ID: ' . $record3_id . '</p>';

// Get all records
$records = get_test_records($table_id);

// Display records
echo '<h2>Test Records</h2>';
echo '<table border="1" cellpadding="5">';
echo '<tr><th>ID</th><th>Student ID</th><th>Name</th></tr>';
foreach ($records as $record) {
    echo '<tr>';
    echo '<td>' . $record->id . '</td>';
    echo '<td>' . $record->student_id . '</td>';
    echo '<td>' . $record->name . '</td>';
    echo '</tr>';
}
echo '</table>';

echo '<p>Test completed successfully!</p>';
