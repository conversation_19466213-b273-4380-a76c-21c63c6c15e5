<?php
/**
 * License API
 *
 * Handles license API endpoints for the Database App Builder plugin.
 *
 * @package    Database_Admin_Builder
 * @subpackage Database_Admin_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_License_API {

    /**
     * The single instance of the class.
     *
     * @var DAB_License_API
     */
    protected static $_instance = null;

    /**
     * Main License API Instance.
     *
     * Ensures only one instance of License API is loaded or can be loaded.
     *
     * @return DAB_License_API - Main instance.
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor.
     */
    public function __construct() {
        // Register REST API routes
        add_action('rest_api_init', array($this, 'register_routes'));
    }

    /**
     * Register REST API routes.
     */
    public function register_routes() {
        register_rest_route('dab-license/v1', '/activate', array(
            'methods' => 'POST',
            'callback' => array($this, 'activate_license'),
            'permission_callback' => array($this, 'check_admin_permission'),
        ));
        
        register_rest_route('dab-license/v1', '/deactivate', array(
            'methods' => 'POST',
            'callback' => array($this, 'deactivate_license'),
            'permission_callback' => array($this, 'check_admin_permission'),
        ));
        
        register_rest_route('dab-license/v1', '/check', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_license'),
            'permission_callback' => array($this, 'check_admin_permission'),
        ));
        
        register_rest_route('dab-license/v1', '/validate', array(
            'methods' => 'POST',
            'callback' => array($this, 'validate_license'),
            'permission_callback' => '__return_true', // Public endpoint
        ));
    }

    /**
     * Check if user has admin permission.
     *
     * @return bool Whether the user has admin permission.
     */
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }

    /**
     * Activate license.
     *
     * @param WP_REST_Request $request REST API request.
     * @return WP_REST_Response REST API response.
     */
    public function activate_license($request) {
        // Get license key from request
        $license_key = $request->get_param('license_key');
        
        if (empty($license_key)) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License key is required.', 'db-app-builder'),
            ), 400);
        }
        
        // Validate license key
        $license = $this->validate_license_key($license_key);
        
        if (!$license) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('Invalid license key.', 'db-app-builder'),
            ), 400);
        }
        
        // Check if license is already activated
        if ($license->activation_count >= $license->activation_limit) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License has reached its activation limit.', 'db-app-builder'),
            ), 400);
        }
        
        // Check if license has expired
        if (!empty($license->expires_at) && strtotime($license->expires_at) < time()) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License has expired.', 'db-app-builder'),
            ), 400);
        }
        
        // Update license status
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_license_keys';
        
        $wpdb->update(
            $table_name,
            array(
                'status' => 'valid',
                'activation_count' => $license->activation_count + 1,
            ),
            array('id' => $license->id),
            array('%s', '%d'),
            array('%d')
        );
        
        // Return success response
        return new WP_REST_Response(array(
            'success' => true,
            'license' => 'valid',
            'expires' => $license->expires_at,
            'message' => __('License activated successfully.', 'db-app-builder'),
        ), 200);
    }

    /**
     * Deactivate license.
     *
     * @param WP_REST_Request $request REST API request.
     * @return WP_REST_Response REST API response.
     */
    public function deactivate_license($request) {
        // Get license key from request
        $license_key = $request->get_param('license_key');
        
        if (empty($license_key)) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License key is required.', 'db-app-builder'),
            ), 400);
        }
        
        // Validate license key
        $license = $this->validate_license_key($license_key);
        
        if (!$license) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('Invalid license key.', 'db-app-builder'),
            ), 400);
        }
        
        // Update license status
        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_license_keys';
        
        $wpdb->update(
            $table_name,
            array(
                'status' => 'inactive',
                'activation_count' => max(0, $license->activation_count - 1),
            ),
            array('id' => $license->id),
            array('%s', '%d'),
            array('%d')
        );
        
        // Return success response
        return new WP_REST_Response(array(
            'success' => true,
            'license' => 'inactive',
            'message' => __('License deactivated successfully.', 'db-app-builder'),
        ), 200);
    }

    /**
     * Check license.
     *
     * @param WP_REST_Request $request REST API request.
     * @return WP_REST_Response REST API response.
     */
    public function check_license($request) {
        // Get license key from request
        $license_key = $request->get_param('license_key');
        
        if (empty($license_key)) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License key is required.', 'db-app-builder'),
            ), 400);
        }
        
        // Validate license key
        $license = $this->validate_license_key($license_key);
        
        if (!$license) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('Invalid license key.', 'db-app-builder'),
            ), 400);
        }
        
        // Check if license has expired
        $status = $license->status;
        if (!empty($license->expires_at) && strtotime($license->expires_at) < time()) {
            $status = 'expired';
            
            // Update license status
            global $wpdb;
            $table_name = $wpdb->prefix . 'dab_license_keys';
            
            $wpdb->update(
                $table_name,
                array('status' => 'expired'),
                array('id' => $license->id),
                array('%s'),
                array('%d')
            );
        }
        
        // Return license status
        return new WP_REST_Response(array(
            'success' => true,
            'license' => $status,
            'expires' => $license->expires_at,
            'activation_count' => $license->activation_count,
            'activation_limit' => $license->activation_limit,
            'message' => __('License status retrieved successfully.', 'db-app-builder'),
        ), 200);
    }

    /**
     * Validate license.
     *
     * @param WP_REST_Request $request REST API request.
     * @return WP_REST_Response REST API response.
     */
    public function validate_license($request) {
        // Get license key from request
        $license_key = $request->get_param('license_key');
        $url = $request->get_param('url');
        
        if (empty($license_key)) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('License key is required.', 'db-app-builder'),
            ), 400);
        }
        
        // Validate license key
        $license = $this->validate_license_key($license_key);
        
        if (!$license) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => __('Invalid license key.', 'db-app-builder'),
            ), 400);
        }
        
        // Check if license has expired
        $status = $license->status;
        if (!empty($license->expires_at) && strtotime($license->expires_at) < time()) {
            $status = 'expired';
        }
        
        // Return license status
        return new WP_REST_Response(array(
            'success' => true,
            'license' => $status,
            'expires' => $license->expires_at,
            'message' => __('License validated successfully.', 'db-app-builder'),
        ), 200);
    }

    /**
     * Validate license key.
     *
     * @param string $license_key License key.
     * @return object|false License object or false if invalid.
     */
    private function validate_license_key($license_key) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dab_license_keys';
        
        $license = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE license_key = %s",
                $license_key
            )
        );
        
        return $license;
    }
}

// Initialize License API
function DAB_License_API() {
    return DAB_License_API::instance();
}

// Global for backwards compatibility
$GLOBALS['dab_license_api'] = DAB_License_API();
