/**
 * Multi-select Field Scripts
 *
 * JavaScript for multi-select dropdown fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all multi-select fields
    function initMultiselectFields() {
        $('.dab-multiselect-field').each(function() {
            var $field = $(this);
            var $select = $field.find('.dab-multiselect');
            var $value = $field.find('input[type="hidden"]');
            
            // Skip if already initialized
            if ($field.data('initialized')) {
                return;
            }
            
            // Mark as initialized
            $field.data('initialized', true);
            
            // Initialize Select2
            $select.select2({
                placeholder: $select.data('placeholder'),
                allowClear: true,
                multiple: true,
                width: '100%',
                maximumSelectionLength: $select.data('max-selections') > 0 ? $select.data('max-selections') : null,
                minimumResultsForSearch: $select.data('allow-search') ? 0 : Infinity
            });
            
            // Add "Select All" option if enabled
            if ($select.data('select-all')) {
                addSelectAllOption($select);
            }
            
            // Update the hidden value field when selection changes
            $select.on('change', function() {
                updateValue($select, $value);
            });
            
            // Initialize the hidden value
            updateValue($select, $value);
        });
    }
    
    // Function to update the hidden value field
    function updateValue($select, $value) {
        var selectedValues = $select.val() || [];
        
        // Remove "select-all" from the actual values if it exists
        var valueIndex = selectedValues.indexOf('select-all');
        if (valueIndex > -1) {
            selectedValues.splice(valueIndex, 1);
        }
        
        // Update the hidden value field with JSON
        $value.val(JSON.stringify(selectedValues));
    }
    
    // Function to add "Select All" option
    function addSelectAllOption($select) {
        // Check if "Select All" option already exists
        if ($select.find('option[value="select-all"]').length === 0) {
            // Add the "Select All" option at the top
            $select.prepend('<option value="select-all">' + dabMultiselectL10n.selectAll + '</option>');
        }
        
        // Handle "Select All" option
        $select.on('change', function(e) {
            if (e.target.value === 'select-all') {
                var allOptions = [];
                $select.find('option').each(function() {
                    var value = $(this).val();
                    if (value !== 'select-all') {
                        allOptions.push(value);
                    }
                });
                
                $select.val(allOptions);
                $select.trigger('change');
            }
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        // Define localization object if not defined
        if (typeof dabMultiselectL10n === 'undefined') {
            window.dabMultiselectL10n = {
                selectAll: 'Select All'
            };
        }
        
        initMultiselectFields();
        
        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            initMultiselectFields();
        });
    });
    
    // Add a global function to format multiselect values
    window.dabFormatMultiselect = function(value, options) {
        options = options || {};
        
        // Parse the value
        var selectedValues = [];
        if (!value) {
            return '';
        }
        
        if (typeof value === 'string' && value.indexOf('[') === 0) {
            try {
                selectedValues = JSON.parse(value);
                if (!Array.isArray(selectedValues)) {
                    selectedValues = [];
                }
            } catch (e) {
                // If parsing fails, try comma-separated format
                selectedValues = value.split(',').map(function(item) {
                    return item.trim();
                });
            }
        } else if (typeof value === 'string') {
            // Handle comma-separated values
            selectedValues = value.split(',').map(function(item) {
                return item.trim();
            });
        } else if (Array.isArray(value)) {
            selectedValues = value;
        }
        
        if (selectedValues.length === 0) {
            return '';
        }
        
        // Format options
        var labelMap = options.labelMap || {};
        var separator = options.separator || ', ';
        var maxItems = options.maxItems || 0;
        
        // Map values to labels
        var formattedValues = selectedValues.map(function(val) {
            return labelMap[val] || val;
        });
        
        // Limit the number of items if specified
        if (maxItems > 0 && formattedValues.length > maxItems) {
            var visibleItems = formattedValues.slice(0, maxItems);
            var remainingCount = formattedValues.length - maxItems;
            return visibleItems.join(separator) + ' ' + 
                   '(+' + remainingCount + ' ' + (remainingCount === 1 ? 'more' : 'more') + ')';
        }
        
        // Join the formatted values
        return formattedValues.join(separator);
    };
})(jQuery);
