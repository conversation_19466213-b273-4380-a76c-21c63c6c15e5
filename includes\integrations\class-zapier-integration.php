<?php
/**
 * Zapier Integration
 *
 * Handles integration with Zapier webhooks to send form submissions to Zapier
 */
if (!defined('ABSPATH')) exit;

class DAB_Zapier_Integration {

    /**
     * Initialize the class
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_test_zapier_webhook', array($this, 'ajax_test_webhook'));
    }

    /**
     * Check if Zapier integration is enabled for a form
     *
     * @param int $form_id Form ID
     * @return bool Whether Zapier integration is enabled
     */
    public static function is_enabled($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';
        
        $enabled = $wpdb->get_var($wpdb->prepare(
            "SELECT zapier_enabled FROM $forms_table WHERE id = %d",
            $form_id
        ));
        
        return !empty($enabled);
    }

    /**
     * Get webhook URLs for a form
     *
     * @param int $form_id Form ID
     * @return array Array of webhook URLs
     */
    public static function get_webhooks($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';
        
        $webhooks = $wpdb->get_var($wpdb->prepare(
            "SELECT zapier_webhooks FROM $forms_table WHERE id = %d",
            $form_id
        ));
        
        if (empty($webhooks)) {
            return array();
        }
        
        $webhooks = maybe_unserialize($webhooks);
        
        return is_array($webhooks) ? $webhooks : array();
    }

    /**
     * Send form data to Zapier webhooks
     *
     * @param int $form_id Form ID
     * @param array $data Form data
     * @return array Array of results, with webhook URL as key and result as value
     */
    public static function send_to_zapier($form_id, $data) {
        // Check if integration is enabled
        if (!self::is_enabled($form_id)) {
            return array('error' => 'Zapier integration is not enabled for this form');
        }
        
        // Get webhook URLs
        $webhooks = self::get_webhooks($form_id);
        
        if (empty($webhooks)) {
            return array('error' => 'No webhook URLs configured for this form');
        }
        
        // Send data to each webhook
        $results = array();
        
        foreach ($webhooks as $webhook) {
            $result = self::send_to_webhook($webhook, $data);
            $results[$webhook] = $result;
        }
        
        return $results;
    }

    /**
     * Send data to a webhook URL
     *
     * @param string $webhook_url Webhook URL
     * @param array $data Data to send
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function send_to_webhook($webhook_url, $data) {
        // Validate webhook URL
        if (empty($webhook_url) || !filter_var($webhook_url, FILTER_VALIDATE_URL)) {
            return new WP_Error('invalid_webhook_url', __('Invalid webhook URL', 'db-app-builder'));
        }
        
        // Add timestamp
        $data['timestamp'] = current_time('mysql');
        
        // Send data to webhook
        $response = wp_remote_post($webhook_url, array(
            'method' => 'POST',
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data),
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code < 200 || $response_code >= 300) {
            $response_body = wp_remote_retrieve_body($response);
            return new WP_Error(
                'webhook_error',
                sprintf(
                    __('Webhook returned error: %s (HTTP %d)', 'db-app-builder'),
                    $response_body,
                    $response_code
                )
            );
        }
        
        return true;
    }

    /**
     * AJAX handler to test a webhook URL
     */
    public function ajax_test_webhook() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_zapier_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }
        
        // Check webhook URL
        if (!isset($_POST['webhook_url']) || empty($_POST['webhook_url'])) {
            wp_send_json_error(array('message' => __('Webhook URL is required', 'db-app-builder')));
        }
        
        $webhook_url = esc_url_raw($_POST['webhook_url']);
        
        // Send test data to webhook
        $test_data = array(
            'test' => true,
            'message' => 'This is a test from Database App Builder',
            'timestamp' => current_time('mysql'),
        );
        
        $result = self::send_to_webhook($webhook_url, $test_data);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Webhook test successful!', 'db-app-builder')));
    }
}

// Initialize the Zapier integration
$dab_zapier_integration = new DAB_Zapier_Integration();
