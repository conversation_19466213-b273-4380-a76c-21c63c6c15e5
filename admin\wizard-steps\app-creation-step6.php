<?php
/**
 * Application Creation Wizard - Step 6: Completion
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get saved data
$table_id = isset($progress['data']['table_id']) ? intval($progress['data']['table_id']) : 0;
$form_id = isset($progress['data']['form_id']) ? intval($progress['data']['form_id']) : 0;
$view_id = isset($progress['data']['view_id']) ? intval($progress['data']['view_id']) : 0;

// Check if table exists
$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$forms_table = $wpdb->prefix . 'dab_forms';
$views_table = $wpdb->prefix . 'dab_views';

$table_info = null;
if ($table_id) {
    $table_info = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $table_id
    ));
}

// Get fields count
$fields_count = 0;
if ($table_id) {
    $fields_count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $fields_table WHERE table_id = %d",
        $table_id
    ));
}

// Get form info
$form_info = null;
if ($form_id) {
    $form_info = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $forms_table WHERE id = %d",
        $form_id
    ));
}

// Get view info
$view_info = null;
if ($view_id) {
    $view_info = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $views_table WHERE id = %d",
        $view_id
    ));
}

// Generate a sample page with shortcodes if requested
if (isset($_POST['dab_create_page']) && $_POST['dab_create_page'] === '1') {
    $page_title = sanitize_text_field($_POST['page_title']);
    
    // Create the page
    $page_content = '';
    
    if ($form_id) {
        $page_content .= "<!-- Form Shortcode -->\n";
        $page_content .= "[dab_form id=\"$form_id\"]\n\n";
    }
    
    if ($view_id) {
        $page_content .= "<!-- View Shortcode -->\n";
        $page_content .= "[dab_view id=\"$view_id\"]\n";
    }
    
    $page_id = wp_insert_post([
        'post_title' => $page_title,
        'post_content' => $page_content,
        'post_status' => 'publish',
        'post_type' => 'page',
    ]);
    
    if ($page_id) {
        $progress['data']['page_id'] = $page_id;
        update_option('dab_wizard_progress', [$wizard_type => $progress]);
    }
}
?>

<div class="dab-wizard-form">
    <?php if (!$table_id || !$table_info): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Your application setup is incomplete. Please go back and complete the previous steps.', 'db-app-builder'); ?></p>
        </div>
    <?php else: ?>
        <div class="dab-wizard-completion">
            <div class="dab-wizard-completion-header">
                <div class="dab-wizard-completion-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <h2 class="dab-wizard-completion-title"><?php _e('Application Setup Complete!', 'db-app-builder'); ?></h2>
                <p class="dab-wizard-completion-subtitle"><?php _e('Your database application has been successfully created.', 'db-app-builder'); ?></p>
            </div>
            
            <div class="dab-wizard-completion-summary">
                <h3><?php _e('Application Summary', 'db-app-builder'); ?></h3>
                
                <div class="dab-wizard-summary-item">
                    <div class="dab-wizard-summary-icon">
                        <span class="dashicons dashicons-database"></span>
                    </div>
                    <div class="dab-wizard-summary-content">
                        <h4><?php _e('Table', 'db-app-builder'); ?></h4>
                        <p><strong><?php echo esc_html($table_info->table_label); ?></strong> (<?php echo esc_html($table_info->table_slug); ?>)</p>
                        <p><?php printf(_n('%d field', '%d fields', $fields_count, 'db-app-builder'), $fields_count); ?></p>
                        <a href="<?php echo admin_url('admin.php?page=dab_fields&table_id=' . $table_id); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary"><?php _e('Manage Fields', 'db-app-builder'); ?></a>
                    </div>
                </div>
                
                <?php if ($form_info): ?>
                    <div class="dab-wizard-summary-item">
                        <div class="dab-wizard-summary-icon">
                            <span class="dashicons dashicons-feedback"></span>
                        </div>
                        <div class="dab-wizard-summary-content">
                            <h4><?php _e('Form', 'db-app-builder'); ?></h4>
                            <p><strong><?php echo esc_html($form_info->form_name); ?></strong></p>
                            <p><?php _e('Shortcode:', 'db-app-builder'); ?> <code>[dab_form id="<?php echo esc_attr($form_info->id); ?>"]</code></p>
                            <a href="<?php echo admin_url('admin.php?page=dab_forms&action=edit&id=' . $form_info->id); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary"><?php _e('Edit Form', 'db-app-builder'); ?></a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($view_info): ?>
                    <div class="dab-wizard-summary-item">
                        <div class="dab-wizard-summary-icon">
                            <span class="dashicons dashicons-list-view"></span>
                        </div>
                        <div class="dab-wizard-summary-content">
                            <h4><?php _e('View', 'db-app-builder'); ?></h4>
                            <p><strong><?php echo esc_html($view_info->view_name); ?></strong></p>
                            <p><?php _e('Shortcode:', 'db-app-builder'); ?> <code>[dab_view id="<?php echo esc_attr($view_info->id); ?>"]</code></p>
                            <a href="<?php echo admin_url('admin.php?page=dab_views&action=edit&id=' . $view_info->id); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary"><?php _e('Edit View', 'db-app-builder'); ?></a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($progress['data']['page_id'])): 
                    $page_id = $progress['data']['page_id'];
                    $page_url = get_permalink($page_id);
                    $page_title = get_the_title($page_id);
                ?>
                    <div class="dab-wizard-summary-item">
                        <div class="dab-wizard-summary-icon">
                            <span class="dashicons dashicons-admin-page"></span>
                        </div>
                        <div class="dab-wizard-summary-content">
                            <h4><?php _e('Page', 'db-app-builder'); ?></h4>
                            <p><strong><?php echo esc_html($page_title); ?></strong></p>
                            <a href="<?php echo esc_url($page_url); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary" target="_blank"><?php _e('View Page', 'db-app-builder'); ?></a>
                            <a href="<?php echo admin_url('post.php?post=' . $page_id . '&action=edit'); ?>" class="dab-btn dab-btn-sm dab-btn-outline-primary"><?php _e('Edit Page', 'db-app-builder'); ?></a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="dab-wizard-summary-item">
                        <div class="dab-wizard-summary-icon">
                            <span class="dashicons dashicons-admin-page"></span>
                        </div>
                        <div class="dab-wizard-summary-content">
                            <h4><?php _e('Create a Page', 'db-app-builder'); ?></h4>
                            <p><?php _e('Create a WordPress page with your form and view shortcodes.', 'db-app-builder'); ?></p>
                            
                            <form id="dab-create-page" method="post">
                                <input type="hidden" name="dab_create_page" value="1">
                                <div class="dab-wizard-form-inline">
                                    <input type="text" name="page_title" placeholder="<?php _e('Page Title', 'db-app-builder'); ?>" class="dab-wizard-form-input" required>
                                    <button type="submit" class="dab-btn dab-btn-primary"><?php _e('Create Page', 'db-app-builder'); ?></button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="dab-wizard-next-steps">
                <h3><?php _e('Next Steps', 'db-app-builder'); ?></h3>
                <div class="dab-wizard-next-steps-grid">
                    <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-wizard-next-step-card">
                        <div class="dab-wizard-next-step-icon">
                            <span class="dashicons dashicons-database"></span>
                        </div>
                        <h4><?php _e('Manage Tables', 'db-app-builder'); ?></h4>
                        <p><?php _e('Create additional tables or modify existing ones.', 'db-app-builder'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dab_forms'); ?>" class="dab-wizard-next-step-card">
                        <div class="dab-wizard-next-step-icon">
                            <span class="dashicons dashicons-feedback"></span>
                        </div>
                        <h4><?php _e('Manage Forms', 'db-app-builder'); ?></h4>
                        <p><?php _e('Create additional forms or modify existing ones.', 'db-app-builder'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dab_views'); ?>" class="dab-wizard-next-step-card">
                        <div class="dab-wizard-next-step-icon">
                            <span class="dashicons dashicons-list-view"></span>
                        </div>
                        <h4><?php _e('Manage Views', 'db-app-builder'); ?></h4>
                        <p><?php _e('Create additional views or modify existing ones.', 'db-app-builder'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dab_wizard_workflow_setup'); ?>" class="dab-wizard-next-step-card">
                        <div class="dab-wizard-next-step-icon">
                            <span class="dashicons dashicons-list-view"></span>
                        </div>
                        <h4><?php _e('Setup Approval Workflow', 'db-app-builder'); ?></h4>
                        <p><?php _e('Configure approval workflows for your tables.', 'db-app-builder'); ?></p>
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.dab-wizard-notice-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
}

.dab-wizard-completion-header {
    text-align: center;
    margin-bottom: 40px;
}

.dab-wizard-completion-icon {
    margin-bottom: 20px;
}

.dab-wizard-completion-icon .dashicons {
    font-size: 60px;
    width: 60px;
    height: 60px;
    color: #46b450;
}

.dab-wizard-completion-title {
    font-size: 24px;
    margin-bottom: 10px;
}

.dab-wizard-completion-subtitle {
    font-size: 16px;
    color: #666;
}

.dab-wizard-completion-summary {
    margin-bottom: 40px;
}

.dab-wizard-summary-item {
    display: flex;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.dab-wizard-summary-icon {
    margin-right: 20px;
}

.dab-wizard-summary-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: #2271b1;
}

.dab-wizard-summary-content {
    flex-grow: 1;
}

.dab-wizard-summary-content h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.dab-wizard-form-inline {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.dab-wizard-next-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.dab-wizard-next-step-card {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.dab-wizard-next-step-card:hover {
    background-color: #e9ecef;
    transform: translateY(-5px);
}

.dab-wizard-next-step-icon {
    margin-bottom: 15px;
}

.dab-wizard-next-step-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: #2271b1;
}

.dab-wizard-next-step-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.dab-wizard-next-step-card p {
    margin: 0;
    color: #666;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Handle page creation form submission
    $('#dab-create-page').on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            success: function(response) {
                // Reload the page to show the new page
                window.location.reload();
            }
        });
    });
});
</script>
