<?php
/**
 * Stripe Payment Gateway
 *
 * This class handles Stripe payment processing for the Database App Builder plugin.
 */
if (!defined('ABSPATH')) exit;

class DAB_Stripe_Gateway {

    /**
     * Gateway ID
     */
    private $id = 'stripe';

    /**
     * API Key
     */
    private $api_key = '';

    /**
     * Public Key
     */
    private $public_key = '';

    /**
     * Test Mode
     */
    private $test_mode = true;

    /**
     * Constructor
     */
    public function __construct() {
        // Load settings
        $this->api_key = DAB_Settings_Manager::get('stripe_secret_key', '');
        $this->public_key = DAB_Settings_Manager::get('stripe_publishable_key', '');
        $this->test_mode = DAB_Settings_Manager::get('stripe_test_mode', 'yes') === 'yes';
        
        // If test mode is enabled, use test keys
        if ($this->test_mode) {
            $this->api_key = DAB_Settings_Manager::get('stripe_test_secret_key', '');
            $this->public_key = DAB_Settings_Manager::get('stripe_test_publishable_key', '');
        }
        
        // Register AJAX handlers
        add_action('wp_ajax_dab_stripe_create_payment_intent', array($this, 'ajax_create_payment_intent'));
        add_action('wp_ajax_nopriv_dab_stripe_create_payment_intent', array($this, 'ajax_create_payment_intent'));
        
        add_action('wp_ajax_dab_stripe_confirm_payment', array($this, 'ajax_confirm_payment'));
        add_action('wp_ajax_nopriv_dab_stripe_confirm_payment', array($this, 'ajax_confirm_payment'));
    }

    /**
     * Get gateway title
     */
    public function get_title() {
        return __('Stripe', 'db-app-builder');
    }

    /**
     * Get gateway description
     */
    public function get_description() {
        return __('Accept credit card payments via Stripe.', 'db-app-builder');
    }

    /**
     * Check if gateway is configured
     */
    public function is_configured() {
        return !empty($this->api_key) && !empty($this->public_key);
    }

    /**
     * Get settings fields
     */
    public function get_settings_fields() {
        return array(
            array(
                'name' => 'stripe_test_mode',
                'label' => __('Test Mode', 'db-app-builder'),
                'type' => 'select',
                'options' => array(
                    'yes' => __('Yes', 'db-app-builder'),
                    'no' => __('No', 'db-app-builder'),
                ),
                'default' => 'yes',
                'description' => __('Enable test mode to use Stripe test API keys.', 'db-app-builder'),
            ),
            array(
                'name' => 'stripe_test_publishable_key',
                'label' => __('Test Publishable Key', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your Stripe test publishable key.', 'db-app-builder'),
            ),
            array(
                'name' => 'stripe_test_secret_key',
                'label' => __('Test Secret Key', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your Stripe test secret key.', 'db-app-builder'),
            ),
            array(
                'name' => 'stripe_publishable_key',
                'label' => __('Live Publishable Key', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your Stripe live publishable key.', 'db-app-builder'),
            ),
            array(
                'name' => 'stripe_secret_key',
                'label' => __('Live Secret Key', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your Stripe live secret key.', 'db-app-builder'),
            ),
            array(
                'name' => 'stripe_webhook_secret',
                'label' => __('Webhook Secret', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your Stripe webhook secret for payment status updates.', 'db-app-builder'),
            ),
        );
    }

    /**
     * Render payment form
     */
    public function render_payment_form($field, $options) {
        // Check if Stripe is configured
        if (!$this->is_configured()) {
            return '<div class="dab-payment-error">' . __('Stripe is not properly configured. Please contact the administrator.', 'db-app-builder') . '</div>';
        }
        
        // Get amount and currency
        $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';
        $amount = isset($options['payment_amount']) ? floatval($options['payment_amount']) : 0;
        $currency = isset($options['payment_currency']) ? $options['payment_currency'] : 'USD';
        
        // Generate a unique ID for this payment form
        $form_id = 'stripe-form-' . uniqid();
        
        // Start output buffer
        ob_start();
        
        ?>
        <div class="dab-stripe-form" id="<?php echo esc_attr($form_id); ?>">
            <div class="dab-stripe-card-element"></div>
            <div class="dab-stripe-card-errors" role="alert"></div>
            <button type="button" class="dab-stripe-submit"><?php _e('Pay', 'db-app-builder'); ?> <?php echo esc_html(DAB_Payment_Gateway::format_currency($amount, $currency)); ?></button>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Initialize Stripe
            var stripe = Stripe('<?php echo esc_js($this->public_key); ?>');
            var elements = stripe.elements();
            
            // Create card element
            var cardElement = elements.create('card', {
                style: {
                    base: {
                        color: '#32325d',
                        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                        fontSmoothing: 'antialiased',
                        fontSize: '16px',
                        '::placeholder': {
                            color: '#aab7c4'
                        }
                    },
                    invalid: {
                        color: '#fa755a',
                        iconColor: '#fa755a'
                    }
                }
            });
            
            // Mount card element
            cardElement.mount('#<?php echo esc_js($form_id); ?> .dab-stripe-card-element');
            
            // Handle form submission
            $('#<?php echo esc_js($form_id); ?> .dab-stripe-submit').on('click', function(e) {
                e.preventDefault();
                
                var $form = $('#<?php echo esc_js($form_id); ?>');
                var $button = $(this);
                var $errors = $form.find('.dab-stripe-card-errors');
                
                // Disable button to prevent multiple submissions
                $button.prop('disabled', true).text('<?php _e('Processing...', 'db-app-builder'); ?>');
                
                // Clear previous errors
                $errors.empty();
                
                // Create payment intent
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'dab_stripe_create_payment_intent',
                        amount: <?php echo esc_js($amount * 100); ?>, // Stripe requires amount in cents
                        currency: '<?php echo esc_js($currency); ?>',
                        description: '<?php echo esc_js(isset($options['payment_description']) ? $options['payment_description'] : ''); ?>',
                        nonce: '<?php echo wp_create_nonce('dab_stripe_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success && response.data.client_secret) {
                            // Confirm payment with card element
                            stripe.confirmCardPayment(response.data.client_secret, {
                                payment_method: {
                                    card: cardElement,
                                    billing_details: {
                                        name: '<?php echo esc_js(wp_get_current_user()->display_name); ?>'
                                    }
                                }
                            }).then(function(result) {
                                if (result.error) {
                                    // Show error
                                    $errors.text(result.error.message);
                                    $button.prop('disabled', false).text('<?php _e('Try Again', 'db-app-builder'); ?>');
                                } else {
                                    // Payment succeeded
                                    $button.text('<?php _e('Payment Successful!', 'db-app-builder'); ?>');
                                    
                                    // Update payment status
                                    $('#<?php echo esc_js($field->field_slug); ?>').val(result.paymentIntent.id);
                                    
                                    // Notify the form that payment is complete
                                    $(document).trigger('dab_payment_complete', [result.paymentIntent.id]);
                                }
                            });
                        } else {
                            // Show error
                            $errors.text(response.data.message || '<?php _e('Error creating payment. Please try again.', 'db-app-builder'); ?>');
                            $button.prop('disabled', false).text('<?php _e('Try Again', 'db-app-builder'); ?>');
                        }
                    },
                    error: function() {
                        $errors.text('<?php _e('Error connecting to server. Please try again.', 'db-app-builder'); ?>');
                        $button.prop('disabled', false).text('<?php _e('Try Again', 'db-app-builder'); ?>');
                    }
                });
            });
        });
        </script>
        <?php
        
        return ob_get_clean();
    }

    /**
     * AJAX handler to create a payment intent
     */
    public function ajax_create_payment_intent() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_stripe_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }
        
        // Check if Stripe is configured
        if (!$this->is_configured()) {
            wp_send_json_error(array('message' => __('Stripe is not properly configured', 'db-app-builder')));
        }
        
        // Get amount and currency
        $amount = isset($_POST['amount']) ? intval($_POST['amount']) : 0;
        $currency = isset($_POST['currency']) ? sanitize_text_field($_POST['currency']) : 'USD';
        $description = isset($_POST['description']) ? sanitize_text_field($_POST['description']) : '';
        
        // Validate amount
        if ($amount <= 0) {
            wp_send_json_error(array('message' => __('Invalid payment amount', 'db-app-builder')));
        }
        
        try {
            // Load Stripe API
            require_once(plugin_dir_path(dirname(__FILE__)) . 'libs/stripe-php/init.php');
            
            // Set API key
            \Stripe\Stripe::setApiKey($this->api_key);
            
            // Create payment intent
            $intent = \Stripe\PaymentIntent::create([
                'amount' => $amount,
                'currency' => strtolower($currency),
                'description' => $description,
                'metadata' => [
                    'integration_check' => 'accept_a_payment',
                    'website' => get_bloginfo('name'),
                ],
            ]);
            
            // Return client secret
            wp_send_json_success(array(
                'client_secret' => $intent->client_secret,
                'payment_intent_id' => $intent->id,
            ));
        } catch (\Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * AJAX handler to confirm payment
     */
    public function ajax_confirm_payment() {
        // Implementation will be added
    }

    /**
     * Process webhook
     */
    public function process_webhook() {
        // Implementation will be added
    }
}
