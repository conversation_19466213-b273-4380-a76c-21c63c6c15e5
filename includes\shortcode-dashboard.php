<?php
/**
 * Dashboard Shortcode
 *
 * Shortcode for embedding dashboards on frontend pages.
 */
if (!defined('ABSPATH')) exit;

/**
 * Register dashboard shortcode
 */
function dab_dashboard_shortcode($atts) {
    $atts = shortcode_atts(array(
        'id' => 0,
    ), $atts, 'dab_dashboard');

    $dashboard_id = intval($atts['id']);

    if ($dashboard_id <= 0) {
        return '<p class="dab-error">' . __('Invalid dashboard ID.', 'db-app-builder') . '</p>';
    }

    // Get dashboard
    $dashboard = DAB_Simple_Dashboard_Manager::get_dashboard($dashboard_id);

    if (!$dashboard) {
        return '<p class="dab-error">' . __('Dashboard not found.', 'db-app-builder') . '</p>';
    }

    // Check if dashboard is public
    if (!$dashboard->is_public) {
        return '<p class="dab-error">' . __('This dashboard is not public.', 'db-app-builder') . '</p>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-simple-dashboard-view', plugin_dir_url(dirname(__FILE__)) . 'assets/css/simple-dashboard-view.css', array(), DAB_VERSION);
    wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js', array(), '3.7.1', true);
    wp_enqueue_script('dab-simple-dashboard-view', plugin_dir_url(dirname(__FILE__)) . 'assets/js/simple-dashboard-view.js', array('jquery', 'chart-js'), DAB_VERSION, true);

    // Localize script with necessary variables
    wp_localize_script('dab-simple-dashboard-view', 'dab_dashboard', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_dashboard_nonce'),
        'dashboard_id' => $dashboard_id,
        'dashboard' => $dashboard,
        'i18n' => array(
            'loading' => __('Loading...', 'db-app-builder'),
            'error' => __('Error loading data.', 'db-app-builder'),
            'no_data' => __('No data available.', 'db-app-builder'),
        )
    ));

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-dashboard-view-container">
        <h2 class="dab-dashboard-title"><?php echo esc_html($dashboard->title); ?></h2>

        <?php if (!empty($dashboard->description)): ?>
        <div class="dab-dashboard-description">
            <?php echo wp_kses_post($dashboard->description); ?>
        </div>
        <?php endif; ?>

        <div class="dab-dashboard-grid" id="dashboard-grid">
            <?php
            // Check if we have a layout defined
            $layout = null;
            if ($dashboard && isset($dashboard->layout)) {
                $layout = json_decode($dashboard->layout, true);
            }

            if ($layout && !empty($layout['rows'])):
                // Render rows and columns from layout
                foreach ($layout['rows'] as $row_index => $row):
                    // Get row settings
                    $row_settings = isset($row['settings']) ? $row['settings'] : array();
                    $row_style = '';

                    if (!empty($row_settings)) {
                        $row_style_array = array();

                        if (isset($row_settings['height'])) {
                            $row_style_array[] = 'min-height: ' . intval($row_settings['height']) . 'px';
                        }

                        if (isset($row_settings['background']) && !empty($row_settings['background'])) {
                            $row_style_array[] = 'background-color: ' . esc_attr($row_settings['background']);
                        }

                        if (isset($row_settings['padding'])) {
                            $row_style_array[] = 'padding: ' . intval($row_settings['padding']) . 'px';
                        }

                        if (isset($row_settings['marginBottom'])) {
                            $row_style_array[] = 'margin-bottom: ' . intval($row_settings['marginBottom']) . 'px';
                        }

                        if (isset($row_settings['border']) && isset($row_settings['borderWidth']) && isset($row_settings['borderColor'])) {
                            $row_style_array[] = 'border: ' . intval($row_settings['borderWidth']) . 'px ' .
                                                esc_attr($row_settings['border']) . ' ' .
                                                esc_attr($row_settings['borderColor']);
                        }

                        if (isset($row_settings['borderRadius'])) {
                            $row_style_array[] = 'border-radius: ' . intval($row_settings['borderRadius']) . 'px';
                        }

                        if (!empty($row_style_array)) {
                            $row_style = 'style="' . implode('; ', $row_style_array) . '"';
                        }
                    }
            ?>
                <div class="dab-dashboard-row" data-row-id="<?php echo $row_index; ?>" <?php echo $row_style; ?>>
                    <?php foreach ($row['columns'] as $col_index => $column):
                        // Get column settings
                        $column_settings = isset($column['settings']) ? $column['settings'] : array();
                        $column_style = '';
                        $column_width = isset($column['width']) ? intval($column['width']) : 1;

                        if (!empty($column_settings) || $column_width > 0) {
                            $column_style_array = array();

                            // Set flex based on width
                            $column_style_array[] = 'flex: ' . $column_width . ' 1 0';

                            if (isset($column_settings['background']) && !empty($column_settings['background'])) {
                                $column_style_array[] = 'background-color: ' . esc_attr($column_settings['background']);
                            }

                            if (isset($column_settings['padding'])) {
                                $column_style_array[] = 'padding: ' . intval($column_settings['padding']) . 'px';
                            }

                            if (isset($column_settings['border']) && isset($column_settings['borderWidth']) && isset($column_settings['borderColor'])) {
                                $column_style_array[] = 'border: ' . intval($column_settings['borderWidth']) . 'px ' .
                                                      esc_attr($column_settings['border']) . ' ' .
                                                      esc_attr($column_settings['borderColor']);
                            }

                            if (isset($column_settings['borderRadius'])) {
                                $column_style_array[] = 'border-radius: ' . intval($column_settings['borderRadius']) . 'px';
                            }

                            if (!empty($column_style_array)) {
                                $column_style = 'style="' . implode('; ', $column_style_array) . '"';
                            }
                        }
                    ?>
                        <div class="dab-dashboard-column" data-column-id="<?php echo $col_index; ?>" data-width="<?php echo $column_width; ?>" <?php echo $column_style; ?>>
                            <?php
                            // Render widgets in this column
                            if (!empty($column['widgets'])) {
                                foreach ($column['widgets'] as $widget_id) {
                                    // Find the widget in the dashboard widgets
                                    $widget = null;
                                    if ($dashboard && !empty($dashboard->widgets)) {
                                        foreach ($dashboard->widgets as $w) {
                                            if ($w->id == $widget_id) {
                                                $widget = $w;
                                                break;
                                            }
                                        }
                                    }

                                    if ($widget) {
                                        $settings = json_decode($widget->settings, true);
                            ?>
                                        <div class="dab-dashboard-widget"
                                             data-widget-id="<?php echo $widget->id; ?>"
                                             data-widget-type="<?php echo $widget->widget_type; ?>"
                                             data-row-id="<?php echo $row_index; ?>"
                                             data-column-id="<?php echo $col_index; ?>">
                                            <div class="dab-widget-header">
                                                <h3 class="dab-widget-title"><?php echo esc_html($widget->title); ?></h3>
                                                <div class="dab-widget-actions">
                                                    <button type="button" class="dab-widget-refresh" title="<?php _e('Refresh', 'db-app-builder'); ?>">
                                                        <span class="dashicons dashicons-update"></span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="dab-widget-content">
                                                <div class="dab-widget-loading">
                                                    <span class="spinner is-active"></span>
                                                    <p><?php _e('Loading...', 'db-app-builder'); ?></p>
                                                </div>
                                                <div class="dab-widget-data"></div>
                                            </div>
                                        </div>
                            <?php
                                    }
                                }
                            }
                            ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php
                endforeach;
            else:
                // No layout defined, fall back to the old grid layout
                if (!empty($dashboard->widgets)):
                    // Create a default row with a single column
            ?>
                <div class="dab-dashboard-row" data-row-id="0">
                    <div class="dab-dashboard-column" data-column-id="0" data-width="12" style="flex: 12 1 0">
                        <?php foreach ($dashboard->widgets as $widget):
                            $settings = json_decode($widget->settings, true);
                        ?>
                            <div class="dab-dashboard-widget"
                                 data-widget-id="<?php echo $widget->id; ?>"
                                 data-widget-type="<?php echo $widget->widget_type; ?>"
                                 data-row-id="0"
                                 data-column-id="0">
                                <div class="dab-widget-header">
                                    <h3 class="dab-widget-title"><?php echo esc_html($widget->title); ?></h3>
                                    <div class="dab-widget-actions">
                                        <button type="button" class="dab-widget-refresh" title="<?php _e('Refresh', 'db-app-builder'); ?>">
                                            <span class="dashicons dashicons-update"></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="dab-widget-content">
                                    <div class="dab-widget-loading">
                                        <span class="spinner is-active"></span>
                                        <p><?php _e('Loading...', 'db-app-builder'); ?></p>
                                    </div>
                                    <div class="dab-widget-data"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="dab-no-widgets">
                    <p><?php _e('No widgets found in this dashboard.', 'db-app-builder'); ?></p>
                </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_dashboard', 'dab_dashboard_shortcode');
