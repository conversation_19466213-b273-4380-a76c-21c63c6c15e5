/**
 * Kanban Board JavaScript
 * 
 * Interactive Kanban board functionality for the Database App Builder plugin
 */

(function($) {
    'use strict';

    // Global variables
    let currentBoard = null;
    let currentCard = null;
    let isDirty = false;

    /**
     * Initialize Kanban boards
     */
    function initKanbanBoards() {
        $('.dab-kanban-container').each(function() {
            const container = $(this);
            const fieldId = container.data('field-id');
            const recordId = container.data('record-id');
            
            initKanbanBoard(container, fieldId, recordId);
        });
    }

    /**
     * Initialize a single Kanban board
     */
    function initKanbanBoard(container, fieldId, recordId) {
        currentBoard = {
            container: container,
            fieldId: fieldId,
            recordId: recordId,
            data: getBoardData(container)
        };

        // Initialize drag and drop
        initDragAndDrop(container);
        
        // Bind events
        bindKanbanEvents(container);
        
        // Auto-save on changes
        setupAutoSave(container);
    }

    /**
     * Get board data from hidden input
     */
    function getBoardData(container) {
        const hiddenInput = container.find('input[type="hidden"]');
        try {
            return JSON.parse(hiddenInput.val() || '{"columns":[]}');
        } catch (e) {
            console.error('Error parsing board data:', e);
            return { columns: [] };
        }
    }

    /**
     * Save board data to hidden input
     */
    function saveBoardData(container, data) {
        const hiddenInput = container.find('input[type="hidden"]');
        hiddenInput.val(JSON.stringify(data));
        isDirty = true;
    }

    /**
     * Initialize drag and drop functionality
     */
    function initDragAndDrop(container) {
        // Make cards sortable within columns
        container.find('.dab-kanban-cards').sortable({
            connectWith: '.dab-kanban-cards',
            placeholder: 'ui-sortable-placeholder',
            tolerance: 'pointer',
            cursor: 'move',
            helper: 'clone',
            opacity: 0.8,
            start: function(event, ui) {
                ui.placeholder.height(ui.item.height());
                ui.helper.addClass('ui-sortable-helper');
            },
            update: function(event, ui) {
                updateCardPositions(container);
            },
            receive: function(event, ui) {
                updateCardPositions(container);
            }
        });

        // Make columns sortable
        container.find('.dab-kanban-board').sortable({
            items: '.dab-kanban-column',
            placeholder: 'dab-column-placeholder',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            update: function(event, ui) {
                updateColumnPositions(container);
            }
        });
    }

    /**
     * Bind Kanban events
     */
    function bindKanbanEvents(container) {
        // Add column button
        container.on('click', '.dab-add-column', function() {
            addNewColumn(container);
        });

        // Add card button
        container.on('click', '.dab-add-card', function() {
            const columnElement = $(this).closest('.dab-kanban-column');
            const columnId = columnElement.data('column-id');
            addNewCard(container, columnId);
        });

        // Edit card
        container.on('click', '.dab-kanban-card', function() {
            const cardElement = $(this);
            const cardId = cardElement.data('card-id');
            editCard(container, cardId, cardElement);
        });

        // Edit column title
        container.on('blur', '.dab-column-title', function() {
            const columnElement = $(this).closest('.dab-kanban-column');
            const columnId = columnElement.data('column-id');
            const newTitle = $(this).text().trim();
            updateColumnTitle(container, columnId, newTitle);
        });

        // Column menu
        container.on('click', '.dab-column-menu', function() {
            const columnElement = $(this).closest('.dab-kanban-column');
            showColumnMenu(columnElement);
        });
    }

    /**
     * Setup auto-save functionality
     */
    function setupAutoSave(container) {
        setInterval(function() {
            if (isDirty) {
                saveToServer(container);
                isDirty = false;
            }
        }, 5000); // Auto-save every 5 seconds
    }

    /**
     * Add new column
     */
    function addNewColumn(container) {
        const board = container.find('.dab-kanban-board');
        const columnCount = board.find('.dab-kanban-column').length;
        const newColumnId = Date.now(); // Temporary ID
        
        const columnHtml = `
            <div class="dab-kanban-column" data-column-id="${newColumnId}" style="border-top: 3px solid #3498db">
                <div class="dab-kanban-column-header">
                    <h4 class="dab-column-title" contenteditable="true">New Column</h4>
                    <div class="dab-column-actions">
                        <button type="button" class="dab-btn-icon dab-add-card" title="${dabKanbanData.i18n.addCard}">
                            <span class="dashicons dashicons-plus-alt"></span>
                        </button>
                        <button type="button" class="dab-btn-icon dab-column-menu" title="${dabKanbanData.i18n.editColumn}">
                            <span class="dashicons dashicons-menu"></span>
                        </button>
                    </div>
                </div>
                <div class="dab-kanban-cards" data-column-id="${newColumnId}">
                </div>
            </div>
        `;
        
        board.append(columnHtml);
        
        // Re-initialize drag and drop for new column
        const newColumn = board.find(`[data-column-id="${newColumnId}"]`);
        newColumn.find('.dab-kanban-cards').sortable({
            connectWith: '.dab-kanban-cards',
            placeholder: 'ui-sortable-placeholder',
            tolerance: 'pointer',
            cursor: 'move',
            helper: 'clone',
            opacity: 0.8,
            update: function(event, ui) {
                updateCardPositions(container);
            },
            receive: function(event, ui) {
                updateCardPositions(container);
            }
        });
        
        // Focus on title for editing
        newColumn.find('.dab-column-title').focus().select();
        
        updateColumnPositions(container);
    }

    /**
     * Add new card
     */
    function addNewCard(container, columnId) {
        currentCard = {
            id: 0,
            columnId: columnId,
            title: '',
            description: '',
            priority: 'medium',
            assignedTo: '',
            dueDate: '',
            labels: []
        };
        
        showCardModal(container, true);
    }

    /**
     * Edit existing card
     */
    function editCard(container, cardId, cardElement) {
        // Extract card data from DOM
        currentCard = {
            id: cardId,
            columnId: cardElement.closest('.dab-kanban-cards').data('column-id'),
            title: cardElement.find('.dab-card-title').text(),
            description: cardElement.find('.dab-card-description').text(),
            priority: getPriorityFromClass(cardElement),
            assignedTo: getAssignedFromElement(cardElement),
            dueDate: getDueDateFromElement(cardElement),
            labels: getLabelsFromElement(cardElement)
        };
        
        showCardModal(container, false);
    }

    /**
     * Show card editing modal
     */
    function showCardModal(container, isNew) {
        const modal = $('#dab-card-modal');
        
        // Set modal title
        const title = isNew ? dabKanbanData.i18n.addCard : dabKanbanData.i18n.editCard;
        modal.find('#dab-card-modal-title').text(title);
        
        // Populate form fields
        modal.find('#dab-card-title').val(currentCard.title);
        modal.find('#dab-card-description').val(currentCard.description);
        modal.find('#dab-card-priority').val(currentCard.priority);
        modal.find('#dab-card-assigned').val(currentCard.assignedTo);
        modal.find('#dab-card-due-date').val(currentCard.dueDate);
        
        // Show/hide delete button
        const deleteBtn = modal.find('.dab-delete-card');
        if (isNew) {
            deleteBtn.hide();
        } else {
            deleteBtn.show();
        }
        
        // Show modal
        modal.show();
        modal.find('#dab-card-title').focus();
    }

    /**
     * Update card positions after drag and drop
     */
    function updateCardPositions(container) {
        const boardData = { columns: [] };
        
        container.find('.dab-kanban-column').each(function(columnIndex) {
            const columnElement = $(this);
            const columnId = columnElement.data('column-id');
            const columnTitle = columnElement.find('.dab-column-title').text();
            const columnColor = extractColorFromStyle(columnElement.attr('style'));
            
            const columnData = {
                id: columnId,
                title: columnTitle,
                color: columnColor,
                position: columnIndex,
                cards: []
            };
            
            columnElement.find('.dab-kanban-card').each(function(cardIndex) {
                const cardElement = $(this);
                const cardId = cardElement.data('card-id');
                
                const cardData = {
                    id: cardId,
                    title: cardElement.find('.dab-card-title').text(),
                    description: cardElement.find('.dab-card-description').text(),
                    priority: getPriorityFromClass(cardElement),
                    assignedTo: getAssignedFromElement(cardElement),
                    dueDate: getDueDateFromElement(cardElement),
                    labels: getLabelsFromElement(cardElement),
                    position: cardIndex
                };
                
                columnData.cards.push(cardData);
            });
            
            boardData.columns.push(columnData);
        });
        
        saveBoardData(container, boardData);
    }

    /**
     * Update column positions after drag and drop
     */
    function updateColumnPositions(container) {
        updateCardPositions(container); // This will update everything including column positions
    }

    /**
     * Update column title
     */
    function updateColumnTitle(container, columnId, newTitle) {
        updateCardPositions(container);
    }

    /**
     * Save data to server
     */
    function saveToServer(container) {
        const fieldId = container.data('field-id');
        const recordId = container.data('record-id');
        const boardData = getBoardData(container);
        
        $.ajax({
            url: dabKanbanData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_save_kanban_data',
                field_id: fieldId,
                record_id: recordId,
                board_data: boardData,
                nonce: dabKanbanData.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Kanban board saved successfully');
                } else {
                    console.error('Error saving kanban board:', response.data);
                }
            },
            error: function() {
                console.error('AJAX error saving kanban board');
            }
        });
    }

    /**
     * Helper functions
     */
    function getPriorityFromClass(cardElement) {
        if (cardElement.hasClass('dab-priority-high')) return 'high';
        if (cardElement.hasClass('dab-priority-low')) return 'low';
        return 'medium';
    }

    function getAssignedFromElement(cardElement) {
        const assigneeImg = cardElement.find('.dab-card-assignee img');
        return assigneeImg.length ? assigneeImg.attr('data-user-id') || '' : '';
    }

    function getDueDateFromElement(cardElement) {
        const dueDateElement = cardElement.find('.dab-card-due-date');
        return dueDateElement.length ? dueDateElement.attr('data-due-date') || '' : '';
    }

    function getLabelsFromElement(cardElement) {
        const labels = [];
        cardElement.find('.dab-card-label').each(function() {
            labels.push({
                name: $(this).text(),
                color: $(this).css('background-color')
            });
        });
        return labels;
    }

    function extractColorFromStyle(styleAttr) {
        if (!styleAttr) return '#3498db';
        const match = styleAttr.match(/border-top:\s*3px\s+solid\s+(#[0-9a-fA-F]{6})/);
        return match ? match[1] : '#3498db';
    }

    /**
     * Modal event handlers
     */
    $(document).ready(function() {
        // Close modal
        $(document).on('click', '.dab-modal-close', function() {
            $('#dab-card-modal').hide();
        });

        // Save card
        $(document).on('click', '.dab-save-card', function() {
            saveCard();
        });

        // Delete card
        $(document).on('click', '.dab-delete-card', function() {
            if (confirm(dabKanbanData.i18n.confirmDelete)) {
                deleteCard();
            }
        });

        // Close modal on outside click
        $(document).on('click', '.dab-modal', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        // Initialize Kanban boards
        initKanbanBoards();
    });

    /**
     * Save card from modal
     */
    function saveCard() {
        const modal = $('#dab-card-modal');
        const container = currentBoard.container;
        
        // Get form data
        const cardData = {
            id: currentCard.id || Date.now(),
            title: modal.find('#dab-card-title').val(),
            description: modal.find('#dab-card-description').val(),
            priority: modal.find('#dab-card-priority').val(),
            assignedTo: modal.find('#dab-card-assigned').val(),
            dueDate: modal.find('#dab-card-due-date').val(),
            labels: currentCard.labels || []
        };

        // Validate required fields
        if (!cardData.title.trim()) {
            alert('Card title is required');
            return;
        }

        // Create or update card in DOM
        if (currentCard.id === 0) {
            // New card
            createCardElement(container, currentCard.columnId, cardData);
        } else {
            // Update existing card
            updateCardElement(container, cardData);
        }

        // Update positions and save
        updateCardPositions(container);
        
        // Close modal
        modal.hide();
    }

    /**
     * Delete card
     */
    function deleteCard() {
        const container = currentBoard.container;
        const cardElement = container.find(`[data-card-id="${currentCard.id}"]`);
        
        cardElement.remove();
        updateCardPositions(container);
        
        $('#dab-card-modal').hide();
    }

    /**
     * Create new card element in DOM
     */
    function createCardElement(container, columnId, cardData) {
        const cardsContainer = container.find(`[data-column-id="${columnId}"].dab-kanban-cards`);
        const priorityClass = `dab-priority-${cardData.priority}`;
        
        const cardHtml = `
            <div class="dab-kanban-card ${priorityClass}" data-card-id="${cardData.id}">
                <h5 class="dab-card-title">${escapeHtml(cardData.title)}</h5>
                ${cardData.description ? `<p class="dab-card-description">${escapeHtml(cardData.description)}</p>` : ''}
                <div class="dab-card-footer">
                    ${cardData.dueDate ? `<span class="dab-card-due-date" data-due-date="${cardData.dueDate}">
                        <span class="dashicons dashicons-calendar-alt"></span>
                        ${formatDate(cardData.dueDate)}
                    </span>` : ''}
                    <span class="dab-card-priority dab-priority-${cardData.priority}" title="${cardData.priority} Priority"></span>
                </div>
            </div>
        `;
        
        cardsContainer.append(cardHtml);
    }

    /**
     * Update existing card element in DOM
     */
    function updateCardElement(container, cardData) {
        const cardElement = container.find(`[data-card-id="${currentCard.id}"]`);
        
        // Update classes
        cardElement.removeClass('dab-priority-low dab-priority-medium dab-priority-high');
        cardElement.addClass(`dab-priority-${cardData.priority}`);
        
        // Update content
        cardElement.find('.dab-card-title').text(cardData.title);
        
        const descElement = cardElement.find('.dab-card-description');
        if (cardData.description) {
            if (descElement.length) {
                descElement.text(cardData.description);
            } else {
                cardElement.find('.dab-card-title').after(`<p class="dab-card-description">${escapeHtml(cardData.description)}</p>`);
            }
        } else {
            descElement.remove();
        }
        
        // Update footer
        const footer = cardElement.find('.dab-card-footer');
        footer.html(`
            ${cardData.dueDate ? `<span class="dab-card-due-date" data-due-date="${cardData.dueDate}">
                <span class="dashicons dashicons-calendar-alt"></span>
                ${formatDate(cardData.dueDate)}
            </span>` : ''}
            <span class="dab-card-priority dab-priority-${cardData.priority}" title="${cardData.priority} Priority"></span>
        `);
    }

    /**
     * Utility functions
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }

})(jQuery);
