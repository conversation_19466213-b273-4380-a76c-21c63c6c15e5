/**
 * Progress Tracker CSS
 * 
 * Styles for the progress tracker field component
 */

.dab-progress-tracker {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dab-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.dab-progress-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.dab-progress-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-add-milestone-btn {
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.dab-add-milestone-btn:hover {
    background: #005a87;
}

.dab-progress-container {
    padding: 20px;
}

.dab-progress-overview {
    margin-bottom: 30px;
}

.dab-progress-bar-container {
    margin-bottom: 20px;
}

.dab-progress-bar-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.dab-progress-percentage {
    font-weight: 600;
    color: #0073aa;
    font-size: 16px;
}

.dab-progress-bar {
    width: 100%;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.dab-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    border-radius: 6px;
    transition: width 0.3s ease;
    position: relative;
}

.dab-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.dab-progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.dab-progress-stat {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.dab-progress-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.dab-progress-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-progress-stat.completed .dab-progress-stat-value {
    color: #4caf50;
}

.dab-progress-stat.in-progress .dab-progress-stat-value {
    color: #ff9800;
}

.dab-progress-stat.pending .dab-progress-stat-value {
    color: #f44336;
}

.dab-milestones-section {
    margin-top: 30px;
}

.dab-milestones-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dab-milestones-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.dab-milestone {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
}

.dab-milestone:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #ccc;
}

.dab-milestone-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.dab-milestone-status.completed {
    background: #4caf50;
    color: white;
}

.dab-milestone-status.in-progress {
    background: #ff9800;
    color: white;
}

.dab-milestone-status.pending {
    background: #f0f0f0;
    border: 2px solid #ddd;
    color: #666;
}

.dab-milestone-content {
    flex: 1;
}

.dab-milestone-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.dab-milestone-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.dab-milestone-meta {
    display: flex;
    gap: 15px;
    margin-top: 8px;
    font-size: 12px;
    color: #888;
}

.dab-milestone-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.dab-milestone-actions {
    display: flex;
    gap: 8px;
    margin-left: 15px;
}

.dab-milestone-action-btn {
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.2s ease;
}

.dab-milestone-action-btn:hover {
    background: #f5f5f5;
    border-color: #999;
    color: #333;
}

.dab-milestone-action-btn.edit {
    color: #0073aa;
    border-color: #0073aa;
}

.dab-milestone-action-btn.edit:hover {
    background: #e3f2fd;
}

.dab-milestone-action-btn.delete {
    color: #dc3545;
    border-color: #dc3545;
}

.dab-milestone-action-btn.delete:hover {
    background: #ffebee;
}

/* Milestone Modal */
.dab-milestone-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.dab-milestone-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.dab-milestone-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-milestone-modal-title {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.dab-milestone-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.dab-milestone-modal-close:hover {
    background: #f0f0f0;
}

.dab-milestone-modal-body {
    padding: 20px;
}

.dab-milestone-form-group {
    margin-bottom: 15px;
}

.dab-milestone-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.dab-milestone-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.dab-milestone-form-control:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-milestone-form-control.textarea {
    min-height: 80px;
    resize: vertical;
}

.dab-milestone-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.dab-milestone-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dab-milestone-btn-primary {
    background: #0073aa;
    color: white;
}

.dab-milestone-btn-primary:hover {
    background: #005a87;
}

.dab-milestone-btn-secondary {
    background: #f0f0f0;
    color: #333;
}

.dab-milestone-btn-secondary:hover {
    background: #e0e0e0;
}

.dab-milestone-btn-danger {
    background: #dc3545;
    color: white;
}

.dab-milestone-btn-danger:hover {
    background: #c82333;
}

/* Empty State */
.dab-progress-empty {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.dab-progress-empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.dab-progress-empty h3 {
    margin: 0 0 10px 0;
    color: #333;
}

/* Loading State */
.dab-progress-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.dab-progress-loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-progress-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dab-progress-controls {
        justify-content: center;
    }
    
    .dab-progress-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dab-milestone {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .dab-milestone-actions {
        margin-left: 0;
        align-self: flex-end;
    }
    
    .dab-milestone-modal-content {
        width: 95%;
    }
}
