/**
 * Frontend Authentication JavaScript
 * 
 * Handles login, registration, and authentication functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Login Form Handler
    $('#dab-login-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const $errorDiv = $('#dab-login-error');
        const $successDiv = $('#dab-login-success');
        
        // Clear previous messages
        $errorDiv.hide();
        $successDiv.hide();
        
        // Get form data
        const formData = {
            action: 'dab_frontend_login',
            nonce: dab_auth.nonce,
            username_or_email: $('#username_or_email').val(),
            password: $('#password').val(),
            remember: $('#remember').is(':checked') ? 'true' : 'false'
        };
        
        // Validate required fields
        if (!formData.username_or_email || !formData.password) {
            showError($errorDiv, dab_auth.messages.required_fields);
            return;
        }
        
        // Show loading state
        $submitBtn.addClass('loading');
        
        // Submit login request
        $.post(dab_auth.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showSuccess($successDiv, response.data.message);
                    
                    // Redirect after successful login
                    setTimeout(function() {
                        window.location.href = response.data.redirect_url || dab_auth.redirect_url;
                    }, 1000);
                } else {
                    showError($errorDiv, response.data || dab_auth.messages.login_error);
                }
            })
            .fail(function() {
                showError($errorDiv, dab_auth.messages.login_error);
            })
            .always(function() {
                $submitBtn.removeClass('loading');
            });
    });

    // Registration Form Handler
    $('#dab-register-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const $errorDiv = $('#dab-register-error');
        const $successDiv = $('#dab-register-success');
        
        // Clear previous messages
        $errorDiv.hide();
        $successDiv.hide();
        
        // Get form data
        const formData = {
            action: 'dab_frontend_register',
            nonce: dab_auth.nonce,
            username: $('#username').val(),
            email: $('#email').val(),
            password: $('#password').val(),
            first_name: $('#first_name').val(),
            last_name: $('#last_name').val()
        };
        
        // Validate required fields
        if (!formData.username || !formData.email || !formData.password) {
            showError($errorDiv, dab_auth.messages.required_fields);
            return;
        }
        
        // Validate password length
        if (formData.password.length < 6) {
            showError($errorDiv, dab_auth.messages.weak_password);
            return;
        }
        
        // Validate password confirmation
        const confirmPassword = $('#confirm_password').val();
        if (formData.password !== confirmPassword) {
            showError($errorDiv, dab_auth.messages.password_mismatch);
            return;
        }
        
        // Check terms agreement
        if (!$('#agree_terms').is(':checked')) {
            showError($errorDiv, 'You must agree to the terms and conditions.');
            return;
        }
        
        // Show loading state
        $submitBtn.addClass('loading');
        
        // Submit registration request
        $.post(dab_auth.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showSuccess($successDiv, response.data.message);
                    
                    // Clear form
                    $form[0].reset();
                    
                    // Redirect after successful registration
                    setTimeout(function() {
                        window.location.href = dab_auth.redirect_url;
                    }, 3000);
                } else {
                    showError($errorDiv, response.data || dab_auth.messages.register_error);
                }
            })
            .fail(function() {
                showError($errorDiv, dab_auth.messages.register_error);
            })
            .always(function() {
                $submitBtn.removeClass('loading');
            });
    });

    // Forgot Password Modal
    $('#dab-forgot-password-link').on('click', function(e) {
        e.preventDefault();
        $('#dab-forgot-password-modal').show();
    });

    $('#dab-close-forgot-modal').on('click', function() {
        $('#dab-forgot-password-modal').hide();
    });

    // Close modal when clicking outside
    $('#dab-forgot-password-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // Forgot Password Form Handler
    $('#dab-forgot-password-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const $errorDiv = $('#dab-forgot-error');
        const $successDiv = $('#dab-forgot-success');
        
        // Clear previous messages
        $errorDiv.hide();
        $successDiv.hide();
        
        // Get form data
        const formData = {
            action: 'dab_reset_password',
            nonce: dab_auth.nonce,
            email: $('#forgot_email').val()
        };
        
        // Validate email
        if (!formData.email) {
            showError($errorDiv, 'Email address is required.');
            return;
        }
        
        // Show loading state
        $submitBtn.addClass('loading');
        
        // Submit reset request
        $.post(dab_auth.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showSuccess($successDiv, response.data);
                    $form[0].reset();
                } else {
                    showError($errorDiv, response.data || 'Failed to send reset email.');
                }
            })
            .fail(function() {
                showError($errorDiv, 'Failed to send reset email.');
            })
            .always(function() {
                $submitBtn.removeClass('loading');
            });
    });

    // Logout Handler
    $(document).on('click', '#dab-logout-btn', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        
        // Show loading state
        $btn.addClass('loading');
        
        // Submit logout request
        $.post(dab_auth.ajax_url, {
            action: 'dab_frontend_logout',
            nonce: dab_auth.nonce
        })
        .done(function(response) {
            if (response.success) {
                // Redirect to login page
                window.location.href = response.data.redirect_url || '/login/';
            } else {
                alert('Logout failed. Please try again.');
            }
        })
        .fail(function() {
            alert('Logout failed. Please try again.');
        })
        .always(function() {
            $btn.removeClass('loading');
        });
    });

    // Profile Update Handler
    $('#dab-profile-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const $errorDiv = $('#dab-profile-error');
        const $successDiv = $('#dab-profile-success');
        
        // Clear previous messages
        $errorDiv.hide();
        $successDiv.hide();
        
        // Get form data
        const formData = {
            action: 'dab_update_profile',
            nonce: dab_auth.nonce,
            first_name: $('#first_name').val(),
            last_name: $('#last_name').val(),
            email: $('#email').val(),
            phone: $('#phone').val()
        };
        
        // Show loading state
        $submitBtn.addClass('loading');
        
        // Submit update request
        $.post(dab_auth.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showSuccess($successDiv, response.data);
                } else {
                    showError($errorDiv, response.data || 'Failed to update profile.');
                }
            })
            .fail(function() {
                showError($errorDiv, 'Failed to update profile.');
            })
            .always(function() {
                $submitBtn.removeClass('loading');
            });
    });

    // Password Change Handler
    $('#dab-password-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const $errorDiv = $('#dab-password-error');
        const $successDiv = $('#dab-password-success');
        
        // Clear previous messages
        $errorDiv.hide();
        $successDiv.hide();
        
        // Get form data
        const currentPassword = $('#current_password').val();
        const newPassword = $('#new_password').val();
        const confirmPassword = $('#confirm_new_password').val();
        
        // Validate required fields
        if (!currentPassword || !newPassword || !confirmPassword) {
            showError($errorDiv, 'All password fields are required.');
            return;
        }
        
        // Validate password length
        if (newPassword.length < 6) {
            showError($errorDiv, 'New password must be at least 6 characters long.');
            return;
        }
        
        // Validate password confirmation
        if (newPassword !== confirmPassword) {
            showError($errorDiv, 'New passwords do not match.');
            return;
        }
        
        // Show loading state
        $submitBtn.addClass('loading');
        
        // Submit password change request
        $.post(dab_auth.ajax_url, {
            action: 'dab_change_password',
            nonce: dab_auth.nonce,
            current_password: currentPassword,
            new_password: newPassword
        })
        .done(function(response) {
            if (response.success) {
                showSuccess($successDiv, response.data);
                $form[0].reset();
            } else {
                showError($errorDiv, response.data || 'Failed to change password.');
            }
        })
        .fail(function() {
            showError($errorDiv, 'Failed to change password.');
        })
        .always(function() {
            $submitBtn.removeClass('loading');
        });
    });

    // Helper Functions
    function showError($element, message) {
        $element.text(message).show();
        $('html, body').animate({
            scrollTop: $element.offset().top - 100
        }, 500);
    }

    function showSuccess($element, message) {
        $element.text(message).show();
        $('html, body').animate({
            scrollTop: $element.offset().top - 100
        }, 500);
    }

    // Real-time validation for registration form
    $('#password, #confirm_password').on('keyup', function() {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        const $confirmField = $('#confirm_password');
        
        if (confirmPassword && password !== confirmPassword) {
            $confirmField.css('border-color', '#dc3545');
        } else {
            $confirmField.css('border-color', '#ddd');
        }
    });

    // Email validation
    $('input[type="email"]').on('blur', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).css('border-color', '#dc3545');
        } else {
            $(this).css('border-color', '#ddd');
        }
    });
});
