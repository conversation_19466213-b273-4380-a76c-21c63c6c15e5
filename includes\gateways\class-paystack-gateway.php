<?php
/**
 * Paystack Payment Gateway
 *
 * This class handles Paystack payment processing for the Database App Builder plugin.
 */
if (!defined('ABSPATH')) exit;

class DAB_Paystack_Gateway {

    /**
     * Gateway ID
     */
    private $id = 'paystack';

    /**
     * Public Key
     */
    private $public_key = '';

    /**
     * Secret Key
     */
    private $secret_key = '';

    /**
     * Test Mode
     */
    private $test_mode = true;

    /**
     * Constructor
     */
    public function __construct() {
        // Load settings
        $this->public_key = DAB_Settings_Manager::get('paystack_public_key', '');
        $this->secret_key = DAB_Settings_Manager::get('paystack_secret_key', '');
        $this->test_mode = DAB_Settings_Manager::get('paystack_test_mode', 'yes') === 'yes';

        // If test mode is enabled, use test keys
        if ($this->test_mode) {
            $this->public_key = DAB_Settings_Manager::get('paystack_test_public_key', '');
            $this->secret_key = DAB_Settings_Manager::get('paystack_test_secret_key', '');
        }

        // Register AJAX handlers
        add_action('wp_ajax_dab_paystack_verify_payment', array($this, 'ajax_verify_payment'));
        add_action('wp_ajax_nopriv_dab_paystack_verify_payment', array($this, 'ajax_verify_payment'));
    }

    /**
     * Get gateway title
     */
    public function get_title() {
        return __('Paystack', 'db-app-builder');
    }

    /**
     * Get gateway description
     */
    public function get_description() {
        return __('Accept payments via Paystack.', 'db-app-builder');
    }

    /**
     * Check if gateway is configured
     */
    public function is_configured() {
        return !empty($this->public_key) && !empty($this->secret_key);
    }

    /**
     * Get settings fields
     */
    public function get_settings_fields() {
        return array(
            array(
                'name' => 'paystack_test_mode',
                'label' => __('Test Mode', 'db-app-builder'),
                'type' => 'select',
                'options' => array(
                    'yes' => __('Yes', 'db-app-builder'),
                    'no' => __('No', 'db-app-builder'),
                ),
                'default' => 'yes',
                'description' => __('Enable test mode to use Paystack test API keys.', 'db-app-builder'),
            ),
            array(
                'name' => 'paystack_test_public_key',
                'label' => __('Test Public Key', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your Paystack test public key.', 'db-app-builder'),
            ),
            array(
                'name' => 'paystack_test_secret_key',
                'label' => __('Test Secret Key', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your Paystack test secret key.', 'db-app-builder'),
            ),
            array(
                'name' => 'paystack_public_key',
                'label' => __('Live Public Key', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Enter your Paystack live public key.', 'db-app-builder'),
            ),
            array(
                'name' => 'paystack_secret_key',
                'label' => __('Live Secret Key', 'db-app-builder'),
                'type' => 'password',
                'description' => __('Enter your Paystack live secret key.', 'db-app-builder'),
            ),
        );
    }

    /**
     * Render payment form
     */
    public function render_payment_form($field, $options) {
        // Check if Paystack is configured
        if (!$this->is_configured()) {
            return '<div class="dab-payment-error">' . __('Paystack is not properly configured. Please contact the administrator.', 'db-app-builder') . '</div>';
        }

        // Get amount and currency
        $amount_type = isset($options['payment_amount_type']) ? $options['payment_amount_type'] : 'fixed';
        $amount = isset($options['payment_amount']) ? floatval($options['payment_amount']) : 0;
        $currency = isset($options['payment_currency']) ? $options['payment_currency'] : 'NGN';

        // Debug output
        error_log('Paystack payment options: ' . print_r($options, true));
        error_log('Amount: ' . $amount . ', Currency: ' . $currency);

        // Paystack only supports certain currencies
        $supported_currencies = array('NGN', 'USD', 'GHS', 'ZAR', 'KES');
        if (!in_array($currency, $supported_currencies)) {
            $currency = 'NGN';
        }

        // Generate a unique ID for this payment form
        $form_id = 'paystack-form-' . uniqid();

        // Get current user info
        $current_user = wp_get_current_user();
        $email = $current_user->user_email;
        if (empty($email)) {
            $email = 'guest_' . uniqid() . '@example.com';
        }

        // Start output buffer
        ob_start();

        ?>
        <div class="dab-paystack-form" id="<?php echo esc_attr($form_id); ?>">
            <button type="button" class="dab-paystack-button"><?php _e('Pay with Paystack', 'db-app-builder'); ?></button>
            <div class="dab-paystack-errors" role="alert"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Wait for Paystack SDK to load
            var paystackInterval = setInterval(function() {
                if (typeof PaystackPop !== 'undefined') {
                    clearInterval(paystackInterval);
                    initPaystack();
                }
            }, 100);

            function initPaystack() {
                $('.dab-paystack-button').on('click', function(e) {
                    e.preventDefault();

                    var handler = PaystackPop.setup({
                        key: '<?php echo esc_js($this->public_key); ?>',
                        email: '<?php echo esc_js($email); ?>',
                        amount: <?php echo esc_js($amount * 100); ?>, // Paystack amount is in kobo (100 kobo = 1 Naira)
                        currency: '<?php echo esc_js($currency); ?>',
                        ref: 'DAB_' + Math.floor((Math.random() * 1000000000) + 1),
                        callback: function(response) {
                            // Verify the transaction
                            $.ajax({
                                url: dabPayment.ajaxUrl,
                                type: 'POST',
                                dataType: 'json',
                                data: {
                                    action: 'dab_paystack_verify_payment',
                                    reference: response.reference,
                                    nonce: dabPayment.paystackNonce
                                },
                                success: function(response) {
                                    if (response.success) {
                                        // Payment succeeded
                                        $('#<?php echo esc_js($form_id); ?> .dab-paystack-errors').text('<?php _e('Payment successful!', 'db-app-builder'); ?>').removeClass('error').addClass('success');

                                        // Update payment status
                                        $('#<?php echo esc_js($field->field_slug); ?>').val(response.data.reference);

                                        // Notify the form that payment is complete
                                        $(document).trigger('dab_payment_complete', [response.data.reference]);
                                    } else {
                                        // Show error
                                        var errorMsg = response.data.message || '<?php _e('Error verifying payment', 'db-app-builder'); ?>';
                                        $('#<?php echo esc_js($form_id); ?> .dab-paystack-errors').text(errorMsg).addClass('error');
                                    }
                                },
                                error: function() {
                                    $('#<?php echo esc_js($form_id); ?> .dab-paystack-errors').text('<?php _e('Error connecting to server', 'db-app-builder'); ?>').addClass('error');
                                }
                            });
                        },
                        onClose: function() {
                            // Handle when the user closes the payment modal
                            $('#<?php echo esc_js($form_id); ?> .dab-paystack-errors').text('<?php _e('Payment cancelled', 'db-app-builder'); ?>').addClass('error');
                        }
                    });

                    handler.openIframe();
                });
            }
        });
        </script>
        <?php

        return ob_get_clean();
    }

    /**
     * AJAX handler to verify Paystack payment
     */
    public function ajax_verify_payment() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_paystack_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }

        // Check if Paystack is configured
        if (!$this->is_configured()) {
            wp_send_json_error(array('message' => __('Paystack is not properly configured', 'db-app-builder')));
        }

        // Get reference
        $reference = isset($_POST['reference']) ? sanitize_text_field($_POST['reference']) : '';

        if (empty($reference)) {
            wp_send_json_error(array('message' => __('Invalid payment reference', 'db-app-builder')));
        }

        try {
            // Verify the transaction
            $response = wp_remote_get('https://api.paystack.co/transaction/verify/' . $reference, array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $this->secret_key,
                ),
            ));

            if (is_wp_error($response)) {
                wp_send_json_error(array('message' => $response->get_error_message()));
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['status']) && $body['status'] === true && isset($body['data']['status']) && $body['data']['status'] === 'success') {
                wp_send_json_success(array(
                    'reference' => $reference,
                    'amount' => $body['data']['amount'] / 100, // Convert from kobo to Naira
                    'currency' => $body['data']['currency'],
                    'email' => $body['data']['customer']['email'],
                ));
            } else {
                wp_send_json_error(array('message' => __('Payment verification failed', 'db-app-builder')));
            }
        } catch (\Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Process webhook
     */
    public function process_webhook() {
        // Implementation will be added
    }
}
