<?php
/**
 * Enhanced Email Notifier
 *
 * This class provides improved email functionality to prevent emails from going to spam folders.
 */
if (!defined('ABSPATH')) exit;

class DAB_Enhanced_Email {

    /**
     * Send an enhanced email with proper headers and formatting
     *
     * @param string|array $to Recipient email address(es)
     * @param string $subject Email subject
     * @param string $message Email message (can be plain text or HTML)
     * @param array $args Optional arguments (from_name, from_email, reply_to, attachments, etc.)
     * @return bool Whether the email was sent successfully
     */
    public static function send($to, $subject, $message, $args = []) {
        // Get custom email settings if available
        $from_name = get_option('dab_email_from_name', get_bloginfo('name'));
        $from_email = get_option('dab_email_from_email', get_option('admin_email'));
        $reply_to = get_option('dab_email_reply_to', get_option('admin_email'));

        // Set default arguments
        $defaults = [
            'from_name' => $from_name,
            'from_email' => $from_email,
            'reply_to' => $reply_to,
            'cc' => '',
            'bcc' => '',
            'attachments' => [],
            'is_html' => true,
            'priority' => 'normal', // normal, high, low
            'tracking_id' => uniqid('dab-', true),
        ];

        // Merge defaults with provided args
        $args = wp_parse_args($args, $defaults);

        // Prepare headers
        $headers = [];

        // Set content type
        if ($args['is_html']) {
            $headers[] = 'Content-Type: text/html; charset=UTF-8';
        } else {
            $headers[] = 'Content-Type: text/plain; charset=UTF-8';
        }

        // Set From header with proper formatting
        $headers[] = sprintf('From: %s <%s>', $args['from_name'], $args['from_email']);

        // Set Reply-To header
        if (!empty($args['reply_to'])) {
            $headers[] = sprintf('Reply-To: %s', $args['reply_to']);
        }

        // Set CC header
        if (!empty($args['cc'])) {
            $headers[] = sprintf('Cc: %s', $args['cc']);
        }

        // Set BCC header
        if (!empty($args['bcc'])) {
            $headers[] = sprintf('Bcc: %s', $args['bcc']);
        }

        // Set priority headers
        if ($args['priority'] === 'high') {
            $headers[] = 'X-Priority: 1';
            $headers[] = 'X-MSMail-Priority: High';
            $headers[] = 'Importance: High';
        } elseif ($args['priority'] === 'low') {
            $headers[] = 'X-Priority: 5';
            $headers[] = 'X-MSMail-Priority: Low';
            $headers[] = 'Importance: Low';
        }

        // Add Message-ID header to help with spam prevention
        $headers[] = sprintf('Message-ID: <%s@%s>', $args['tracking_id'], $_SERVER['HTTP_HOST']);

        // Add X-Mailer header to identify the sender
        $headers[] = sprintf('X-Mailer: WordPress/%s | DB App Builder/%s', get_bloginfo('version'), DAB_VERSION);

        // Format message as HTML if needed
        if ($args['is_html']) {
            $message = self::format_html_email($message, $subject, $args);
        }

        // Send the email
        return wp_mail($to, $subject, $message, $headers, $args['attachments']);
    }

    /**
     * Format a message as HTML email with proper styling
     *
     * @param string $message The email message
     * @param string $subject The email subject
     * @param array $args Additional arguments
     * @return string Formatted HTML email
     */
    private static function format_html_email($message, $subject, $args) {
        // Convert line breaks to <br> tags if the message doesn't contain HTML
        if (strip_tags($message) === $message) {
            $message = nl2br($message);
        }

        // Get site info
        $site_name = get_bloginfo('name');
        $site_url = get_bloginfo('url');

        // Start HTML email template
        $html = '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>' . esc_html($subject) . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <h1 style="color: #0073aa; margin-top: 0; font-size: 24px;">' . esc_html($subject) . '</h1>
                <div style="background-color: #fff; padding: 20px; border-radius: 5px; border: 1px solid #e9ecef;">
                    ' . $message . '
                </div>
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d; text-align: center;">
                    <p>This email was sent from <a href="' . esc_url($site_url) . '" style="color: #0073aa; text-decoration: none;">' . esc_html($site_name) . '</a></p>
                    <p style="margin-top: 10px;">If you have any questions, please contact us at <a href="mailto:' . esc_attr($args['reply_to']) . '" style="color: #0073aa; text-decoration: none;">' . esc_html($args['reply_to']) . '</a></p>
                </div>
            </div>
        </body>
        </html>';

        return $html;
    }

    /**
     * Send approval notification to approvers
     *
     * @param int $table_id Table ID
     * @param int $record_id Record ID
     * @param object $level Approval level object
     * @return bool Whether the email was sent successfully
     */
    public static function send_approval_notification($table_id, $record_id, $level) {
        global $wpdb;

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        // Get record info
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) return false;

        // Get submitter info
        $submitter = get_userdata($record->user_id);
        $submitter_name = $submitter ? $submitter->display_name : 'Unknown';

        // Get admin URL for approval
        $admin_url = admin_url("admin.php?page=dab_approvals&table_id={$table_id}");

        // Prepare email content
        $subject = sprintf('Action Required: Approval for %s #%d', $table->table_label, $record_id);

        // Get all users with the required roles for this approval level
        $approver_roles = maybe_unserialize($level->approver_roles);
        $recipients = [];

        // Add the notification email if specified
        if (!empty($level->notification_email)) {
            $recipients[] = $level->notification_email;
        }

        // Get all users with the required roles
        if (!empty($approver_roles) && is_array($approver_roles)) {
            $users = get_users(['role__in' => $approver_roles]);
            foreach ($users as $user) {
                if (!in_array($user->user_email, $recipients)) {
                    $recipients[] = $user->user_email;
                }
            }
        }

        // Always include administrators - they should have access to all workflows by default
        $admins = get_users(['role' => 'administrator']);
        foreach ($admins as $admin) {
            if (!in_array($admin->user_email, $recipients)) {
                $recipients[] = $admin->user_email;
            }
        }

        // If no recipients, return
        if (empty($recipients)) {
            return false;
        }

        // Create HTML message
        $message = '<p>A record requires your approval.</p>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Table:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($table->table_label) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Record ID:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($record_id) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Submitted By:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($submitter_name) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Approval Level:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($level->level_name) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Date Submitted:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($record->created_at)) . '</td></tr>';
        $message .= '</table>';

        // Add record details
        $message .= '<h3>Record Details</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';

        // Get fields for this table
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d ORDER BY field_order",
            $table_id
        ));

        foreach ($fields as $field) {
            $field_value = isset($record->{$field->field_slug}) ? $record->{$field->field_slug} : '';

            // Format field value based on type
            if ($field->field_type === 'lookup' && !empty($field_value)) {
                $lookup_table_id = intval($field->lookup_table_id);
                $display_column = sanitize_text_field($field->lookup_display_column);
                if ($lookup_table_id && $display_column) {
                    $lookup_table_slug = $wpdb->get_var(
                        $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                    );
                    if ($lookup_table_slug) {
                        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                        $lookup_id = intval($field_value);
                        $field_value = $wpdb->get_var(
                            $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                        );
                    }
                }
            } elseif ($field->field_type === 'date' && !empty($field_value)) {
                $field_value = date_i18n(get_option('date_format'), strtotime($field_value));
            } elseif (($field->field_type === 'boolean' || $field->field_type === 'checkbox') && $field_value !== '') {
                $field_value = !empty($field_value) ? 'Yes' : 'No';
            }

            $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($field->field_label) . ':</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($field_value) . '</td></tr>';
        }

        $message .= '</table>';

        // Add direct approval link with token for security
        $token = wp_create_nonce('dab_approval_' . $record_id);
        $approve_url = add_query_arg([
            'page' => 'dab_approvals',
            'table_id' => $table_id,
            'record_id' => $record_id,
            'action' => 'approve',
            'token' => $token
        ], admin_url('admin.php'));

        $reject_url = add_query_arg([
            'page' => 'dab_approvals',
            'table_id' => $table_id,
            'record_id' => $record_id,
            'action' => 'reject',
            'token' => $token
        ], admin_url('admin.php'));

        // Add action buttons
        $message .= '<div style="margin: 20px 0;">';
        $message .= '<a href="' . esc_url($approve_url) . '" style="display: inline-block; padding: 10px 20px; background-color: #28a745; color: #ffffff; text-decoration: none; border-radius: 3px; margin-right: 10px;">Approve</a>';
        $message .= '<a href="' . esc_url($reject_url) . '" style="display: inline-block; padding: 10px 20px; background-color: #dc3545; color: #ffffff; text-decoration: none; border-radius: 3px; margin-right: 10px;">Reject</a>';
        $message .= '<a href="' . esc_url($admin_url) . '" style="display: inline-block; padding: 10px 20px; background-color: #0073aa; color: #ffffff; text-decoration: none; border-radius: 3px;">View All Approvals</a>';
        $message .= '</div>';

        // Add note about roles
        if (!empty($approver_roles) && is_array($approver_roles)) {
            $role_names = array_map(function($role) {
                $role_obj = get_role($role);
                return translate_user_role($role_obj->name);
            }, $approver_roles);

            $message .= '<p style="margin-top: 20px; color: #666;"><strong>Note:</strong> You are receiving this email because you have one of the following roles: ' . implode(', ', $role_names) . '.</p>';
        }

        // Add footer
        $message .= '<p style="color: #666; font-size: 12px; margin-top: 20px;">This is an automated message from the Database App Builder plugin. Please do not reply to this email.</p>';

        // Send the email to all recipients
        $success = true;
        foreach ($recipients as $recipient) {
            $result = self::send($recipient, $subject, $message, [
                'reply_to' => $submitter ? $submitter->user_email : '',
                'priority' => 'high',
                'tracking_id' => 'approval-' . $record_id . '-' . $level->id . '-' . uniqid(),
            ]);

            $success = $success && $result;
        }

        return $success;
    }

    /**
     * Notify submitter of approval status
     *
     * @param int $table_id Table ID
     * @param int $record_id Record ID
     * @param string $status Approval status (Approved or Rejected)
     * @param string $notes Approval notes
     * @return bool Whether the email was sent successfully
     */
    public static function notify_submitter($table_id, $record_id, $status, $notes) {
        global $wpdb;

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        // Get record info
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record || empty($record->user_id)) return false;

        // Get user info
        $user = get_userdata($record->user_id);
        if (!$user || empty($user->user_email)) return false;

        // Get approver info
        $approver_id = get_current_user_id();
        $approver = get_userdata($approver_id);
        $approver_name = $approver ? $approver->display_name : 'System';

        // Prepare email content
        $subject = sprintf('%s: Your %s record has been %s', get_bloginfo('name'), $table->table_label, strtolower($status));

        // Set status color
        $status_color = $status === 'Approved' ? '#28a745' : '#dc3545';

        // Create HTML message
        $message = '<p>Hello ' . esc_html($user->display_name) . ',</p>';
        $message .= '<p>Your submitted record has been <span style="font-weight: bold; color: ' . $status_color . ';">' . esc_html($status) . '</span>.</p>';

        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Table:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($table->table_label) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Record ID:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($record_id) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Status:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;"><span style="font-weight: bold; color: ' . $status_color . ';">' . esc_html($status) . '</span></td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">' . ($status === 'Approved' ? 'Approved' : 'Reviewed') . ' By:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($approver_name) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Date Submitted:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($record->created_at)) . '</td></tr>';
        $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Date ' . ($status === 'Approved' ? 'Approved' : 'Reviewed') . ':</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . date_i18n(get_option('date_format') . ' ' . get_option('time_format')) . '</td></tr>';

        if (!empty($notes)) {
            $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Notes:</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . nl2br(esc_html($notes)) . '</td></tr>';
        }

        $message .= '</table>';

        // Add record details
        $message .= '<h3>Record Details</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';

        // Get fields for this table
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d ORDER BY field_order",
            $table_id
        ));

        foreach ($fields as $field) {
            $field_value = isset($record->{$field->field_slug}) ? $record->{$field->field_slug} : '';

            // Format field value based on type
            if ($field->field_type === 'lookup' && !empty($field_value)) {
                $lookup_table_id = intval($field->lookup_table_id);
                $display_column = sanitize_text_field($field->lookup_display_column);
                if ($lookup_table_id && $display_column) {
                    $lookup_table_slug = $wpdb->get_var(
                        $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                    );
                    if ($lookup_table_slug) {
                        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                        $lookup_id = intval($field_value);
                        $field_value = $wpdb->get_var(
                            $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                        );
                    }
                }
            } elseif ($field->field_type === 'date' && !empty($field_value)) {
                $field_value = date_i18n(get_option('date_format'), strtotime($field_value));
            } elseif (($field->field_type === 'boolean' || $field->field_type === 'checkbox') && $field_value !== '') {
                $field_value = !empty($field_value) ? 'Yes' : 'No';
            }

            $message .= '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($field->field_label) . ':</th><td style="padding: 8px; border-bottom: 1px solid #ddd;">' . esc_html($field_value) . '</td></tr>';
        }

        $message .= '</table>';

        if ($status === 'Rejected') {
            $message .= '<div style="margin: 20px 0; padding: 15px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24;">';
            $message .= '<p><strong>Your record was rejected.</strong> You may resubmit your record with corrections.</p>';
            $message .= '</div>';
        } else {
            $message .= '<div style="margin: 20px 0; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">';
            $message .= '<p><strong>Your record was approved.</strong> No further action is required.</p>';
            $message .= '</div>';
        }

        // Add footer
        $message .= '<p style="color: #666; font-size: 12px; margin-top: 20px;">This is an automated message from the Database App Builder plugin. Please do not reply to this email.</p>';

        // Send the email
        return self::send($user->user_email, $subject, $message, [
            'from_name' => $approver_name,
            'reply_to' => $approver ? $approver->user_email : '',
            'priority' => 'high',
            'tracking_id' => 'status-' . $record_id . '-' . strtolower($status) . '-' . uniqid(),
        ]);
    }

    /**
     * Check if an email is likely to go to spam
     *
     * @param string $subject Email subject
     * @param string $message Email message
     * @param string $from_email From email address
     * @return array Array of warnings and suggestions
     */
    public static function check_spam_score($subject, $message, $from_email) {
        $warnings = [];

        // Check subject line for spam triggers
        $spam_subject_words = [
            'free', 'buy', 'discount', 'cash', 'credit', 'deal', 'cheap', 'save',
            'money', 'limited time', 'offer', 'click', 'urgent', 'important',
            'act now', 'apply now', 'hurry', 'last chance', 'winner', 'congratulations'
        ];

        $subject_lower = $subject !== null ? strtolower((string)$subject) : '';
        foreach ($spam_subject_words as $word) {
            if ($subject_lower !== '' && strpos($subject_lower, $word) !== false) {
                $warnings[] = "Subject contains potential spam trigger word: \"$word\"";
                break; // Only report one subject warning
            }
        }

        // Check for excessive capitalization in subject
        $subject_string = $subject !== null ? (string)$subject : '';
        if (strlen($subject_string) > 0 && (strlen(preg_replace('/[^A-Z]/', '', $subject_string)) / strlen($subject_string)) > 0.3) {
            $warnings[] = "Subject contains too many capital letters, which may trigger spam filters";
        }

        // Check for excessive exclamation marks
        if ($subject_string !== '' && substr_count($subject_string, '!') > 1) {
            $warnings[] = "Subject contains multiple exclamation marks, which may trigger spam filters";
        }

        // Check for dollar signs
        if ($subject_string !== '' && strpos($subject_string, '$') !== false) {
            $warnings[] = "Subject contains dollar signs, which may trigger spam filters";
        }

        // Check from email domain
        $domain = substr(strrchr($from_email, "@"), 1);
        if (!$domain || $domain === 'gmail.com' || $domain === 'yahoo.com' || $domain === 'hotmail.com') {
            $warnings[] = "Using a free email provider as your From address may trigger spam filters. Use an email from your website's domain instead.";
        }

        // Check for SPF and DKIM records
        if ($domain && function_exists('checkdnsrr')) {
            $has_spf = checkdnsrr($domain, 'TXT');
            if (!$has_spf) {
                $warnings[] = "Your domain may not have SPF records configured, which can affect email deliverability";
            }
        }

        // Check content for HTML without text alternative
        $message_string = is_null($message) ? '' : (string)$message;
        if ($message_string !== '' && strpos($message_string, '<html') !== false && strip_tags($message_string) === '') {
            $warnings[] = "Email contains HTML without a text alternative, which may trigger spam filters";
        }

        // Check for suspicious links
        $message_lower = strtolower($message_string);
        $link_count = 0;
        if ($message_lower !== '') {
            $link_count = substr_count($message_lower, '<a href=') + substr_count($message_lower, 'http://') + substr_count($message_lower, 'https://');
        }
        if ($link_count > 3) {
            $warnings[] = "Email contains multiple links, which may trigger spam filters";
        }

        // Check for image-only emails
        if ($message_lower !== '' && substr_count($message_lower, '<img') > 0 && strlen(strip_tags($message_string)) < 50) {
            $warnings[] = "Email appears to be mostly images with little text, which may trigger spam filters";
        }

        // Add general suggestions if no specific warnings
        if (empty($warnings)) {
            $warnings[] = "No obvious spam triggers detected, but email deliverability depends on many factors";
        }

        // Add general suggestions
        $suggestions = [
            "Use a professional email address from your website's domain",
            "Set up SPF and DKIM records for your domain",
            "Consider using an SMTP plugin for WordPress",
            "Balance text and images in your emails",
            "Avoid excessive links, all-caps text, and multiple exclamation marks",
            "Ask recipients to add your email address to their contacts"
        ];

        return [
            'warnings' => $warnings,
            'suggestions' => $suggestions
        ];
    }
}
