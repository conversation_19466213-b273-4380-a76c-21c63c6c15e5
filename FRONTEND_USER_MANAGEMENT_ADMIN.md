# Frontend User Management System - Admin Guide

## Overview

The Database App Builder plugin now includes a comprehensive frontend user management system with a powerful admin interface. This allows administrators to create, manage, and monitor frontend users who can access your database applications without needing WordPress admin access.

## Features

### Admin Interface Features

1. **User Management Dashboard**
   - View all frontend users in a paginated table
   - Search users by username, email, or name
   - Filter users by status (active, inactive, pending) and role
   - Bulk actions for user management

2. **User Statistics**
   - Total users count
   - Active users count
   - Pending users count
   - Verified users count
   - Today's registrations

3. **User Operations**
   - Create new users manually
   - Edit existing user details
   - Delete users with confirmation
   - Send verification emails
   - Export user data to CSV

4. **User Roles**
   - Admin: Full access to all features
   - Editor: Can edit data
   - User: Standard user access
   - Viewer: Read-only access

5. **User Status Management**
   - Active: User can log in and access the system
   - Inactive: User account is disabled
   - Pending: User registered but needs approval/verification

## Accessing the Admin Interface

1. Log in to your WordPress admin dashboard
2. Navigate to **Database App Builder** → **Frontend Users**
3. You'll see the user management interface with statistics and user list

## Managing Users

### Creating a New User

1. Click the **"Add New User"** button
2. Fill in the required information:
   - Username (required)
   - Email (required)
   - Password (required for new users)
   - First Name
   - Last Name
   - Role
   - Status
3. Click **"Save User"**

### Editing a User

1. Click the **"Edit"** button next to any user
2. Modify the user information in the modal
3. For password changes, enter a new password or leave blank to keep current
4. Click **"Save User"**

### Deleting a User

1. Click the **"Delete"** button next to any user
2. Confirm the deletion in the popup dialog
3. The user and all associated data will be permanently removed

### Sending Verification Emails

1. For unverified users, click the **"Verify"** button
2. Confirm sending the verification email
3. The user will receive an email with a verification link

### Bulk Actions

1. Select multiple users using the checkboxes
2. Choose an action from the "Bulk Actions" dropdown:
   - Activate: Set selected users to active status
   - Deactivate: Set selected users to inactive status
   - Delete: Remove selected users
3. Click **"Apply"**

## Filtering and Searching

### Search Users
- Use the search box in the top right to search by username, email, first name, or last name

### Filter by Status
- Use the status dropdown to filter users by their current status

### Filter by Role
- Use the role dropdown to filter users by their assigned role

## Exporting User Data

1. Click the **"Export CSV"** button in the page header
2. A CSV file will be downloaded containing all user data
3. The CSV includes: ID, Username, Email, Name, Role, Status, Verification status, and timestamps

## User Statistics Dashboard

The statistics cards at the top of the page provide quick insights:

- **Total Users**: All registered frontend users
- **Active Users**: Users who can currently log in
- **Pending Users**: Users awaiting approval or verification
- **Verified Users**: Users who have verified their email addresses
- **Today's Registrations**: New users registered today

## Frontend User Database Tables

The system creates and manages several database tables:

1. **wp_dab_frontend_users**: Main user data
2. **wp_dab_user_sessions**: Active user sessions
3. **wp_dab_user_meta**: Additional user metadata
4. **wp_dab_user_dashboard_configs**: User dashboard configurations
5. **wp_dab_user_access_log**: User activity logs

## Security Features

- **Nonce Verification**: All admin actions are protected with WordPress nonces
- **Capability Checks**: Only users with 'manage_options' capability can access admin features
- **Password Hashing**: User passwords are securely hashed
- **Session Management**: Secure session handling with expiration
- **Email Verification**: Optional email verification for new registrations

## Integration with Existing Features

The frontend user system integrates seamlessly with:

- **Role-based Permissions**: Users inherit permissions based on their assigned roles
- **Data Access Control**: Users can only access data they have permissions for
- **Dashboard System**: Users can have personalized dashboards
- **Chat System**: Users can participate in chat groups based on permissions
- **Approval Workflows**: Users can submit data for approval

## Customization Options

### User Roles
You can modify the available roles by editing the role dropdown in the admin interface. The system supports custom roles that integrate with the existing permission system.

### User Fields
Additional user fields can be added by modifying the database schema and admin interface forms.

### Email Templates
Verification and password reset email templates can be customized by modifying the email sending functions in the Frontend User Manager class.

## Troubleshooting

### Common Issues

1. **Users can't log in**
   - Check if user status is "active"
   - Verify email verification status if required
   - Check user permissions

2. **Verification emails not sending**
   - Verify WordPress email configuration
   - Check spam folders
   - Ensure proper SMTP setup

3. **CSV export not working**
   - Check user permissions
   - Verify server write permissions
   - Check for PHP memory limits

### Debug Information

Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## API and Hooks

### Available AJAX Actions

- `dab_get_user_stats`: Get user statistics
- `dab_export_users_csv`: Export users to CSV
- `dab_send_verification_email`: Send verification email to user

### WordPress Hooks

The system uses standard WordPress hooks for integration:
- `init`: Initialize the user management system
- `wp_ajax_*`: Handle AJAX requests
- `admin_menu`: Add admin menu items

## Best Practices

1. **Regular Backups**: Always backup your database before bulk operations
2. **User Permissions**: Assign appropriate roles based on user needs
3. **Email Verification**: Enable email verification for security
4. **Session Cleanup**: The system automatically cleans up expired sessions
5. **Monitor Activity**: Regularly review user statistics and activity logs

This comprehensive frontend user management system provides administrators with full control over frontend users while maintaining security and ease of use.
