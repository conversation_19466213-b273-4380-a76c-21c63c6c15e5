<?php
/**
 * Wizard Manager Class
 *
 * Manages the guided setup wizards for common tasks
 */
if (!defined('ABSPATH')) exit;

class DAB_Wizard_Manager {

    /**
     * Initialize the class
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_save_wizard_progress', array($this, 'save_wizard_progress'));
        add_action('wp_ajax_dab_get_wizard_data', array($this, 'get_wizard_data'));

        // Register scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Safely handle hook parameter to prevent deprecated warnings
        $hook = function_exists('dab_safe_string') ? dab_safe_string($hook) : ($hook !== null ? (string)$hook : '');

        // Only load on wizard pages
        $safe_strpos = function_exists('dab_safe_strpos') ? 'dab_safe_strpos' : 'strpos';
        if ($hook === '' || $safe_strpos($hook, 'dab_wizard') === false) {
            return;
        }

        // Enqueue wizard styles
        wp_enqueue_style('dab-wizard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/wizard.css', array(), DAB_VERSION);

        // Enqueue wizard script
        wp_enqueue_script('dab-wizard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/wizard.js', array('jquery'), DAB_VERSION, true);

        // Localize script
        wp_localize_script('dab-wizard', 'dab_wizard', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'admin_url' => admin_url(),
            'nonce' => wp_create_nonce('dab_wizard_nonce'),
            'strings' => array(
                'next' => __('Next', 'db-app-builder'),
                'previous' => __('Previous', 'db-app-builder'),
                'finish' => __('Finish', 'db-app-builder'),
                'saving' => __('Saving...', 'db-app-builder'),
                'error' => __('An error occurred. Please try again.', 'db-app-builder'),
                'confirm_exit' => __('Are you sure you want to exit the wizard? Your progress will be saved.', 'db-app-builder')
            )
        ));
    }

    /**
     * Save wizard progress via AJAX
     */
    public function save_wizard_progress() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_wizard_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Get wizard data
        $wizard_type = sanitize_text_field($_POST['wizard_type']);
        $step = intval($_POST['step']);
        $data = isset($_POST['data']) ? $_POST['data'] : array();

        // Sanitize data
        $data = $this->sanitize_wizard_data($data);

        // Get existing progress
        $progress = get_option('dab_wizard_progress', array());

        // Update progress
        if (!isset($progress[$wizard_type])) {
            $progress[$wizard_type] = array();
        }

        // Merge with existing data instead of replacing
        if (isset($progress[$wizard_type]['data']) && is_array($progress[$wizard_type]['data'])) {
            $progress[$wizard_type]['data'] = array_merge($progress[$wizard_type]['data'], $data);
        } else {
            $progress[$wizard_type]['data'] = $data;
        }

        $progress[$wizard_type]['current_step'] = $step;
        $progress[$wizard_type]['last_updated'] = current_time('mysql');

        // Process data based on wizard type and step
        $this->process_wizard_data($wizard_type, $step, $progress[$wizard_type]['data']);

        // Save progress
        update_option('dab_wizard_progress', $progress);

        wp_send_json_success(array(
            'message' => __('Progress saved', 'db-app-builder'),
            'step' => $step,
            'data' => $progress[$wizard_type]['data']
        ));
    }

    /**
     * Get wizard data via AJAX
     */
    public function get_wizard_data() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_wizard_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Get wizard type
        $wizard_type = sanitize_text_field($_POST['wizard_type']);

        // Get progress
        $progress = get_option('dab_wizard_progress', array());

        if (isset($progress[$wizard_type])) {
            wp_send_json_success($progress[$wizard_type]);
        } else {
            wp_send_json_success(array(
                'current_step' => 1,
                'data' => array()
            ));
        }
    }

    /**
     * Sanitize wizard data
     */
    private function sanitize_wizard_data($data) {
        if (!is_array($data)) {
            return array();
        }

        $sanitized = array();

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitize_wizard_data($value);
            } else {
                $sanitized[$key] = sanitize_text_field($value);
            }
        }

        return $sanitized;
    }

    /**
     * Get wizard progress
     */
    public function get_wizard_progress($wizard_type) {
        $progress = get_option('dab_wizard_progress', array());

        if (isset($progress[$wizard_type])) {
            return $progress[$wizard_type];
        }

        return array(
            'current_step' => 1,
            'data' => array()
        );
    }

    /**
     * Reset wizard progress
     */
    public function reset_wizard_progress($wizard_type) {
        $progress = get_option('dab_wizard_progress', array());

        if (isset($progress[$wizard_type])) {
            unset($progress[$wizard_type]);
            update_option('dab_wizard_progress', $progress);
        }
    }

    /**
     * Process wizard data based on wizard type and step
     *
     * This method handles any special processing needed for each step of the wizard
     *
     * @param string $wizard_type The type of wizard
     * @param int $step The current step
     * @param array $data The wizard data
     */
    private function process_wizard_data($wizard_type, $step, &$data) {
        global $wpdb;

        switch ($wizard_type) {
            case 'app_creation':
                $this->process_app_creation_data($step, $data);
                break;

            case 'workflow_setup':
                $this->process_workflow_setup_data($step, $data);
                break;

            case 'dashboard_builder':
                $this->process_dashboard_builder_data($step, $data);
                break;
        }
    }

    /**
     * Process application creation wizard data
     *
     * @param int $step The current step
     * @param array $data The wizard data
     */
    private function process_app_creation_data($step, &$data) {
        global $wpdb;

        switch ($step) {
            case 2: // Table Structure
                // Generate table slug if not provided
                if (!empty($data['table_name']) && empty($data['table_slug'])) {
                    $data['table_slug'] = sanitize_title($data['table_name']);
                }
                break;

            case 3: // Fields Configuration
                // Create table if it doesn't exist
                if (!empty($data['table_name']) && !empty($data['table_slug'])) {
                    $tables_table = $wpdb->prefix . 'dab_tables';

                    // Check if table exists
                    $existing_table = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $tables_table WHERE table_slug = %s",
                        $data['table_slug']
                    ));

                    if (!$existing_table) {
                        // Create the table
                        $wpdb->insert($tables_table, [
                            'table_label' => $data['table_name'],
                            'table_slug' => $data['table_slug'],
                            'description' => isset($data['table_description']) ? $data['table_description'] : '',
                            'created_at' => current_time('mysql'),
                        ]);

                        $table_id = $wpdb->insert_id;
                        $data['table_id'] = $table_id;

                        // Create the data table
                        DAB_DB_Manager::create_data_table($data['table_slug']);
                    } else {
                        $data['table_id'] = $existing_table->id;
                    }
                }
                break;
        }
    }

    /**
     * Process workflow setup wizard data
     *
     * @param int $step The current step
     * @param array $data The wizard data
     */
    private function process_workflow_setup_data($step, &$data) {
        global $wpdb;

        // Process workflow setup data
        switch ($step) {
            case 1: // Select Table
                // Nothing to process here
                break;
        }
    }

    /**
     * Process dashboard builder wizard data
     *
     * @param int $step The current step
     * @param array $data The wizard data
     */
    private function process_dashboard_builder_data($step, &$data) {
        global $wpdb;

        // Process dashboard builder data
        switch ($step) {
            case 1: // Dashboard Details
                // Nothing to process here
                break;
        }
    }
}

// Initialize Wizard Manager
new DAB_Wizard_Manager();
