<?php
/**
 * Payment Settings Page
 *
 * This file contains the payment settings page for the Database App Builder plugin.
 */
if (!defined('ABSPATH')) exit;

// Check if form is submitted
if (isset($_POST['dab_save_payment_settings'])) {
    // Verify nonce
    if (!isset($_POST['dab_payment_settings_nonce']) || !wp_verify_nonce($_POST['dab_payment_settings_nonce'], 'dab_payment_settings')) {
        echo '<div class="notice notice-error is-dismissible"><p>' . __('Security check failed', 'db-app-builder') . '</p></div>';
    } else {
        // Save Stripe settings
        if (isset($_POST['stripe_test_mode'])) {
            DAB_Settings_Manager::set('stripe_test_mode', sanitize_text_field($_POST['stripe_test_mode']));
        }
        if (isset($_POST['stripe_test_publishable_key'])) {
            DAB_Settings_Manager::set('stripe_test_publishable_key', sanitize_text_field($_POST['stripe_test_publishable_key']));
        }
        if (isset($_POST['stripe_test_secret_key'])) {
            DAB_Settings_Manager::set('stripe_test_secret_key', sanitize_text_field($_POST['stripe_test_secret_key']));
        }
        if (isset($_POST['stripe_publishable_key'])) {
            DAB_Settings_Manager::set('stripe_publishable_key', sanitize_text_field($_POST['stripe_publishable_key']));
        }
        if (isset($_POST['stripe_secret_key'])) {
            DAB_Settings_Manager::set('stripe_secret_key', sanitize_text_field($_POST['stripe_secret_key']));
        }
        if (isset($_POST['stripe_webhook_secret'])) {
            DAB_Settings_Manager::set('stripe_webhook_secret', sanitize_text_field($_POST['stripe_webhook_secret']));
        }
        
        // Save PayPal settings
        if (isset($_POST['paypal_test_mode'])) {
            DAB_Settings_Manager::set('paypal_test_mode', sanitize_text_field($_POST['paypal_test_mode']));
        }
        if (isset($_POST['paypal_sandbox_client_id'])) {
            DAB_Settings_Manager::set('paypal_sandbox_client_id', sanitize_text_field($_POST['paypal_sandbox_client_id']));
        }
        if (isset($_POST['paypal_sandbox_client_secret'])) {
            DAB_Settings_Manager::set('paypal_sandbox_client_secret', sanitize_text_field($_POST['paypal_sandbox_client_secret']));
        }
        if (isset($_POST['paypal_client_id'])) {
            DAB_Settings_Manager::set('paypal_client_id', sanitize_text_field($_POST['paypal_client_id']));
        }
        if (isset($_POST['paypal_client_secret'])) {
            DAB_Settings_Manager::set('paypal_client_secret', sanitize_text_field($_POST['paypal_client_secret']));
        }
        if (isset($_POST['paypal_webhook_id'])) {
            DAB_Settings_Manager::set('paypal_webhook_id', sanitize_text_field($_POST['paypal_webhook_id']));
        }
        
        // Save Paystack settings
        if (isset($_POST['paystack_test_mode'])) {
            DAB_Settings_Manager::set('paystack_test_mode', sanitize_text_field($_POST['paystack_test_mode']));
        }
        if (isset($_POST['paystack_test_public_key'])) {
            DAB_Settings_Manager::set('paystack_test_public_key', sanitize_text_field($_POST['paystack_test_public_key']));
        }
        if (isset($_POST['paystack_test_secret_key'])) {
            DAB_Settings_Manager::set('paystack_test_secret_key', sanitize_text_field($_POST['paystack_test_secret_key']));
        }
        if (isset($_POST['paystack_public_key'])) {
            DAB_Settings_Manager::set('paystack_public_key', sanitize_text_field($_POST['paystack_public_key']));
        }
        if (isset($_POST['paystack_secret_key'])) {
            DAB_Settings_Manager::set('paystack_secret_key', sanitize_text_field($_POST['paystack_secret_key']));
        }
        
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Payment settings saved successfully!', 'db-app-builder') . '</p></div>';
    }
}

// Load current settings
$stripe_test_mode = DAB_Settings_Manager::get('stripe_test_mode', 'yes');
$stripe_test_publishable_key = DAB_Settings_Manager::get('stripe_test_publishable_key', '');
$stripe_test_secret_key = DAB_Settings_Manager::get('stripe_test_secret_key', '');
$stripe_publishable_key = DAB_Settings_Manager::get('stripe_publishable_key', '');
$stripe_secret_key = DAB_Settings_Manager::get('stripe_secret_key', '');
$stripe_webhook_secret = DAB_Settings_Manager::get('stripe_webhook_secret', '');

$paypal_test_mode = DAB_Settings_Manager::get('paypal_test_mode', 'yes');
$paypal_sandbox_client_id = DAB_Settings_Manager::get('paypal_sandbox_client_id', '');
$paypal_sandbox_client_secret = DAB_Settings_Manager::get('paypal_sandbox_client_secret', '');
$paypal_client_id = DAB_Settings_Manager::get('paypal_client_id', '');
$paypal_client_secret = DAB_Settings_Manager::get('paypal_client_secret', '');
$paypal_webhook_id = DAB_Settings_Manager::get('paypal_webhook_id', '');

$paystack_test_mode = DAB_Settings_Manager::get('paystack_test_mode', 'yes');
$paystack_test_public_key = DAB_Settings_Manager::get('paystack_test_public_key', '');
$paystack_test_secret_key = DAB_Settings_Manager::get('paystack_test_secret_key', '');
$paystack_public_key = DAB_Settings_Manager::get('paystack_public_key', '');
$paystack_secret_key = DAB_Settings_Manager::get('paystack_secret_key', '');

// Get active tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'stripe';
?>

<div class="wrap dab-admin-wrap">
    <h1><?php _e('Payment Gateway Settings', 'db-app-builder'); ?></h1>
    
    <p class="description"><?php _e('Configure payment gateways to accept payments through your forms.', 'db-app-builder'); ?></p>
    
    <h2 class="nav-tab-wrapper">
        <a href="?page=dab_payment_settings&tab=stripe" class="nav-tab <?php echo $active_tab === 'stripe' ? 'nav-tab-active' : ''; ?>"><?php _e('Stripe', 'db-app-builder'); ?></a>
        <a href="?page=dab_payment_settings&tab=paypal" class="nav-tab <?php echo $active_tab === 'paypal' ? 'nav-tab-active' : ''; ?>"><?php _e('PayPal', 'db-app-builder'); ?></a>
        <a href="?page=dab_payment_settings&tab=paystack" class="nav-tab <?php echo $active_tab === 'paystack' ? 'nav-tab-active' : ''; ?>"><?php _e('Paystack', 'db-app-builder'); ?></a>
    </h2>
    
    <form method="post" action="">
        <?php wp_nonce_field('dab_payment_settings', 'dab_payment_settings_nonce'); ?>
        
        <?php if ($active_tab === 'stripe'): ?>
            <div id="stripe-settings" class="dab-settings-tab">
                <h3><?php _e('Stripe Settings', 'db-app-builder'); ?></h3>
                
                <div class="dab-settings-info">
                    <p><?php _e('Stripe allows you to accept credit card payments directly on your forms.', 'db-app-builder'); ?></p>
                    <p><?php _e('To get your API keys:', 'db-app-builder'); ?></p>
                    <ol>
                        <li><?php _e('Create a <a href="https://dashboard.stripe.com/register" target="_blank">Stripe account</a> if you don\'t have one.', 'db-app-builder'); ?></li>
                        <li><?php _e('Go to the <a href="https://dashboard.stripe.com/apikeys" target="_blank">API keys section</a> in your Stripe Dashboard.', 'db-app-builder'); ?></li>
                        <li><?php _e('Copy your Publishable key and Secret key.', 'db-app-builder'); ?></li>
                    </ol>
                </div>
                
                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Mode', 'db-app-builder'); ?></th>
                        <td>
                            <select name="stripe_test_mode">
                                <option value="yes" <?php selected($stripe_test_mode, 'yes'); ?>><?php _e('Yes', 'db-app-builder'); ?></option>
                                <option value="no" <?php selected($stripe_test_mode, 'no'); ?>><?php _e('No', 'db-app-builder'); ?></option>
                            </select>
                            <p class="description"><?php _e('Enable test mode to use Stripe test API keys.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Publishable Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="stripe_test_publishable_key" value="<?php echo esc_attr($stripe_test_publishable_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Stripe test publishable key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Secret Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="stripe_test_secret_key" value="<?php echo esc_attr($stripe_test_secret_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Stripe test secret key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Publishable Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="stripe_publishable_key" value="<?php echo esc_attr($stripe_publishable_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Stripe live publishable key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Secret Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="stripe_secret_key" value="<?php echo esc_attr($stripe_secret_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Stripe live secret key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Webhook Secret', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="stripe_webhook_secret" value="<?php echo esc_attr($stripe_webhook_secret); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Stripe webhook secret for payment status updates.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
        <?php elseif ($active_tab === 'paypal'): ?>
            <div id="paypal-settings" class="dab-settings-tab">
                <h3><?php _e('PayPal Settings', 'db-app-builder'); ?></h3>
                
                <div class="dab-settings-info">
                    <p><?php _e('PayPal allows you to accept payments through PayPal accounts and credit cards.', 'db-app-builder'); ?></p>
                    <p><?php _e('To get your API credentials:', 'db-app-builder'); ?></p>
                    <ol>
                        <li><?php _e('Create a <a href="https://developer.paypal.com/developer/applications/" target="_blank">PayPal Developer account</a> if you don\'t have one.', 'db-app-builder'); ?></li>
                        <li><?php _e('Create a new REST API app in the PayPal Developer Dashboard.', 'db-app-builder'); ?></li>
                        <li><?php _e('Copy your Client ID and Secret.', 'db-app-builder'); ?></li>
                    </ol>
                </div>
                
                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Mode', 'db-app-builder'); ?></th>
                        <td>
                            <select name="paypal_test_mode">
                                <option value="yes" <?php selected($paypal_test_mode, 'yes'); ?>><?php _e('Yes', 'db-app-builder'); ?></option>
                                <option value="no" <?php selected($paypal_test_mode, 'no'); ?>><?php _e('No', 'db-app-builder'); ?></option>
                            </select>
                            <p class="description"><?php _e('Enable test mode to use PayPal sandbox.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Sandbox Client ID', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="paypal_sandbox_client_id" value="<?php echo esc_attr($paypal_sandbox_client_id); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your PayPal sandbox client ID.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Sandbox Client Secret', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="paypal_sandbox_client_secret" value="<?php echo esc_attr($paypal_sandbox_client_secret); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your PayPal sandbox client secret.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Client ID', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="paypal_client_id" value="<?php echo esc_attr($paypal_client_id); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your PayPal live client ID.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Client Secret', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="paypal_client_secret" value="<?php echo esc_attr($paypal_client_secret); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your PayPal live client secret.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Webhook ID', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="paypal_webhook_id" value="<?php echo esc_attr($paypal_webhook_id); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your PayPal webhook ID for payment status updates.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
        <?php elseif ($active_tab === 'paystack'): ?>
            <div id="paystack-settings" class="dab-settings-tab">
                <h3><?php _e('Paystack Settings', 'db-app-builder'); ?></h3>
                
                <div class="dab-settings-info">
                    <p><?php _e('Paystack allows you to accept payments through various methods including cards, bank transfers, and mobile money.', 'db-app-builder'); ?></p>
                    <p><?php _e('To get your API keys:', 'db-app-builder'); ?></p>
                    <ol>
                        <li><?php _e('Create a <a href="https://dashboard.paystack.com/#/signup" target="_blank">Paystack account</a> if you don\'t have one.', 'db-app-builder'); ?></li>
                        <li><?php _e('Go to the <a href="https://dashboard.paystack.com/#/settings/developer" target="_blank">API Keys & Webhooks section</a> in your Paystack Dashboard.', 'db-app-builder'); ?></li>
                        <li><?php _e('Copy your Public key and Secret key.', 'db-app-builder'); ?></li>
                    </ol>
                </div>
                
                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Mode', 'db-app-builder'); ?></th>
                        <td>
                            <select name="paystack_test_mode">
                                <option value="yes" <?php selected($paystack_test_mode, 'yes'); ?>><?php _e('Yes', 'db-app-builder'); ?></option>
                                <option value="no" <?php selected($paystack_test_mode, 'no'); ?>><?php _e('No', 'db-app-builder'); ?></option>
                            </select>
                            <p class="description"><?php _e('Enable test mode to use Paystack test API keys.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Public Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="paystack_test_public_key" value="<?php echo esc_attr($paystack_test_public_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Paystack test public key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Test Secret Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="paystack_test_secret_key" value="<?php echo esc_attr($paystack_test_secret_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Paystack test secret key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Public Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="paystack_public_key" value="<?php echo esc_attr($paystack_public_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Paystack live public key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                    
                    <tr valign="top">
                        <th scope="row"><?php _e('Live Secret Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="paystack_secret_key" value="<?php echo esc_attr($paystack_secret_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Paystack live secret key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
        <?php endif; ?>
        
        <p><input type="submit" name="dab_save_payment_settings" class="button-primary" value="<?php _e('Save Settings', 'db-app-builder'); ?>"></p>
    </form>
</div>

<style>
.dab-settings-info {
    background: #f8f9fa;
    border-left: 4px solid #0073aa;
    padding: 15px;
    margin: 15px 0;
}
.dab-settings-info ol {
    margin-left: 20px;
}
</style>
