/**
 * Database App Builder Wizard
 *
 * JavaScript for the guided setup wizards
 */
(function($) {
    'use strict';

    // Initialize wizard when document is ready
    $(document).ready(function() {
        console.log('Wizard script loaded');
        if ($('.dab-wizard-container').length) {
            console.log('Wizard container found, initializing wizard');
            initWizard();
        } else {
            console.log('Wizard container not found');
        }
    });

    /**
     * Initialize wizard
     */
    function initWizard() {
        const $wizardContainer = $('.dab-wizard-container');
        const wizardType = $wizardContainer.data('wizard-type');

        console.log('Wizard container data:', $wizardContainer.data());
        console.log('Wizard type from container:', wizardType);

        // If wizard type is not available from the container, try to get it from the localized data
        const finalWizardType = wizardType || (typeof dab_wizard_data !== 'undefined' ? dab_wizard_data.wizard_type : null);

        console.log('Final wizard type:', finalWizardType);

        if (!finalWizardType) {
            console.error('Wizard type not found. Trying to extract from URL...');

            // Try to extract from URL
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');

            if (pageParam && pageParam.startsWith('dab_wizard_')) {
                const extractedType = pageParam.replace('dab_wizard_', '');
                console.log('Extracted wizard type from URL:', extractedType);

                // Store the extracted type in a global variable
                window.extractedWizardType = extractedType;

                // Initialize with the extracted type
                loadWizardData(extractedType);
                initWizardNavigation(extractedType);
                initFormHandling(extractedType);
                initAutoSave(extractedType);
                initExitConfirmation();

                return;
            }

            console.error('Could not extract wizard type from URL. Wizard initialization failed.');
            return;
        }

        // Store the wizard type in a global variable
        window.wizardType = finalWizardType;

        // Initialize wizard data
        let wizardData = {};

        // Load wizard data
        loadWizardData(finalWizardType);

        // Initialize wizard navigation
        initWizardNavigation(finalWizardType);

        // Initialize form handling
        initFormHandling(finalWizardType);

        // Initialize auto-save
        initAutoSave(finalWizardType);

        // Initialize exit confirmation
        initExitConfirmation();
    }

    /**
     * Load wizard data
     */
    function loadWizardData(wizardType) {
        $.ajax({
            url: dab_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_wizard_data',
                wizard_type: wizardType,
                nonce: dab_wizard.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Store wizard data
                    window.wizardData = response.data;

                    // Populate form fields
                    populateFormFields(response.data.data);
                }
            }
        });
    }

    /**
     * Populate form fields with saved data
     */
    function populateFormFields(data) {
        if (!data) return;

        // Loop through data and populate form fields
        Object.keys(data).forEach(function(key) {
            const $field = $('[name="' + key + '"]');

            if ($field.length) {
                const value = data[key];

                // Handle different field types
                if ($field.is(':checkbox')) {
                    $field.prop('checked', value === 'on' || value === true);
                } else if ($field.is(':radio')) {
                    $field.filter('[value="' + value + '"]').prop('checked', true);
                } else if ($field.is('select')) {
                    $field.val(value);
                    $field.trigger('change'); // Trigger change for select2 and other enhanced selects
                } else {
                    $field.val(value);
                }
            }
        });
    }

    /**
     * Initialize wizard navigation
     */
    function initWizardNavigation(wizardType) {
        console.log('Initializing wizard navigation for type:', wizardType);

        // Next button click
        $('.dab-wizard-next-btn').on('click', function() {
            console.log('Next button clicked');
            const nextStep = $(this).data('step');
            console.log('Navigating to step:', nextStep);
            navigateToStep(wizardType, nextStep);
        });

        // Previous button click
        $('.dab-wizard-prev-btn').on('click', function() {
            console.log('Previous button clicked');
            const prevStep = $(this).data('step');
            console.log('Navigating to step:', prevStep);
            navigateToStep(wizardType, prevStep);
        });

        // Finish button click
        $('.dab-wizard-finish-btn').on('click', function() {
            console.log('Finish button clicked');
            finishWizard(wizardType);
        });
    }

    /**
     * Navigate to a specific step
     */
    function navigateToStep(wizardType, step) {
        console.log('Navigating to step:', step, 'for wizard type:', wizardType);

        // Use the global wizard type if available
        if (!wizardType && window.wizardType) {
            wizardType = window.wizardType;
            console.log('Using global wizard type:', wizardType);
        }

        // Use the extracted wizard type if available
        if (!wizardType && window.extractedWizardType) {
            wizardType = window.extractedWizardType;
            console.log('Using extracted wizard type:', wizardType);
        }

        if (!wizardType) {
            console.error('Wizard type is not defined. Cannot navigate.');

            // Try to get the wizard type from the URL
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');

            if (pageParam && pageParam.startsWith('dab_wizard_')) {
                const extractedType = pageParam.replace('dab_wizard_', '');
                console.log('Extracted wizard type from URL:', extractedType);
                wizardType = extractedType;
            } else {
                console.error('Could not extract wizard type from URL. Using fallback navigation.');
                // Fallback to simple navigation without saving
                window.location.href = window.location.pathname +
                    window.location.search.replace(/&?step=\d+/, '') +
                    '&step=' + step;
                return;
            }
        }

        // Get form data before navigation
        const formData = {};

        $('.dab-wizard-body input, .dab-wizard-body select, .dab-wizard-body textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');

            if (name) {
                if ($field.is(':checkbox')) {
                    formData[name] = $field.is(':checked') ? 'on' : 'off';
                } else if ($field.is(':radio')) {
                    if ($field.is(':checked')) {
                        formData[name] = $field.val();
                    }
                } else {
                    formData[name] = $field.val();
                }
            }
        });

        console.log('Form data before navigation:', formData);

        // Special handling for table creation in step 2
        if (step === 3 && formData.table_name) {
            // Generate table slug if not provided
            if (!formData.table_slug) {
                formData.table_slug = formData.table_name.toLowerCase()
                    .replace(/[^a-z0-9]+/g, '_')
                    .replace(/^_+|_+$/g, '');
            }
        }

        // Save current step data
        saveWizardProgress(wizardType, function() {
            console.log('Progress saved, redirecting to step:', step);

            // Redirect to the new step
            const baseUrl = window.location.pathname + window.location.search.replace(/&?step=\d+/, '');
            const newUrl = baseUrl + (baseUrl.indexOf('?') !== -1 ? '&' : '?') + 'step=' + step;

            console.log('Redirecting to:', newUrl);
            window.location.href = newUrl;
        }, formData);
    }

    /**
     * Finish wizard
     */
    function finishWizard(wizardType) {
        // Save final step data
        saveWizardProgress(wizardType, function() {
            // Process final actions based on wizard type
            switch (wizardType) {
                case 'app_creation':
                    // Redirect to the new application
                    window.location.href = dab_wizard.admin_url + 'admin.php?page=dab_tables';
                    break;

                case 'workflow_setup':
                    // Redirect to the approval dashboard
                    window.location.href = dab_wizard.admin_url + 'admin.php?page=dab_approvals';
                    break;

                case 'dashboard_builder':
                    // Redirect to the dashboard preview
                    const dashboardId = window.wizardData.data.dashboard_id;
                    if (dashboardId) {
                        window.location.href = dab_wizard.admin_url + 'admin.php?page=dab_dashboard_preview&id=' + dashboardId;
                    } else {
                        window.location.href = dab_wizard.admin_url + 'admin.php?page=dab_dashboard_examples';
                    }
                    break;

                default:
                    // Redirect to wizards dashboard
                    window.location.href = dab_wizard.admin_url + 'admin.php?page=dab_wizards';
            }
        });
    }

    /**
     * Initialize form handling
     */
    function initFormHandling(wizardType) {
        // Form field change
        $('.dab-wizard-body input, .dab-wizard-body select, .dab-wizard-body textarea').on('change', function() {
            // Mark form as changed
            window.wizardFormChanged = true;
        });

        // Clear form after successful submission
        $(document).on('dab:form:submitted', function(e, formId) {
            // Reset the form
            $('#' + formId).trigger('reset');

            // Reset form changed flag
            window.wizardFormChanged = false;
        });
    }

    /**
     * Initialize auto-save
     */
    function initAutoSave(wizardType) {
        // Auto-save every 30 seconds
        setInterval(function() {
            if (window.wizardFormChanged) {
                saveWizardProgress(wizardType);
            }
        }, 30000);
    }

    /**
     * Save wizard progress
     */
    function saveWizardProgress(wizardType, callback, customFormData) {
        // Get current step
        const currentStep = parseInt($('.dab-wizard-step.active').index()) + 1;

        console.log('Saving progress for step:', currentStep, 'wizard type:', wizardType);

        // Get form data
        let formData = customFormData || {};

        if (!customFormData) {
            $('.dab-wizard-body input, .dab-wizard-body select, .dab-wizard-body textarea').each(function() {
                const $field = $(this);
                const name = $field.attr('name');

                if (name) {
                    if ($field.is(':checkbox')) {
                        formData[name] = $field.is(':checked') ? 'on' : 'off';
                    } else if ($field.is(':radio')) {
                        if ($field.is(':checked')) {
                            formData[name] = $field.val();
                        }
                    } else {
                        formData[name] = $field.val();
                    }
                }
            });
        }

        console.log('Form data:', formData);

        // Show save indicator
        $('.dab-wizard-save-indicator').addClass('active');

        // If no AJAX URL is available, call the callback directly
        if (!dab_wizard || !dab_wizard.ajax_url) {
            console.error('AJAX URL not available. Cannot save progress.');
            $('.dab-wizard-save-indicator').removeClass('active');

            if (typeof callback === 'function') {
                callback();
            }
            return;
        }

        // Save data
        $.ajax({
            url: dab_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_save_wizard_progress',
                wizard_type: wizardType,
                step: currentStep,
                data: formData,
                nonce: dab_wizard.nonce
            },
            success: function(response) {
                console.log('Progress saved response:', response);

                // Reset form changed flag
                window.wizardFormChanged = false;

                // Hide save indicator after a delay
                setTimeout(function() {
                    $('.dab-wizard-save-indicator').removeClass('active');
                }, 1000);

                // Call callback if provided
                if (typeof callback === 'function') {
                    callback();
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving progress:', error);

                // Hide save indicator
                $('.dab-wizard-save-indicator').removeClass('active');

                // Show error message
                alert(dab_wizard.strings ? dab_wizard.strings.error : 'An error occurred. Please try again.');

                // Call callback anyway to allow navigation
                if (typeof callback === 'function') {
                    callback();
                }
            }
        });
    }

    /**
     * Initialize exit confirmation
     */
    function initExitConfirmation() {
        // Confirm before leaving if form has unsaved changes
        $(window).on('beforeunload', function() {
            if (window.wizardFormChanged) {
                return dab_wizard.strings.confirm_exit;
            }
        });
    }

})(jQuery);
