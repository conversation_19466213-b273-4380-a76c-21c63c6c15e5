/**
 * Enhanced Approval Dashboard
 *
 * This script provides a modern, user-friendly interface for the approval workflow system.
 */
jQuery(document).ready(function($) {
    // Fix for dropdown menus
    fixDropdowns();

    // Initialize approval dashboard
    initApprovalDashboard();

    /**
     * Initialize the approval dashboard
     */
    function initApprovalDashboard() {



        // Initialize loading overlay
        $('.dab-loading-overlay').css('display', 'flex').hide();

        // Toggle approval history
        $('.view-history-btn').on('click', function() {
            const recordId = $(this).data('record');
            $('#history-' + recordId).slideToggle();

            // Change button text
            if ($(this).text() === 'View History') {
                $(this).text('Hide History');
            } else {
                $(this).text('View History');
            }
        });

        // Initialize approval modals
        initApprovalModals();

        // Initialize approval filters
        initApprovalFilters();

        // Initialize approval notifications
        initApprovalNotifications();

        // Initialize approval comments
        initApprovalComments();

        // Initialize approval status indicators
        updateApprovalStatusIndicators();
    }

    /**
     * Initialize approval modals
     */
    function initApprovalModals() {
        // Open approval modal when clicking approve/reject buttons
        $('.dab-approve-btn, .dab-reject-btn').on('click', function(e) {
            e.preventDefault();

            const action = $(this).hasClass('dab-approve-btn') ? 'approve' : 'reject';
            // Use attr() instead of data() to ensure we get the raw attribute value
            const recordId = $(this).attr('data-record');
            const tableId = $(this).attr('data-table');
            const recordTitle = $(this).attr('data-title') || 'Record #' + recordId;



            // Validate data before proceeding
            if (!recordId || recordId === 'undefined') {

                alert("Error: Record ID is missing. Please try again.");
                return;
            }

            if (!tableId || tableId === 'undefined') {

                alert("Error: Table ID is missing. Please try again.");
                return;
            }

            // Populate modal with record data
            $('#dab-approval-modal-title').text(action === 'approve' ? 'Approve Record' : 'Reject Record');

            // Make sure we have a valid record title
            const displayTitle = recordTitle || ('Record #' + recordId);


            // Store the record ID and title as data attributes on the modal
            $('#dab-approval-modal').attr('data-record-id', recordId).attr('data-table-id', tableId);

            $('#dab-approval-record-title').text(displayTitle);
            $('#dab-approval-action').val(action);
            $('#dab-approval-record-id').val(recordId);
            $('#dab-approval-table-id').val(tableId);

            // Clear previous notes
            $('#dab-approval-note').val('');

            // Show appropriate button
            $('.dab-modal-approve-btn, .dab-modal-reject-btn').hide();
            if (action === 'approve') {
                $('.dab-modal-approve-btn').show();
            } else {
                $('.dab-modal-reject-btn').show();
            }

            // Show modal
            $('#dab-approval-modal').css('display', 'flex').show();
            $('.dab-modal-overlay').show();
        });

        // Close modal when clicking close button, cancel button, or overlay
        $('.dab-modal-close, .dab-modal-close-btn, .dab-modal-overlay').on('click', function() {
            $('#dab-approval-modal').hide();
            $('#dab-record-details-modal').hide();
            $('.dab-modal-overlay').hide();
        });

        // Direct click handlers for approve/reject buttons in the modal
        $('#dab-modal-approve-button, #dab-modal-reject-button').on('click', function(e) {
            e.preventDefault();

            // Determine action based on which button was clicked
            const isApprove = $(this).attr('id') === 'dab-modal-approve-button';
            const action = isApprove ? 'approve' : 'reject';

            // Get record and table IDs from the hidden fields
            let recordId = $('#dab-approval-record-id').val();
            let tableId = $('#dab-approval-table-id').val();
            const note = $('#dab-approval-note').val();

            // If the hidden fields don't have values, try getting from the modal's data attributes
            if (!recordId || recordId === 'undefined') {
                recordId = $('#dab-approval-modal').attr('data-record-id');

            }

            if (!tableId || tableId === 'undefined') {
                tableId = $('#dab-approval-modal').attr('data-table-id');

            }



            // Validate data before proceeding
            if (!recordId || recordId === 'undefined') {

                alert("Error: Record ID is missing. Please try again.");
                return;
            }

            if (!tableId || tableId === 'undefined') {

                alert("Error: Table ID is missing. Please try again.");
                return;
            }

            // Show loading state
            const button = $(this);
            const originalText = button.text();
            button.text('Processing...').prop('disabled', true);

            // Show loading overlay
            $('.dab-loading-overlay').show();

            // Create a data object for the AJAX request
            const ajaxData = {
                action: 'dab_process_approval',
                dab_approve_action: action,
                record_id: recordId,
                table_id: tableId,
                approval_note: note,
                nonce: dab_vars.approval_nonce || dab_vars.nonce
            };



            // Make the AJAX request
            $.ajax({
                url: dab_vars.ajax_url,
                type: 'POST',
                data: ajaxData,
                success: function(response) {


                    // Hide loading overlay
                    $('.dab-loading-overlay').hide();

                    // Hide modal
                    $('#dab-approval-modal').hide();
                    $('.dab-modal-overlay').hide();

                    if (response.success) {


                        // Show success message
                        const message = action === 'approve' ?
                            'Record has been approved successfully.' :
                            'Record has been rejected successfully.';

                        // Add success message with better styling
                        const successNotice = $('<div class="dab-notice dab-notice-success"><p>' + message + '</p><button type="button" class="dab-notice-dismiss">&times;</button></div>');
                        $('.dab-dashboard-header').after(successNotice);

                        // Auto-dismiss after 5 seconds
                        setTimeout(function() {
                            successNotice.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 5000);

                        // Click to dismiss
                        successNotice.find('.dab-notice-dismiss').on('click', function() {
                            successNotice.fadeOut(function() {
                                $(this).remove();
                            });
                        });

                        // Find the record element - try multiple ways
                        let recordElement = $('.dab-approval-record[data-record-id="' + recordId + '"]');

                        // If not found by data-record-id, try finding by buttons
                        if (!recordElement.length) {
                            recordElement = $('.dab-approval-record').filter(function() {
                                return $(this).find('button[data-record="' + recordId + '"]').length > 0;
                            });
                        }

                        if (recordElement.length) {


                            // Update the status attribute
                            recordElement.attr('data-status', action === 'approve' ? 'Approved' : 'Rejected');

                            // Update the status badge
                            const statusBadge = recordElement.find('.dab-approval-status');
                            statusBadge.removeClass('dab-status-pending dab-status-approved dab-status-rejected')
                                      .addClass('dab-status-' + action.toLowerCase());
                            statusBadge.text(action === 'approve' ? 'Approved' : 'Rejected');

                            // Update the buttons based on the new status
                            if (action === 'approve') {
                                // If approved, hide approve button and show only reject button
                                recordElement.find('.dab-approve-btn').hide();
                                recordElement.find('.dab-reject-btn').show();
                            } else {
                                // If rejected, hide reject button and show only approve button
                                recordElement.find('.dab-reject-btn').hide();
                                recordElement.find('.dab-approve-btn').show();
                            }

                            // Add approval info if it doesn't exist
                            if (recordElement.find('.dab-approval-info').length === 0) {
                                const currentUser = dab_vars.current_user_name || 'You';
                                const infoText = action === 'approve' ?
                                    'Approved by: ' + currentUser :
                                    'Rejected by: ' + currentUser;
                                const infoSpan = $('<span class="dab-approval-info">' + infoText + '</span>');
                                recordElement.find('.dab-record-actions').append(infoSpan);
                            } else {
                                // Update existing approval info
                                const currentUser = dab_vars.current_user_name || 'You';
                                const infoText = action === 'approve' ?
                                    'Approved by: ' + currentUser :
                                    'Rejected by: ' + currentUser;
                                recordElement.find('.dab-approval-info').text(infoText);
                            }

                            // Update counts - this will reflect the change in the dashboard counters
                            updateApprovalCounts();

                            // Reload the page after a short delay to ensure counters and buttons are updated
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {

                            window.location.reload();
                        }
                    } else {
                        // Show error message
                        const errorMessage = response.data || 'An error occurred. Please try again.';

                        alert(errorMessage);

                        // Reset button
                        button.text(originalText).prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading overlay
                    $('.dab-loading-overlay').hide();

                    // Show error message with details
                    console.error("AJAX error:", status, error);
                    console.error("Response text:", xhr.responseText);

                    let errorMsg = 'An error occurred. Please try again.';
                    try {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse && jsonResponse.data) {
                            errorMsg = jsonResponse.data;
                        }
                    } catch (e) {

                    }

                    alert("Error: " + errorMsg);

                    // Reset button
                    button.text(originalText).prop('disabled', false);

                    // Hide modal
                    $('#dab-approval-modal').hide();
                    $('.dab-modal-overlay').hide();
                }
            });
        });



        // View record details in modal
        $('.dab-view-record-btn').on('click', function(e) {
            e.preventDefault();

            // Use attr() instead of data() to ensure we get the raw attribute value
            const recordId = $(this).attr('data-record');
            const tableId = $(this).attr('data-table');



            // Validate data before proceeding
            if (!recordId || recordId === 'undefined') {

                alert("Error: Record ID is missing. Please try again.");
                return;
            }

            if (!tableId || tableId === 'undefined') {

                alert("Error: Table ID is missing. Please try again.");
                return;
            }

            // Show loading overlay
            $('.dab-loading-overlay').show();

            // Fetch record details via AJAX
            $.ajax({
                url: dab_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_record_details',
                    record_id: recordId,
                    table_id: tableId,
                    nonce: dab_vars.approval_nonce || dab_vars.nonce
                },
                success: function(response) {
                    // Hide loading overlay
                    $('.dab-loading-overlay').hide();

                    if (response.success) {

                        // Populate modal with record details
                        $('#dab-record-details-content').html(response.data.html);
                        $('#dab-record-details-modal').css('display', 'flex').show();
                        $('.dab-modal-overlay').show();
                    } else {

                        alert("Error: " + (response.data.message || "Failed to retrieve record details"));
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading overlay
                    $('.dab-loading-overlay').hide();


                    alert("Error: Failed to retrieve record details. Please try again.");
                }
            });
        });
    }

    /**
     * Initialize approval filters
     */
    function initApprovalFilters() {
        // Filter by status
        $('#dab-filter-status').on('change', function() {
            const status = $(this).val();


            // Apply filters
            applyFilters();
        });

        // Filter by date range
        $('#dab-filter-date-from, #dab-filter-date-to').on('change', function() {
            // Apply filters
            applyFilters();
        });

        // Search records
        $('#dab-approval-search').on('input', function() {
            // Apply filters with a slight delay for better performance
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(function() {
                applyFilters();
            }, 300);
        });

        // Clear filters button
        $('.dab-clear-filters').on('click', function() {
            // Reset all filters
            $('#dab-filter-status').val('all');
            $('#dab-filter-date-from, #dab-filter-date-to').val('');
            $('#dab-approval-search').val('');

            // Show all records
            $('.dab-approval-record').show();

            // Update counts
            updateApprovalCounts();

            // Show success message
            const message = $('<div class="dab-notice dab-notice-success"><p>Filters have been cleared.</p><button type="button" class="dab-notice-dismiss">&times;</button></div>');
            $('.dab-dashboard-filters').after(message);

            // Auto-dismiss after 3 seconds
            setTimeout(function() {
                message.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);

            // Click to dismiss
            message.find('.dab-notice-dismiss').on('click', function() {
                message.fadeOut(function() {
                    $(this).remove();
                });
            });
        });

        // Initial filter application (in case there are URL parameters)
        applyFilters();
    }

    /**
     * Apply all filters
     */
    function applyFilters() {
        // Get filter values
        const status = $('#dab-filter-status').val();
        const fromDate = $('#dab-filter-date-from').val();
        const toDate = $('#dab-filter-date-to').val();
        const searchText = $('#dab-approval-search').val().toLowerCase();

        // Show all records initially
        $('.dab-approval-record').show();

        // Apply status filter
        if (status !== 'all') {
            $('.dab-approval-record').not('[data-status="' + status + '"]').hide();
        }

        // Apply date filter
        if (fromDate || toDate) {
            $('.dab-approval-record:visible').each(function() {
                const recordDate = $(this).attr('data-date');
                let showRecord = true;

                if (fromDate && recordDate < fromDate) {
                    showRecord = false;
                }

                if (toDate && recordDate > toDate) {
                    showRecord = false;
                }

                if (!showRecord) {
                    $(this).hide();
                }
            });
        }

        // Apply search filter
        if (searchText) {
            $('.dab-approval-record:visible').each(function() {
                const recordText = $(this).text().toLowerCase();
                if (!recordText.includes(searchText)) {
                    $(this).hide();
                }
            });
        }

        // Update counts
        updateApprovalCounts();

        // Add active filter indicators
        updateFilterIndicators();
    }

    /**
     * Update filter indicators
     */
    function updateFilterIndicators() {
        // Remove existing indicators
        $('.dab-active-filters').remove();

        // Get active filters
        const activeFilters = [];

        const status = $('#dab-filter-status').val();
        if (status !== 'all') {
            activeFilters.push('Status: ' + status);
        }

        const fromDate = $('#dab-filter-date-from').val();
        if (fromDate) {
            activeFilters.push('From: ' + fromDate);
        }

        const toDate = $('#dab-filter-date-to').val();
        if (toDate) {
            activeFilters.push('To: ' + toDate);
        }

        const searchText = $('#dab-approval-search').val();
        if (searchText) {
            activeFilters.push('Search: ' + searchText);
        }

        // If there are active filters, show them
        if (activeFilters.length > 0) {
            const filterIndicators = $('<div class="dab-active-filters"></div>');

            // Add each filter as a badge
            activeFilters.forEach(function(filter) {
                filterIndicators.append('<span class="dab-filter-badge">' + filter + '</span>');
            });

            // Add clear button
            filterIndicators.append('<button type="button" class="dab-clear-filters">Clear All</button>');

            // Add to the page
            $('.dab-dashboard-filters').after(filterIndicators);

            // Reinitialize clear filters button
            $('.dab-clear-filters').on('click', function() {
                // Reset all filters
                $('#dab-filter-status').val('all');
                $('#dab-filter-date-from, #dab-filter-date-to').val('');
                $('#dab-approval-search').val('');

                // Show all records
                $('.dab-approval-record').show();

                // Update counts
                updateApprovalCounts();

                // Remove filter indicators
                $('.dab-active-filters').remove();
            });
        }
    }

    /**
     * Update approval counts
     */
    function updateApprovalCounts() {
        // Get counts of visible records by status
        const totalVisible = $('.dab-approval-record:visible').length;
        const pendingVisible = $('.dab-approval-record[data-status="Pending"]:visible').length;
        const approvedVisible = $('.dab-approval-record[data-status="Approved"]:visible').length;
        const rejectedVisible = $('.dab-approval-record[data-status="Rejected"]:visible').length;

        // Get counts of assigned records (those with data-assigned="true")
        const assignedVisible = $('.dab-approval-record[data-assigned="true"]:visible').length;
        const assignedPendingVisible = $('.dab-approval-record[data-assigned="true"][data-status="Pending"]:visible').length;

        // Update the counter displays with animation
        animateCounter($('#dab-count-total'), totalVisible);
        animateCounter($('#dab-count-pending'), pendingVisible);
        animateCounter($('#dab-count-approved'), approvedVisible);
        animateCounter($('#dab-count-rejected'), rejectedVisible);

        // Also update the original stats if they exist
        if ($('.dab-stat-pending .dab-stat-number').length) {
            animateCounter($('.dab-stat-pending .dab-stat-number'), pendingVisible);

            // Update the "assigned to you" text if it exists
            if ($('.dab-stat-user-pending').length) {
                $('.dab-stat-user-pending').text(assignedPendingVisible + ' assigned to you');

                // Highlight if there are assigned records
                if (assignedPendingVisible > 0) {
                    $('.dab-stat-user-pending').addClass('dab-highlight-count');
                } else {
                    $('.dab-stat-user-pending').removeClass('dab-highlight-count');
                }
            }
        }

        if ($('.dab-stat-approved .dab-stat-number').length) {
            animateCounter($('.dab-stat-approved .dab-stat-number'), approvedVisible);
        }

        if ($('.dab-stat-rejected .dab-stat-number').length) {
            animateCounter($('.dab-stat-rejected .dab-stat-number'), rejectedVisible);
        }

        if ($('.dab-stat-total .dab-stat-number').length) {
            animateCounter($('.dab-stat-total .dab-stat-number'), totalVisible);
        }

        // If no visible records, show empty state message
        if (totalVisible === 0) {
            // Check if empty state already exists
            if ($('.dab-empty-state').length === 0) {
                const emptyState = $('<div class="dab-empty-state">' +
                    '<div class="dab-empty-icon">📋</div>' +
                    '<h3>No Records Found</h3>' +
                    '<p>There are no records matching your current filter criteria.</p>' +
                    '<button type="button" class="dab-clear-filters dab-empty-state-btn">Clear Filters</button>' +
                    '</div>');

                // If approval records container exists, replace its contents
                if ($('.dab-approval-records').length) {
                    $('.dab-approval-records').html(emptyState);
                } else {
                    // Otherwise append after the filters
                    $('.dab-dashboard-filters').after(emptyState);
                }

                // Initialize clear filters button in empty state
                $('.dab-empty-state-btn').on('click', function() {
                    // Reset all filters
                    $('#dab-filter-status').val('all');
                    $('#dab-filter-date-from, #dab-filter-date-to').val('');
                    $('#dab-approval-search').val('');

                    // Apply filters
                    applyFilters();
                });
            }
        } else {
            // If we have records but empty state is showing, remove it
            if ($('.dab-empty-state').length) {
                $('.dab-empty-state').remove();
            }
        }

        // Update the page title to show pending count
        if (pendingVisible > 0) {
            document.title = '(' + pendingVisible + ') Approval Dashboard';
        } else {
            document.title = 'Approval Dashboard';
        }
    }

    /**
     * Animate counter from current value to new value
     */
    function animateCounter($element, newValue) {
        // Get current value
        const currentValue = parseInt($element.text()) || 0;

        // If the values are the same, no need to animate
        if (currentValue === newValue) {
            return;
        }

        // Determine if we're counting up or down
        const countUp = newValue > currentValue;

        // Add highlight class
        $element.addClass('dab-counter-highlight');

        // Remove highlight class after animation
        setTimeout(function() {
            $element.removeClass('dab-counter-highlight');
        }, 1000);

        // Set the new value immediately
        $element.text(newValue);
    }

    /**
     * Initialize approval notifications
     */
    function initApprovalNotifications() {
        // Check for new approvals periodically
        if ($('.dab-approval-dashboard').length) {
            setInterval(checkForNewApprovals, 60000); // Check every minute
        }

        // Initialize cancel submission button
        $('.dab-cancel-submission-btn').on('click', function() {
            const recordId = $(this).data('record');
            const tableId = $(this).data('table');

            if (confirm('Are you sure you want to cancel this submission? This will mark it as rejected.')) {
                // Show loading state
                $(this).addClass('dab-loading');

                // Send cancel request
                $.ajax({
                    url: dab_vars.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'dab_cancel_submission',
                        nonce: dab_vars.approval_nonce || dab_vars.nonce,
                        record_id: recordId,
                        table_id: tableId
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            alert('Submission cancelled successfully.');

                            // Reload the page to reflect changes
                            window.location.reload();
                        } else {
                            alert('Error: ' + (response.data || 'Failed to cancel submission'));
                        }
                    },
                    error: function() {
                        alert('Error: Failed to cancel submission');
                    },
                    complete: function() {
                        // Remove loading state
                        $('.dab-cancel-submission-btn').removeClass('dab-loading');
                    }
                });
            }
        });
    }

    /**
     * Check for new approvals
     */
    function checkForNewApprovals() {
        const lastCheck = localStorage.getItem('dabLastApprovalCheck') || 0;

        $.ajax({
            url: dab_vars.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_check_new_approvals',
                last_check: lastCheck,
                nonce: dab_vars.nonce
            },
            success: function(response) {
                if (response.success && response.data.count > 0) {
                    // Show notification
                    showApprovalNotification(response.data.count);

                    // Update last check time
                    localStorage.setItem('dabLastApprovalCheck', response.data.timestamp);
                }
            }
        });
    }

    /**
     * Show approval notification
     */
    function showApprovalNotification(count) {
        const notification = $('<div class="dab-notification">You have ' + count + ' new item(s) to approve</div>');
        $('body').append(notification);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);

        // Click to dismiss
        notification.on('click', function() {
            $(this).fadeOut(function() {
                $(this).remove();
            });
        });
    }

    /**
     * Fix dropdown menus to ensure they display properly
     */
    function fixDropdowns() {
        // Ensure select elements have proper styling and behavior
        $('select.dab-filter-select, select[name="table_id"]').each(function() {
            // Ensure the select has the proper appearance
            $(this).css({
                'position': 'relative',
                'z-index': '100',
                'height': 'auto',
                'appearance': 'menulist',
                '-webkit-appearance': 'menulist',
                '-moz-appearance': 'menulist'
            });

            // Add click handler to ensure dropdown opens
            $(this).on('mousedown', function(e) {
                e.stopPropagation();
            });
        });

        // Fix any parent elements that might be interfering with the dropdown
        $('select.dab-filter-select, select[name="table_id"]').parents().css('overflow', 'visible');
    }

    /**
     * Initialize approval comments
     */
    function initApprovalComments() {
        // Toggle comment form
        $('.dab-add-comment-btn').on('click', function() {
            const recordId = $(this).data('record');
            $('#dab-comment-form-' + recordId).slideToggle();
        });

        // Submit comment
        $('.dab-comment-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const recordId = form.data('record');
            const tableId = form.data('table');
            const comment = form.find('.dab-comment-text').val();

            if (!comment) return;

            $.ajax({
                url: dab_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_add_approval_comment',
                    record_id: recordId,
                    table_id: tableId,
                    comment: comment,
                    nonce: dab_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Add comment to list
                        const commentHtml = '<div class="dab-comment">' +
                            '<div class="dab-comment-author">' + response.data.author + '</div>' +
                            '<div class="dab-comment-date">' + response.data.date + '</div>' +
                            '<div class="dab-comment-text">' + response.data.comment + '</div>' +
                            '</div>';

                        $('#dab-comments-' + recordId).append(commentHtml);

                        // Clear form
                        form.find('.dab-comment-text').val('');
                        form.slideUp();
                    }
                }
            });
        });
    }

    /**
     * Update approval status indicators
     */
    function updateApprovalStatusIndicators() {
        $('.dab-approval-status').each(function() {
            const status = $(this).data('status');

            // Add appropriate class
            $(this).addClass('dab-status-' + status.toLowerCase());
        });
    }
});
