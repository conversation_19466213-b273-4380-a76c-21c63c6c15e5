/**
 * Simple Dashboard View Styles
 */

/* Dashboard View Container */
.dab-dashboard-view-wrap {
    margin: 20px 0;
}

.dab-dashboard-view-container {
    margin-top: 20px;
}

.dab-dashboard-description {
    margin-bottom: 20px;
    color: #555;
}

/* Dashboard Grid */
.dab-dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: 400px;
    padding: 10px;
}

/* Row Styles */
.dab-dashboard-row {
    display: flex;
    gap: 15px;
    min-height: 100px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
}

/* Column Styles */
.dab-dashboard-column {
    flex: 1 1 0;
    min-height: 100px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 15px;
    box-sizing: border-box;
}

/* Dashboard Widget */
.dab-dashboard-widget {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 100px;
}

.dab-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f9f9f9;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.dab-widget-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.dab-widget-actions {
    display: flex;
    gap: 5px;
}

.dab-widget-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.dab-widget-actions button:hover {
    background-color: #f0f0f0;
    color: #0073aa;
}

.dab-widget-content {
    flex: 1;
    padding: 15px;
    overflow: auto;
    position: relative;
}

.dab-widget-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.dab-widget-loading .spinner {
    float: none;
    margin: 0 0 10px 0;
}

.dab-widget-data {
    height: 100%;
}

/* No Widgets Message */
.dab-no-widgets {
    grid-column: 1 / -1;
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.dab-no-widgets p {
    margin-bottom: 20px;
    color: #555;
}

/* Widget Types */

/* Table Widget */
.dab-widget-table {
    width: 100%;
    border-collapse: collapse;
}

.dab-widget-table th,
.dab-widget-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.dab-widget-table th {
    background-color: #f9f9f9;
    font-weight: 600;
}

.dab-widget-table tr:hover {
    background-color: #f9f9f9;
}

/* Chart Widget */
.dab-widget-chart-container {
    width: 100%;
    height: 100%;
    min-height: 200px;
}

/* Metric Widget */
.dab-widget-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.dab-widget-metric-value {
    font-size: 36px;
    font-weight: 700;
    color: #0073aa;
    margin-bottom: 10px;
}

.dab-widget-metric-label {
    font-size: 14px;
    color: #555;
}

/* Text Widget */
.dab-widget-text {
    padding: 10px;
}

/* KPI Widget */
.dab-widget-kpi {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 15px;
    border-radius: 5px;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.dab-kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dab-kpi-icon {
    font-size: 24px;
    color: #555;
}

.dab-kpi-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f0f0f0;
}

.dab-kpi-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.dab-kpi-value {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 5px;
}

.dab-kpi-label {
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
}

.dab-kpi-target {
    font-size: 12px;
    color: #777;
    margin-top: 5px;
}

/* KPI Status Colors */
.dab-kpi-success .dab-kpi-status {
    background-color: #d4edda;
}

.dab-kpi-success .dab-kpi-status .dashicons {
    color: #28a745;
}

.dab-kpi-warning .dab-kpi-status {
    background-color: #fff3cd;
}

.dab-kpi-warning .dab-kpi-status .dashicons {
    color: #ffc107;
}

.dab-kpi-danger .dab-kpi-status {
    background-color: #f8d7da;
}

.dab-kpi-danger .dab-kpi-status .dashicons {
    color: #dc3545;
}

/* KPI Color Themes */
.dab-color-primary .dab-kpi-value {
    color: #0073aa;
}

.dab-color-success .dab-kpi-value {
    color: #28a745;
}

.dab-color-warning .dab-kpi-value {
    color: #ffc107;
}

.dab-color-danger .dab-kpi-value {
    color: #dc3545;
}

.dab-color-info .dab-kpi-value {
    color: #17a2b8;
}

/* List Widget */
.dab-widget-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto;
}

.dab-list-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.dab-list-item:last-child {
    border-bottom: none;
}

.dab-list-item:hover {
    background-color: #f9f9f9;
}

.dab-list-item-icon {
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-list-item-icon .dashicons {
    font-size: 20px;
    color: #0073aa;
}

.dab-list-item-content {
    flex: 1;
}

.dab-list-item-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.dab-list-item-description {
    font-size: 12px;
    color: #777;
}

/* Progress Widget */
.dab-widget-progress {
    padding: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.dab-progress-label {
    font-weight: 600;
    margin-bottom: 10px;
}

.dab-progress-container {
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
}

.dab-progress-bar {
    height: 100%;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: width 0.5s ease;
}

.dab-progress-text {
    color: #fff;
    font-size: 12px;
    font-weight: 600;
}

.dab-progress-stats {
    display: flex;
    justify-content: flex-end;
    font-size: 12px;
    color: #777;
}

.dab-progress-separator {
    margin: 0 5px;
}

/* Progress Bar Colors */
.dab-progress-primary {
    background-color: #0073aa;
}

.dab-progress-success {
    background-color: #28a745;
}

.dab-progress-warning {
    background-color: #ffc107;
}

.dab-progress-danger {
    background-color: #dc3545;
}

.dab-progress-info {
    background-color: #17a2b8;
}

/* Image Widget */
.dab-widget-image {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.dab-image {
    max-width: 100%;
    max-height: calc(100% - 30px);
    object-fit: contain;
}

.dab-image-caption {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #777;
}

/* Error Messages */
.dab-widget-error {
    padding: 15px;
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 3px;
    margin-bottom: 15px;
}

/* Responsive */
@media (max-width: 1200px) {
    .dab-dashboard-grid {
        gap: 15px;
        padding: 8px;
    }

    .dab-dashboard-row {
        padding: 8px;
        gap: 10px;
    }

    .dab-dashboard-column {
        padding: 8px;
    }
}

@media (max-width: 992px) {
    /* Keep rows as flex containers that don't wrap by default */
    .dab-dashboard-row {
        display: flex;
        flex-wrap: nowrap; /* Don't wrap by default for consistent layout */
        overflow-x: auto; /* Allow horizontal scrolling if needed */
        gap: 10px;
    }

    /* Columns should maintain their relative widths */
    .dab-dashboard-column {
        min-width: 200px; /* Ensure columns have a minimum width */
    }
}

@media (max-width: 768px) {
    /* On very small screens, allow wrapping for better mobile experience */
    .dab-dashboard-row {
        flex-wrap: wrap;
    }

    .dab-dashboard-column {
        flex: 1 1 100%;
        min-width: 100%;
    }

    /* Adjust widget padding for better mobile display */
    .dab-widget-content {
        padding: 10px;
    }

    /* Make table responsive */
    .dab-widget-table-container {
        overflow-x: auto;
    }
}
