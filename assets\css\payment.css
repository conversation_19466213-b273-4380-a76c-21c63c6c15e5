/**
 * Payment Field Styles
 * 
 * Styles for payment fields in the Database App Builder plugin.
 */

/* Payment Field Container */
.dab-payment-field {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.dab-payment-field:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Payment Amount Display */
.dab-payment-amount-display {
    font-size: 1.2em;
    margin-bottom: 15px;
    color: #495057;
}

.dab-payment-amount {
    font-weight: bold;
    color: #0073aa;
}

/* Payment Amount Input */
.dab-payment-amount-input {
    margin-bottom: 15px;
    position: relative;
}

.dab-payment-amount-input input {
    padding-left: 30px;
    width: 100%;
    max-width: 200px;
}

.dab-currency-symbol {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Payment Description */
.dab-payment-description {
    margin-bottom: 15px;
    color: #6c757d;
    font-style: italic;
}

/* Payment Button */
.dab-payment-button {
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dab-payment-button:hover {
    background-color: #005177;
}

.dab-payment-button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Payment Form Container */
.dab-payment-form-container {
    margin-top: 15px;
}

/* Error Messages */
.dab-payment-error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

/* Success Messages */
.dab-payment-success {
    color: #28a745;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

/* Stripe Specific Styles */
.dab-stripe-form {
    max-width: 500px;
}

.dab-stripe-card-element {
    background-color: white;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-bottom: 15px;
}

.dab-stripe-card-errors {
    color: #dc3545;
    margin-bottom: 15px;
    min-height: 20px;
}

.dab-stripe-submit {
    background-color: #635bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dab-stripe-submit:hover {
    background-color: #4b45c6;
}

.dab-stripe-submit:disabled {
    background-color: #a5a1e5;
    cursor: not-allowed;
}

/* PayPal Specific Styles */
.dab-paypal-form {
    max-width: 500px;
}

.dab-paypal-button-container {
    margin-bottom: 15px;
}

.dab-paypal-errors {
    color: #dc3545;
    margin-bottom: 15px;
    min-height: 20px;
}

.dab-paypal-errors.success {
    color: #28a745;
}

/* Paystack Specific Styles */
.dab-paystack-form {
    max-width: 500px;
}

.dab-paystack-button {
    background-color: #00c3f7;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dab-paystack-button:hover {
    background-color: #0099c4;
}

.dab-paystack-button:disabled {
    background-color: #7fdff9;
    cursor: not-allowed;
}

.dab-paystack-errors {
    color: #dc3545;
    margin-bottom: 15px;
    min-height: 20px;
}

.dab-paystack-errors.success {
    color: #28a745;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .dab-payment-field {
        padding: 15px;
    }
    
    .dab-payment-amount-display {
        font-size: 1.1em;
    }
    
    .dab-payment-button,
    .dab-stripe-submit,
    .dab-paystack-button {
        width: 100%;
    }
}
