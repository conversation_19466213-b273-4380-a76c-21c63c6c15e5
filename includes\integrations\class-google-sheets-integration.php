<?php
/**
 * Google Sheets Integration
 *
 * Handles integration with Google Sheets API to send form submissions to Google Sheets
 */
if (!defined('ABSPATH')) exit;

class DAB_Google_Sheets_Integration {

    /**
     * Initialize the class
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_test_google_sheets_connection', array($this, 'ajax_test_connection'));
    }

    /**
     * Get Google Sheets API credentials
     *
     * @return array Google Sheets API credentials
     */
    public static function get_credentials() {
        return array(
            'client_id' => DAB_Settings_Manager::get('google_sheets_client_id', ''),
            'client_secret' => DAB_Settings_Manager::get('google_sheets_client_secret', ''),
            'api_key' => DAB_Settings_Manager::get('google_sheets_api_key', ''),
            'access_token' => DAB_Settings_Manager::get('google_sheets_access_token', ''),
            'refresh_token' => DAB_Settings_Manager::get('google_sheets_refresh_token', ''),
            'token_expires' => DAB_Settings_Manager::get('google_sheets_token_expires', 0),
        );
    }

    /**
     * Check if Google Sheets integration is configured
     *
     * @return bool Whether Google Sheets integration is configured
     */
    public static function is_configured() {
        $credentials = self::get_credentials();
        return !empty($credentials['client_id']) && !empty($credentials['client_secret']) && !empty($credentials['api_key']);
    }

    /**
     * Send form data to Google Sheets
     *
     * @param int $form_id Form ID
     * @param array $data Form data
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function send_to_google_sheets($form_id, $data) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        // Get form data
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));

        // Check if integration is enabled for this form
        if (!$form || empty($form->google_sheets_enabled)) {
            return new WP_Error('integration_disabled', __('Google Sheets integration is not enabled for this form', 'db-app-builder'));
        }

        // Get spreadsheet ID and worksheet name
        $spreadsheet_id = $form->google_sheets_spreadsheet_id;
        $worksheet_name = $form->google_sheets_worksheet_name;

        if (empty($spreadsheet_id)) {
            return new WP_Error('missing_spreadsheet_id', __('Google Sheets spreadsheet ID is not set', 'db-app-builder'));
        }

        // Get form fields
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));

        if (!$form) {
            return new WP_Error('form_not_found', __('Form not found', 'db-app-builder'));
        }

        // Get form fields
        $fields = maybe_unserialize($form->fields);
        if (empty($fields)) {
            return new WP_Error('no_fields', __('No fields found for this form', 'db-app-builder'));
        }

        // Prepare data for Google Sheets
        $values = array();
        foreach ($fields as $field_id) {
            $field_id = intval($field_id);
            $fields_table = $wpdb->prefix . 'dab_fields';
            $field = $wpdb->get_row($wpdb->prepare("SELECT * FROM $fields_table WHERE id = %d", $field_id));

            if ($field && isset($data[$field->field_slug])) {
                $values[] = $data[$field->field_slug];
            } else {
                $values[] = ''; // Empty value for missing fields
            }
        }

        // Send data to Google Sheets
        return self::append_row_to_sheet($spreadsheet_id, $worksheet_name, $values);
    }

    /**
     * Append a row to a Google Sheet
     *
     * @param string $spreadsheet_id Google Sheets spreadsheet ID
     * @param string $worksheet_name Worksheet name (optional)
     * @param array $values Values to append
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function append_row_to_sheet($spreadsheet_id, $worksheet_name = '', $values = array()) {
        if (!self::is_configured()) {
            return new WP_Error('not_configured', __('Google Sheets integration is not configured', 'db-app-builder'));
        }

        // Get credentials
        $credentials = self::get_credentials();

        // Check if access token is valid
        if (empty($credentials['access_token']) || $credentials['token_expires'] < time()) {
            // Refresh token
            $result = self::refresh_access_token();
            if (is_wp_error($result)) {
                return $result;
            }
            $credentials = self::get_credentials(); // Get updated credentials
        }

        // Prepare request
        $range = !empty($worksheet_name) ? $worksheet_name : 'Sheet1';
        $api_url = "https://sheets.googleapis.com/v4/spreadsheets/{$spreadsheet_id}/values/{$range}:append?valueInputOption=USER_ENTERED";

        $body = array(
            'values' => array($values)
        );

        // Send request
        $response = wp_remote_post($api_url, array(
            'method' => 'POST',
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $credentials['access_token'],
            ),
            'body' => json_encode($body),
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code !== 200) {
            $error_message = isset($response_body['error']['message']) ? $response_body['error']['message'] : __('Unknown error', 'db-app-builder');
            return new WP_Error('api_error', $error_message);
        }

        return true;
    }

    /**
     * Refresh Google Sheets access token
     *
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public static function refresh_access_token() {
        $credentials = self::get_credentials();

        if (empty($credentials['client_id']) || empty($credentials['client_secret']) || empty($credentials['refresh_token'])) {
            return new WP_Error('missing_credentials', __('Missing Google Sheets API credentials', 'db-app-builder'));
        }

        $response = wp_remote_post('https://oauth2.googleapis.com/token', array(
            'method' => 'POST',
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.1',
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded',
            ),
            'body' => array(
                'client_id' => $credentials['client_id'],
                'client_secret' => $credentials['client_secret'],
                'refresh_token' => $credentials['refresh_token'],
                'grant_type' => 'refresh_token',
            ),
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code !== 200) {
            $error_message = isset($response_body['error_description']) ? $response_body['error_description'] : __('Unknown error', 'db-app-builder');
            return new WP_Error('token_refresh_error', $error_message);
        }

        // Save new access token
        DAB_Settings_Manager::set('google_sheets_access_token', $response_body['access_token']);
        DAB_Settings_Manager::set('google_sheets_token_expires', time() + $response_body['expires_in']);

        return true;
    }

    /**
     * AJAX handler to test Google Sheets connection
     */
    public function ajax_test_connection() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_google_sheets_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }

        // Check if integration is configured
        if (!self::is_configured()) {
            wp_send_json_error(array('message' => __('Google Sheets integration is not configured', 'db-app-builder')));
        }

        // Test connection
        $spreadsheet_id = isset($_POST['spreadsheet_id']) ? sanitize_text_field($_POST['spreadsheet_id']) : '';
        $worksheet_name = isset($_POST['worksheet_name']) ? sanitize_text_field($_POST['worksheet_name']) : '';

        if (empty($spreadsheet_id)) {
            wp_send_json_error(array('message' => __('Spreadsheet ID is required', 'db-app-builder')));
        }

        // Test by appending a test row
        $result = self::append_row_to_sheet($spreadsheet_id, $worksheet_name, array('Test connection from Database App Builder', date('Y-m-d H:i:s')));

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }

        wp_send_json_success(array('message' => __('Connection successful! A test row has been added to your spreadsheet.', 'db-app-builder')));
    }
}

// Initialize the Google Sheets integration
$dab_google_sheets_integration = new DAB_Google_Sheets_Integration();
