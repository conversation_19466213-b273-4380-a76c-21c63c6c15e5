<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Enqueue enhanced dashboard styles
wp_enqueue_style('dab-enhanced-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/enhanced-dashboard.css', array(), DAB_VERSION);

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$forms_table = $wpdb->prefix . 'dab_forms';

// Get basic counts
$total_tables = $wpdb->get_var("SELECT COUNT(*) FROM $tables_table");
$total_fields = $wpdb->get_var("SELECT COUNT(*) FROM $fields_table");
$total_forms = $wpdb->get_var("SELECT COUNT(*) FROM $forms_table");

// Get total records across all data tables
$total_records = 0;
$tables = $wpdb->get_results("SELECT table_slug FROM $tables_table");
foreach ($tables as $tbl) {
    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($tbl->table_slug);
    if ($wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table) {
        $count = (int) $wpdb->get_var("SELECT COUNT(*) FROM $data_table");
        $total_records += $count;
    }
}
?>

<div class="wrap dab-admin-wrap dab-enhanced-dashboard dab-animate-fade-in">
    <div class="dab-enhanced-header">
        <h1 class="dab-enhanced-title">Database App Builder</h1>
        <p class="dab-enhanced-subtitle">Manage your database applications from one central dashboard</p>
    </div>

    <!-- Stats Section -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card dab-animate-scale-in" data-delay="100">
            <div class="dab-counter">
                <div class="dab-counter-icon dab-counter-icon-primary">
                    <span class="dashicons dashicons-database"></span>
                </div>
                <div class="dab-counter-content">
                    <h2 class="dab-counter-value"><?php echo esc_html($total_tables); ?></h2>
                    <p class="dab-counter-label">Tables</p>
                </div>
            </div>
        </div>

        <div class="dab-stat-card dab-animate-scale-in" data-delay="200">
            <div class="dab-counter">
                <div class="dab-counter-icon dab-counter-icon-info">
                    <span class="dashicons dashicons-editor-table"></span>
                </div>
                <div class="dab-counter-content">
                    <h2 class="dab-counter-value"><?php echo esc_html($total_fields); ?></h2>
                    <p class="dab-counter-label">Fields</p>
                </div>
            </div>
        </div>

        <div class="dab-stat-card dab-animate-scale-in" data-delay="300">
            <div class="dab-counter">
                <div class="dab-counter-icon dab-counter-icon-success">
                    <span class="dashicons dashicons-feedback"></span>
                </div>
                <div class="dab-counter-content">
                    <h2 class="dab-counter-value"><?php echo esc_html($total_forms); ?></h2>
                    <p class="dab-counter-label">Forms</p>
                </div>
            </div>
        </div>

        <div class="dab-stat-card dab-animate-scale-in" data-delay="400">
            <div class="dab-counter">
                <div class="dab-counter-icon dab-counter-icon-warning">
                    <span class="dashicons dashicons-admin-page"></span>
                </div>
                <div class="dab-counter-content">
                    <h2 class="dab-counter-value"><?php echo esc_html($total_records); ?></h2>
                    <p class="dab-counter-label">Records</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Features Section -->
    <h2 class="dab-heading dab-heading-2">Data Management</h2>
    <div class="dab-cards-grid">
        <!-- Tables Card -->
        <div class="dab-dashboard-card card-primary dab-animate-slide-up" data-delay="100">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-database"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Tables</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Create and manage database tables to store your application data.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-btn dab-btn-sm dab-btn-primary">Manage Tables</a>
            </div>
        </div>

        <!-- Forms Card -->
        <div class="dab-dashboard-card card-success dab-animate-slide-up" data-delay="200">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-feedback"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Forms</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Create custom forms for data entry and user submissions.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_forms'); ?>" class="dab-btn dab-btn-sm dab-btn-success">Manage Forms</a>
            </div>
        </div>

        <!-- Views Card -->
        <div class="dab-dashboard-card card-info dab-animate-slide-up" data-delay="300">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-visibility"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Views</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Create custom views to display your data in different formats.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_views'); ?>" class="dab-btn dab-btn-sm dab-btn-info">Manage Views</a>
            </div>
        </div>

        <!-- Data Management Card -->
        <div class="dab-dashboard-card card-warning dab-animate-slide-up" data-delay="400">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-admin-page"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Data Records</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">View, edit, and manage your data records across all tables.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_data'); ?>" class="dab-btn dab-btn-sm dab-btn-warning">Manage Data</a>
            </div>
        </div>
    </div>

    <!-- Advanced Features Section -->
    <h2 class="dab-heading dab-heading-2">Advanced Features</h2>
    <div class="dab-cards-grid">
        <!-- Dashboards Card -->
        <div class="dab-dashboard-card card-secondary dab-animate-slide-up" data-delay="500">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-chart-area"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Dashboards</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Create custom dashboards to visualize your data with charts and widgets.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_dashboard_builder'); ?>" class="dab-btn dab-btn-sm dab-btn-secondary">Manage Dashboards</a>
            </div>
        </div>

        <!-- Integrations Card -->
        <div class="dab-dashboard-card card-primary dab-animate-slide-up" data-delay="600">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-admin-plugins"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Integrations</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Connect your data with external services like Google Sheets and Zapier.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_settings'); ?>" class="dab-btn dab-btn-sm dab-btn-primary">Manage Integrations</a>
            </div>
        </div>

        <!-- Permissions Card -->
        <div class="dab-dashboard-card card-info dab-animate-slide-up" data-delay="700">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-shield"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Permissions</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Manage user roles and permissions for your database applications.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_settings#permissions'); ?>" class="dab-btn dab-btn-sm dab-btn-info">Manage Permissions</a>
            </div>
        </div>

        <!-- Payments Card -->
        <div class="dab-dashboard-card card-success dab-animate-slide-up" data-delay="800">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-money-alt"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Payments</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Configure payment gateways and manage payment transactions.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_payment_settings'); ?>" class="dab-btn dab-btn-sm dab-btn-success">Payment Settings</a>
            </div>
        </div>
    </div>

    <!-- Help & Resources Section -->
    <h2 class="dab-heading dab-heading-2">Help & Resources</h2>
    <div class="dab-cards-grid">
        <!-- Documentation Card -->
        <div class="dab-dashboard-card card-info dab-animate-slide-up" data-delay="900">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-book"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Documentation</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <p class="dab-dashboard-card-description">Access comprehensive documentation and tutorials to get the most out of Database App Builder.</p>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_documentation'); ?>" class="dab-btn dab-btn-sm dab-btn-info">View Documentation</a>
            </div>
        </div>

        <!-- Getting Started Card -->
        <div class="dab-dashboard-card card-warning dab-animate-slide-up" data-delay="1000">
            <div class="dab-dashboard-card-header">
                <div class="dab-dashboard-card-icon">
                    <span class="dashicons dashicons-welcome-learn-more"></span>
                </div>
                <h3 class="dab-dashboard-card-title">Getting Started</h3>
            </div>
            <div class="dab-dashboard-card-body">
                <ol style="padding-left: 20px; margin-top: 0;">
                    <li style="margin-bottom: 10px;">Create a <strong>Table</strong> to define your data structure</li>
                    <li style="margin-bottom: 10px;">Add <strong>Fields</strong> to your table to store different types of data</li>
                    <li style="margin-bottom: 10px;">Create a <strong>Form</strong> to allow users to input data</li>
                    <li style="margin-bottom: 10px;">Create a <strong>View</strong> to display your data</li>
                    <li style="margin-bottom: 10px;">Build a <strong>Dashboard</strong> to visualize your data</li>
                </ol>
            </div>
            <div class="dab-dashboard-card-footer">
                <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-btn dab-btn-sm dab-btn-warning">Start Building</a>
            </div>
        </div>
    </div>
</div>
