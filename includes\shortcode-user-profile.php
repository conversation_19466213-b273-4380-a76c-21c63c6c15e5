<?php
/**
 * User Profile Shortcode
 *
 * Provides frontend user profile management functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register user profile shortcode
 */
function dab_user_profile_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_avatar' => 'true',
        'show_password_change' => 'true',
        'redirect_after_update' => ''
    ), $atts, 'dab_user_profile');

    // Check if user is logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    if (!$current_user) {
        return '<div class="dab-auth-required">
                    <p>' . __('You must be logged in to view this page.', 'db-app-builder') . '</p>
                    <p><a href="' . home_url('/login/') . '" class="dab-btn dab-btn-primary">' . __('Login', 'db-app-builder') . '</a></p>
                </div>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-user-profile', plugin_dir_url(dirname(__FILE__)) . 'assets/css/user-profile.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-user-profile', plugin_dir_url(dirname(__FILE__)) . 'assets/js/user-profile.js', array('jquery'), DAB_VERSION, true);

    // Localize script
    wp_localize_script('dab-user-profile', 'dab_profile', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_frontend_nonce'),
        'current_user' => array(
            'id' => $current_user->id ?? 0,
            'username' => $current_user->username ?? '',
            'email' => $current_user->email ?? '',
            'first_name' => $current_user->first_name ?? '',
            'last_name' => $current_user->last_name ?? '',
            'phone' => $current_user->phone ?? '',
            'email_verified' => $current_user->email_verified ?? false
        ),
        'messages' => array(
            'update_success' => __('Profile updated successfully!', 'db-app-builder'),
            'update_error' => __('Failed to update profile. Please try again.', 'db-app-builder'),
            'password_success' => __('Password changed successfully!', 'db-app-builder'),
            'password_error' => __('Failed to change password. Please try again.', 'db-app-builder'),
            'password_mismatch' => __('New passwords do not match.', 'db-app-builder'),
            'weak_password' => __('Password must be at least 6 characters long.', 'db-app-builder'),
            'current_password_wrong' => __('Current password is incorrect.', 'db-app-builder')
        )
    ));

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-user-profile">
        <!-- Profile Header -->
        <div class="dab-profile-header">
            <div class="dab-profile-info">
                <?php if ($atts['show_avatar'] === 'true'): ?>
                <div class="dab-profile-avatar">
                    <?php if (!empty($current_user->avatar_url)): ?>
                        <img src="<?php echo esc_url($current_user->avatar_url); ?>" alt="<?php _e('Profile Avatar', 'db-app-builder'); ?>">
                    <?php else: ?>
                        <div class="dab-avatar-placeholder">
                            <span class="dashicons dashicons-admin-users"></span>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <div class="dab-profile-details">
                    <h1><?php echo esc_html(trim(($current_user->first_name ?? '') . ' ' . ($current_user->last_name ?? ''))) ?: esc_html($current_user->username ?? ''); ?></h1>
                    <p class="dab-profile-email">
                        <?php echo esc_html($current_user->email ?? ''); ?>
                        <?php if ($current_user->email_verified ?? false): ?>
                            <span class="dab-verified-badge"><?php _e('Verified', 'db-app-builder'); ?></span>
                        <?php else: ?>
                            <span class="dab-unverified-badge"><?php _e('Unverified', 'db-app-builder'); ?></span>
                        <?php endif; ?>
                    </p>
                    <p class="dab-profile-role"><?php echo esc_html(ucfirst($current_user->role ?? '')); ?></p>
                </div>
            </div>
            <div class="dab-profile-actions">
                <a href="<?php echo home_url('/user-dashboard/'); ?>" class="dab-btn dab-btn-secondary">
                    <span class="dashicons dashicons-dashboard"></span>
                    <?php _e('Dashboard', 'db-app-builder'); ?>
                </a>
            </div>
        </div>

        <div class="dab-profile-content">
            <!-- Profile Information Form -->
            <div class="dab-profile-section">
                <h2 class="dab-section-title"><?php _e('Profile Information', 'db-app-builder'); ?></h2>

                <form id="dab-profile-form" class="dab-profile-form">
                    <div class="dab-form-row">
                        <div class="dab-form-group dab-form-col-6">
                            <label for="first_name"><?php _e('First Name', 'db-app-builder'); ?></label>
                            <input type="text" id="first_name" name="first_name" class="dab-form-control"
                                   value="<?php echo esc_attr($current_user->first_name ?? ''); ?>">
                        </div>
                        <div class="dab-form-group dab-form-col-6">
                            <label for="last_name"><?php _e('Last Name', 'db-app-builder'); ?></label>
                            <input type="text" id="last_name" name="last_name" class="dab-form-control"
                                   value="<?php echo esc_attr($current_user->last_name ?? ''); ?>">
                        </div>
                    </div>

                    <div class="dab-form-group">
                        <label for="email"><?php _e('Email Address', 'db-app-builder'); ?></label>
                        <input type="email" id="email" name="email" class="dab-form-control"
                               value="<?php echo esc_attr($current_user->email ?? ''); ?>">
                        <small class="dab-form-help"><?php _e('Changing your email will require re-verification.', 'db-app-builder'); ?></small>
                    </div>

                    <div class="dab-form-group">
                        <label for="phone"><?php _e('Phone Number', 'db-app-builder'); ?></label>
                        <input type="tel" id="phone" name="phone" class="dab-form-control"
                               value="<?php echo esc_attr($current_user->phone ?? ''); ?>">
                    </div>

                    <div class="dab-form-group">
                        <button type="submit" class="dab-btn dab-btn-primary">
                            <span class="dab-btn-text"><?php _e('Update Profile', 'db-app-builder'); ?></span>
                            <span class="dab-btn-loading" style="display: none;">
                                <span class="dab-spinner"></span>
                                <?php _e('Updating...', 'db-app-builder'); ?>
                            </span>
                        </button>
                    </div>

                    <div class="dab-form-messages">
                        <div class="dab-alert dab-alert-error" id="dab-profile-error" style="display: none;"></div>
                        <div class="dab-alert dab-alert-success" id="dab-profile-success" style="display: none;"></div>
                    </div>
                </form>
            </div>

            <?php if ($atts['show_password_change'] === 'true'): ?>
            <!-- Password Change Form -->
            <div class="dab-profile-section">
                <h2 class="dab-section-title"><?php _e('Change Password', 'db-app-builder'); ?></h2>

                <form id="dab-password-form" class="dab-password-form">
                    <div class="dab-form-group">
                        <label for="current_password"><?php _e('Current Password', 'db-app-builder'); ?></label>
                        <input type="password" id="current_password" name="current_password" class="dab-form-control" required>
                    </div>

                    <div class="dab-form-group">
                        <label for="new_password"><?php _e('New Password', 'db-app-builder'); ?></label>
                        <input type="password" id="new_password" name="new_password" class="dab-form-control" required>
                        <small class="dab-form-help"><?php _e('Password must be at least 6 characters long.', 'db-app-builder'); ?></small>
                    </div>

                    <div class="dab-form-group">
                        <label for="confirm_new_password"><?php _e('Confirm New Password', 'db-app-builder'); ?></label>
                        <input type="password" id="confirm_new_password" name="confirm_new_password" class="dab-form-control" required>
                    </div>

                    <div class="dab-form-group">
                        <button type="submit" class="dab-btn dab-btn-primary">
                            <span class="dab-btn-text"><?php _e('Change Password', 'db-app-builder'); ?></span>
                            <span class="dab-btn-loading" style="display: none;">
                                <span class="dab-spinner"></span>
                                <?php _e('Changing...', 'db-app-builder'); ?>
                            </span>
                        </button>
                    </div>

                    <div class="dab-form-messages">
                        <div class="dab-alert dab-alert-error" id="dab-password-error" style="display: none;"></div>
                        <div class="dab-alert dab-alert-success" id="dab-password-success" style="display: none;"></div>
                    </div>
                </form>
            </div>
            <?php endif; ?>

            <!-- Account Information -->
            <div class="dab-profile-section">
                <h2 class="dab-section-title"><?php _e('Account Information', 'db-app-builder'); ?></h2>

                <div class="dab-account-info">
                    <div class="dab-info-item">
                        <label><?php _e('Username', 'db-app-builder'); ?></label>
                        <span><?php echo esc_html($current_user->username ?? ''); ?></span>
                    </div>
                    <div class="dab-info-item">
                        <label><?php _e('Account Status', 'db-app-builder'); ?></label>
                        <span class="dab-status-badge dab-status-<?php echo esc_attr($current_user->status ?? 'active'); ?>">
                            <?php echo esc_html(ucfirst($current_user->status ?? 'active')); ?>
                        </span>
                    </div>
                    <div class="dab-info-item">
                        <label><?php _e('Member Since', 'db-app-builder'); ?></label>
                        <span><?php echo date('F j, Y', strtotime($current_user->created_at ?? 'now')); ?></span>
                    </div>
                    <?php if ($current_user->last_login ?? false): ?>
                    <div class="dab-info-item">
                        <label><?php _e('Last Login', 'db-app-builder'); ?></label>
                        <span><?php echo date('F j, Y g:i A', strtotime($current_user->last_login)); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <style>
    .dab-user-profile {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .dab-profile-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding: 30px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
    }

    .dab-profile-info {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .dab-profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dab-profile-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .dab-avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e9ecef;
        color: #6c757d;
    }

    .dab-avatar-placeholder .dashicons {
        font-size: 40px;
        width: 40px;
        height: 40px;
    }

    .dab-profile-details h1 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 24px;
        font-weight: 600;
    }

    .dab-profile-email {
        margin: 0 0 5px 0;
        color: #666;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .dab-profile-role {
        margin: 0;
        color: #999;
        font-size: 14px;
    }

    .dab-verified-badge, .dab-unverified-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .dab-verified-badge {
        background: #d4edda;
        color: #155724;
    }

    .dab-unverified-badge {
        background: #f8d7da;
        color: #721c24;
    }

    .dab-profile-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .dab-profile-section {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
    }

    .dab-section-title {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 18px;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #e1e5e9;
    }

    .dab-form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 0;
    }

    .dab-form-col-6 {
        flex: 1;
    }

    .dab-form-group {
        margin-bottom: 20px;
    }

    .dab-form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }

    .dab-form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
        box-sizing: border-box;
    }

    .dab-form-control:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    }

    .dab-form-help {
        display: block;
        margin-top: 5px;
        color: #6c757d;
        font-size: 12px;
    }

    .dab-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border: 1px solid transparent;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s;
        background: none;
    }

    .dab-btn-primary {
        background-color: #007cba;
        color: white;
        border-color: #007cba;
    }

    .dab-btn-primary:hover {
        background-color: #005a87;
        border-color: #005a87;
    }

    .dab-btn-secondary {
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .dab-btn-secondary:hover {
        background-color: #545b62;
        border-color: #545b62;
    }

    .dab-btn-loading {
        display: none;
    }

    .dab-btn.loading .dab-btn-text {
        display: none;
    }

    .dab-btn.loading .dab-btn-loading {
        display: inline-flex;
    }

    .dab-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
        margin-right: 8px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .dab-alert {
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .dab-alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .dab-alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .dab-account-info {
        display: grid;
        gap: 15px;
    }

    .dab-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .dab-info-item:last-child {
        border-bottom: none;
    }

    .dab-info-item label {
        font-weight: 500;
        color: #333;
        margin: 0;
    }

    .dab-status-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .dab-status-active {
        background: #d4edda;
        color: #155724;
    }

    .dab-status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .dab-auth-required {
        text-align: center;
        padding: 40px 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .dab-profile-header {
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
        }

        .dab-profile-info {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .dab-form-row {
            flex-direction: column;
            gap: 0;
        }

        .dab-user-profile {
            padding: 10px;
        }

        .dab-profile-section {
            padding: 20px;
        }

        .dab-info-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }
    }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_user_profile', 'dab_user_profile_shortcode');
