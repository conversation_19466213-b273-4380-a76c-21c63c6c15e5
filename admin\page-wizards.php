<?php
/**
 * Wizards Dashboard Page
 *
 * This page displays the available guided setup wizards
 */
if (!defined('ABSPATH')) exit;

// Enqueue styles
wp_enqueue_style('dab-admin-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-style.css', array(), DAB_VERSION);
wp_enqueue_style('dab-modern-admin', plugin_dir_url(dirname(__FILE__)) . 'assets/css/modern-admin.css', array(), DAB_VERSION);
wp_enqueue_style('dab-wizard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/wizard.css', array(), DAB_VERSION);
?>

<div class="wrap dab-admin-wrap dab-animate-fade-in">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title"><?php _e('Database App Builder - Guided Setup Wizards', 'db-app-builder'); ?></h1>
    </div>

    <div class="dab-wizard-dashboard">
        <div class="dab-wizard-intro">
            <p class="dab-wizard-description">
                <?php _e('Welcome to the Guided Setup Wizards! These step-by-step guides will help you quickly set up and configure your database applications.', 'db-app-builder'); ?>
            </p>
        </div>

        <div class="dab-layout">
            <!-- Application Creation Wizard -->
            <div class="dab-col dab-col-md-4">
                <div class="dab-card dab-animate-slide-up" data-delay="100">
                    <div class="dab-card-header">
                        <h3 class="dab-card-title"><?php _e('Create New Application', 'db-app-builder'); ?></h3>
                    </div>
                    <div class="dab-card-body">
                        <div class="dab-wizard-card-icon">
                            <span class="dashicons dashicons-database"></span>
                        </div>
                        <p class="dab-wizard-card-description">
                            <?php _e('Create a new database application with tables, fields, and forms in just a few steps.', 'db-app-builder'); ?>
                        </p>
                        <ul class="dab-wizard-features">
                            <li><?php _e('Define table structure', 'db-app-builder'); ?></li>
                            <li><?php _e('Add custom fields', 'db-app-builder'); ?></li>
                            <li><?php _e('Create data entry forms', 'db-app-builder'); ?></li>
                            <li><?php _e('Set up data views', 'db-app-builder'); ?></li>
                        </ul>
                    </div>
                    <div class="dab-card-footer">
                        <a href="<?php echo admin_url('admin.php?page=dab_wizard_app_creation'); ?>" class="dab-btn dab-btn-primary dab-btn-block">
                            <?php _e('Start Wizard', 'db-app-builder'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Workflow Setup Wizard -->
            <div class="dab-col dab-col-md-4">
                <div class="dab-card dab-animate-slide-up" data-delay="200">
                    <div class="dab-card-header">
                        <h3 class="dab-card-title"><?php _e('Setup Approval Workflow', 'db-app-builder'); ?></h3>
                    </div>
                    <div class="dab-card-body">
                        <div class="dab-wizard-card-icon">
                            <span class="dashicons dashicons-list-view"></span>
                        </div>
                        <p class="dab-wizard-card-description">
                            <?php _e('Configure approval workflows for your database tables with multiple approval levels.', 'db-app-builder'); ?>
                        </p>
                        <ul class="dab-wizard-features">
                            <li><?php _e('Define approval levels', 'db-app-builder'); ?></li>
                            <li><?php _e('Assign approvers', 'db-app-builder'); ?></li>
                            <li><?php _e('Configure notifications', 'db-app-builder'); ?></li>
                            <li><?php _e('Set up approval dashboards', 'db-app-builder'); ?></li>
                        </ul>
                    </div>
                    <div class="dab-card-footer">
                        <a href="<?php echo admin_url('admin.php?page=dab_wizard_workflow_setup'); ?>" class="dab-btn dab-btn-primary dab-btn-block">
                            <?php _e('Start Wizard', 'db-app-builder'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Dashboard Building Wizard -->
            <div class="dab-col dab-col-md-4">
                <div class="dab-card dab-animate-slide-up" data-delay="300">
                    <div class="dab-card-header">
                        <h3 class="dab-card-title"><?php _e('Build Custom Dashboard', 'db-app-builder'); ?></h3>
                    </div>
                    <div class="dab-card-body">
                        <div class="dab-wizard-card-icon">
                            <span class="dashicons dashicons-chart-area"></span>
                        </div>
                        <p class="dab-wizard-card-description">
                            <?php _e('Create a personalized dashboard with cards, counters, charts, and tables to visualize your data.', 'db-app-builder'); ?>
                        </p>
                        <ul class="dab-wizard-features">
                            <li><?php _e('Select dashboard layout', 'db-app-builder'); ?></li>
                            <li><?php _e('Add data widgets', 'db-app-builder'); ?></li>
                            <li><?php _e('Configure widget settings', 'db-app-builder'); ?></li>
                            <li><?php _e('Publish your dashboard', 'db-app-builder'); ?></li>
                        </ul>
                    </div>
                    <div class="dab-card-footer">
                        <a href="<?php echo admin_url('admin.php?page=dab_wizard_dashboard_builder'); ?>" class="dab-btn dab-btn-primary dab-btn-block">
                            <?php _e('Start Wizard', 'db-app-builder'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
