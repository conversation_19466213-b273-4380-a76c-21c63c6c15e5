<?php
/**
 * Chat Debug Helper
 *
 * Provides debugging information for the chat system
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Debug chat system
 */
function dab_debug_chat_system() {
    $debug_info = array();
    
    // Check if user is logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    $debug_info['user_logged_in'] = $current_user ? true : false;
    $debug_info['current_user'] = $current_user;
    
    // Check if chat tables exist
    global $wpdb;
    $tables_to_check = array(
        'dab_chat_messages' => $wpdb->prefix . 'dab_chat_messages',
        'dab_chat_groups' => $wpdb->prefix . 'dab_chat_groups',
        'dab_chat_group_members' => $wpdb->prefix . 'dab_chat_group_members',
        'dab_frontend_users' => $wpdb->prefix . 'dab_frontend_users',
        'dab_user_sessions' => $wpdb->prefix . 'dab_user_sessions'
    );
    
    foreach ($tables_to_check as $table_name => $table_full_name) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_full_name'") == $table_full_name;
        $debug_info['tables'][$table_name] = $table_exists;
    }
    
    // Check if chat assets exist
    $plugin_url = plugin_dir_url(dirname(__FILE__));
    $assets_to_check = array(
        'chat_css' => $plugin_url . 'assets/css/chat.css',
        'chat_js' => $plugin_url . 'assets/js/chat.js'
    );
    
    foreach ($assets_to_check as $asset_name => $asset_url) {
        $debug_info['assets'][$asset_name] = $asset_url;
    }
    
    // Check if AJAX handlers are registered
    $debug_info['ajax_handlers'] = array(
        'dab_get_conversations' => has_action('wp_ajax_dab_get_conversations'),
        'dab_get_messages' => has_action('wp_ajax_dab_get_messages'),
        'dab_send_message' => has_action('wp_ajax_dab_send_message'),
        'dab_get_groups' => has_action('wp_ajax_dab_get_groups'),
        'dab_get_users' => has_action('wp_ajax_dab_get_users')
    );
    
    // Check if shortcode is registered
    $debug_info['shortcode_registered'] = shortcode_exists('dab_chat');
    
    return $debug_info;
}

/**
 * Display debug information shortcode
 */
function dab_chat_debug_shortcode($atts) {
    if (!current_user_can('manage_options')) {
        return '<p>Debug information is only available for administrators.</p>';
    }
    
    $debug_info = dab_debug_chat_system();
    
    ob_start();
    ?>
    <div class="dab-chat-debug" style="background: #f9f9f9; padding: 20px; border: 1px solid #ddd; margin: 20px 0;">
        <h3>Chat System Debug Information</h3>
        
        <h4>User Authentication</h4>
        <ul>
            <li><strong>User Logged In:</strong> <?php echo $debug_info['user_logged_in'] ? 'Yes' : 'No'; ?></li>
            <?php if ($debug_info['current_user']): ?>
                <li><strong>User ID:</strong> <?php echo $debug_info['current_user']->id; ?></li>
                <li><strong>Username:</strong> <?php echo $debug_info['current_user']->username; ?></li>
                <li><strong>Email:</strong> <?php echo $debug_info['current_user']->email; ?></li>
            <?php endif; ?>
        </ul>
        
        <h4>Database Tables</h4>
        <ul>
            <?php foreach ($debug_info['tables'] as $table => $exists): ?>
                <li><strong><?php echo $table; ?>:</strong> 
                    <span style="color: <?php echo $exists ? 'green' : 'red'; ?>">
                        <?php echo $exists ? 'Exists' : 'Missing'; ?>
                    </span>
                </li>
            <?php endforeach; ?>
        </ul>
        
        <h4>Assets</h4>
        <ul>
            <?php foreach ($debug_info['assets'] as $asset => $url): ?>
                <li><strong><?php echo $asset; ?>:</strong> <a href="<?php echo $url; ?>" target="_blank"><?php echo $url; ?></a></li>
            <?php endforeach; ?>
        </ul>
        
        <h4>AJAX Handlers</h4>
        <ul>
            <?php foreach ($debug_info['ajax_handlers'] as $handler => $registered): ?>
                <li><strong><?php echo $handler; ?>:</strong> 
                    <span style="color: <?php echo $registered ? 'green' : 'red'; ?>">
                        <?php echo $registered ? 'Registered' : 'Not Registered'; ?>
                    </span>
                </li>
            <?php endforeach; ?>
        </ul>
        
        <h4>Shortcode</h4>
        <ul>
            <li><strong>dab_chat shortcode:</strong> 
                <span style="color: <?php echo $debug_info['shortcode_registered'] ? 'green' : 'red'; ?>">
                    <?php echo $debug_info['shortcode_registered'] ? 'Registered' : 'Not Registered'; ?>
                </span>
            </li>
        </ul>
        
        <h4>Test Chat</h4>
        <p>If everything above shows as working, try the chat below:</p>
        <?php echo do_shortcode('[dab_chat]'); ?>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_chat_debug', 'dab_chat_debug_shortcode');

/**
 * Add debug information to admin bar
 */
function dab_add_chat_debug_to_admin_bar($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    $status = $current_user ? 'Logged In' : 'Not Logged In';
    
    $wp_admin_bar->add_node(array(
        'id' => 'dab-chat-debug',
        'title' => 'Chat Debug: ' . $status,
        'href' => '#',
        'meta' => array(
            'title' => 'Frontend User Status: ' . $status
        )
    ));
}
add_action('admin_bar_menu', 'dab_add_chat_debug_to_admin_bar', 100);
