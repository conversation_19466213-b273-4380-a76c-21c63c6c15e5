# Frontend User System Setup Guide

This guide will help you set up the frontend user management system for the DB App Builder plugin.

## Overview

The enhanced plugin now includes a complete frontend user management system that works independently of WordPress admin, providing:

- User registration and login pages
- User dashboard for data management
- User profile management
- Session management
- Role-based access control
- Email verification
- Password reset functionality

## Required Pages Setup

Create the following WordPress pages and add the specified shortcodes:

### 1. Login Page
- **Page Title**: Login
- **Page Slug**: login
- **Content**: `[dab_user_login]`

### 2. Registration Page
- **Page Title**: Register
- **Page Slug**: register
- **Content**: `[dab_user_register]`

### 3. User Dashboard
- **Page Title**: User Dashboard
- **Page Slug**: user-dashboard
- **Content**: `[dab_user_dashboard]`

### 4. User Profile
- **Page Title**: User Profile
- **Page Slug**: user-profile
- **Content**: `[dab_user_profile]`

### 5. Password Reset Page (Optional)
- **Page Title**: Reset Password
- **Page Slug**: reset-password
- **Content**: Custom form for password reset (handled via email links)

## Shortcode Options

### Login Shortcode: `[dab_user_login]`
```
[dab_user_login redirect="/custom-page/" show_register_link="true" show_forgot_password="true"]
```
- `redirect`: URL to redirect after successful login (default: /user-dashboard/)
- `show_register_link`: Show link to registration page (default: true)
- `show_forgot_password`: Show forgot password link (default: true)

### Registration Shortcode: `[dab_user_register]`
```
[dab_user_register redirect="/login/" show_login_link="true" require_email_verification="true"]
```
- `redirect`: URL to redirect after successful registration (default: /login/)
- `show_login_link`: Show link to login page (default: true)
- `require_email_verification`: Require email verification (default: true)

### User Dashboard Shortcode: `[dab_user_dashboard]`
```
[dab_user_dashboard show_stats="true" show_recent_activity="true" show_tables="true"]
```
- `show_stats`: Display user statistics cards (default: true)
- `show_recent_activity`: Show recent activity section (default: true)
- `show_tables`: Show accessible tables section (default: true)

### User Profile Shortcode: `[dab_user_profile]`
```
[dab_user_profile show_avatar="true" show_password_change="true" redirect_after_update=""]
```
- `show_avatar`: Display user avatar section (default: true)
- `show_password_change`: Show password change form (default: true)
- `redirect_after_update`: URL to redirect after profile update

## Database Tables

The plugin automatically creates the following tables:

1. **`wp_dab_frontend_users`**: Stores frontend user accounts
2. **`wp_dab_user_sessions`**: Manages user sessions
3. **`wp_dab_user_meta`**: Additional user metadata
4. **`wp_dab_user_dashboard_configs`**: User dashboard configurations
5. **`wp_dab_user_access_log`**: Logs user data access

## User Roles

The system supports the following user roles:

- **admin**: Full access to all tables and data
- **user**: Limited access based on permissions
- **custom roles**: Can be defined through the existing role permissions system

## Integration with Existing System

The frontend user system integrates seamlessly with the existing plugin features:

### Data Access Control
- Users can only access tables they have permissions for
- Data is automatically filtered by user_id
- Role-based permissions are respected

### Approval Workflows
- Frontend users can submit data that goes through approval workflows
- Admins can manage approvals through the WordPress admin

### Form Submissions
- Frontend users can submit forms
- Data is automatically associated with their user account

## Navigation Menu Setup

Add the following menu items to your WordPress navigation:

**For Logged-out Users:**
- Login (/login/)
- Register (/register/)

**For Logged-in Users:**
- Dashboard (/user-dashboard/)
- Profile (/user-profile/)
- Logout (JavaScript action)

You can use conditional menu plugins or custom code to show different menu items based on login status.

## Styling and Customization

### CSS Classes
The frontend system uses consistent CSS classes that you can customize:

- `.dab-user-dashboard`: Main dashboard container
- `.dab-login-container`: Login form container
- `.dab-register-container`: Registration form container
- `.dab-profile-header`: Profile header section
- `.dab-btn`: Button styles
- `.dab-form-control`: Form input styles
- `.dab-modal`: Modal dialogs

### JavaScript Events
The system triggers custom JavaScript events that you can listen to:

```javascript
// Login success
document.addEventListener('dab_login_success', function(e) {
    console.log('User logged in:', e.detail.user);
});

// Registration success
document.addEventListener('dab_register_success', function(e) {
    console.log('User registered:', e.detail.user_id);
});

// Profile updated
document.addEventListener('dab_profile_updated', function(e) {
    console.log('Profile updated');
});
```

## Security Features

### Session Management
- Secure session tokens
- Configurable session expiration
- IP address and user agent tracking
- Automatic cleanup of expired sessions

### Password Security
- Passwords are hashed using PHP's password_hash()
- Minimum password length requirements
- Password strength validation

### CSRF Protection
- All AJAX requests use WordPress nonces
- Form submissions are protected against CSRF attacks

### Email Verification
- Optional email verification for new accounts
- Secure verification tokens
- Automatic token expiration

## Email Configuration

The system sends emails for:
- Email verification
- Password reset
- Account notifications

Make sure your WordPress site is configured to send emails properly. Consider using an SMTP plugin for reliable email delivery.

## Troubleshooting

### Common Issues

1. **Login/Registration forms not working**
   - Check that the pages exist with correct shortcodes
   - Verify JavaScript is loading properly
   - Check browser console for errors

2. **Email verification not working**
   - Ensure WordPress can send emails
   - Check spam folders
   - Verify email configuration

3. **Dashboard not showing data**
   - Check user permissions
   - Verify table access rights
   - Ensure data exists for the user

4. **Styling issues**
   - Check that CSS files are loading
   - Verify there are no theme conflicts
   - Use browser developer tools to debug

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For additional support or customization needs, refer to the plugin documentation or contact the development team.
