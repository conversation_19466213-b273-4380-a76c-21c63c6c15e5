/**
 * Error Suppression Script for Database App Builder
 * 
 * This script suppresses all error messages and console logs
 * to provide a clean admin dashboard experience.
 */

(function() {
    'use strict';

    // Suppress all console methods
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug
    };

    // Override console methods to suppress output
    console.log = function() { /* Suppressed */ };
    console.error = function() { /* Suppressed */ };
    console.warn = function() { /* Suppressed */ };
    console.info = function() { /* Suppressed */ };
    console.debug = function() { /* Suppressed */ };

    // Suppress global error handlers
    window.addEventListener('error', function(e) {
        e.preventDefault();
        return true;
    }, true);

    window.addEventListener('unhandledrejection', function(e) {
        e.preventDefault();
        return true;
    }, true);

    // Override common error display functions
    window.showError = function() { /* Suppressed */ };
    window.alert = function() { /* Suppressed */ };
    
    // Suppress SweetAlert errors if available
    if (typeof Swal !== 'undefined') {
        const originalSwal = Swal.fire;
        Swal.fire = function(options) {
            // Only suppress error alerts
            if (typeof options === 'object' && options.icon === 'error') {
                return Promise.resolve({ isConfirmed: false });
            }
            return originalSwal.apply(this, arguments);
        };
    }

    // Suppress jQuery AJAX errors globally
    if (typeof $ !== 'undefined') {
        $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
            event.preventDefault();
            return false;
        });
    }

    // Suppress WordPress admin notices
    document.addEventListener('DOMContentLoaded', function() {
        // Hide existing error notices
        const errorNotices = document.querySelectorAll('.notice-error, .error, .dab-admin-notice-error');
        errorNotices.forEach(function(notice) {
            notice.style.display = 'none';
        });

        // Monitor for new error notices and hide them
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node is an error notice
                        if (node.classList && (
                            node.classList.contains('notice-error') ||
                            node.classList.contains('error') ||
                            node.classList.contains('dab-admin-notice-error')
                        )) {
                            node.style.display = 'none';
                        }

                        // Check for error notices within the added node
                        const errorElements = node.querySelectorAll('.notice-error, .error, .dab-admin-notice-error');
                        errorElements.forEach(function(errorEl) {
                            errorEl.style.display = 'none';
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });

    // Suppress PHP error output in AJAX responses
    if (typeof $ !== 'undefined') {
        const originalAjax = $.ajax;
        $.ajax = function(options) {
            const originalSuccess = options.success;
            const originalError = options.error;

            // Override success callback to filter out PHP errors
            options.success = function(data, textStatus, jqXHR) {
                // If response contains PHP errors, clean it
                if (typeof data === 'string' && (
                    data.includes('PHP Warning:') ||
                    data.includes('PHP Notice:') ||
                    data.includes('PHP Deprecated:') ||
                    data.includes('PHP Fatal error:')
                )) {
                    // Try to extract JSON from the response
                    const jsonMatch = data.match(/\{.*\}/);
                    if (jsonMatch) {
                        try {
                            data = JSON.parse(jsonMatch[0]);
                        } catch (e) {
                            data = { success: false, data: 'Request completed' };
                        }
                    } else {
                        data = { success: false, data: 'Request completed' };
                    }
                }

                if (originalSuccess) {
                    originalSuccess.call(this, data, textStatus, jqXHR);
                }
            };

            // Override error callback to suppress error displays
            options.error = function(jqXHR, textStatus, errorThrown) {
                // Silently handle errors without displaying them
                if (originalError) {
                    // Call original error handler but suppress any error displays
                    try {
                        originalError.call(this, jqXHR, textStatus, errorThrown);
                    } catch (e) {
                        // Suppress any errors in the error handler itself
                    }
                }
            };

            return originalAjax.call(this, options);
        };
    }

    // Suppress form validation errors
    document.addEventListener('DOMContentLoaded', function() {
        // Override HTML5 form validation
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('invalid', function(e) {
                e.preventDefault();
                return false;
            }, true);
        });
    });

    // Create a global flag to indicate error suppression is active
    window.DAB_ERROR_SUPPRESSION_ACTIVE = true;

    // Provide a way to restore console for debugging if needed
    window.DAB_restoreConsole = function() {
        console.log = originalConsole.log;
        console.error = originalConsole.error;
        console.warn = originalConsole.warn;
        console.info = originalConsole.info;
        console.debug = originalConsole.debug;
    };

})();
