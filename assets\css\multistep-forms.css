/**
 * Multi-Step Forms Styles
 * 
 * Modern wizard-style form styling for the Database App Builder plugin
 */

/* Multi-Step Form Container */
.dab-multistep-form-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin: 20px 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Form Header */
.dab-multistep-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f8f9fa;
}

.dab-form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.dab-form-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Progress Indicator */
.dab-progress-indicator {
  margin-bottom: 40px;
}

.dab-progress-bar {
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  margin-bottom: 20px;
  overflow: hidden;
}

.dab-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.dab-progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.dab-progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.dab-progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #ecf0f1;
  z-index: 1;
}

.dab-progress-step.completed:not(:last-child)::after {
  background: #3498db;
}

.dab-step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ecf0f1;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.dab-progress-step.current .dab-step-number {
  background: #3498db;
  color: white;
  transform: scale(1.1);
}

.dab-progress-step.completed .dab-step-number {
  background: #27ae60;
  color: white;
}

.dab-step-label {
  font-size: 12px;
  color: #7f8c8d;
  text-align: center;
  font-weight: 500;
  max-width: 80px;
  line-height: 1.3;
}

.dab-progress-step.current .dab-step-label {
  color: #3498db;
  font-weight: 600;
}

.dab-progress-step.completed .dab-step-label {
  color: #27ae60;
}

/* Form Steps */
.dab-multistep-form {
  margin-bottom: 20px;
}

.dab-form-step {
  animation: dab-fade-in 0.4s ease;
}

.dab-step-header {
  margin-bottom: 25px;
  text-align: center;
}

.dab-step-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.dab-step-description {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

.dab-step-fields {
  margin-bottom: 30px;
}

/* Form Fields */
.dab-form-field {
  margin-bottom: 20px;
}

.dab-form-field label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.dab-required {
  color: #e74c3c;
  font-weight: 700;
}

.dab-field-description {
  font-size: 13px;
  color: #7f8c8d;
  margin-bottom: 8px;
  line-height: 1.4;
}

.dab-form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.dab-form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.dab-form-control:invalid {
  border-color: #e74c3c;
}

.dab-form-control:invalid:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

/* Radio and Checkbox Styles */
.dab-radio-label,
.dab-checkbox-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
  font-weight: 400;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.dab-radio-label:hover,
.dab-checkbox-label:hover {
  background: #f8f9fa;
}

.dab-radio-label input,
.dab-checkbox-label input {
  margin-right: 8px;
  width: auto;
}

/* Field Error Messages */
.dab-field-error {
  color: #e74c3c;
  font-size: 13px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.dab-field-error::before {
  content: '⚠';
  font-size: 14px;
}

/* Form Navigation */
.dab-form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

.dab-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-width: 120px;
  justify-content: center;
}

.dab-btn-primary {
  background: #3498db;
  color: white;
}

.dab-btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.dab-btn-secondary {
  background: #95a5a6;
  color: white;
}

.dab-btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

.dab-btn-success {
  background: #27ae60;
  color: white;
}

.dab-btn-success:hover {
  background: #229954;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.dab-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Form Actions */
.dab-form-actions {
  text-align: center;
  margin-top: 20px;
}

.dab-save-progress {
  background: #f39c12;
  color: white;
}

.dab-save-progress:hover {
  background: #e67e22;
}

/* Loading States */
.dab-form-loading {
  position: relative;
}

.dab-form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.dab-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #ecf0f1;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: dab-spin 1s linear infinite;
}

/* Validation States */
.dab-form-field.has-error .dab-form-control {
  border-color: #e74c3c;
}

.dab-form-field.has-success .dab-form-control {
  border-color: #27ae60;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dab-multistep-form-container {
    padding: 20px;
    margin: 10px;
  }
  
  .dab-form-title {
    font-size: 1.5rem;
  }
  
  .dab-progress-steps {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .dab-progress-step {
    flex: none;
    min-width: 60px;
  }
  
  .dab-progress-step:not(:last-child)::after {
    display: none;
  }
  
  .dab-step-label {
    max-width: none;
    font-size: 11px;
  }
  
  .dab-form-navigation {
    flex-direction: column;
    gap: 15px;
  }
  
  .dab-btn {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .dab-step-number {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
  
  .dab-step-title {
    font-size: 1.25rem;
  }
  
  .dab-form-control {
    padding: 10px 12px;
  }
}

/* Animations */
@keyframes dab-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dab-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success Message */
.dab-form-success {
  text-align: center;
  padding: 40px 20px;
  background: #d5f4e6;
  border-radius: 8px;
  margin: 20px 0;
}

.dab-form-success .dashicons {
  font-size: 48px;
  color: #27ae60;
  margin-bottom: 15px;
}

.dab-form-success h3 {
  color: #27ae60;
  margin-bottom: 10px;
}

.dab-form-success p {
  color: #2c3e50;
  margin: 0;
}

/* Conditional Logic Indicators */
.dab-form-field.dab-conditional-hidden {
  display: none;
}

.dab-form-field.dab-conditional-show {
  animation: dab-fade-in 0.3s ease;
}

/* Step Validation */
.dab-step-invalid {
  border-left: 4px solid #e74c3c;
  padding-left: 15px;
  background: #fdf2f2;
}

.dab-step-valid {
  border-left: 4px solid #27ae60;
  padding-left: 15px;
  background: #f0f9f4;
}
