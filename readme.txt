=== Database App Builder ===
Contributors: yourname
Tags: database, app builder, form builder, data management, dashboard
Requires at least: 5.0
Tested up to: 6.3
Requires PHP: 7.0
Stable tag: 1.0.4
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Build custom database-driven applications inside WordPress without coding.

== Description ==

Database App Builder is a powerful WordPress plugin that allows you to create custom database applications without writing a single line of code. Create forms, views, dashboards, and approval workflows with an intuitive interface.

= Key Features =

* **Custom Database Tables**: Create and manage custom database tables with ease
* **Form Builder**: Build forms to collect and manage data
* **View Builder**: Create custom views to display your data
* **Dashboard Builder**: Build interactive dashboards to visualize your data
* **Approval Workflows**: Set up multi-level approval processes
* **Role-Based Permissions**: Control who can view and edit your data
* **Conditional Logic**: Create dynamic forms with conditional logic
* **Relationships**: Create relationships between tables
* **Formula Fields**: Calculate values automatically
* **Payment Integration**: Accept payments with Stripe, PayPal, and Paystack
* **Third-Party Integrations**: Connect with Google Sheets, Zapier, and more

= Use Cases =

* Customer Relationship Management (CRM)
* Project Management
* Inventory Management
* HR Management
* Event Registration
* Membership Management
* And much more!

== Installation ==

1. Upload the `db-app-builder` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to 'Database App Builder' in the admin menu to start building your applications

== Frequently Asked Questions ==

= Do I need to know how to code to use this plugin? =

No, Database App Builder is designed to be used without any coding knowledge. The intuitive interface allows you to create complex database applications with just a few clicks.

= Can I create relationships between tables? =

Yes, you can create one-to-many and many-to-many relationships between tables. This allows you to create complex data structures.

= Is my data secure? =

Yes, all data is stored in your WordPress database and is protected by the same security measures as your WordPress site. The plugin also includes role-based permissions to control who can view and edit your data.

= Can I export my data? =

Yes, you can export your data to CSV, Excel, and PDF formats.

= Does it work with any theme? =

Yes, Database App Builder is designed to work with any WordPress theme.

== Screenshots ==

1. Dashboard overview
2. Form builder interface
3. View builder interface
4. Dashboard builder interface
5. Approval workflow configuration

== Changelog ==

= 1.0.4 =
* Added uninstall settings page for better data management
* Improved security with proper nonce verification
* Enhanced admin/public code separation
* Fixed SQL injection vulnerabilities
* Added proper escaping for all output
* Improved database query performance
* Added translation support with .pot file
* Fixed various bugs and improved stability

= 1.0.3 =
* Added payment gateway integrations (Stripe, PayPal, Paystack)
* Added third-party integrations (Google Sheets, Zapier)
* Improved dashboard builder with more visualization options
* Enhanced form builder with conditional logic
* Added role-based permissions for tables and fields
* Fixed various bugs and improved stability

= 1.0.2 =
* Added approval workflows
* Added formula fields
* Added relationship fields
* Improved form builder interface
* Enhanced view builder with filtering and sorting options
* Fixed various bugs and improved stability

= 1.0.1 =
* Added dashboard builder
* Added export functionality
* Improved form builder with more field types
* Enhanced view builder with pagination
* Fixed various bugs and improved stability

= 1.0.0 =
* Initial release

== Upgrade Notice ==

= 1.0.4 =
This version includes important security improvements and bug fixes. Please update as soon as possible.

== Additional Information ==

For more information, please visit [our website](https://yourdomain.com/database-app-builder).

== Credits ==

* [jQuery](https://jquery.com/) - JavaScript library
* [Chart.js](https://www.chartjs.org/) - JavaScript charting library
* [DataTables](https://datatables.net/) - jQuery plugin for table functionality
