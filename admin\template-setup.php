<?php
/**
 * Manual Template Setup
 *
 * Use this file if templates are not showing up automatically
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Manual template setup
if (isset($_GET['setup_templates']) && $_GET['setup_templates'] === '1') {
    if (class_exists('DAB_Template_Manager')) {
        try {
            DAB_Template_Manager::create_tables();
            echo '<div class="notice notice-success"><p>Template system setup completed successfully!</p></div>';
        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p>Error setting up templates: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    } else {
        echo '<div class="notice notice-error"><p>Template Manager class not found. Please check if the plugin is properly installed.</p></div>';
    }
}

// Force reload templates
if (isset($_GET['force_reload']) && $_GET['force_reload'] === '1') {
    if (class_exists('DAB_Template_Manager')) {
        try {
            global $wpdb;
            $templates_table = $wpdb->prefix . 'dab_app_templates';

            // Clear existing system templates
            $wpdb->query("DELETE FROM $templates_table WHERE is_system = 1");

            // Re-insert all templates
            DAB_Template_Manager::create_tables();

            $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_system = 1");
            echo '<div class="notice notice-success"><p>Templates force reloaded successfully! Now have ' . intval($new_count) . ' system templates.</p></div>';
        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p>Error force reloading templates: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
}

?>
<div class="wrap">
    <h1>Template System Setup</h1>

    <div class="card">
        <h2>Manual Template Setup</h2>
        <p>If you don't see the "App Templates" menu in your Database App Builder, click the button below to manually set up the template system.</p>

        <p>
            <a href="<?php echo admin_url('admin.php?page=dab_template_setup&setup_templates=1'); ?>"
               class="button button-primary">
                Set Up Template System
            </a>

            <a href="<?php echo admin_url('admin.php?page=dab_template_setup&force_reload=1'); ?>"
               class="button button-secondary"
               onclick="return confirm('This will reload all system templates. Continue?')">
                Force Reload All Templates
            </a>
        </p>

        <p><small><strong>Force Reload:</strong> Use this if templates are missing or not showing correctly. This will clear and reload all 19 system templates.</small></p>
    </div>

    <div class="card">
        <h2>Troubleshooting</h2>
        <h3>Check Database Tables</h3>
        <p>The template system requires these database tables:</p>
        <ul>
            <li><code><?php echo $GLOBALS['wpdb']->prefix; ?>dab_app_templates</code></li>
            <li><code><?php echo $GLOBALS['wpdb']->prefix; ?>dab_template_installations</code></li>
        </ul>

        <?php
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $installations_table = $wpdb->prefix . 'dab_template_installations';

        $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") === $templates_table;
        $installations_exists = $wpdb->get_var("SHOW TABLES LIKE '$installations_table'") === $installations_table;
        ?>

        <h3>Table Status</h3>
        <ul>
            <li>Templates Table:
                <?php if ($templates_exists): ?>
                    <span style="color: green;">✓ Exists</span>
                <?php else: ?>
                    <span style="color: red;">✗ Missing</span>
                <?php endif; ?>
            </li>
            <li>Installations Table:
                <?php if ($installations_exists): ?>
                    <span style="color: green;">✓ Exists</span>
                <?php else: ?>
                    <span style="color: red;">✗ Missing</span>
                <?php endif; ?>
            </li>
        </ul>

        <?php if ($templates_exists): ?>
            <?php
            $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
            $system_template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_system = 1");
            ?>
            <h3>Available Templates</h3>
            <p>Total templates in database: <strong><?php echo intval($template_count); ?></strong></p>
            <p>System templates: <strong><?php echo intval($system_template_count); ?></strong> (Expected: 19)</p>

            <?php if ($system_template_count >= 19): ?>
                <p style="color: green;">✓ All templates are loaded and ready to use!</p>
                <p><a href="<?php echo admin_url('admin.php?page=dab_app_templates'); ?>" class="button">Go to App Templates</a></p>
            <?php elseif ($template_count > 0): ?>
                <p style="color: orange;">⚠ Some templates found but not all system templates are loaded. Try the setup button above.</p>
                <p><a href="<?php echo admin_url('admin.php?page=dab_app_templates'); ?>" class="button">Go to App Templates</a></p>
            <?php else: ?>
                <p style="color: red;">❌ No templates found. Try the setup button above.</p>
            <?php endif; ?>

            <?php if ($system_template_count > 0): ?>
                <h4>Template Categories</h4>
                <?php
                $categories = $wpdb->get_results("SELECT category, COUNT(*) as count FROM $templates_table WHERE is_system = 1 GROUP BY category ORDER BY category");
                if ($categories): ?>
                    <ul>
                        <?php foreach ($categories as $cat): ?>
                            <li><strong><?php echo esc_html(ucwords(str_replace('_', ' ', $cat->category))); ?>:</strong> <?php echo intval($cat->count); ?> templates</li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Need Help?</h2>
        <p>If you're still having issues accessing templates:</p>
        <ol>
            <li><strong>Check Plugin Version</strong>: Make sure you have the latest version</li>
            <li><strong>Check PHP Errors</strong>: Look for any PHP errors in your error log</li>
            <li><strong>Check Permissions</strong>: Make sure your user has admin permissions</li>
            <li><strong>Clear Cache</strong>: Clear any caching plugins you might have</li>
        </ol>
    </div>
</div>

<style>
.card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
    margin-top: 0;
}

.card h3 {
    color: #23282d;
    margin-top: 20px;
}

.card ul {
    margin-left: 20px;
}

.card code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
