<?php
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$views_table = $wpdb->prefix . 'dab_views';
$fields_table = $wpdb->prefix . 'dab_fields';

$message = '';

// Handle Delete
if (isset($_GET['delete_view'])) {
    $view_id = intval($_GET['delete_view']);
    $wpdb->delete($views_table, ['id' => $view_id]);
    $message = "View deleted successfully.";
}

// Handle Create/Update
if (isset($_POST['dab_save_view'])) {
    $view_name = sanitize_text_field($_POST['view_name']);
    $table_id = intval($_POST['table_id']);
    $visible_fields = isset($_POST['visible_fields']) ? array_map('sanitize_text_field', $_POST['visible_fields']) : [];
    $filters = isset($_POST['filters']) ? $_POST['filters'] : [];
    $sort_order = sanitize_text_field($_POST['sort_order']);
    $is_public = isset($_POST['is_public']) ? 1 : 0;

    if (isset($_POST['view_id']) && $_POST['view_id']) {
        $wpdb->update($views_table, [
            'view_name' => $view_name,
            'table_id' => $table_id,
            'selected_fields' => maybe_serialize($visible_fields),
            'filter_conditions' => maybe_serialize($filters),
            'sort_order' => $sort_order,
            'is_public' => $is_public,
        ], ['id' => intval($_POST['view_id'])]);

        $message = "View updated successfully.";
    } else {
        $wpdb->insert($views_table, [
            'view_name' => $view_name,
            'table_id' => $table_id,
            'selected_fields' => maybe_serialize($visible_fields),
            'filter_conditions' => maybe_serialize($filters),
            'sort_order' => $sort_order,
            'is_public' => $is_public,
            'created_at' => current_time('mysql')
        ]);

        $message = "View created successfully.";
    }
}

// Get all tables for dropdown
$tables = $wpdb->get_results("SELECT id, table_label FROM $tables_table ORDER BY table_label ASC");

// Get all views with table labels
$views = $wpdb->get_results("
    SELECT v.*, t.table_label as table_name 
    FROM $views_table v
    LEFT JOIN $tables_table t ON v.table_id = t.id
    ORDER BY v.view_name ASC
");

$edit_mode = false;
$edit_view = null;
$selected_table_id = 0;

if (isset($_GET['edit_view'])) {
    $edit_mode = true;
    $edit_id = intval($_GET['edit_view']);
    $edit_view = $wpdb->get_row($wpdb->prepare("SELECT * FROM $views_table WHERE id = %d", $edit_id));
    if ($edit_view) {
        $selected_table_id = $edit_view->table_id;
    }
} elseif (isset($_POST['table_id'])) {
    $selected_table_id = intval($_POST['table_id']);
}

$fields = $selected_table_id ? $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $selected_table_id)) : [];
$visible_fields = $edit_view && isset($edit_view->selected_fields) ? maybe_unserialize($edit_view->selected_fields) : [];
$filters = $edit_view && isset($edit_view->filter_conditions) ? maybe_unserialize($edit_view->filter_conditions) : [];
$sort_order = $edit_view && isset($edit_view->sort_order) ? $edit_view->sort_order : '';
$is_public = $edit_view && isset($edit_view->is_public) ? $edit_view->is_public : 0;
?>

<div class="wrap">
    <h1><?php echo $edit_mode ? 'Edit View' : 'Create View'; ?></h1>

    <?php if (!empty($message)): ?>
        <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
    <?php endif; ?>

    <form method="post">
        <?php if ($edit_mode): ?>
            <input type="hidden" name="view_id" value="<?php echo esc_attr($edit_view->id); ?>">
        <?php endif; ?>

        <table class="form-table">
            <tr>
                <th>View Name</th>
                <td><input type="text" name="view_name" required value="<?php echo $edit_mode ? esc_attr($edit_view->view_name) : ''; ?>"></td>
            </tr>
            <tr>
                <th>Select Table</th>
                <td>
                    <select name="table_id" onchange="this.form.submit();" required>
                        <option value="">-- Choose Table --</option>
                        <?php foreach ($tables as $tbl): ?>
                            <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($selected_table_id, $tbl->id); ?>>
                                <?php echo esc_html($tbl->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
        </table>

        <?php if ($selected_table_id && $fields): ?>
            <h3>Visible Fields</h3>
            <ul style="columns:2;list-style:none;padding-left:0;">
                <?php foreach ($fields as $f): ?>
                    <li>
                        <label>
                            <input type="checkbox" name="visible_fields[]" value="<?php echo esc_attr($f->field_slug); ?>"
                                <?php checked(in_array($f->field_slug, $visible_fields)); ?>>
                            <?php echo esc_html($f->field_label); ?>
                        </label>
                    </li>
                <?php endforeach; ?>
            </ul>

            <h3>Filter Conditions</h3>
            <?php foreach ($fields as $f): ?>
                <p>
                    <label><?php echo esc_html($f->field_label); ?>:
                        <input type="text" name="filters[<?php echo esc_attr($f->field_slug); ?>]"
                            value="<?php echo esc_attr($filters[$f->field_slug] ?? ''); ?>">
                    </label>
                </p>
            <?php endforeach; ?>

            <p>
                <label>Sort Order:
                    <select name="sort_order">
                        <option value="">-- Select Field --</option>
                        <?php foreach ($fields as $f): ?>
                            <option value="<?php echo esc_attr($f->field_slug); ?>" <?php selected($sort_order, $f->field_slug); ?>>
                                <?php echo esc_html($f->field_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </label>
            </p>

            <p>
                <label>
                    <input type="checkbox" name="is_public" value="1" <?php checked($is_public); ?>> Make View Public
                </label>
            </p>
        <?php endif; ?>

        <p><input type="submit" name="dab_save_view" class="button button-primary" value="<?php echo $edit_mode ? 'Update View' : 'Create View'; ?>"></p>
    </form>

    <hr>
    <h2>Existing Views</h2>
    <?php if (!empty($views)): ?>
        <table class="widefat striped">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Table</th>
                    <th>Public</th>
                    <th>Shortcode</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($views as $view): ?>
                    <?php
                        $table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $view->table_id));
                        $is_public = isset($view->is_public) ? $view->is_public : 0;
                    ?>
                    <tr>
                        <td><?php echo esc_html($view->view_name); ?></td>
                        <td><?php echo esc_html($table ? $table->table_label : '—'); ?></td>
                        <td><?php echo $is_public ? 'Yes' : 'No'; ?></td>
                        <td><code>[dab_view id="<?php echo esc_attr($view->id); ?>"]</code></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=dab_views&edit_view=' . $view->id); ?>">Edit</a> |
                            <a href="<?php echo admin_url('admin.php?page=dab_views&delete_view=' . $view->id); ?>"
                               onclick="return confirm('Are you sure you want to delete this view?');">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No views created yet.</p>
    <?php endif; ?>
</div>
