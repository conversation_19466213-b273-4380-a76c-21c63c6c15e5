<?php
/**
 * Email Notifier
 *
 * This class handles email notifications for form submissions.
 */
if (!defined('ABSPATH')) exit;

class DAB_Email_Notifier {

    /**
     * Send a notification email
     *
     * @param string|array $to_email Recipient email address(es)
     * @param string $subject Email subject
     * @param string $message Email message
     * @param array $headers Email headers
     * @param array $attachments Email attachments
     * @return bool Whether the email was sent successfully
     */
    public static function send_notification($to_email, $subject, $message, $headers = [], $attachments = []) {
        if (empty($to_email) || empty($subject) || empty($message)) return false;

        // Use the enhanced email class for better deliverability
        return DAB_Enhanced_Email::send($to_email, $subject, $message, [
            'attachments' => $attachments,
            'tracking_id' => 'notification-' . uniqid(),
        ]);
    }

    /**
     * Trigger an email notification on form submission
     *
     * @param object $form Form object
     * @param array $data Form data
     * @return bool Whether the email was sent successfully
     */
    public static function trigger_on_form_submission($form, $data) {
        if (empty($form->notify_email) || empty($form->notify_message)) return false;

        // Get form name for subject
        $subject = sprintf('New Form Submission: %s', $form->form_name);

        // Replace placeholders in message
        $message = self::replace_placeholders($form->notify_message, $data);

        // Add record ID if available
        if (isset($data['id'])) {
            $message .= '<p><strong>Record ID:</strong> ' . esc_html($data['id']) . '</p>';
        }

        // Add submission timestamp
        $message .= '<p><strong>Submitted:</strong> ' . date_i18n(get_option('date_format') . ' ' . get_option('time_format')) . '</p>';

        // Get submitter info if available
        $user_info = '';
        if (isset($data['user_id']) && $data['user_id']) {
            $user = get_userdata($data['user_id']);
            if ($user) {
                $user_info = sprintf(
                    '<p><strong>Submitted by:</strong> %s (%s)</p>',
                    esc_html($user->display_name),
                    esc_html($user->user_email)
                );
            }
        }

        if ($user_info) {
            $message .= $user_info;
        }

        // Add admin link if available
        if (isset($data['id']) && isset($form->table_id)) {
            $admin_url = admin_url('admin.php?page=dab_data&table_id=' . $form->table_id . '&action=edit&id=' . $data['id']);
            $message .= '<p><a href="' . esc_url($admin_url) . '" style="display: inline-block; background-color: #0073aa; color: #fff; padding: 10px 15px; text-decoration: none; border-radius: 3px;">View Record in Admin</a></p>';
        }

        // Send to all recipients
        $recipients = explode(',', $form->notify_email);
        $success = true;

        foreach ($recipients as $recipient) {
            $recipient = trim($recipient);
            if (!empty($recipient)) {
                // Get current user as sender if available
                $current_user_id = get_current_user_id();
                $args = ['tracking_id' => 'form-' . $form->id . '-' . time()];

                if ($current_user_id) {
                    $user = get_userdata($current_user_id);
                    if ($user) {
                        $args['from_name'] = $user->display_name;
                        $args['from_email'] = $user->user_email;
                        $args['reply_to'] = $user->user_email;
                    }
                }

                $result = DAB_Enhanced_Email::send($recipient, $subject, $message, $args);
                $success = $success && $result;
            }
        }

        return $success;
    }

    /**
     * Replace placeholders in text with actual values
     *
     * @param string $text Text with placeholders
     * @param array $data Data to replace placeholders with
     * @return string Text with placeholders replaced
     */
    private static function replace_placeholders($text, $data) {
        foreach ($data as $key => $value) {
            // Handle file/image fields
            if (is_numeric($value) && $key !== 'id' && $key !== 'user_id') {
                $attachment_url = wp_get_attachment_url($value);
                if ($attachment_url) {
                    $value = '<a href="' . esc_url($attachment_url) . '">' . esc_html(basename($attachment_url)) . '</a>';
                }
            }

            // Make sure value is not null before using it in str_replace
            $safe_value = $value !== null ? (string)$value : '';
            if (function_exists('dab_safe_str_replace')) {
                $text = dab_safe_str_replace('{{' . $key . '}}', esc_html($safe_value), $text);
            } else {
                $text = str_replace('{{' . $key . '}}', esc_html($safe_value), ($text ?? ''));
            }
        }

        // Convert line breaks to <br> tags if the message doesn't contain HTML
        if (strip_tags($text) === $text) {
            $text = nl2br($text);
        }

        return $text;
    }
}
?>
