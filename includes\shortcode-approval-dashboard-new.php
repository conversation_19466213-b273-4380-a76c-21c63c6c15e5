<?php
/**
 * Enhanced Approval Dashboard Shortcode
 * 
 * This file provides a modern, user-friendly approval dashboard for the workflow system.
 */
if (!defined('ABSPATH')) exit;

add_shortcode('dab_approval_dashboard', function () {
    global $wpdb, $current_user;

    wp_get_current_user();
    $user_id = get_current_user_id();
    if (!$user_id) return '<p>You must be logged in to view this dashboard.</p>';

    // Enqueue required styles and scripts
    wp_enqueue_style('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/approval-dashboard.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/approval-dashboard.js', array('jquery'), DAB_VERSION, true);
    
    // Get all tables
    $tables = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}dab_tables");
    
    // Start output buffer
    ob_start();
    
    // Include approval modal template
    include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/approval-modal.php');
    
    // Dashboard header with stats
    echo '<div class="dab-approval-dashboard">';
    echo '<div class="dab-dashboard-header">';
    echo '<h2 class="dab-dashboard-title">Your Approval Dashboard</h2>';
    
    // Get approval stats
    $pending_count = 0;
    $approved_count = 0;
    $rejected_count = 0;
    
    foreach ($tables as $table) {
        $table_slug = sanitize_title($table->table_slug);
        $data_table = $wpdb->prefix . 'dab_' . $table_slug;
        
        // Count records by status
        $pending = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $data_table WHERE user_id = %d AND approval_status = 'Pending'", 
            $user_id
        ));
        
        $approved = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $data_table WHERE user_id = %d AND approval_status = 'Approved'", 
            $user_id
        ));
        
        $rejected = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $data_table WHERE user_id = %d AND approval_status = 'Rejected'", 
            $user_id
        ));
        
        $pending_count += $pending;
        $approved_count += $approved;
        $rejected_count += $rejected;
    }
    
    // Display stats
    echo '<div class="dab-dashboard-stats">';
    echo '<div class="dab-stat-card dab-stat-total">';
    echo '<div class="dab-stat-number" id="dab-count-total">' . ($pending_count + $approved_count + $rejected_count) . '</div>';
    echo '<div class="dab-stat-label">Total Records</div>';
    echo '</div>';
    
    echo '<div class="dab-stat-card dab-stat-pending">';
    echo '<div class="dab-stat-number" id="dab-count-pending">' . $pending_count . '</div>';
    echo '<div class="dab-stat-label">Pending</div>';
    echo '</div>';
    
    echo '<div class="dab-stat-card dab-stat-approved">';
    echo '<div class="dab-stat-number" id="dab-count-approved">' . $approved_count . '</div>';
    echo '<div class="dab-stat-label">Approved</div>';
    echo '</div>';
    
    echo '<div class="dab-stat-card dab-stat-rejected">';
    echo '<div class="dab-stat-number" id="dab-count-rejected">' . $rejected_count . '</div>';
    echo '<div class="dab-stat-label">Rejected</div>';
    echo '</div>';
    echo '</div>'; // End stats
    
    echo '</div>'; // End header
    
    // Dashboard filters
    echo '<div class="dab-dashboard-filters">';
    
    echo '<div class="dab-filter-group">';
    echo '<label class="dab-filter-label">Status:</label>';
    echo '<select id="dab-filter-status" class="dab-filter-select">';
    echo '<option value="all">All</option>';
    echo '<option value="Pending">Pending</option>';
    echo '<option value="Approved">Approved</option>';
    echo '<option value="Rejected">Rejected</option>';
    echo '</select>';
    echo '</div>';
    
    echo '<div class="dab-filter-group">';
    echo '<label class="dab-filter-label">Date:</label>';
    echo '<input type="date" id="dab-filter-date-from" class="dab-filter-date" placeholder="From">';
    echo '<span>to</span>';
    echo '<input type="date" id="dab-filter-date-to" class="dab-filter-date" placeholder="To">';
    echo '</div>';
    
    echo '<div class="dab-filter-search">';
    echo '<input type="text" id="dab-approval-search" placeholder="Search records...">';
    echo '</div>';
    
    echo '</div>'; // End filters
    
    // Records container
    echo '<div class="dab-approval-records">';
    
    // Loop through tables
    foreach ($tables as $table) {
        $table_slug = sanitize_title($table->table_slug);
        $data_table = $wpdb->prefix . 'dab_' . $table_slug;
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d", 
            $table->id
        ));

        if (empty($fields)) continue;

        $records = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $data_table WHERE user_id = %d ORDER BY id DESC", 
            $user_id
        ));

        if (empty($records)) continue;
        
        // Display table name as section header
        echo '<div class="dab-section-header">';
        echo '<h3 class="dab-section-title">' . esc_html($table->table_label) . '</h3>';
        echo '</div>';
        
        // Loop through records
        foreach ($records as $record) {
            // Get primary field for record title
            $primary_field = reset($fields);
            $record_title = $record->{$primary_field->field_slug} ?? 'Record #' . $record->id;
            
            // Format date
            $created_date = !empty($record->created_at) ? date('Y-m-d', strtotime($record->created_at)) : '';
            
            // Get submitter info
            $submitter = get_userdata($record->user_id);
            $submitter_name = $submitter ? $submitter->display_name : 'Unknown';
            
            echo '<div class="dab-approval-record" data-status="' . esc_attr($record->approval_status) . '" data-date="' . esc_attr($created_date) . '">';
            
            // Record header
            echo '<div class="dab-record-header">';
            echo '<div>';
            echo '<h4 class="dab-record-title">' . esc_html($record_title) . '</h4>';
            echo '<div class="dab-record-meta">';
            echo '<div class="dab-record-submitter">Submitted by: ' . esc_html($submitter_name) . '</div>';
            echo '<div class="dab-record-date">Date: ' . esc_html($created_date) . '</div>';
            echo '</div>';
            echo '</div>';
            
            // Status badge
            echo '<div class="dab-approval-status dab-status-' . strtolower($record->approval_status) . '" data-status="' . esc_attr($record->approval_status) . '">';
            echo esc_html($record->approval_status);
            echo '</div>';
            echo '</div>'; // End record header
            
            // Record content - show key fields
            echo '<div class="dab-record-content">';
            
            // Show up to 3 important fields
            $display_fields = array_slice($fields, 0, 3);
            foreach ($display_fields as $field) {
                $field_value = $record->{$field->field_slug} ?? '';
                
                // Format field value based on type
                if ($field->field_type === 'lookup') {
                    $lookup_table_id = intval($field->lookup_table_id);
                    $display_column = sanitize_text_field($field->lookup_display_column);
                    if ($lookup_table_id && $display_column) {
                        $lookup_table_slug = $wpdb->get_var(
                            $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                        );
                        if ($lookup_table_slug) {
                            $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                            $lookup_id = intval($field_value);
                            $field_value = $wpdb->get_var(
                                $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                            );
                        }
                    }
                }
                
                echo '<div class="dab-record-field">';
                echo '<div class="dab-field-label">' . esc_html($field->field_label) . '</div>';
                echo '<div class="dab-field-value">' . esc_html($field_value) . '</div>';
                echo '</div>';
            }
            echo '</div>'; // End record content
            
            // Approval history
            $history = DAB_Approval_Manager::get_approval_history($table->id, $record->id);
            if (!empty($history)) {
                echo '<div class="dab-approval-history" id="history-' . $record->id . '" style="display:none;">';
                echo '<h5 class="dab-history-title">Approval History</h5>';
                echo '<ul class="dab-history-list">';
                
                foreach ($history as $entry) {
                    $user = get_userdata($entry->user_id);
                    $username = $user ? $user->display_name : 'Unknown';
                    
                    echo '<li class="dab-history-item">';
                    echo '<div class="dab-history-meta">';
                    echo '<span class="dab-history-user">' . esc_html($username) . '</span>';
                    echo '<span class="dab-history-date">' . date('Y-m-d H:i', strtotime($entry->created_at)) . '</span>';
                    echo '</div>';
                    
                    echo '<div>';
                    echo '<span class="dab-history-status dab-status-' . strtolower($entry->status) . '">' . esc_html($entry->status) . '</span>';
                    echo ' at level "' . esc_html($entry->level_name) . '"';
                    echo '</div>';
                    
                    if (!empty($entry->notes)) {
                        echo '<div class="dab-history-notes">' . esc_html($entry->notes) . '</div>';
                    }
                    
                    echo '</li>';
                }
                
                echo '</ul>';
                echo '</div>';
            }
            
            // Record actions
            echo '<div class="dab-record-actions">';
            
            // View details button
            echo '<button type="button" class="dab-action-btn dab-view-btn dab-view-record-btn" data-record="' . $record->id . '" data-table="' . $table->id . '">View Details</button>';
            
            // View history button (if history exists)
            if (!empty($history)) {
                echo '<button type="button" class="dab-action-btn dab-view-btn view-history-btn" data-record="' . $record->id . '">View History</button>';
            }
            
            // Resubmit button for rejected records
            if ($record->approval_status === 'Rejected') {
                echo '<form method="post" style="display:inline;">';
                echo '<input type="hidden" name="resubmit" value="' . $record->id . '">';
                echo '<input type="hidden" name="table_slug" value="' . $table_slug . '">';
                echo '<button type="submit" class="dab-action-btn dab-approve-btn">Resubmit</button>';
                echo '</form>';
            }
            
            echo '</div>'; // End record actions
            
            echo '</div>'; // End approval record
        }
    }
    
    echo '</div>'; // End approval records
    echo '</div>'; // End approval dashboard
    
    // Return the output buffer
    return ob_get_clean();
});

add_action('init', function () {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['resubmit'], $_POST['table_slug'])) {
        global $wpdb;

        $record_id = intval($_POST['resubmit']);
        $table_slug = sanitize_text_field($_POST['table_slug']);
        $table_name = $wpdb->prefix . 'dab_' . $table_slug;

        // Get table ID
        $table_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}dab_tables WHERE table_slug = %s",
            $table_slug
        ));

        if (!$table_id) return;

        // Update record status
        $wpdb->update(
            $table_name,
            [
                'approval_status' => 'Pending',
                'approval_notes' => ''
            ],
            ['id' => $record_id]
        );

        // Initialize approval workflow
        DAB_Approval_Manager::initialize_approval($table_id, $record_id);

        // Redirect to prevent form resubmission
        wp_redirect(add_query_arg('resubmitted', '1', $_SERVER['HTTP_REFERER']));
        exit;
    }
});
