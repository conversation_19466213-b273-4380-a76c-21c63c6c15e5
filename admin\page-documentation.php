<?php
/**
 * Documentation Page
 *
 * Provides comprehensive documentation on how to use the Database App Builder plugin.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}
?>

<div class="wrap dab-admin-wrap">
    <h1>Database App Builder Documentation</h1>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Introduction</h2>
        </div>
        <div class="dab-card-body">
            <p>Database App Builder is a powerful WordPress plugin that allows you to create custom database-driven applications without coding. This documentation will guide you through all the features and functions of the plugin.</p>

            <p>Whether you're building a simple contact form or a complex business application, Database App Builder provides all the tools you need to create, manage, and visualize your data.</p>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Getting Started</h2>
        </div>
        <div class="dab-card-body">
            <h3>Core Concepts</h3>
            <p>Database App Builder is built around these core components:</p>
            <ul>
                <li><strong>Tables</strong>: Define the structure of your data</li>
                <li><strong>Fields</strong>: Specify what type of data each table can store</li>
                <li><strong>Forms</strong>: Allow users to input data into your tables</li>
                <li><strong>Views</strong>: Display your data in various formats</li>
                <li><strong>Dashboards</strong>: Create visual representations of your data</li>
            </ul>

            <h3>Quick Start Guide</h3>
            <ol>
                <li>Create a <strong>Table</strong> to define your data structure</li>
                <li>Add <strong>Fields</strong> to your table to store different types of data</li>
                <li>Create a <strong>Form</strong> to allow users to input data</li>
                <li>Create a <strong>View</strong> to display your data</li>
                <li>Build a <strong>Dashboard</strong> to visualize your data</li>
            </ol>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Tables</h2>
        </div>
        <div class="dab-card-body">
            <h3>Creating Tables</h3>
            <p>Tables are the foundation of your database application. To create a table:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Tables</strong></li>
                <li>Click the <strong>Create New Table</strong> button</li>
                <li>Enter a label and description for your table</li>
                <li>Click <strong>Create Table</strong></li>
            </ol>

            <h3>Table Properties</h3>
            <ul>
                <li><strong>Label</strong>: The human-readable name of your table</li>
                <li><strong>Slug</strong>: The machine-readable name (automatically generated from the label)</li>
                <li><strong>Description</strong>: A brief explanation of what the table is for</li>
            </ul>

            <h3>Default Fields</h3>
            <p>When you create a table, the following fields are automatically added:</p>
            <ul>
                <li><strong>ID</strong>: A unique identifier for each record</li>
                <li><strong>User ID</strong>: The WordPress user who created the record</li>
                <li><strong>Created At</strong>: The date and time when the record was created</li>
                <li><strong>Updated At</strong>: The date and time when the record was last updated</li>
            </ul>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Fields</h2>
        </div>
        <div class="dab-card-body">
            <h3>Adding Fields to Tables</h3>
            <p>Fields define what type of data your table can store. To add fields to a table:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Fields</strong></li>
                <li>Select the table you want to add fields to</li>
                <li>Click the <strong>Add New Field</strong> button</li>
                <li>Configure the field properties</li>
                <li>Click <strong>Save Field</strong></li>
            </ol>

            <h3>Field Types</h3>
            <p>Database App Builder supports the following field types:</p>
            <ul>
                <li><strong>Text</strong>: For short text like names or titles</li>
                <li><strong>Textarea</strong>: For longer text like descriptions or comments</li>
                <li><strong>Number</strong>: For numeric values</li>
                <li><strong>Email</strong>: For email addresses</li>
                <li><strong>URL</strong>: For web addresses</li>
                <li><strong>Date</strong>: For dates</li>
                <li><strong>Time</strong>: For times</li>
                <li><strong>DateTime</strong>: For date and time values</li>
                <li><strong>Select</strong>: For dropdown lists with predefined options</li>
                <li><strong>Radio</strong>: For selecting one option from a list</li>
                <li><strong>Checkbox</strong>: For yes/no or true/false values</li>
                <li><strong>File</strong>: For file uploads</li>
                <li><strong>Image</strong>: For image uploads</li>
                <li><strong>Relationship</strong>: For connecting records from different tables</li>
                <li><strong>Formula</strong>: For calculated values based on other fields</li>
                <li><strong>User</strong>: For selecting WordPress users</li>
                <li><strong>Role</strong>: For selecting WordPress user roles</li>
                <li><strong>Payment</strong>: For accepting payments through forms</li>
                <li><strong>Signature Capture</strong>: For capturing electronic signatures</li>
                <li><strong>Audio/Video Media</strong>: For uploading, recording, or embedding audio and video content</li>
                <li><strong>Conditional Logic</strong>: For fields that change their value based on conditions from other fields</li>
                <li><strong>Social Media Links</strong>: For collecting and displaying social media profile links</li>
                <li><strong>Multi-select Dropdown</strong>: For selecting multiple options from a dropdown list</li>
                <li><strong>Currency (Enhanced)</strong>: For handling monetary values with currency symbols and formatting</li>
            </ul>

            <h3>Field Properties</h3>
            <p>Each field has the following properties:</p>
            <ul>
                <li><strong>Label</strong>: The human-readable name of the field</li>
                <li><strong>Slug</strong>: The machine-readable name (automatically generated from the label)</li>
                <li><strong>Type</strong>: The type of data the field will store</li>
                <li><strong>Description</strong>: A brief explanation of what the field is for</li>
                <li><strong>Required</strong>: Whether the field must be filled in</li>
                <li><strong>Default Value</strong>: The initial value of the field</li>
                <li><strong>Placeholder</strong>: Text that appears in the field before the user enters a value</li>
                <li><strong>Help Text</strong>: Additional information to help users fill in the field</li>
            </ul>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Forms</h2>
        </div>
        <div class="dab-card-body">
            <h3>Creating Forms</h3>
            <p>Forms allow users to input data into your tables. To create a form:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Forms</strong></li>
                <li>Click the <strong>Create New Form</strong> button</li>
                <li>Select the table the form will add data to</li>
                <li>Configure the form settings</li>
                <li>Select which fields to include in the form</li>
                <li>Click <strong>Save Form</strong></li>
            </ol>

            <h3>Form Settings</h3>
            <ul>
                <li><strong>Title</strong>: The name of the form</li>
                <li><strong>Description</strong>: A brief explanation of what the form is for</li>
                <li><strong>Success Message</strong>: The message displayed after a successful submission</li>
                <li><strong>Submit Button Text</strong>: The text on the submit button</li>
                <li><strong>Redirect URL</strong>: Where to send users after form submission</li>
                <li><strong>Email Notification</strong>: Send email notifications when the form is submitted</li>
            </ul>

            <h3>Using Forms</h3>
            <p>You can add forms to your WordPress pages or posts using shortcodes:</p>
            <code>[dab_form id="1"]</code>
            <p>Replace "1" with the ID of your form.</p>

            <h3>Conditional Logic</h3>
            <p>Forms support conditional logic to show or hide fields based on user input. To set up conditional logic:</p>
            <ol>
                <li>Edit your form</li>
                <li>Scroll to the Conditional Logic section</li>
                <li>Use the visual builder to create your conditions</li>
                <li>Save your form</li>
            </ol>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Views</h2>
        </div>
        <div class="dab-card-body">
            <h3>Creating Views</h3>
            <p>Views allow you to display your data in various formats. To create a view:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Views</strong></li>
                <li>Click the <strong>Create New View</strong> button</li>
                <li>Select the table the view will display data from</li>
                <li>Configure the view settings</li>
                <li>Select which fields to include in the view</li>
                <li>Click <strong>Save View</strong></li>
            </ol>

            <h3>View Settings</h3>
            <ul>
                <li><strong>Title</strong>: The name of the view</li>
                <li><strong>Description</strong>: A brief explanation of what the view is for</li>
                <li><strong>Layout</strong>: How the data will be displayed (table, list, grid, etc.)</li>
                <li><strong>Pagination</strong>: How many records to show per page</li>
                <li><strong>Sorting</strong>: Which field to sort by and in what order</li>
                <li><strong>Filtering</strong>: Allow users to filter the data</li>
                <li><strong>Public</strong>: Whether the view is accessible to non-logged-in users</li>
            </ul>

            <h3>Using Views</h3>
            <p>You can add views to your WordPress pages or posts using shortcodes:</p>
            <code>[dab_view id="1"]</code>
            <p>Replace "1" with the ID of your view.</p>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Dashboards</h2>
        </div>
        <div class="dab-card-body">
            <h3>Creating Dashboards</h3>
            <p>Dashboards allow you to create visual representations of your data. To create a dashboard:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Dashboards</strong></li>
                <li>Click the <strong>Create New Dashboard</strong> button</li>
                <li>Enter a title and description for your dashboard</li>
                <li>Click <strong>Create Dashboard</strong></li>
                <li>Use the dashboard builder to add and configure widgets</li>
                <li>Click <strong>Save Dashboard</strong></li>
            </ol>

            <h3>Dashboard Widgets</h3>
            <p>Dashboards support the following widget types:</p>
            <ul>
                <li><strong>Table</strong>: Display data in a table format</li>
                <li><strong>Chart</strong>: Visualize data with various chart types (bar, line, pie, etc.)</li>
                <li><strong>Counter</strong>: Show a count of records or a calculated value</li>
                <li><strong>Text</strong>: Add custom text or HTML content</li>
            </ul>

            <h3>Using Dashboards</h3>
            <p>You can add dashboards to your WordPress pages or posts using shortcodes:</p>
            <code>[dab_dashboard id="1"]</code>
            <p>Replace "1" with the ID of your dashboard.</p>

            <h3>Dashboard Layout</h3>
            <p>The dashboard builder uses a responsive grid system that allows you to:</p>
            <ul>
                <li>Drag and drop widgets to position them</li>
                <li>Resize widgets by dragging their corners</li>
                <li>Create rows and columns for better organization</li>
                <li>Automatically adjust layout for different screen sizes</li>
            </ul>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Data Management</h2>
        </div>
        <div class="dab-card-body">
            <h3>Data Dashboard</h3>
            <p>The Data Management dashboard allows you to view, edit, and manage your data across all tables. To access it:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Data Management</strong></li>
                <li>Select the table you want to manage</li>
                <li>Use the filters and search to find specific records</li>
                <li>Click on a record to view or edit it</li>
            </ol>

            <h3>Importing and Exporting Data</h3>
            <p>You can import data from CSV files and export data to CSV or Excel format:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Data Management</strong></li>
                <li>Select the table you want to import to or export from</li>
                <li>Click the <strong>Import</strong> or <strong>Export</strong> button</li>
                <li>Follow the instructions in the wizard</li>
            </ol>

            <h3>Bulk Actions</h3>
            <p>You can perform bulk actions on multiple records at once:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Data Management</strong></li>
                <li>Select the table you want to manage</li>
                <li>Check the boxes next to the records you want to modify</li>
                <li>Select an action from the Bulk Actions dropdown</li>
                <li>Click <strong>Apply</strong></li>
            </ol>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Permissions System</h2>
        </div>
        <div class="dab-card-body">
            <h3>Role-Based Permissions</h3>
            <p>Database App Builder includes a comprehensive role-based permissions system that allows you to control which users can view, edit, delete, and export records in your database tables.</p>

            <h3>Table-Level Permissions</h3>
            <p>For each WordPress user role, you can set the following permissions at the table level:</p>
            <ul>
                <li><strong>View Records</strong>: Users with this role can view records in the table</li>
                <li><strong>Edit Records</strong>: Users with this role can edit records in the table</li>
                <li><strong>Delete Records</strong>: Users with this role can delete records in the table</li>
                <li><strong>Export Records</strong>: Users with this role can export records from the table</li>
            </ul>

            <h3>Field-Level Permissions</h3>
            <p>For each WordPress user role, you can set the following permissions at the field level:</p>
            <ul>
                <li><strong>View Field</strong>: Users with this role can view this field in records</li>
                <li><strong>Edit Field</strong>: Users with this role can edit this field in records</li>
            </ul>

            <h3>Record Ownership</h3>
            <p>Users can always view, edit, and delete records they created, regardless of their role permissions. This ensures that users can manage their own data even if they don't have general permissions for the table.</p>

            <h3>Managing Permissions</h3>
            <p>To manage permissions:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Permissions Dashboard</strong></li>
                <li>Select the tab for the type of permissions you want to manage (Tables or Fields)</li>
                <li>Configure permissions for each user role</li>
                <li>Click <strong>Save Permissions</strong></li>
            </ol>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Approval Workflows</h2>
        </div>
        <div class="dab-card-body">
            <h3>Setting Up Approval Workflows</h3>
            <p>Approval workflows allow you to create a multi-step approval process for data submissions. To set up an approval workflow:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Approval Workflows</strong></li>
                <li>Select the table you want to add a workflow to</li>
                <li>Click <strong>Add Workflow</strong></li>
                <li>Configure the workflow settings</li>
                <li>Add approval levels and assign approvers</li>
                <li>Click <strong>Save Workflow</strong></li>
            </ol>

            <h3>Approval Levels</h3>
            <p>Each approval workflow can have multiple levels, with each level requiring approval before the record moves to the next level. For each level, you can specify:</p>
            <ul>
                <li><strong>Level Name</strong>: A descriptive name for the approval level</li>
                <li><strong>Approver Roles</strong>: Which user roles can approve at this level</li>
                <li><strong>Specific Approvers</strong>: Individual users who can approve at this level</li>
                <li><strong>Email Notifications</strong>: Send emails when a record reaches this level</li>
            </ul>

            <h3>Managing Approvals</h3>
            <p>To manage pending approvals:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Pending Approvals</strong></li>
                <li>Select the table you want to view approvals for</li>
                <li>Review the pending records</li>
                <li>Click <strong>Approve</strong> or <strong>Reject</strong> for each record</li>
                <li>Add notes explaining your decision</li>
            </ol>

            <h3>Approval History</h3>
            <p>The plugin maintains a complete history of all approval actions, including:</p>
            <ul>
                <li>Who approved or rejected each record</li>
                <li>When the action was taken</li>
                <li>Notes explaining the decision</li>
                <li>Which level the approval or rejection occurred at</li>
            </ul>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Payment Integration</h2>
        </div>
        <div class="dab-card-body">
            <h3>Available Payment Gateways</h3>
            <p>Database App Builder supports the following payment gateways:</p>
            <ul>
                <li><strong>Stripe</strong>: Accept credit card payments directly on your forms</li>
                <li><strong>PayPal</strong>: Accept payments through PayPal accounts and credit cards</li>
                <li><strong>Paystack</strong>: Accept payments through various methods including cards, bank transfers, and mobile money</li>
            </ul>

            <h3>Setting Up Payment Gateways</h3>
            <p>To configure payment gateways:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Payment Settings</strong></li>
                <li>Enter your API keys for the payment gateways you want to use</li>
                <li>Configure general settings like currency and payment success page</li>
                <li>Click <strong>Save Settings</strong></li>
            </ol>

            <h3>Adding Payment Fields to Forms</h3>
            <p>To add a payment field to a form:</p>
            <ol>
                <li>Edit your form</li>
                <li>Add a new field and select the "Payment" field type</li>
                <li>Configure the payment field settings (gateway, amount, currency, etc.)</li>
                <li>Save your form</li>
            </ol>

            <h3>Tracking Payments</h3>
            <p>To track payments:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Payment Tracking</strong></li>
                <li>View all payment transactions</li>
                <li>Filter by status, date, or payment gateway</li>
                <li>Click on a transaction to view details</li>
            </ol>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Integrations</h2>
        </div>
        <div class="dab-card-body">
            <h3>Available Integrations</h3>
            <p>Database App Builder can integrate with various external services:</p>
            <ul>
                <li><strong>Google Sheets</strong>: Sync form submissions with Google Sheets</li>
                <li><strong>Zapier</strong>: Connect with thousands of apps through Zapier</li>
                <li><strong>Email Marketing</strong>: Add form submissions to your email marketing lists</li>
                <li><strong>CRM Systems</strong>: Send form data to your CRM</li>
                <li><strong>Project Management</strong>: Create tasks in project management tools</li>
            </ul>

            <h3>Setting Up Integrations</h3>
            <p>To set up integrations:</p>
            <ol>
                <li>Go to <strong>Database App Builder > Integrations</strong></li>
                <li>Select the integration you want to configure</li>
                <li>Enter your API keys or connection details</li>
                <li>Map your form fields to the integration fields</li>
                <li>Save your integration settings</li>
            </ol>

            <h3>Example Use Cases</h3>
            <ul>
                <li>Add form submissions to a Google Sheet</li>
                <li>Create tasks in Trello, Asana, or other project management tools</li>
                <li>Add contacts to your CRM or email marketing platform</li>
                <li>Send notifications to Slack or other messaging apps</li>
                <li>Create calendar events from form submissions</li>
            </ul>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Shortcodes Reference</h2>
        </div>
        <div class="dab-card-body">
            <h3>Form Shortcode</h3>
            <p><code>[dab_form id="1"]</code></p>
            <p>Displays a form with the specified ID.</p>

            <h3>View Shortcode</h3>
            <p><code>[dab_view id="1"]</code></p>
            <p>Displays a view with the specified ID.</p>

            <h3>Dashboard Shortcode</h3>
            <p><code>[dab_dashboard id="1"]</code></p>
            <p>Displays a dashboard with the specified ID.</p>

            <h3>Approval Dashboard Shortcode</h3>
            <p><code>[dab_approval_dashboard]</code></p>
            <p>Displays an approval dashboard for the current user.</p>
        </div>
    </div>
</div>
