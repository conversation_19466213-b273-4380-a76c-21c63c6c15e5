# Chat System Integration Guide

This guide covers the comprehensive chat system that has been integrated into the DB App Builder plugin, providing real-time messaging capabilities for frontend users.

## Overview

The chat system includes:
- **Real-time messaging** between users
- **Group chat functionality** with admin-managed groups
- **User status tracking** (online/offline)
- **Message management** (send, delete, search)
- **Responsive design** that works on all devices
- **Admin interface** for group management
- **Integration** with the existing frontend user system

## Features

### Core Messaging
- Direct messages between users
- Group messaging with multiple participants
- Real-time message delivery and updates
- Message history and pagination
- Typing indicators
- Message deletion (own messages only)
- Search functionality for users and groups

### Group Management
- Public and private groups
- Admin-controlled group creation
- Member management (add/remove users)
- Role-based permissions (admin/member)
- Group information and settings
- Maximum member limits

### User Experience
- Modern, responsive chat interface
- Real-time status updates
- Conversation list with unread counts
- Message timestamps and read status
- File attachment support (planned)
- Emoji reactions (planned)

## Database Structure

The chat system creates the following tables:

### `wp_dab_chat_messages`
Stores all chat messages with metadata:
- `id` - Unique message ID
- `sender_id` - User who sent the message
- `recipient_id` - Direct message recipient (NULL for group messages)
- `group_id` - Group ID for group messages (NULL for direct messages)
- `message` - Message content
- `message_type` - Type of message (text, image, file, etc.)
- `attachment_url` - URL for file attachments
- `is_read` - Read status
- `is_deleted` - Soft delete flag
- `reply_to_id` - ID of message being replied to
- `created_at` - Timestamp

### `wp_dab_chat_conversations`
Manages direct message conversations:
- `id` - Conversation ID
- `user1_id` - First participant
- `user2_id` - Second participant
- `last_message_id` - Reference to latest message
- `last_activity` - Last activity timestamp
- `user1_deleted`/`user2_deleted` - Deletion flags

### `wp_dab_chat_groups`
Stores chat group information:
- `id` - Group ID
- `name` - Group name
- `description` - Group description
- `group_type` - public/private
- `created_by` - Creator user ID
- `max_members` - Maximum allowed members
- `is_active` - Active status

### `wp_dab_chat_group_members`
Manages group memberships:
- `id` - Membership ID
- `group_id` - Group reference
- `user_id` - Member user ID
- `role` - Member role (admin/member)
- `joined_at` - Join timestamp
- `last_read_message_id` - Last read message for unread counts

### `wp_dab_chat_user_status`
Tracks user online status:
- `user_id` - User reference
- `status` - online/offline/away
- `status_message` - Custom status message
- `last_seen` - Last activity timestamp

## Setup Instructions

### 1. Automatic Installation
The chat system is automatically installed when you activate the enhanced plugin. The installer will:
- Create all necessary database tables
- Set up the chat page with the `[dab_chat]` shortcode
- Configure default settings

### 2. Manual Page Creation
If you prefer to create the chat page manually:

```
Page Title: Chat
Page Slug: chat
Content: [dab_chat]
```

### 3. Shortcode Options
The chat shortcode supports several customization options:

```
[dab_chat height="600px" show_groups="true" show_users="true" default_view="conversations"]
```

**Parameters:**
- `height` - Chat container height (default: 600px)
- `show_groups` - Enable group chat functionality (default: true)
- `show_users` - Enable user search and direct messaging (default: true)
- `default_view` - Initial view: conversations, groups, or users (default: conversations)

## Admin Management

### Accessing Chat Groups Management
1. Go to **WordPress Admin → DB App Builder → Chat Groups**
2. View all existing groups and their statistics
3. Create new groups or manage existing ones

### Creating Groups
1. Click "Add New Group" button
2. Fill in group details:
   - **Name** - Group display name (required)
   - **Description** - Optional group description
   - **Type** - Public (anyone can join) or Private (invite-only)
   - **Max Members** - Maximum number of members (default: 100)
3. Click "Save Group"

### Managing Group Members
1. Click "Manage" next to any group in the list
2. View current members and their roles
3. Add new members by selecting from the user dropdown
4. Remove members or change their roles
5. Assign admin roles to trusted members

### Group Permissions
- **Group Admins** can:
  - Add and remove members
  - Edit group settings
  - Delete the group
  - Manage member roles

- **Group Members** can:
  - Send and receive messages
  - View group information
  - Leave the group

## Frontend User Experience

### Chat Interface Layout
The chat interface consists of three main areas:

1. **Sidebar** (left)
   - User profile and status
   - Navigation tabs (Chats, Groups, Users)
   - Search functionality
   - Conversation/group list

2. **Main Chat Area** (center)
   - Welcome screen when no chat is selected
   - Active conversation with message history
   - Message input area with send button

3. **Modals** (overlay)
   - New chat creation
   - Group creation
   - Settings and preferences

### Starting Conversations
**Direct Messages:**
1. Click the "+" button or go to Users tab
2. Search for a user by name or username
3. Click on a user to start chatting

**Group Chats:**
1. Go to Groups tab
2. Browse available public groups
3. Click "Join" to join a group
4. Or create a new group if enabled

### Sending Messages
1. Select a conversation or group
2. Type your message in the input area
3. Press Enter or click the send button
4. Messages appear instantly in the chat

### Message Features
- **Real-time delivery** - Messages appear immediately
- **Read receipts** - See when messages are read
- **Timestamps** - Hover over messages to see exact time
- **Delete messages** - Delete your own messages
- **Search** - Find specific conversations or users

## Technical Implementation

### Real-time Updates
The chat system uses AJAX polling to provide real-time functionality:
- Messages are checked every 3 seconds
- User status is updated every 30 seconds
- Typing indicators timeout after 2 seconds
- Conversations list refreshes automatically

### Security Features
- **CSRF Protection** - All requests use WordPress nonces
- **User Authentication** - Only logged-in users can access chat
- **Message Validation** - All input is sanitized and validated
- **Permission Checks** - Users can only access authorized groups
- **Soft Deletes** - Messages are marked as deleted, not permanently removed

### Performance Optimizations
- **Pagination** - Messages are loaded in batches of 50
- **Efficient Queries** - Optimized database queries with proper indexing
- **Caching** - User status and conversation data is cached
- **Lazy Loading** - Content loads only when needed

## Customization Options

### Styling
The chat system uses CSS classes that can be customized:

```css
.dab-chat-container { /* Main chat container */ }
.dab-chat-sidebar { /* Left sidebar */ }
.dab-chat-main { /* Main chat area */ }
.dab-chat-message { /* Individual messages */ }
.dab-chat-message.own { /* User's own messages */ }
```

### JavaScript Events
Listen to custom events for advanced integrations:

```javascript
// New message received
document.addEventListener('dab_message_received', function(e) {
    console.log('New message:', e.detail);
});

// User status changed
document.addEventListener('dab_user_status_changed', function(e) {
    console.log('Status changed:', e.detail);
});
```

### Hooks and Filters
WordPress hooks for extending functionality:

```php
// Filter message before sending
add_filter('dab_chat_message_before_send', function($message, $user_id) {
    // Modify message content
    return $message;
}, 10, 2);

// Action after message sent
add_action('dab_chat_message_sent', function($message_id, $user_id) {
    // Perform additional actions
}, 10, 2);
```

## Mobile Responsiveness

The chat system is fully responsive and optimized for mobile devices:
- **Touch-friendly interface** with appropriate button sizes
- **Responsive layout** that adapts to screen size
- **Mobile navigation** with collapsible sidebar
- **Optimized performance** for mobile networks
- **Gesture support** for common actions

## Troubleshooting

### Common Issues

1. **Chat not loading**
   - Verify the user is logged in
   - Check that the chat page exists with the correct shortcode
   - Ensure JavaScript is enabled

2. **Messages not appearing**
   - Check browser console for JavaScript errors
   - Verify AJAX requests are working
   - Check user permissions

3. **Groups not showing**
   - Ensure groups are created and active
   - Check user membership status
   - Verify group permissions

4. **Performance issues**
   - Check database table indexes
   - Monitor AJAX request frequency
   - Consider caching optimizations

### Debug Mode
Enable WordPress debug mode for detailed error information:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Future Enhancements

Planned features for future releases:
- **File attachments** - Share images and documents
- **Voice messages** - Record and send audio messages
- **Video calls** - Integrated video calling
- **Message reactions** - Emoji reactions to messages
- **Message threading** - Reply to specific messages
- **Push notifications** - Browser notifications for new messages
- **Message encryption** - End-to-end encryption for privacy
- **Chat bots** - Automated responses and assistance

## Support and Documentation

For additional support:
1. Check the plugin documentation
2. Review the troubleshooting section
3. Contact the development team
4. Submit feature requests or bug reports

The chat system is designed to be extensible and customizable to meet your specific needs while providing a solid foundation for real-time communication.
