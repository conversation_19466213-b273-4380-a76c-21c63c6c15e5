<?php
/**
 * Enhanced Formula Engine
 * 
 * Advanced formula calculation engine with support for:
 * - Mathematical functions (SUM, AVERAGE, MIN, MAX, etc.)
 * - Date/time functions
 * - Text manipulation functions
 * - Cross-table calculations
 * - Conditional logic
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Enhanced_Formula_Engine {
    
    /**
     * Initialize the enhanced formula engine
     */
    public static function init() {
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        
        // AJAX handlers for formula validation and calculation
        add_action('wp_ajax_dab_validate_formula', array(__CLASS__, 'ajax_validate_formula'));
        add_action('wp_ajax_dab_calculate_formula', array(__CLASS__, 'ajax_calculate_formula'));
        add_action('wp_ajax_dab_get_table_fields', array(__CLASS__, 'ajax_get_table_fields'));
        
        // Hook into form processing to calculate formulas
        add_action('dab_before_save_form_data', array(__CLASS__, 'calculate_form_formulas'), 10, 3);
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_script(
            'dab-enhanced-formula-engine',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-formula-engine.js',
            array('jquery'),
            DAB_VERSION,
            true
        );
        
        wp_localize_script('dab-enhanced-formula-engine', 'dabFormulaEngine', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_formula_nonce'),
            'functions' => self::get_available_functions()
        ));
    }
    
    /**
     * Get available formula functions
     */
    public static function get_available_functions() {
        return array(
            // Mathematical Functions
            'SUM' => array(
                'description' => 'Sum of values',
                'syntax' => 'SUM(value1, value2, ...)',
                'example' => 'SUM({field1}, {field2}, {field3})'
            ),
            'AVERAGE' => array(
                'description' => 'Average of values',
                'syntax' => 'AVERAGE(value1, value2, ...)',
                'example' => 'AVERAGE({field1}, {field2})'
            ),
            'MIN' => array(
                'description' => 'Minimum value',
                'syntax' => 'MIN(value1, value2, ...)',
                'example' => 'MIN({field1}, {field2})'
            ),
            'MAX' => array(
                'description' => 'Maximum value',
                'syntax' => 'MAX(value1, value2, ...)',
                'example' => 'MAX({field1}, {field2})'
            ),
            'COUNT' => array(
                'description' => 'Count non-empty values',
                'syntax' => 'COUNT(value1, value2, ...)',
                'example' => 'COUNT({field1}, {field2})'
            ),
            'ROUND' => array(
                'description' => 'Round to specified decimal places',
                'syntax' => 'ROUND(value, decimals)',
                'example' => 'ROUND({field1}, 2)'
            ),
            'ABS' => array(
                'description' => 'Absolute value',
                'syntax' => 'ABS(value)',
                'example' => 'ABS({field1})'
            ),
            'POWER' => array(
                'description' => 'Raise to power',
                'syntax' => 'POWER(base, exponent)',
                'example' => 'POWER({field1}, 2)'
            ),
            'SQRT' => array(
                'description' => 'Square root',
                'syntax' => 'SQRT(value)',
                'example' => 'SQRT({field1})'
            ),
            
            // Date/Time Functions
            'NOW' => array(
                'description' => 'Current date and time',
                'syntax' => 'NOW()',
                'example' => 'NOW()'
            ),
            'TODAY' => array(
                'description' => 'Current date',
                'syntax' => 'TODAY()',
                'example' => 'TODAY()'
            ),
            'YEAR' => array(
                'description' => 'Extract year from date',
                'syntax' => 'YEAR(date)',
                'example' => 'YEAR({date_field})'
            ),
            'MONTH' => array(
                'description' => 'Extract month from date',
                'syntax' => 'MONTH(date)',
                'example' => 'MONTH({date_field})'
            ),
            'DAY' => array(
                'description' => 'Extract day from date',
                'syntax' => 'DAY(date)',
                'example' => 'DAY({date_field})'
            ),
            'DATEDIFF' => array(
                'description' => 'Difference between dates in days',
                'syntax' => 'DATEDIFF(date1, date2)',
                'example' => 'DATEDIFF({end_date}, {start_date})'
            ),
            'DATEADD' => array(
                'description' => 'Add days to date',
                'syntax' => 'DATEADD(date, days)',
                'example' => 'DATEADD({start_date}, 30)'
            ),
            
            // Text Functions
            'CONCAT' => array(
                'description' => 'Concatenate text values',
                'syntax' => 'CONCAT(text1, text2, ...)',
                'example' => 'CONCAT({first_name}, " ", {last_name})'
            ),
            'UPPER' => array(
                'description' => 'Convert to uppercase',
                'syntax' => 'UPPER(text)',
                'example' => 'UPPER({field1})'
            ),
            'LOWER' => array(
                'description' => 'Convert to lowercase',
                'syntax' => 'LOWER(text)',
                'example' => 'LOWER({field1})'
            ),
            'LEN' => array(
                'description' => 'Length of text',
                'syntax' => 'LEN(text)',
                'example' => 'LEN({field1})'
            ),
            'LEFT' => array(
                'description' => 'Left characters',
                'syntax' => 'LEFT(text, count)',
                'example' => 'LEFT({field1}, 5)'
            ),
            'RIGHT' => array(
                'description' => 'Right characters',
                'syntax' => 'RIGHT(text, count)',
                'example' => 'RIGHT({field1}, 3)'
            ),
            'MID' => array(
                'description' => 'Middle characters',
                'syntax' => 'MID(text, start, count)',
                'example' => 'MID({field1}, 2, 5)'
            ),
            
            // Conditional Functions
            'IF' => array(
                'description' => 'Conditional logic',
                'syntax' => 'IF(condition, value_if_true, value_if_false)',
                'example' => 'IF({field1} > 100, "High", "Low")'
            ),
            'ISNULL' => array(
                'description' => 'Check if value is null/empty',
                'syntax' => 'ISNULL(value)',
                'example' => 'ISNULL({field1})'
            ),
            'ISBLANK' => array(
                'description' => 'Check if value is blank',
                'syntax' => 'ISBLANK(value)',
                'example' => 'ISBLANK({field1})'
            ),
            
            // Cross-table Functions
            'LOOKUP' => array(
                'description' => 'Lookup value from another table',
                'syntax' => 'LOOKUP(table_slug, lookup_field, return_field, criteria)',
                'example' => 'LOOKUP("products", "id", "price", {product_id})'
            ),
            'SUMIF' => array(
                'description' => 'Sum values based on criteria',
                'syntax' => 'SUMIF(table_slug, criteria_field, criteria_value, sum_field)',
                'example' => 'SUMIF("orders", "customer_id", {customer_id}, "amount")'
            ),
            'COUNTIF' => array(
                'description' => 'Count records based on criteria',
                'syntax' => 'COUNTIF(table_slug, criteria_field, criteria_value)',
                'example' => 'COUNTIF("orders", "status", "completed")'
            )
        );
    }
    
    /**
     * Validate formula syntax
     */
    public static function validate_formula($formula) {
        $errors = array();
        
        try {
            // Check for balanced parentheses
            if (!self::check_balanced_parentheses($formula)) {
                $errors[] = 'Unbalanced parentheses in formula';
            }
            
            // Check for valid function syntax
            $functions = self::extract_functions($formula);
            foreach ($functions as $function) {
                if (!self::is_valid_function($function)) {
                    $errors[] = "Invalid function: {$function['name']}";
                }
            }
            
            // Check for valid field references
            $field_refs = self::extract_field_references($formula);
            foreach ($field_refs as $field_ref) {
                if (!self::is_valid_field_reference($field_ref)) {
                    $errors[] = "Invalid field reference: {$field_ref}";
                }
            }
            
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
        
        return $errors;
    }
    
    /**
     * Calculate formula value
     */
    public static function calculate_formula($formula, $context = array()) {
        try {
            // Replace field references with values
            $processed_formula = self::replace_field_references($formula, $context);
            
            // Process functions
            $processed_formula = self::process_functions($processed_formula, $context);
            
            // Evaluate the final expression
            return self::safe_evaluate($processed_formula);
            
        } catch (Exception $e) {
            throw new Exception('Formula calculation error: ' . $e->getMessage());
        }
    }
    
    /**
     * Replace field references with actual values
     */
    private static function replace_field_references($formula, $context) {
        return preg_replace_callback('/\{([^}]+)\}/', function($matches) use ($context) {
            $field_name = $matches[1];
            
            if (isset($context['fields'][$field_name])) {
                $value = $context['fields'][$field_name];
                
                // Handle different value types
                if (is_numeric($value)) {
                    return $value;
                } elseif (is_string($value)) {
                    return '"' . addslashes($value) . '"';
                } else {
                    return '""';
                }
            }
            
            return '0'; // Default to 0 for missing fields
        }, $formula);
    }
    
    /**
     * Process formula functions
     */
    private static function process_functions($formula, $context) {
        // Process functions in order of complexity (innermost first)
        while (preg_match('/([A-Z_]+)\s*\([^()]*\)/', $formula)) {
            $formula = preg_replace_callback('/([A-Z_]+)\s*\(([^()]*)\)/', function($matches) use ($context) {
                $function_name = $matches[1];
                $args_string = $matches[2];
                
                return self::execute_function($function_name, $args_string, $context);
            }, $formula);
        }
        
        return $formula;
    }
    
    /**
     * Execute a specific function
     */
    private static function execute_function($function_name, $args_string, $context) {
        $args = self::parse_function_arguments($args_string);
        
        switch ($function_name) {
            case 'SUM':
                return array_sum(array_map('floatval', $args));
                
            case 'AVERAGE':
                $numeric_args = array_map('floatval', $args);
                return count($numeric_args) > 0 ? array_sum($numeric_args) / count($numeric_args) : 0;
                
            case 'MIN':
                return min(array_map('floatval', $args));
                
            case 'MAX':
                return max(array_map('floatval', $args));
                
            case 'COUNT':
                return count(array_filter($args, function($arg) {
                    return !empty(trim($arg, '"'));
                }));
                
            case 'ROUND':
                $value = floatval($args[0]);
                $decimals = isset($args[1]) ? intval($args[1]) : 0;
                return round($value, $decimals);
                
            case 'ABS':
                return abs(floatval($args[0]));
                
            case 'POWER':
                return pow(floatval($args[0]), floatval($args[1]));
                
            case 'SQRT':
                return sqrt(floatval($args[0]));
                
            case 'NOW':
                return '"' . current_time('Y-m-d H:i:s') . '"';
                
            case 'TODAY':
                return '"' . current_time('Y-m-d') . '"';
                
            case 'YEAR':
                $date = trim($args[0], '"');
                return date('Y', strtotime($date));
                
            case 'MONTH':
                $date = trim($args[0], '"');
                return date('n', strtotime($date));
                
            case 'DAY':
                $date = trim($args[0], '"');
                return date('j', strtotime($date));
                
            case 'DATEDIFF':
                $date1 = trim($args[0], '"');
                $date2 = trim($args[1], '"');
                $diff = strtotime($date1) - strtotime($date2);
                return floor($diff / (60 * 60 * 24));
                
            case 'DATEADD':
                $date = trim($args[0], '"');
                $days = intval($args[1]);
                return '"' . date('Y-m-d', strtotime($date . ' + ' . $days . ' days')) . '"';
                
            case 'CONCAT':
                $result = '';
                foreach ($args as $arg) {
                    $result .= trim($arg, '"');
                }
                return '"' . $result . '"';
                
            case 'UPPER':
                return '"' . strtoupper(trim($args[0], '"')) . '"';
                
            case 'LOWER':
                return '"' . strtolower(trim($args[0], '"')) . '"';
                
            case 'LEN':
                return strlen(trim($args[0], '"'));
                
            case 'LEFT':
                $text = trim($args[0], '"');
                $count = intval($args[1]);
                return '"' . substr($text, 0, $count) . '"';
                
            case 'RIGHT':
                $text = trim($args[0], '"');
                $count = intval($args[1]);
                return '"' . substr($text, -$count) . '"';
                
            case 'MID':
                $text = trim($args[0], '"');
                $start = intval($args[1]) - 1; // Convert to 0-based index
                $count = intval($args[2]);
                return '"' . substr($text, $start, $count) . '"';
                
            case 'IF':
                $condition = self::safe_evaluate($args[0]);
                return $condition ? $args[1] : $args[2];
                
            case 'ISNULL':
                $value = trim($args[0], '"');
                return empty($value) ? 1 : 0;
                
            case 'ISBLANK':
                $value = trim($args[0], '"');
                return ($value === '' || $value === null) ? 1 : 0;
                
            case 'LOOKUP':
                return self::execute_lookup_function($args, $context);
                
            case 'SUMIF':
                return self::execute_sumif_function($args, $context);
                
            case 'COUNTIF':
                return self::execute_countif_function($args, $context);
                
            default:
                throw new Exception("Unknown function: {$function_name}");
        }
    }
    
    /**
     * Parse function arguments
     */
    private static function parse_function_arguments($args_string) {
        $args = array();
        $current_arg = '';
        $quote_count = 0;
        $paren_count = 0;
        
        for ($i = 0; $i < strlen($args_string); $i++) {
            $char = $args_string[$i];
            
            if ($char === '"') {
                $quote_count++;
            } elseif ($char === '(') {
                $paren_count++;
            } elseif ($char === ')') {
                $paren_count--;
            } elseif ($char === ',' && $quote_count % 2 === 0 && $paren_count === 0) {
                $args[] = trim($current_arg);
                $current_arg = '';
                continue;
            }
            
            $current_arg .= $char;
        }
        
        if (!empty($current_arg)) {
            $args[] = trim($current_arg);
        }
        
        return $args;
    }
    
    /**
     * Safe evaluation of mathematical expressions
     */
    private static function safe_evaluate($expression) {
        // Remove any potentially dangerous functions
        $expression = preg_replace('/[^0-9+\-*\/\(\)\.\s]/', '', $expression);
        
        // Use eval only for simple mathematical expressions
        if (preg_match('/^[0-9+\-*\/\(\)\.\s]+$/', $expression)) {
            return eval("return {$expression};");
        }
        
        throw new Exception('Invalid expression for evaluation');
    }
    
    /**
     * Check if parentheses are balanced
     */
    private static function check_balanced_parentheses($formula) {
        $count = 0;
        for ($i = 0; $i < strlen($formula); $i++) {
            if ($formula[$i] === '(') {
                $count++;
            } elseif ($formula[$i] === ')') {
                $count--;
                if ($count < 0) {
                    return false;
                }
            }
        }
        return $count === 0;
    }
    
    /**
     * Extract functions from formula
     */
    private static function extract_functions($formula) {
        $functions = array();
        preg_match_all('/([A-Z_]+)\s*\(/', $formula, $matches);
        
        foreach ($matches[1] as $function_name) {
            $functions[] = array('name' => $function_name);
        }
        
        return $functions;
    }
    
    /**
     * Extract field references from formula
     */
    private static function extract_field_references($formula) {
        preg_match_all('/\{([^}]+)\}/', $formula, $matches);
        return $matches[1];
    }
    
    /**
     * Check if function is valid
     */
    private static function is_valid_function($function) {
        $available_functions = array_keys(self::get_available_functions());
        return in_array($function['name'], $available_functions);
    }
    
    /**
     * Check if field reference is valid
     */
    private static function is_valid_field_reference($field_ref) {
        // Basic validation - field name should not be empty and contain valid characters
        return !empty($field_ref) && preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $field_ref);
    }
    
    /**
     * AJAX handler for formula validation
     */
    public static function ajax_validate_formula() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_formula_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $formula = sanitize_text_field($_POST['formula']);
        $errors = self::validate_formula($formula);
        
        if (empty($errors)) {
            wp_send_json_success('Formula is valid');
        } else {
            wp_send_json_error($errors);
        }
    }
    
    /**
     * AJAX handler for formula calculation
     */
    public static function ajax_calculate_formula() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_formula_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $formula = sanitize_text_field($_POST['formula']);
        $context = isset($_POST['context']) ? $_POST['context'] : array();
        
        try {
            $result = self::calculate_formula($formula, $context);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * AJAX handler for getting table fields
     */
    public static function ajax_get_table_fields() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_formula_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $table_id = intval($_POST['table_id']);
        
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT field_slug, field_label, field_type FROM {$fields_table} WHERE table_id = %d ORDER BY field_order",
            $table_id
        ));
        
        wp_send_json_success($fields);
    }
    
    /**
     * Calculate formulas before saving form data
     */
    public static function calculate_form_formulas($form_id, $data, $table_id) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';
        
        // Get formula fields for this table
        $formula_fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$fields_table} WHERE table_id = %d AND field_type = 'formula'",
            $table_id
        ));
        
        foreach ($formula_fields as $field) {
            if (!empty($field->formula_expression)) {
                try {
                    $context = array('fields' => $data);
                    $result = self::calculate_formula($field->formula_expression, $context);
                    $data[$field->field_slug] = $result;
                } catch (Exception $e) {
                    // Log error but don't break the form submission
                    error_log('Formula calculation error for field ' . $field->field_slug . ': ' . $e->getMessage());
                    $data[$field->field_slug] = '';
                }
            }
        }
        
        return $data;
    }
    
    /**
     * Execute LOOKUP function
     */
    private static function execute_lookup_function($args, $context) {
        global $wpdb;
        
        $table_slug = trim($args[0], '"');
        $lookup_field = trim($args[1], '"');
        $return_field = trim($args[2], '"');
        $criteria = trim($args[3], '"');
        
        $table_name = $wpdb->prefix . 'dab_' . $table_slug;
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT `{$return_field}` FROM `{$table_name}` WHERE `{$lookup_field}` = %s LIMIT 1",
            $criteria
        ));
        
        return $result ? '"' . $result . '"' : '""';
    }
    
    /**
     * Execute SUMIF function
     */
    private static function execute_sumif_function($args, $context) {
        global $wpdb;
        
        $table_slug = trim($args[0], '"');
        $criteria_field = trim($args[1], '"');
        $criteria_value = trim($args[2], '"');
        $sum_field = trim($args[3], '"');
        
        $table_name = $wpdb->prefix . 'dab_' . $table_slug;
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(`{$sum_field}`) FROM `{$table_name}` WHERE `{$criteria_field}` = %s",
            $criteria_value
        ));
        
        return floatval($result);
    }
    
    /**
     * Execute COUNTIF function
     */
    private static function execute_countif_function($args, $context) {
        global $wpdb;
        
        $table_slug = trim($args[0], '"');
        $criteria_field = trim($args[1], '"');
        $criteria_value = trim($args[2], '"');
        
        $table_name = $wpdb->prefix . 'dab_' . $table_slug;
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM `{$table_name}` WHERE `{$criteria_field}` = %s",
            $criteria_value
        ));
        
        return intval($result);
    }
}
