/**
 * Dropdown Options Builder
 * 
 * Provides a user-friendly interface for creating and managing dropdown options
 * with support for key-value pairs and drag-and-drop reordering.
 */
jQuery(document).ready(function($) {
    // Initialize the dropdown builder
    function initDropdownBuilder() {
        // Only initialize if the options row exists
        if ($('#options_row').length === 0) {
            return;
        }

        // Create the builder container
        const $optionsTextarea = $('#options');
        const $builderContainer = $('<div class="dab-dropdown-builder"></div>');
        
        // Hide the original textarea
        $optionsTextarea.hide();
        
        // Add the builder after the textarea
        $optionsTextarea.after($builderContainer);
        
        // Create the options list
        const $optionsList = $('<div class="dab-dropdown-options-list"></div>');
        $builderContainer.append($optionsList);
        
        // Create the form for adding new options
        const $addOptionForm = $(
            '<div class="dab-dropdown-option-form">' +
                '<div class="dab-dropdown-option-form-group">' +
                    '<label for="dab-dropdown-option-label">Option Label</label>' +
                    '<input type="text" id="dab-dropdown-option-label" placeholder="Display text for the option">' +
                '</div>' +
                '<div class="dab-dropdown-option-form-group">' +
                    '<label for="dab-dropdown-option-value">Option Value (optional)</label>' +
                    '<input type="text" id="dab-dropdown-option-value" placeholder="Value stored in database">' +
                '</div>' +
                '<div class="dab-dropdown-option-form-actions">' +
                    '<button type="button" class="dab-dropdown-option-form-save">Add Option</button>' +
                    '<button type="button" class="dab-dropdown-option-form-cancel">Cancel</button>' +
                '</div>' +
            '</div>'
        );
        
        // Add the form to the builder
        $builderContainer.append($addOptionForm);
        
        // Add a button to add new options
        const $addButton = $(
            '<button type="button" class="button button-primary dab-dropdown-add-button">' +
                '<span class="dashicons dashicons-plus"></span> Add Option' +
            '</button>'
        );
        $builderContainer.append($addButton);
        
        // Hide the form initially
        $addOptionForm.hide();
        
        // Parse existing options from the textarea
        function parseOptions() {
            const options = [];
            const lines = $optionsTextarea.val().split('\n');
            
            lines.forEach(line => {
                line = line.trim();
                if (line) {
                    const parts = line.split(':');
                    if (parts.length > 1) {
                        // Key-value pair
                        options.push({
                            label: parts[0].trim(),
                            value: parts.slice(1).join(':').trim()
                        });
                    } else {
                        // Simple option
                        options.push({
                            label: line,
                            value: line
                        });
                    }
                }
            });
            
            return options;
        }
        
        // Render the options list
        function renderOptions() {
            const options = parseOptions();
            $optionsList.empty();
            
            if (options.length === 0) {
                $optionsList.append(
                    '<div class="dab-dropdown-options-empty">' +
                        'No options added yet. Click "Add Option" to create your first option.' +
                    '</div>'
                );
                return;
            }
            
            options.forEach((option, index) => {
                const $option = $(
                    '<div class="dab-dropdown-option" data-index="' + index + '">' +
                        '<div class="dab-dropdown-option-drag">' +
                            '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>' +
                        '</div>' +
                        '<div class="dab-dropdown-option-content">' +
                            '<span class="dab-dropdown-option-label">' + option.label + '</span>' +
                            (option.value !== option.label ? '<span class="dab-dropdown-option-value">' + option.value + '</span>' : '') +
                        '</div>' +
                        '<div class="dab-dropdown-option-actions">' +
                            '<button type="button" class="dab-dropdown-option-edit" title="Edit option">' +
                                '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>' +
                            '</button>' +
                            '<button type="button" class="dab-dropdown-option-delete" title="Delete option">' +
                                '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>' +
                            '</button>' +
                        '</div>' +
                    '</div>'
                );
                
                $optionsList.append($option);
            });
            
            // Make the options sortable
            $optionsList.sortable({
                handle: '.dab-dropdown-option-drag',
                update: function() {
                    updateTextarea();
                }
            });
        }
        
        // Update the textarea with the current options
        function updateTextarea() {
            const options = [];
            
            $('.dab-dropdown-option').each(function() {
                const label = $(this).find('.dab-dropdown-option-label').text();
                const valueEl = $(this).find('.dab-dropdown-option-value');
                
                if (valueEl.length && valueEl.text() !== label) {
                    options.push(label + ':' + valueEl.text());
                } else {
                    options.push(label);
                }
            });
            
            $optionsTextarea.val(options.join('\n'));
        }
        
        // Add a new option
        function addOption(label, value) {
            const options = parseOptions();
            
            options.push({
                label: label,
                value: value || label
            });
            
            updateTextareaFromOptions(options);
            renderOptions();
        }
        
        // Update textarea from options array
        function updateTextareaFromOptions(options) {
            const lines = options.map(option => {
                if (option.value !== option.label) {
                    return option.label + ':' + option.value;
                }
                return option.label;
            });
            
            $optionsTextarea.val(lines.join('\n'));
        }
        
        // Edit an option
        function editOption(index, label, value) {
            const options = parseOptions();
            
            options[index] = {
                label: label,
                value: value || label
            };
            
            updateTextareaFromOptions(options);
            renderOptions();
        }
        
        // Delete an option
        function deleteOption(index) {
            const options = parseOptions();
            
            options.splice(index, 1);
            
            updateTextareaFromOptions(options);
            renderOptions();
        }
        
        // Event handlers
        $addButton.on('click', function() {
            // Show the add form
            $addOptionForm.show();
            $addButton.hide();
            $('#dab-dropdown-option-label').focus();
        });
        
        $addOptionForm.on('click', '.dab-dropdown-option-form-save', function() {
            const label = $('#dab-dropdown-option-label').val().trim();
            const value = $('#dab-dropdown-option-value').val().trim();
            
            if (label) {
                addOption(label, value);
                
                // Clear the form
                $('#dab-dropdown-option-label').val('');
                $('#dab-dropdown-option-value').val('');
                
                // Hide the form
                $addOptionForm.hide();
                $addButton.show();
            }
        });
        
        $addOptionForm.on('click', '.dab-dropdown-option-form-cancel', function() {
            // Clear the form
            $('#dab-dropdown-option-label').val('');
            $('#dab-dropdown-option-value').val('');
            
            // Hide the form
            $addOptionForm.hide();
            $addButton.show();
        });
        
        $optionsList.on('click', '.dab-dropdown-option-edit', function() {
            const $option = $(this).closest('.dab-dropdown-option');
            const index = $option.data('index');
            const label = $option.find('.dab-dropdown-option-label').text();
            const value = $option.find('.dab-dropdown-option-value').length ? 
                $option.find('.dab-dropdown-option-value').text() : label;
            
            // Fill the form
            $('#dab-dropdown-option-label').val(label);
            $('#dab-dropdown-option-value').val(value);
            
            // Show the form
            $addOptionForm.show();
            $addButton.hide();
            
            // Change the save button text
            $('.dab-dropdown-option-form-save').text('Update Option');
            
            // Add a data attribute to the form
            $addOptionForm.data('edit-index', index);
            
            $('#dab-dropdown-option-label').focus();
        });
        
        $optionsList.on('click', '.dab-dropdown-option-delete', function() {
            const $option = $(this).closest('.dab-dropdown-option');
            const index = $option.data('index');
            
            if (confirm('Are you sure you want to delete this option?')) {
                deleteOption(index);
            }
        });
        
        // Initialize the builder
        renderOptions();
    }
    
    // Initialize when the field type is changed
    $('#field_type').on('change', function() {
        if ($(this).val() === 'select' || $(this).val() === 'radio' || $(this).val() === 'checkbox') {
            // Load the CSS
            if (!$('link[href*="dropdown-builder.css"]').length) {
                $('<link>')
                    .appendTo('head')
                    .attr({
                        type: 'text/css',
                        rel: 'stylesheet',
                        href: dab_vars.plugin_url + 'assets/css/dropdown-builder.css'
                    });
            }
            
            // Initialize the builder
            setTimeout(initDropdownBuilder, 100);
        }
    });
    
    // Initialize on page load if the field type is already set
    if ($('#field_type').val() === 'select' || $('#field_type').val() === 'radio' || $('#field_type').val() === 'checkbox') {
        // Load the CSS
        if (!$('link[href*="dropdown-builder.css"]').length) {
            $('<link>')
                .appendTo('head')
                .attr({
                    type: 'text/css',
                    rel: 'stylesheet',
                    href: dab_vars.plugin_url + 'assets/css/dropdown-builder.css'
                });
        }
        
        // Initialize the builder
        setTimeout(initDropdownBuilder, 100);
    }
});
