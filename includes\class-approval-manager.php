<?php
if (!defined('ABSPATH')) {
    exit;
}

class DAB_Approval_Manager {

    /**
     * Initialize a new record for approval workflow
     */
    public static function initialize_approval($table_id, $record_id) {
        global $wpdb;

        // Check if table has approval workflow
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $levels = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $approval_levels_table WHERE table_id = %d ORDER BY level_order ASC",
            $table_id
        ));

        if (empty($levels)) {
            // No approval workflow defined, auto-approve
            self::update_record_status($table_id, $record_id, 'Approved', 'Auto-approved (no workflow defined)');
            return;
        }

        // Set initial approval level
        $first_level = $levels[0];
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        $wpdb->update(
            $data_table,
            ['current_approval_level' => $first_level->id],
            ['id' => $record_id]
        );

        // Send notification for first level
        self::send_approval_notification($table_id, $record_id, $first_level);
    }

    /**
     * Process an approval action
     */
    public static function process_approval($table_id, $record_id, $status, $notes, $user_id) {
        global $wpdb;

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get current approval level
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) return false;

        $current_level_id = $record->current_approval_level;

        // Record this approval action
        $approval_history_table = $wpdb->prefix . 'dab_approval_history';
        $wpdb->insert(
            $approval_history_table,
            [
                'record_id' => $record_id,
                'table_id' => $table_id,
                'level_id' => $current_level_id,
                'status' => $status,
                'notes' => $notes,
                'user_id' => $user_id,
                'created_at' => current_time('mysql')
            ]
        );

        if ($status === 'Rejected') {
            // If rejected, update record status and stop workflow
            self::update_record_status($table_id, $record_id, 'Rejected', $notes);
            return true;
        }

        // Get all approval levels
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $levels = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $approval_levels_table WHERE table_id = %d ORDER BY level_order ASC",
            $table_id
        ));

        // Find next level
        $next_level = null;
        $found_current = false;

        foreach ($levels as $level) {
            if ($found_current) {
                $next_level = $level;
                break;
            }

            if ($level->id == $current_level_id) {
                $found_current = true;
            }
        }

        if ($next_level) {
            // Move to next approval level
            $wpdb->update(
                $data_table,
                ['current_approval_level' => $next_level->id],
                ['id' => $record_id]
            );

            // Send notification for next level
            self::send_approval_notification($table_id, $record_id, $next_level);
        } else {
            // Final approval reached
            self::update_record_status($table_id, $record_id, 'Approved', $notes);
        }

        return true;
    }

    /**
     * Update record status
     */
    private static function update_record_status($table_id, $record_id, $status, $notes) {
        global $wpdb;

        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get current record data
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) return false;

        // Update record status
        $result = $wpdb->update(
            $data_table,
            [
                'approval_status' => $status,
                'approval_notes' => $notes,
                'current_approval_level' => 0 // Reset level when final status is set
            ],
            ['id' => $record_id]
        );

        if ($result === false) return false;

        // Add entry to approval history if not already there
        $approval_history_table = $wpdb->prefix . 'dab_approval_history';
        $history_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $approval_history_table
            WHERE table_id = %d AND record_id = %d AND status = %s",
            $table_id, $record_id, $status
        ));

        if (!$history_exists) {
            $wpdb->insert(
                $approval_history_table,
                [
                    'record_id' => $record_id,
                    'table_id' => $table_id,
                    'level_id' => $record->current_approval_level,
                    'status' => $status,
                    'notes' => $notes . ' (Status updated)',
                    'user_id' => get_current_user_id(),
                    'created_at' => current_time('mysql')
                ]
            );
        }

        // Notify submitter of final status
        self::notify_submitter($table_id, $record_id, $status, $notes);

        return true;
    }

    /**
     * Send notification to approvers
     */
    private static function send_approval_notification($table_id, $record_id, $level) {
        // Use the enhanced email class for better deliverability
        return DAB_Enhanced_Email::send_approval_notification($table_id, $record_id, $level);
    }

    /**
     * Notify submitter of final status
     */
    private static function notify_submitter($table_id, $record_id, $status, $notes) {
        // Use the enhanced email class for better deliverability
        return DAB_Enhanced_Email::notify_submitter($table_id, $record_id, $status, $notes);
    }

    /**
     * Check if user can approve at current level
     *
     * @param int $user_id The user ID to check
     * @param int $table_id The table ID
     * @param int $record_id The record ID
     * @return bool Whether the user can approve the record
     */
    public static function can_user_approve($user_id, $table_id, $record_id) {
        global $wpdb;

        if (!$user_id) return false;

        // Get user data
        $user_data = get_userdata($user_id);
        if (!$user_data) return false;

        // Check if user is an administrator - administrators have full access to all workflows
        $is_admin = in_array('administrator', $user_data->roles);

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return false;

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get record information
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) return false;

        // Check if record is pending approval
        if ($record->approval_status !== 'Pending') return false;

        // If admin, they can approve any pending record regardless of level
        if ($is_admin) {
            return true;
        }

        // For non-admins, check if they have the required role for the current level
        if (empty($record->current_approval_level)) return false;

        // Get level details
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $level = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $approval_levels_table WHERE id = %d",
            $record->current_approval_level
        ));

        if (!$level) return false;

        // Check if user is specifically assigned to this level
        $specific_users = isset($level->specific_users) ? maybe_unserialize($level->specific_users) : [];

        if (!empty($specific_users) && in_array($user_id, $specific_users)) {
            return true;
        }

        // Check if user has any of the required roles
        $allowed_roles = maybe_unserialize($level->approver_roles);

        if (empty($allowed_roles)) {
            // If no roles are specified but specific users are, only those users can approve
            return false;
        }

        foreach ($allowed_roles as $role) {
            if (in_array($role, $user_data->roles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Approve a record
     */
    public static function approve_record($table_id, $record_id, $user_id, $notes = '') {
        return self::process_approval($table_id, $record_id, 'Approved', $notes, $user_id);
    }

    /**
     * Reject a record
     */
    public static function reject_record($table_id, $record_id, $user_id, $notes = '') {
        return self::process_approval($table_id, $record_id, 'Rejected', $notes, $user_id);
    }

    /**
     * Get approval history for a record
     */
    public static function get_approval_history($table_id, $record_id) {
        global $wpdb;

        $approval_history_table = $wpdb->prefix . 'dab_approval_history';
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';

        $history = $wpdb->get_results($wpdb->prepare(
            "SELECT h.*, l.level_name
            FROM $approval_history_table h
            LEFT JOIN $approval_levels_table l ON h.level_id = l.id
            WHERE h.table_id = %d AND h.record_id = %d
            ORDER BY h.created_at DESC",
            $table_id, $record_id
        ));

        return $history;
    }

    /**
     * Get current approval level info
     */
    public static function get_current_level_info($table_id, $record_id) {
        global $wpdb;

        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) return null;

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get current approval level
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) return null;

        // If current_approval_level is 0 or empty, check if there are any approval levels defined
        if (empty($record->current_approval_level)) {
            // Get the first approval level for this table
            $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
            $first_level = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $approval_levels_table WHERE table_id = %d ORDER BY level_order ASC LIMIT 1",
                $table_id
            ));

            if ($first_level) {
                // Update the record with the first approval level
                $wpdb->update(
                    $data_table,
                    ['current_approval_level' => $first_level->id],
                    ['id' => $record_id]
                );

                return $first_level;
            }

            return null;
        }

        // Get level details
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $level = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $approval_levels_table WHERE id = %d",
            $record->current_approval_level
        ));

        return $level;
    }



    /**
     * Register AJAX handlers
     */
    public static function register_ajax_handlers() {
        add_action('wp_ajax_dab_process_approval', array(__CLASS__, 'ajax_process_approval'));
        add_action('wp_ajax_dab_get_record_details', array(__CLASS__, 'ajax_get_record_details'));
        add_action('wp_ajax_dab_check_new_approvals', array(__CLASS__, 'ajax_check_new_approvals'));
        add_action('wp_ajax_dab_cancel_submission', array(__CLASS__, 'ajax_cancel_submission'));
    }

    /**
     * AJAX handler for processing approval actions
     */
    public static function ajax_process_approval() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_approval_nonce')) {
            wp_send_json_error('Invalid security token');
            return;
        }

        // Check required parameters
        if (!isset($_POST['dab_approve_action']) || !isset($_POST['record_id']) || !isset($_POST['table_id'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $action = sanitize_text_field($_POST['dab_approve_action']);
        $record_id = intval($_POST['record_id']);
        $table_id = intval($_POST['table_id']);
        $note = isset($_POST['approval_note']) ? sanitize_textarea_field($_POST['approval_note']) : '';
        $user_id = get_current_user_id();

        // Process the approval action
        if ($action === 'approve') {
            $result = self::approve_record($table_id, $record_id, $user_id, $note);
        } else {
            $result = self::reject_record($table_id, $record_id, $user_id, $note);
        }

        if ($result) {
            wp_send_json_success();
        } else {
            wp_send_json_error('Failed to process approval action');
        }
    }

    /**
     * AJAX handler for getting record details
     */
    public static function ajax_get_record_details() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_approval_nonce')) {
            wp_send_json_error('Invalid security token');
            return;
        }

        // Check required parameters
        if (!isset($_POST['record_id']) || !isset($_POST['table_id'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $record_id = intval($_POST['record_id']);
        $table_id = intval($_POST['table_id']);

        // Get record details
        global $wpdb;

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) {
            wp_send_json_error('Table not found');
            return;
        }

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get record
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ), ARRAY_A);

        if (!$record) {
            wp_send_json_error('Record not found');
            return;
        }

        // Get fields
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_fields WHERE table_id = %d ORDER BY field_order",
            $table_id
        ));

        // Format fields for display
        $formatted_fields = array();
        foreach ($fields as $field) {
            $value = isset($record[$field->field_slug]) ? $record[$field->field_slug] : '';

            // Format value based on field type
            if ($field->field_type === 'lookup') {
                $lookup_table_id = intval($field->lookup_table_id);
                $display_column = sanitize_text_field($field->lookup_display_column);
                if ($lookup_table_id && $display_column) {
                    $lookup_table_slug = $wpdb->get_var(
                        $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                    );
                    if ($lookup_table_slug) {
                        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                        $lookup_id = intval($value);
                        $value = $wpdb->get_var(
                            $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                        );
                    }
                }
            } elseif ($field->field_type === 'date') {
                $value = !empty($value) ? date('F j, Y', strtotime($value)) : '';
            } elseif ($field->field_type === 'datetime') {
                $value = !empty($value) ? date('F j, Y g:i a', strtotime($value)) : '';
            } elseif ($field->field_type === 'boolean' || $field->field_type === 'checkbox') {
                $value = !empty($value) ? 'Yes' : 'No';
            }

            $formatted_fields[] = array(
                'label' => $field->field_label,
                'value' => $value
            );
        }

        // Get approval history
        $history = self::get_approval_history($table_id, $record_id);
        $formatted_history = array();

        foreach ($history as $entry) {
            $user = get_userdata($entry->user_id);
            $username = $user ? $user->display_name : 'Unknown';

            $formatted_history[] = array(
                'user' => $username,
                'date' => date('F j, Y g:i a', strtotime($entry->created_at)),
                'status' => $entry->status,
                'level' => $entry->level_name ?: 'Initial Review',
                'notes' => $entry->notes
            );
        }

        // Generate HTML for the modal
        ob_start();
        ?>
        <div class="dab-record-details">
            <?php foreach ($formatted_fields as $field): ?>
            <div class="dab-record-field">
                <div class="dab-field-label"><?php echo esc_html($field['label']); ?></div>
                <div class="dab-field-value"><?php echo esc_html($field['value']); ?></div>
            </div>
            <?php endforeach; ?>

            <div class="dab-record-field">
                <div class="dab-field-label">Approval Status</div>
                <div class="dab-field-value">
                    <span class="dab-status-badge dab-status-<?php echo strtolower($record['approval_status']); ?>">
                        <?php echo esc_html($record['approval_status']); ?>
                    </span>
                </div>
            </div>

            <?php if (!empty($formatted_history)): ?>
            <div class="dab-approval-history">
                <h4>Approval History</h4>
                <ul class="dab-history-list">
                    <?php foreach ($formatted_history as $entry): ?>
                    <li class="dab-history-item">
                        <div class="dab-history-meta">
                            <span class="dab-history-user"><?php echo esc_html($entry['user']); ?></span>
                            <span class="dab-history-date"><?php echo esc_html($entry['date']); ?></span>
                        </div>
                        <div>
                            <span class="dab-history-status dab-status-<?php echo strtolower($entry['status']); ?>">
                                <?php echo esc_html($entry['status']); ?>
                            </span>
                            at level "<?php echo esc_html($entry['level']); ?>"
                        </div>
                        <?php if (!empty($entry['notes'])): ?>
                        <div class="dab-history-notes"><?php echo esc_html($entry['notes']); ?></div>
                        <?php endif; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        <?php
        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'fields' => $formatted_fields,
            'status' => $record['approval_status'],
            'history' => $formatted_history
        ));
    }

    /**
     * AJAX handler for checking new approvals
     */
    public static function ajax_check_new_approvals() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_nonce')) {
            wp_send_json_error('Invalid security token');
            return;
        }

        $last_check = isset($_POST['last_check']) ? intval($_POST['last_check']) : 0;
        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        // Get count of new approvals
        global $wpdb;

        // Get all tables with approval workflows
        $tables_with_approval = $wpdb->get_results(
            "SELECT DISTINCT t.*
            FROM {$wpdb->prefix}dab_tables t
            INNER JOIN {$wpdb->prefix}dab_approval_levels l ON t.id = l.table_id"
        );

        $new_approvals = 0;

        foreach ($tables_with_approval as $table) {
            $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

            // Get pending records updated since last check
            $pending_records = $wpdb->get_results($wpdb->prepare(
                "SELECT id FROM $data_table
                WHERE approval_status = 'Pending'
                AND updated_at > FROM_UNIXTIME(%d)
                AND current_approval_level > 0",
                $last_check
            ));

            foreach ($pending_records as $record) {
                if (self::can_user_approve($user_id, $table->id, $record->id)) {
                    $new_approvals++;
                }
            }
        }

        wp_send_json_success(array(
            'count' => $new_approvals,
            'timestamp' => time()
        ));
    }

    /**
     * AJAX handler for cancelling a submission
     */
    public static function ajax_cancel_submission() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_approval_nonce')) {
            wp_send_json_error('Invalid security token');
            return;
        }

        // Check required parameters
        if (!isset($_POST['record_id']) || !isset($_POST['table_id'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $record_id = intval($_POST['record_id']);
        $table_id = intval($_POST['table_id']);
        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        // Get record to check if user is the owner
        global $wpdb;

        // Get table info
        $table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
            $table_id
        ));

        if (!$table) {
            wp_send_json_error('Table not found');
            return;
        }

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get record
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d",
            $record_id
        ));

        if (!$record) {
            wp_send_json_error('Record not found');
            return;
        }

        // Check if user is the owner of the record
        if ($record->user_id != $user_id && !current_user_can('administrator')) {
            wp_send_json_error('You do not have permission to cancel this submission');
            return;
        }

        // Check if record is pending
        if ($record->approval_status !== 'Pending') {
            wp_send_json_error('Only pending submissions can be cancelled');
            return;
        }

        // Cancel the submission by rejecting it
        $result = self::reject_record($table_id, $record_id, $user_id, 'Submission cancelled by submitter');

        if ($result) {
            wp_send_json_success();
        } else {
            wp_send_json_error('Failed to cancel submission');
        }
    }
}
