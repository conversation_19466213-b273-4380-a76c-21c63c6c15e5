<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Field Types Tutorial - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .field-type-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .field-type-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .field-type-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .field-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .field-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }
        .field-description {
            color: #6c757d;
            margin-bottom: 15px;
        }
        .use-cases {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .use-cases h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .configuration {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .configuration h5 {
            color: #2980b9;
            margin-top: 0;
            font-weight: bold;
        }
        .example-code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step-by-step {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #856404;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #ffc107;
            color: #856404;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 Field Types Tutorial - Master Every Field Type</h1>

        <div class="step-by-step">
            <h4><span class="step-number">1</span>Adding Fields to Your Table</h4>
            <ol>
                <li>Navigate to <strong>Database App Builder → Fields</strong></li>
                <li>Select your table from the dropdown</li>
                <li>Click <strong>"Add New Field"</strong></li>
                <li>Enter field label (e.g., "Customer Name")</li>
                <li>Choose field type from the dropdown</li>
                <li>Configure field-specific options</li>
                <li>Set validation rules if needed</li>
                <li>Click <strong>"Add Field"</strong></li>
            </ol>
        </div>

        <h2>📋 Basic Field Types</h2>

        <div class="field-grid">
            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">T</div>
                    <div class="field-name">Text Field</div>
                </div>
                <div class="field-description">Single-line text input for short text data</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Names (first name, last name, company)</li>
                        <li>Titles and short descriptions</li>
                        <li>Product codes and SKUs</li>
                        <li>Phone numbers and addresses</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Max Length:</strong> Limit character count</li>
                        <li><strong>Placeholder:</strong> Hint text for users</li>
                        <li><strong>Default Value:</strong> Pre-filled value</li>
                        <li><strong>Required:</strong> Make field mandatory</li>
                        <li><strong>Validation:</strong> Custom validation rules</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Customer Name
Placeholder: Enter customer full name
Max Length: 100
Required: Yes
</div>
            </div>

            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">¶</div>
                    <div class="field-name">Textarea Field</div>
                </div>
                <div class="field-description">Multi-line text input for longer content</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Comments and feedback</li>
                        <li>Product descriptions</li>
                        <li>Notes and observations</li>
                        <li>Addresses and instructions</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Rows:</strong> Height of textarea</li>
                        <li><strong>Max Length:</strong> Character limit</li>
                        <li><strong>Rich Text:</strong> Enable formatting</li>
                        <li><strong>Placeholder:</strong> Helper text</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Project Description
Rows: 5
Max Length: 1000
Rich Text: Enabled
</div>
            </div>

            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">#</div>
                    <div class="field-name">Number Field</div>
                </div>
                <div class="field-description">Numeric input with validation and formatting</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Prices and monetary values</li>
                        <li>Quantities and inventory</li>
                        <li>Scores and ratings</li>
                        <li>Measurements and dimensions</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Min/Max Value:</strong> Set number range</li>
                        <li><strong>Decimal Places:</strong> Precision control</li>
                        <li><strong>Step:</strong> Increment value</li>
                        <li><strong>Prefix/Suffix:</strong> Currency symbols</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Product Price
Min Value: 0
Max Value: 10000
Decimal Places: 2
Prefix: $
</div>
            </div>

            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">@</div>
                    <div class="field-name">Email Field</div>
                </div>
                <div class="field-description">Email input with automatic validation</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Contact email addresses</li>
                        <li>User registration forms</li>
                        <li>Newsletter subscriptions</li>
                        <li>Support ticket systems</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Validation:</strong> Automatic email format check</li>
                        <li><strong>Unique:</strong> Prevent duplicate emails</li>
                        <li><strong>Domain Restriction:</strong> Allow specific domains</li>
                        <li><strong>Confirmation:</strong> Require email confirmation</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Email Address
Unique: Yes
Domain Restriction: @company.com
Confirmation Required: Yes
</div>
            </div>
        </div>

        <h2>📅 Date & Time Fields</h2>

        <div class="field-grid">
            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">📅</div>
                    <div class="field-name">Date Field</div>
                </div>
                <div class="field-description">Date picker with calendar interface</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Birth dates and anniversaries</li>
                        <li>Event dates and deadlines</li>
                        <li>Start and end dates</li>
                        <li>Appointment scheduling</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Date Format:</strong> Display format (MM/DD/YYYY, etc.)</li>
                        <li><strong>Min/Max Date:</strong> Restrict date range</li>
                        <li><strong>Default Date:</strong> Today, specific date, or none</li>
                        <li><strong>Disable Dates:</strong> Block weekends or holidays</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Event Date
Format: MM/DD/YYYY
Min Date: Today
Max Date: +1 year
Disable: Weekends
</div>
            </div>

            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">🕐</div>
                    <div class="field-name">Time Field</div>
                </div>
                <div class="field-description">Time picker for hour and minute selection</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Appointment times</li>
                        <li>Meeting schedules</li>
                        <li>Work hours tracking</li>
                        <li>Event timing</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Format:</strong> 12-hour or 24-hour</li>
                        <li><strong>Step:</strong> Minute intervals (15, 30, 60)</li>
                        <li><strong>Min/Max Time:</strong> Business hours restriction</li>
                        <li><strong>Default Time:</strong> Current time or specific</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Appointment Time
Format: 12-hour (AM/PM)
Step: 15 minutes
Min Time: 9:00 AM
Max Time: 5:00 PM
</div>
            </div>
        </div>

        <h2>🎛️ Selection Fields</h2>

        <div class="field-grid">
            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">▼</div>
                    <div class="field-name">Select Dropdown</div>
                </div>
                <div class="field-description">Dropdown menu with predefined options</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Categories and classifications</li>
                        <li>Status selections</li>
                        <li>Country/state selections</li>
                        <li>Priority levels</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Options:</strong> Define available choices</li>
                        <li><strong>Default Selection:</strong> Pre-selected option</li>
                        <li><strong>Allow Empty:</strong> Include "None" option</li>
                        <li><strong>Dynamic Options:</strong> Load from another table</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Priority Level
Options:
- Low
- Medium  
- High
- Critical
Default: Medium
</div>
            </div>

            <div class="field-type-card">
                <div class="field-type-header">
                    <div class="field-icon">◉</div>
                    <div class="field-name">Radio Buttons</div>
                </div>
                <div class="field-description">Single selection from visible options</div>
                
                <div class="use-cases">
                    <h5>🎯 Perfect For:</h5>
                    <ul>
                        <li>Yes/No questions</li>
                        <li>Gender selection</li>
                        <li>Payment methods</li>
                        <li>Satisfaction ratings</li>
                    </ul>
                </div>

                <div class="configuration">
                    <h5>⚙️ Configuration Options:</h5>
                    <ul>
                        <li><strong>Layout:</strong> Horizontal or vertical</li>
                        <li><strong>Options:</strong> Define radio button choices</li>
                        <li><strong>Default:</strong> Pre-selected option</li>
                        <li><strong>Required:</strong> Force selection</li>
                    </ul>
                </div>

                <div class="example-code">
Field Label: Preferred Contact Method
Layout: Horizontal
Options:
- Email
- Phone
- SMS
Default: Email
</div>
            </div>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tip: Field Planning</h5>
            <p>Before adding fields, sketch out your form on paper. Consider the user experience and group related fields together. This planning will save time and create better forms.</p>
        </div>

        <div class="navigation-buttons">
            <a href="complete-user-guide.html" class="nav-button">← Previous: Getting Started</a>
            <a href="advanced-fields-tutorial.html" class="nav-button">Next: Advanced Fields →</a>
        </div>
    </div>
</body>
</html>
