/**
 * Enhanced Dashboard Styles
 * 
 * Modern, card-based dashboard styling for the Database App Builder plugin
 */

/* Import Modern UI Framework */
@import url('modern-ui.css');

/* Dashboard Container */
.dab-enhanced-dashboard {
  padding: var(--dab-spacing-md);
  background-color: var(--dab-gray-100);
}

/* Dashboard Header */
.dab-enhanced-header {
  margin-bottom: var(--dab-spacing-lg);
  padding-bottom: var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
}

.dab-enhanced-title {
  font-size: var(--dab-font-size-3xl);
  font-weight: 700;
  color: var(--dab-gray-900);
  margin-bottom: var(--dab-spacing-sm);
}

.dab-enhanced-subtitle {
  color: var(--dab-gray-600);
  font-size: var(--dab-font-size-lg);
}

/* Dashboard Cards Grid */
.dab-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--dab-spacing-lg);
  margin-bottom: var(--dab-spacing-xl);
}

/* Dashboard Card */
.dab-dashboard-card {
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-lg);
  box-shadow: var(--dab-shadow-md);
  overflow: hidden;
  transition: transform var(--dab-transition-normal) ease, 
              box-shadow var(--dab-transition-normal) ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.dab-dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--dab-shadow-lg);
}

.dab-dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--dab-primary), var(--dab-accent));
}

.dab-dashboard-card.card-primary::before {
  background: linear-gradient(to right, var(--dab-primary), var(--dab-primary-light));
}

.dab-dashboard-card.card-success::before {
  background: linear-gradient(to right, var(--dab-success), #4ad0c5);
}

.dab-dashboard-card.card-info::before {
  background: linear-gradient(to right, var(--dab-info), #7dd8f3);
}

.dab-dashboard-card.card-warning::before {
  background: linear-gradient(to right, var(--dab-warning), #ffb74d);
}

.dab-dashboard-card.card-danger::before {
  background: linear-gradient(to right, var(--dab-danger), #f05565);
}

.dab-dashboard-card.card-secondary::before {
  background: linear-gradient(to right, var(--dab-secondary), #5a2cd0);
}

/* Card Header */
.dab-dashboard-card-header {
  padding: var(--dab-spacing-md);
  display: flex;
  align-items: center;
}

.dab-dashboard-card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--dab-border-radius-md);
  background-color: rgba(67, 97, 238, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--dab-spacing-md);
  color: var(--dab-primary);
  font-size: 24px;
}

.dab-dashboard-card.card-primary .dab-dashboard-card-icon {
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--dab-primary);
}

.dab-dashboard-card.card-success .dab-dashboard-card-icon {
  background-color: rgba(46, 196, 182, 0.1);
  color: var(--dab-success);
}

.dab-dashboard-card.card-info .dab-dashboard-card-icon {
  background-color: rgba(76, 201, 240, 0.1);
  color: var(--dab-info);
}

.dab-dashboard-card.card-warning .dab-dashboard-card-icon {
  background-color: rgba(255, 159, 28, 0.1);
  color: var(--dab-warning);
}

.dab-dashboard-card.card-danger .dab-dashboard-card-icon {
  background-color: rgba(231, 29, 54, 0.1);
  color: var(--dab-danger);
}

.dab-dashboard-card.card-secondary .dab-dashboard-card-icon {
  background-color: rgba(58, 12, 163, 0.1);
  color: var(--dab-secondary);
}

.dab-dashboard-card-title {
  font-size: var(--dab-font-size-lg);
  font-weight: 600;
  color: var(--dab-gray-900);
  margin: 0;
}

/* Card Body */
.dab-dashboard-card-body {
  padding: 0 var(--dab-spacing-md) var(--dab-spacing-md);
  flex: 1;
}

.dab-dashboard-card-description {
  color: var(--dab-gray-600);
  margin-bottom: var(--dab-spacing-md);
  font-size: var(--dab-font-size-md);
}

/* Card Footer */
.dab-dashboard-card-footer {
  padding: var(--dab-spacing-md);
  border-top: 1px solid var(--dab-gray-200);
  display: flex;
  justify-content: flex-end;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .dab-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .dab-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .dab-enhanced-title {
    font-size: var(--dab-font-size-2xl);
  }
  
  .dab-enhanced-subtitle {
    font-size: var(--dab-font-size-md);
  }
}

/* Stats Section */
.dab-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--dab-spacing-md);
  margin-bottom: var(--dab-spacing-xl);
}

.dab-stat-card {
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-sm);
  padding: var(--dab-spacing-md);
  display: flex;
  align-items: center;
  transition: transform var(--dab-transition-fast) ease, 
              box-shadow var(--dab-transition-fast) ease;
}

.dab-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--dab-shadow-md);
}

@media (max-width: 992px) {
  .dab-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .dab-stats-grid {
    grid-template-columns: 1fr;
  }
}
