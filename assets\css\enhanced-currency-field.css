/**
 * Enhanced Currency Field Styles
 * 
 * Styles for enhanced currency fields in the Database App Builder plugin.
 */

/* Currency Field Container */
.dab-enhanced-currency-field {
    margin-bottom: 15px;
}

/* Currency Input Wrapper */
.dab-currency-input-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    position: relative;
}

/* Currency Symbol */
.dab-currency-symbol {
    font-weight: bold;
    color: #555;
    font-size: 1.1em;
    display: inline-block;
    min-width: 20px;
    text-align: center;
}

/* Currency Amount Input */
.dab-currency-amount {
    flex: 1;
    min-width: 100px;
    max-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-currency-amount:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Currency Selector */
.dab-currency-selector {
    min-width: 100px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
    font-size: 1em;
    transition: border-color 0.3s;
}

.dab-currency-selector:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-currency-input-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dab-currency-amount,
    .dab-currency-selector {
        width: 100%;
        max-width: 100%;
    }
    
    .dab-currency-symbol {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }
    
    .dab-currency-symbol + .dab-currency-amount {
        padding-left: 30px;
    }
}

/* Admin-specific styles */
.wp-admin .dab-enhanced-currency-field {
    max-width: 500px;
}

/* Frontend form-specific styles */
.dab-form .dab-enhanced-currency-field {
    margin-bottom: 20px;
}

.dab-form .dab-currency-amount,
.dab-form .dab-currency-selector {
    padding: 10px 15px;
    border-radius: 5px;
}

/* Error state */
.dab-form .dab-enhanced-currency-field.has-error .dab-currency-amount,
.dab-form .dab-enhanced-currency-field.has-error .dab-currency-selector {
    border-color: #dc3545;
}

.dab-form .dab-enhanced-currency-field.has-error .dab-currency-symbol {
    color: #dc3545;
}

/* Disabled state */
.dab-enhanced-currency-field.disabled .dab-currency-amount,
.dab-enhanced-currency-field.disabled .dab-currency-selector {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* Read-only display */
.dab-currency-display {
    font-size: 1.1em;
    font-weight: 500;
    color: #333;
    padding: 8px 0;
}

/* Currency formatting in tables */
table .dab-currency-cell {
    text-align: right;
    font-family: monospace;
    white-space: nowrap;
}
