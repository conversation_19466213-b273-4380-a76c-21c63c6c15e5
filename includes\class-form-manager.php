<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Form_Manager {

    public static function get_form($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        $form = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id)
        );

        if ($form) {
            $form->fields = maybe_unserialize($form->fields);
        }

        return $form;
    }

    public static function get_table_fields($table_id) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        $fields = $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $table_id)
        );

        return $fields;
    }

}
/**
 * Form submission handler
 */
class DAB_Form_Handler {

    /**
     * Process form submission
     */
    public function process_form_submission() {
        // Check if this is a form submission
        if (!isset($_POST['dab_form_id']) || !isset($_POST['dab_form_submit'])) {
            return;
        }

        // Verify nonce
        if (!isset($_POST['dab_form_nonce']) || !wp_verify_nonce($_POST['dab_form_nonce'], 'dab_form_submit')) {
            wp_die(__('Security check failed', 'db-app-builder'));
        }

        $form_id = intval($_POST['dab_form_id']);

        // Get form data
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));

        if (!$form) {
            wp_die(__('Form not found', 'db-app-builder'));
        }

        // Get form fields
        $fields_table = $wpdb->prefix . 'dab_fields';
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
            $form->table_id
        ));

        // Prepare data for insertion
        $data = array();
        $format = array();

        foreach ($fields as $field) {
            $field_name = $field->field_slug;

            // Skip fields not in the form
            if (!isset($_POST[$field_name]) && $field->field_type !== 'checkbox') {
                continue;
            }

            // Handle different field types
            switch ($field->field_type) {
                case 'checkbox':
                    $value = isset($_POST[$field_name]) ? 1 : 0;
                    $data[$field_name] = $value;
                    $format[] = '%d';
                    break;

                case 'number':
                    $value = isset($_POST[$field_name]) ? floatval($_POST[$field_name]) : 0;
                    $data[$field_name] = $value;
                    $format[] = '%f';
                    break;

                case 'file':
                case 'image':
                    // Handle file uploads
                    if (!empty($_FILES[$field_name]['name'])) {
                        $upload = $this->handle_file_upload($field_name, $field->field_type === 'image');
                        if (!is_wp_error($upload)) {
                            $data[$field_name] = $upload;
                            $format[] = '%s';
                        }
                    }
                    break;

                default:
                    $value = isset($_POST[$field_name]) ? sanitize_text_field($_POST[$field_name]) : '';
                    $data[$field_name] = $value;
                    $format[] = '%s';
                    break;
            }
        }

        // Add created_at and updated_at timestamps
        $data['created_at'] = current_time('mysql');
        $data['updated_at'] = current_time('mysql');
        $format[] = '%s';
        $format[] = '%s';

        // Add user_id if user is logged in
        if (is_user_logged_in()) {
            $data['user_id'] = get_current_user_id();
            $format[] = '%d';
        }

        // Get the actual table name
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT table_name FROM $tables_table WHERE id = %d",
            $form->table_id
        ));

        if (!$table_info) {
            wp_die(__('Table not found', 'db-app-builder'));
        }

        $table_name = $wpdb->prefix . 'dab_' . $table_info->table_name;

        // Insert data
        $result = $wpdb->insert($table_name, $data, $format);

        if ($result === false) {
            wp_die(__('Error saving form data', 'db-app-builder') . ': ' . $wpdb->last_error);
        }

        // Get the ID of the inserted record
        $record_id = $wpdb->insert_id;

        // Handle success - redirect to success page or show message
        $redirect_url = '';

        if (!empty($form->success_redirect_url)) {
            $redirect_url = $form->success_redirect_url;
        } else {
            // Redirect back to the same page with success parameter
            $redirect_url = add_query_arg(
                array(
                    'dab_form_success' => 1,
                    'form_id' => $form_id
                ),
                wp_get_referer() ?: home_url()
            );
        }

        // Apply filters to allow customization of redirect URL
        $redirect_url = apply_filters('dab_form_submission_redirect', $redirect_url, $form_id, $record_id);

        // Redirect
        wp_safe_redirect($redirect_url);
        exit;
    }

    /**
     * Handle file upload
     */
    private function handle_file_upload($field_name, $is_image = false) {
        if (empty($_FILES[$field_name]['name'])) {
            return '';
        }

        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        $upload = wp_handle_upload($_FILES[$field_name], array('test_form' => false));

        if (isset($upload['error'])) {
            return new WP_Error('upload_error', $upload['error']);
        }

        if ($is_image) {
            // For images, store as attachment and return attachment ID
            $attachment = array(
                'post_mime_type' => $upload['type'],
                'post_title' => sanitize_file_name($_FILES[$field_name]['name']),
                'post_content' => '',
                'post_status' => 'inherit'
            );

            $attach_id = wp_insert_attachment($attachment, $upload['file']);

            if (!is_wp_error($attach_id)) {
                wp_update_attachment_metadata($attach_id, wp_generate_attachment_metadata($attach_id, $upload['file']));
                return $attach_id;
            } else {
                return $upload['url'];
            }
        }

        return $upload['url'];
    }
}

// Initialize the form handler
$form_handler = new DAB_Form_Handler();
add_action('init', array($form_handler, 'process_form_submission'));
/**
 * Render form shortcode
 */
function render_form_shortcode($atts) {
    $atts = shortcode_atts(array(
        'id' => 0,
    ), $atts, 'dab_form');

    if (empty($atts['id'])) {
        return '<p class="dab-error">' . __('Error: Form ID is required', 'db-app-builder') . '</p>';
    }

    $form_id = intval($atts['id']);

    // Get form data
    global $wpdb;
    $forms_table = $wpdb->prefix . 'dab_forms';
    $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));

    if (!$form) {
        return '<p class="dab-error">' . __('Error: Form not found', 'db-app-builder') . '</p>';
    }

    // Get form fields
    $fields_table = $wpdb->prefix . 'dab_fields';
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
        $form->table_id
    ));

    // Check if form was successfully submitted
    $form_success = isset($_GET['dab_form_success']) && isset($_GET['form_id']) && intval($_GET['form_id']) === $form_id;

    // Start output buffering
    ob_start();

    // Show success message if form was submitted
    if ($form_success) {
        echo '<div class="dab-form-success">';
        echo !empty($form->success_message) ? esc_html($form->success_message) : __('Form submitted successfully!', 'db-app-builder');
        echo '</div>';
    }

    // Form HTML
    echo '<form method="post" enctype="multipart/form-data" class="dab-form" id="dab-form-' . esc_attr($form_id) . '">';

    // Add hidden fields
    echo '<input type="hidden" name="dab_form_id" value="' . esc_attr($form_id) . '">';
    echo '<input type="hidden" name="dab_form_submit" value="1">';
    echo wp_nonce_field('dab_form_submit', 'dab_form_nonce', true, false);

    // Form title
    if (!empty($form->form_title) && $form->show_title) {
        echo '<h3 class="dab-form-title">' . esc_html($form->form_title) . '</h3>';
    }

    // Form description
    if (!empty($form->form_description) && $form->show_description) {
        echo '<div class="dab-form-description">' . wp_kses_post($form->form_description) . '</div>';
    }

    // Render fields
    foreach ($fields as $field) {
        // Skip fields that shouldn't be in the form
        if (isset($field->show_in_form) && !$field->show_in_form) {
            continue;
        }

        render_form_field($field);
    }

    // Submit button
    $submit_text = !empty($form->submit_button_text) ? $form->submit_button_text : __('Submit', 'db-app-builder');
    echo '<div class="dab-form-field dab-form-submit">';
    echo '<button type="submit" class="dab-submit-button">' . esc_html($submit_text) . '</button>';
    echo '</div>';

    echo '</form>';

    // Get the buffered content
    $output = ob_get_clean();

    return $output;
}

/**
 * Render a single form field
 */
function render_form_field($field) {
    $field_id = 'dab-field-' . esc_attr($field->id);
    $field_name = esc_attr($field->field_slug);
    $field_label = esc_html($field->field_label);
    $required = !empty($field->required) ? ' required' : '';
    $required_mark = !empty($field->required) ? ' <span class="dab-required">*</span>' : '';

    echo '<div class="dab-form-field dab-field-type-' . esc_attr($field->field_type) . '">';

    // Label
    echo '<label for="' . $field_id . '">' . $field_label . $required_mark . '</label>';

    // Field input based on type
    switch ($field->field_type) {
        case 'textarea':
            echo '<textarea id="' . $field_id . '" name="' . $field_name . '"' . $required . '></textarea>';
            break;

        case 'select':
            echo '<select id="' . $field_id . '" name="' . $field_name . '"' . $required . ' class="dab-enhanced-dropdown">';
            echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';

            // Try different ways to get options
            $options_found = false;

            // Method 1: Try field_options JSON
            $options = json_decode($field->field_options, true);
            if (!empty($options['choices']) && is_array($options['choices'])) {
                foreach ($options['choices'] as $choice) {
                    if (isset($choice['value']) && isset($choice['label'])) {
                        echo '<option value="' . esc_attr($choice['value']) . '">' . esc_html($choice['label']) . '</option>';
                        $options_found = true;
                    }
                }
            }

            // Method 2: Try options field directly
            if (!$options_found && !empty($field->options)) {
                // First, try to parse as a serialized array
                $direct_options = maybe_unserialize($field->options);

                // If not an array, try to split by newlines
                if (!is_array($direct_options)) {
                    $direct_options = explode("\n", $field->options);
                }

                // Process each option
                if (is_array($direct_options)) {
                    foreach ($direct_options as $option_text) {
                        if (is_string($option_text)) {
                            $option_text = trim($option_text);
                            if (empty($option_text)) continue;

                            // Check if this is a key:value pair
                            $parts = explode(':', $option_text, 2);
                            if (count($parts) > 1) {
                                $label = trim($parts[0]);
                                $value = trim($parts[1]);
                                echo '<option value="' . esc_attr($value) . '">' . esc_html($label) . '</option>';
                            } else {
                                echo '<option value="' . esc_attr($option_text) . '">' . esc_html($option_text) . '</option>';
                            }
                            $options_found = true;
                        }
                    }
                }
            }

            // Debug output for admins
            if (current_user_can('manage_options') && !$options_found) {
                echo '<!-- Debug: No options found for dropdown field: ' . esc_html($field_name) . ' -->';
            }

            echo '</select>';
            break;

        case 'radio':
            echo '<div class="dab-radio-group">';

            // Try different ways to get options
            $options_found = false;

            // Method 1: Try field_options JSON
            $options = json_decode($field->field_options, true);
            if (!empty($options['choices']) && is_array($options['choices'])) {
                foreach ($options['choices'] as $index => $choice) {
                    if (isset($choice['value']) && isset($choice['label'])) {
                        $radio_id = $field_id . '-' . $index;
                        echo '<div class="dab-radio-option">';
                        echo '<input type="radio" id="' . esc_attr($radio_id) . '" name="' . $field_name . '" value="' . esc_attr($choice['value']) . '"' . $required . '>';
                        echo '<label for="' . esc_attr($radio_id) . '">' . esc_html($choice['label']) . '</label>';
                        echo '</div>';
                        $options_found = true;
                    }
                }
            }

            // Method 2: Try options field directly
            if (!$options_found && !empty($field->options)) {
                // First, try to parse as a serialized array
                $direct_options = maybe_unserialize($field->options);

                // If not an array, try to split by newlines
                if (!is_array($direct_options)) {
                    $direct_options = explode("\n", $field->options);
                }

                // Process each option
                if (is_array($direct_options)) {
                    foreach ($direct_options as $index => $option_text) {
                        if (is_string($option_text)) {
                            $option_text = trim($option_text);
                            if (empty($option_text)) continue;

                            // Check if this is a key:value pair
                            $parts = explode(':', $option_text, 2);
                            if (count($parts) > 1) {
                                $label = trim($parts[0]);
                                $value = trim($parts[1]);
                            } else {
                                $label = $option_text;
                                $value = $option_text;
                            }

                            $radio_id = $field_id . '-' . $index;
                            echo '<div class="dab-radio-option">';
                            echo '<input type="radio" id="' . esc_attr($radio_id) . '" name="' . $field_name . '" value="' . esc_attr($value) . '"' . $required . '>';
                            echo '<label for="' . esc_attr($radio_id) . '">' . esc_html($label) . '</label>';
                            echo '</div>';
                            $options_found = true;
                        }
                    }
                }
            }

            // Debug output for admins
            if (current_user_can('manage_options') && !$options_found) {
                echo '<!-- Debug: No options found for radio field: ' . esc_html($field_name) . ' -->';
            }

            echo '</div>';
            break;

        case 'checkbox':
            echo '<div class="dab-checkbox-field">';
            echo '<input type="checkbox" id="' . $field_id . '" name="' . $field_name . '" value="1"' . $required . '>';
            echo '</div>';
            break;

        case 'file':
        case 'image':
            echo '<input type="file" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'date':
            echo '<input type="date" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'time':
            echo '<input type="time" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'datetime':
            echo '<input type="datetime-local" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'number':
            echo '<input type="number" id="' . $field_id . '" name="' . $field_name . '" step="any"' . $required . '>';
            break;

        case 'email':
            echo '<input type="email" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'url':
            echo '<input type="url" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;

        case 'lookup':
            $options = json_decode($field->field_options, true);

            if (!empty($options['lookup_table_id'])) {
                $lookup_table_id = $options['lookup_table_id'];
                $display_field = !empty($options['lookup_display_field']) ? $options['lookup_display_field'] : '';
                $value_field = !empty($options['lookup_value_field']) ? $options['lookup_value_field'] : '';

                // Get lookup table name
                global $wpdb;
                $tables_table = $wpdb->prefix . 'dab_tables';
                $lookup_table_info = $wpdb->get_row($wpdb->prepare(
                    "SELECT table_name FROM $tables_table WHERE id = %d",
                    $lookup_table_id
                ));

                if ($lookup_table_info) {
                    $lookup_table_name = $wpdb->prefix . 'dab_' . $lookup_table_info->table_name;

                    // Get lookup options
                    $lookup_options = $wpdb->get_results(
                        "SELECT * FROM $lookup_table_name ORDER BY id ASC"
                    );

                    echo '<select id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
                    echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';

                    foreach ($lookup_options as $option) {
                        if (isset($option->$display_field) && isset($option->$value_field)) {
                            echo '<option value="' . esc_attr($option->$value_field) . '">' . esc_html($option->$display_field) . '</option>';
                        }
                    }

                    echo '</select>';
                }
            }
            break;

        case 'payment':
            // Use the payment gateway renderer
            if (class_exists('DAB_Payment_Gateway')) {
                DAB_Payment_Gateway::render_payment_field($field, '');
            } else {
                echo '<div class="dab-error">Payment gateway not available</div>';
            }
            break;

        default:
            echo '<input type="text" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
            break;
    }

    // Field description
    if (!empty($field->field_description)) {
        echo '<div class="dab-field-description">' . esc_html($field->field_description) . '</div>';
    }

    echo '</div>';
}
