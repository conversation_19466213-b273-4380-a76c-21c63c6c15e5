<?php
/**
 * Simple Dashboard Manager
 *
 * Handles the simple dashboard builder functionality.
 */
if (!defined('ABSPATH')) exit;

class DAB_Simple_Dashboard_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_save_dashboard', array(__CLASS__, 'ajax_save_dashboard'));
        add_action('wp_ajax_dab_get_dashboard', array(__CLASS__, 'ajax_get_dashboard'));
        add_action('wp_ajax_dab_delete_dashboard', array(__CLASS__, 'ajax_delete_dashboard'));
        add_action('wp_ajax_dab_save_widget', array(__CLASS__, 'ajax_save_widget'));
        add_action('wp_ajax_dab_delete_widget', array(__CLASS__, 'ajax_delete_widget'));
        add_action('wp_ajax_dab_get_widget', array(__CLASS__, 'ajax_get_widget'));
        add_action('wp_ajax_dab_get_widget_data', array(__CLASS__, 'ajax_get_widget_data'));
        add_action('wp_ajax_nopriv_dab_get_widget_data', array(__CLASS__, 'ajax_get_widget_data')); // For frontend
    }

    /**
     * Create dashboard tables
     */
    public static function create_dashboard_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Dashboards table
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';
        $sql_dashboards = "CREATE TABLE IF NOT EXISTS $dashboards_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT NULL,
            layout TEXT NULL,
            is_public TINYINT(1) DEFAULT 0,
            created_by BIGINT(20) UNSIGNED NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        dbDelta($sql_dashboards);

        // Dashboard widgets table
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        $sql_widgets = "CREATE TABLE IF NOT EXISTS $widgets_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            dashboard_id BIGINT(20) UNSIGNED NOT NULL,
            widget_type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            settings TEXT NULL,
            position_x INT DEFAULT 0,
            position_y INT DEFAULT 0,
            width INT DEFAULT 1,
            height INT DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        dbDelta($sql_widgets);

        // Check if created_by column exists in dashboards table
        self::check_and_update_dashboard_tables();
    }

    /**
     * Check and update dashboard tables
     *
     * This function checks if the dashboard tables have all required columns
     * and adds any missing columns.
     */
    public static function check_and_update_dashboard_tables() {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        // Check dashboards table
        $dashboards_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$dashboards_table'") === $dashboards_table;

        if ($dashboards_table_exists) {
            // Check if created_by column exists
            $created_by_exists = $wpdb->get_var("SHOW COLUMNS FROM $dashboards_table LIKE 'created_by'");

            if (!$created_by_exists) {
                // Add created_by column
                $wpdb->query("ALTER TABLE $dashboards_table ADD COLUMN created_by BIGINT(20) UNSIGNED NULL AFTER is_public");
            }
        }

        // Check widgets table
        $widgets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$widgets_table'") === $widgets_table;

        if ($widgets_table_exists) {
            // Check for position_x column
            $position_x_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'position_x'");

            if (!$position_x_exists) {
                // Add position_x column
                $wpdb->query("ALTER TABLE $widgets_table ADD COLUMN position_x INT DEFAULT 0");
            }

            // Check for position_y column
            $position_y_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'position_y'");

            if (!$position_y_exists) {
                // Add position_y column
                $wpdb->query("ALTER TABLE $widgets_table ADD COLUMN position_y INT DEFAULT 0");
            }

            // Check for width column
            $width_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'width'");

            if (!$width_exists) {
                // Add width column
                $wpdb->query("ALTER TABLE $widgets_table ADD COLUMN width INT DEFAULT 1");
            }

            // Check for height column
            $height_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'height'");

            if (!$height_exists) {
                // Add height column
                $wpdb->query("ALTER TABLE $widgets_table ADD COLUMN height INT DEFAULT 1");
            }
        } else {
            // If widgets table doesn't exist, create it
            self::create_dashboard_tables();
        }
    }

    /**
     * Get all dashboards
     *
     * @return array Dashboards
     */
    public static function get_dashboards() {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';

        return $wpdb->get_results("SELECT * FROM $dashboards_table ORDER BY title ASC");
    }

    /**
     * Get dashboard by ID
     *
     * @param int $dashboard_id Dashboard ID
     * @return object|null Dashboard object or null if not found
     */
    public static function get_dashboard($dashboard_id) {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';

        $dashboard = $wpdb->get_row($wpdb->prepare("SELECT * FROM $dashboards_table WHERE id = %d", $dashboard_id));

        if ($dashboard) {
            // Get widgets
            $dashboard->widgets = self::get_dashboard_widgets($dashboard_id);
        }

        return $dashboard;
    }

    /**
     * Get dashboard widgets
     *
     * @param int $dashboard_id Dashboard ID
     * @return array Widgets
     */
    public static function get_dashboard_widgets($dashboard_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        return $wpdb->get_results($wpdb->prepare("SELECT * FROM $widgets_table WHERE dashboard_id = %d ORDER BY id ASC", $dashboard_id));
    }

    /**
     * Save dashboard
     *
     * @param array $data Dashboard data
     * @return int|false Dashboard ID or false on failure
     */
    public static function save_dashboard($data) {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';

        // Check if created_by column exists
        $created_by_exists = $wpdb->get_var("SHOW COLUMNS FROM $dashboards_table LIKE 'created_by'");

        $dashboard_data = array(
            'title' => sanitize_text_field($data['title']),
            'description' => isset($data['description']) ? sanitize_textarea_field($data['description']) : '',
            'is_public' => isset($data['is_public']) ? (int) $data['is_public'] : 0
        );

        // Handle layout data separately to ensure it's properly encoded
        if (isset($data['layout'])) {
            // If layout is already a JSON string, use it directly
            if (is_string($data['layout']) && json_decode($data['layout']) !== null) {
                $dashboard_data['layout'] = $data['layout'];
                error_log('Using pre-encoded layout JSON string');
            } else {
                // Otherwise, encode it
                $dashboard_data['layout'] = wp_json_encode($data['layout']);
                error_log('Encoding layout data to JSON');
            }

            // Log a sample of the layout data for debugging
            error_log('Layout data sample: ' . substr($dashboard_data['layout'], 0, 100) . '...');
        } else {
            $dashboard_data['layout'] = null;
            error_log('No layout data provided');
        }

        // Add created_by only if the column exists
        if ($created_by_exists) {
            $dashboard_data['created_by'] = get_current_user_id();
        }

        if (isset($data['id']) && $data['id'] > 0) {
            // Update
            $wpdb->update(
                $dashboards_table,
                $dashboard_data,
                array('id' => (int) $data['id'])
            );
            return (int) $data['id'];
        } else {
            // Insert
            $wpdb->insert($dashboards_table, $dashboard_data);
            return $wpdb->insert_id;
        }
    }

    /**
     * Delete dashboard
     *
     * @param int $dashboard_id Dashboard ID
     * @return bool Success
     */
    public static function delete_dashboard($dashboard_id) {
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_dashboards';
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        // Delete widgets first
        $wpdb->delete($widgets_table, array('dashboard_id' => $dashboard_id));

        // Delete dashboard
        return $wpdb->delete($dashboards_table, array('id' => $dashboard_id));
    }

    /**
     * Save widget
     *
     * @param array $data Widget data
     * @return int|false Widget ID or false on failure
     */
    public static function save_widget($data) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        // Make sure the widgets table has all required columns
        self::check_and_update_dashboard_tables();

        // Handle settings - could be a JSON string or an array
        $settings = null;
        if (isset($data['settings'])) {
            // If it's a string, try to decode it
            if (is_string($data['settings'])) {
                $decoded = json_decode($data['settings'], true);
                if (is_array($decoded)) {
                    // It's a valid JSON string, store it directly
                    $settings = $data['settings'];
                } else {
                    // It's a string but not valid JSON, try to clean it
                    $cleaned = stripslashes($data['settings']);
                    $decoded = json_decode($cleaned, true);
                    if (is_array($decoded)) {
                        // Store the cleaned JSON
                        $settings = $cleaned;
                    } else {
                        // Last resort, just encode whatever we have
                        $settings = wp_json_encode($data['settings']);
                    }
                }
            } else if (is_array($data['settings'])) {
                // It's already an array, encode it
                $settings = wp_json_encode($data['settings']);
            } else {
                // Fallback for other types
                $settings = wp_json_encode($data['settings']);
            }
        }

        // Basic widget data
        $widget_data = array(
            'dashboard_id' => (int) $data['dashboard_id'],
            'widget_type' => sanitize_text_field($data['widget_type']),
            'title' => sanitize_text_field($data['title']),
            'settings' => $settings
        );

        // Check if position and size columns exist
        $position_x_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'position_x'");
        $position_y_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'position_y'");
        $width_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'width'");
        $height_exists = $wpdb->get_var("SHOW COLUMNS FROM $widgets_table LIKE 'height'");

        // Add position and size data if columns exist
        if ($position_x_exists && isset($data['position_x'])) {
            $widget_data['position_x'] = (int) $data['position_x'];
        }

        if ($position_y_exists && isset($data['position_y'])) {
            $widget_data['position_y'] = (int) $data['position_y'];
        }

        if ($width_exists && isset($data['width'])) {
            $widget_data['width'] = (int) $data['width'];
        }

        if ($height_exists && isset($data['height'])) {
            $widget_data['height'] = (int) $data['height'];
        }

        if (isset($data['id']) && $data['id'] > 0) {
            // Update
            $wpdb->update(
                $widgets_table,
                $widget_data,
                array('id' => (int) $data['id'])
            );
            return (int) $data['id'];
        } else {
            // Insert
            $wpdb->insert($widgets_table, $widget_data);
            return $wpdb->insert_id;
        }
    }

    /**
     * Delete widget
     *
     * @param int $widget_id Widget ID
     * @return bool Success
     */
    public static function delete_widget($widget_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        return $wpdb->delete($widgets_table, array('id' => $widget_id));
    }

    /**
     * Get widget by ID
     *
     * @param int $widget_id Widget ID
     * @return object|null Widget object or null if not found
     */
    public static function get_widget($widget_id) {
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $widgets_table WHERE id = %d", $widget_id));
    }

    /**
     * AJAX handler for saving dashboard
     */
    public static function ajax_save_dashboard() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Make sure the dashboard tables are up to date
        self::check_and_update_dashboard_tables();

        // Check if this is a layout-only update
        $is_layout_only_update = isset($_POST['layout']) && isset($_POST['id']) && !isset($_POST['title']);

        if ($is_layout_only_update) {
            global $wpdb;
            $dashboards_table = $wpdb->prefix . 'dab_dashboards';

            // Get the existing dashboard to preserve title and other fields
            $existing_dashboard = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $dashboards_table WHERE id = %d", $_POST['id'])
            );

            if (!$existing_dashboard) {
                wp_send_json_error('Dashboard not found');
                return;
            }

            // Update only the layout
            $layout_data = wp_json_encode($_POST['layout']);

            // Log the layout data for debugging
            error_log('Saving dashboard layout: ' . substr($layout_data, 0, 100) . '...');

            $result = $wpdb->update(
                $dashboards_table,
                array(
                    'layout' => $layout_data,
                    'updated_at' => current_time('mysql') // Update the timestamp to ensure the change is registered
                ),
                array('id' => (int) $_POST['id'])
            );

            if ($result !== false) {
                wp_send_json_success(array('id' => (int) $_POST['id']));
            } else {
                wp_send_json_error('Failed to save dashboard layout');
            }
            return;
        }

        // For full dashboard updates, title is required
        if (!isset($_POST['title'])) {
            wp_send_json_error('Missing required fields');
            return;
        }

        // Save dashboard
        $dashboard_id = self::save_dashboard($_POST);

        if ($dashboard_id) {
            wp_send_json_success(array('id' => $dashboard_id));
        } else {
            wp_send_json_error('Failed to save dashboard');
        }
    }

    /**
     * AJAX handler for getting dashboard
     */
    public static function ajax_get_dashboard() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check dashboard ID
        if (!isset($_POST['dashboard_id'])) {
            wp_send_json_error('No dashboard ID provided');
            return;
        }

        $dashboard_id = (int) $_POST['dashboard_id'];
        $dashboard = self::get_dashboard($dashboard_id);

        if ($dashboard) {
            wp_send_json_success($dashboard);
        } else {
            wp_send_json_error('Dashboard not found');
        }
    }

    /**
     * AJAX handler for deleting dashboard
     */
    public static function ajax_delete_dashboard() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Check dashboard ID
        if (!isset($_POST['dashboard_id'])) {
            wp_send_json_error('No dashboard ID provided');
            return;
        }

        $dashboard_id = (int) $_POST['dashboard_id'];
        $success = self::delete_dashboard($dashboard_id);

        if ($success) {
            wp_send_json_success();
        } else {
            wp_send_json_error('Failed to delete dashboard');
        }
    }

    /**
     * AJAX handler for saving widget
     */
    public static function ajax_save_widget() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Check required fields
        if (!isset($_POST['dashboard_id']) || !isset($_POST['widget_type']) || !isset($_POST['title'])) {
            wp_send_json_error('Missing required fields');
            return;
        }

        // Make sure the widgets table has all required columns
        self::check_and_update_dashboard_tables();

        try {
            // Save widget
            $widget_id = self::save_widget($_POST);

            if ($widget_id) {
                error_log('AJAX: Widget saved successfully with ID: ' . $widget_id);
                wp_send_json_success(array('id' => $widget_id));
            } else {
                error_log('AJAX: Failed to save widget - no widget ID returned');
                wp_send_json_error('Failed to save widget - no widget ID returned');
            }
        } catch (Exception $e) {
            error_log('AJAX: Exception while saving widget: ' . $e->getMessage());
            wp_send_json_error('Error saving widget: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler for deleting widget
     */
    public static function ajax_delete_widget() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Check widget ID
        if (!isset($_POST['widget_id'])) {
            wp_send_json_error('No widget ID provided');
            return;
        }

        $widget_id = (int) $_POST['widget_id'];
        $success = self::delete_widget($widget_id);

        if ($success) {
            wp_send_json_success();
        } else {
            wp_send_json_error('Failed to delete widget');
        }
    }

    /**
     * AJAX handler for getting widget
     */
    public static function ajax_get_widget() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check widget ID
        if (!isset($_POST['widget_id'])) {
            wp_send_json_error('No widget ID provided');
            return;
        }

        $widget_id = (int) $_POST['widget_id'];

        $widget = self::get_widget($widget_id);

        if (!$widget) {
            wp_send_json_error('Widget not found');
            return;
        }

        // Try to clean up the settings if it's a string
        if (isset($widget->settings) && is_string($widget->settings)) {
            // Try to decode and re-encode to clean it up
            $decoded = json_decode($widget->settings, true);
            if (is_array($decoded)) {
                $widget->settings = json_encode($decoded);
            }
        }

        wp_send_json_success($widget);
    }

    /**
     * AJAX handler for getting widget data
     */
    public static function ajax_get_widget_data() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_dashboard_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check widget ID
        if (!isset($_POST['widget_id'])) {
            wp_send_json_error('No widget ID provided');
            return;
        }

        $widget_id = (int) $_POST['widget_id'];

        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';

        $widget = $wpdb->get_row($wpdb->prepare("SELECT * FROM $widgets_table WHERE id = %d", $widget_id));

        if (!$widget) {
            wp_send_json_error('Widget not found');
            return;
        }

        // Get widget data based on type
        $data = self::get_widget_data($widget);

        wp_send_json_success($data);
    }

    /**
     * Get widget data
     *
     * @param object $widget Widget object
     * @return array Widget data
     */
    public static function get_widget_data($widget) {
        error_log('Getting widget data for widget type: ' . $widget->widget_type);

        // Handle settings - could be a JSON string or already JSON-encoded
        $settings = null;
        if (!empty($widget->settings)) {
            // Try multiple approaches to decode the settings

            // Approach 1: Direct JSON decode
            $decoded = json_decode($widget->settings, true);
            if (is_array($decoded)) {
                $settings = $decoded;
                error_log('Settings decoded directly from JSON: ' . print_r($settings, true));
            }
            // Approach 2: Strip slashes and then decode
            else {
                $stripped = stripslashes($widget->settings);
                $decoded = json_decode($stripped, true);
                if (is_array($decoded)) {
                    $settings = $decoded;
                    error_log('Settings decoded after stripping slashes: ' . print_r($settings, true));
                }
                // Approach 3: Handle escaped JSON string (with double backslashes)
                else {
                    // Replace escaped quotes and backslashes
                    $cleaned = str_replace(array('\\"', '\\\\'), array('"', '\\'), $widget->settings);
                    $decoded = json_decode($cleaned, true);
                    if (is_array($decoded)) {
                        $settings = $decoded;
                        error_log('Settings decoded after cleaning escaped chars: ' . print_r($settings, true));
                    }
                    // Approach 4: Handle double-encoded JSON
                    else {
                        // This handles cases where the JSON is encoded twice
                        $decoded = json_decode(json_decode($widget->settings, true), true);
                        if (is_array($decoded)) {
                            $settings = $decoded;
                            error_log('Settings decoded from double-encoded JSON: ' . print_r($settings, true));
                        }
                        // Approach 5: Last resort - try to extract JSON from the string
                        else {
                            // Try to extract JSON by removing escape characters
                            $pattern = '/\{.*\}/';
                            if (preg_match($pattern, $widget->settings, $matches)) {
                                $jsonStr = $matches[0];
                                // Replace escaped quotes
                                $jsonStr = str_replace('\\"', '"', $jsonStr);
                                $decoded = json_decode($jsonStr, true);
                                if (is_array($decoded)) {
                                    $settings = $decoded;
                                    error_log('Settings extracted and decoded from string: ' . print_r($settings, true));
                                } else {
                                    error_log('Failed to decode settings after extraction: ' . $jsonStr);
                                }
                            } else {
                                error_log('Failed to extract JSON from settings: ' . $widget->settings);
                            }
                        }
                    }
                }
            }

            // If we still couldn't decode the settings
            if (!is_array($settings)) {
                error_log('All attempts to decode settings failed: ' . $widget->settings);
                return array('error' => 'Invalid widget settings');
            }
        } else {
            error_log('Widget has no settings');
            return array('error' => 'Widget has no settings');
        }

        $data = array();

        switch ($widget->widget_type) {
            case 'table':
                $data = self::get_table_widget_data($settings);
                break;
            case 'chart':
                $data = self::get_chart_widget_data($settings);
                break;
            case 'metric':
                $data = self::get_metric_widget_data($settings);
                break;
            case 'text':
                $data = array(
                    'content' => isset($settings['content']) ? $settings['content'] : ''
                );
                break;
            case 'kpi':
                $data = self::get_kpi_widget_data($settings);
                break;
            case 'list':
                $data = self::get_list_widget_data($settings);
                break;
            case 'progress':
                $data = self::get_progress_widget_data($settings);
                break;
            case 'image':
                $data = array(
                    'image_url' => isset($settings['image_url']) ? $settings['image_url'] : '',
                    'alt_text' => isset($settings['alt_text']) ? $settings['alt_text'] : '',
                    'caption' => isset($settings['caption']) ? $settings['caption'] : ''
                );
                break;
            // Add more widget types as needed
            default:
                error_log('Unknown widget type: ' . $widget->widget_type);
                return array('error' => 'Unknown widget type: ' . $widget->widget_type);
        }

        return $data;
    }

    /**
     * Get table widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_table_widget_data($settings) {
        error_log('Getting table widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in settings');
            return array('error' => 'No table ID provided');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        // Use existing data dashboard manager to get table data
        if (class_exists('DAB_Data_Dashboard_Manager')) {
            error_log('Using DAB_Data_Dashboard_Manager to get table data for table ID: ' . $table_id);
            $result = DAB_Data_Dashboard_Manager::get_table_data($table_id, $filters);
            error_log('Table data result: ' . (isset($result['error']) ? 'Error: ' . $result['error'] : 'Success'));
            return $result;
        }

        error_log('DAB_Data_Dashboard_Manager class not available');
        return array('error' => 'Data dashboard manager not available');
    }

    /**
     * Get chart widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_chart_widget_data($settings) {
        error_log('Getting chart widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in chart settings');
            return array('error' => 'No table ID provided');
        }

        if (!isset($settings['chart_type']) || empty($settings['chart_type'])) {
            error_log('No chart type provided in settings');
            return array('error' => 'No chart type provided');
        }

        if (!isset($settings['x_axis']) || empty($settings['x_axis'])) {
            error_log('No X-axis field provided in settings');
            return array('error' => 'No X-axis field provided');
        }

        if (!isset($settings['y_axis']) || empty($settings['y_axis'])) {
            error_log('No Y-axis field provided in settings');
            return array('error' => 'No Y-axis field provided');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID for chart: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $chart_type = sanitize_text_field($settings['chart_type']);
        $x_axis = sanitize_text_field($settings['x_axis']);
        $y_axis = sanitize_text_field($settings['y_axis']);
        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        // Use existing data dashboard manager to get chart data
        if (class_exists('DAB_Data_Dashboard_Manager')) {
            error_log('Using DAB_Data_Dashboard_Manager to get chart data for table ID: ' . $table_id);
            $result = DAB_Data_Dashboard_Manager::get_chart_data($table_id, $chart_type, $x_axis, $y_axis, $filters);

            // Add chart_type to the result if it's not already there
            if (!isset($result['error']) && !isset($result['chart_type'])) {
                $result['chart_type'] = $chart_type;
            }

            // Check if we have valid data
            if (!isset($result['error']) && isset($result['labels']) && isset($result['values'])) {
                // Check if we have data
                if (empty($result['labels']) || empty($result['values'])) {
                    return array('error' => 'No data found for the selected fields');
                }
            }

            return $result;
        }
        return array('error' => 'Data dashboard manager not available');
    }

    /**
     * Get metric widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_metric_widget_data($settings) {
        error_log('Getting metric widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in metric settings');
            return array('error' => 'No table ID provided');
        }

        if (!isset($settings['metric_type']) || empty($settings['metric_type'])) {
            error_log('No metric type provided in settings');
            return array('error' => 'No metric type provided');
        }

        // Field is required for all metric types except count
        if ($settings['metric_type'] !== 'count' && (!isset($settings['field']) || empty($settings['field']))) {
            error_log('No field provided for metric type: ' . $settings['metric_type']);
            return array('error' => 'No field provided for metric calculation');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID for metric: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $metric_type = sanitize_text_field($settings['metric_type']);
        $field = isset($settings['field']) ? sanitize_text_field($settings['field']) : '';
        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {

            return array('error' => 'Table not found');
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table;
        if (!$table_exists) {
            return array('error' => 'Data table does not exist');
        }

        // Build query based on metric type
        $query = "";

        switch ($metric_type) {
            case 'count':
                $query = "SELECT COUNT(*) as value FROM $data_table";
                break;
            case 'sum':
                $query = "SELECT SUM($field) as value FROM $data_table";
                break;
            case 'average':
                $query = "SELECT AVG($field) as value FROM $data_table";
                break;
            case 'min':
                $query = "SELECT MIN($field) as value FROM $data_table";
                break;
            case 'max':
                $query = "SELECT MAX($field) as value FROM $data_table";
                break;
            default:
                error_log('Unknown metric type: ' . $metric_type);
                return array('error' => 'Unknown metric type: ' . $metric_type);
        }

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $filter_field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$filter_field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(' AND ', $where_clauses);
            }
        }

        // Execute query
        $result = $wpdb->get_var($query);

        return array(
            'value' => $result,
            'label' => isset($settings['label']) ? $settings['label'] : '',
            'type' => $metric_type
        );
    }

    /**
     * Get KPI widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_kpi_widget_data($settings) {
        error_log('Getting KPI widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in KPI settings');
            return array('error' => 'No table ID provided');
        }

        if (!isset($settings['metric_type']) || empty($settings['metric_type'])) {
            error_log('No metric type provided in KPI settings');
            return array('error' => 'No metric type provided');
        }

        // Field is required for all metric types except count
        if ($settings['metric_type'] !== 'count' && (!isset($settings['field']) || empty($settings['field']))) {
            error_log('No field provided for KPI metric type: ' . $settings['metric_type']);
            return array('error' => 'No field provided for KPI calculation');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID for KPI: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $metric_type = sanitize_text_field($settings['metric_type']);
        $field = isset($settings['field']) ? sanitize_text_field($settings['field']) : '';
        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {

            return array('error' => 'Table not found');
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table;
        if (!$table_exists) {
            return array('error' => 'Data table does not exist');
        }

        // Build query based on metric type
        $query = "";

        switch ($metric_type) {
            case 'count':
                $query = "SELECT COUNT(*) as value FROM $data_table";
                break;
            case 'sum':
                $query = "SELECT SUM($field) as value FROM $data_table";
                break;
            case 'average':
                $query = "SELECT AVG($field) as value FROM $data_table";
                break;
            case 'min':
                $query = "SELECT MIN($field) as value FROM $data_table";
                break;
            case 'max':
                $query = "SELECT MAX($field) as value FROM $data_table";
                break;
            default:
                error_log('Unknown KPI metric type: ' . $metric_type);
                return array('error' => 'Unknown metric type: ' . $metric_type);
        }

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $filter_field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$filter_field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(' AND ', $where_clauses);
            }
        }

        error_log('Executing KPI query: ' . $query);

        // Execute query
        $result = $wpdb->get_var($query);

        error_log('KPI query result: ' . $result);

        // Format value
        $value = $result;
        if (is_numeric($value)) {
            if ($metric_type === 'average') {
                $value = round(floatval($value), 2);
            } else {
                $value = intval($value);
            }
        }

        // Calculate status based on target
        $status = 'neutral';
        $target = isset($settings['target']) ? floatval($settings['target']) : null;

        if ($target !== null && $target > 0 && is_numeric($value)) {
            $percentage = ($value / $target) * 100;

            if ($percentage >= 100) {
                $status = 'success';
            } elseif ($percentage >= 75) {
                $status = 'warning';
            } else {
                $status = 'danger';
            }
        }

        return array(
            'value' => $value,
            'label' => isset($settings['label']) ? $settings['label'] : '',
            'target' => $target,
            'status' => $status,
            'icon' => isset($settings['icon']) ? $settings['icon'] : '',
            'color' => isset($settings['color']) ? $settings['color'] : 'primary',
            'type' => $metric_type
        );
    }

    /**
     * Get list widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_list_widget_data($settings) {
        error_log('Getting list widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in list settings');
            return array('error' => 'No table ID provided');
        }

        if (!isset($settings['title_field']) || empty($settings['title_field'])) {
            error_log('No title field provided in list settings');
            return array('error' => 'No title field provided');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID for list: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $title_field = sanitize_text_field($settings['title_field']);
        $description_field = isset($settings['description_field']) ? sanitize_text_field($settings['description_field']) : '';
        $icon_field = isset($settings['icon_field']) ? sanitize_text_field($settings['icon_field']) : '';
        $max_items = isset($settings['max_items']) ? intval($settings['max_items']) : 5;
        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            error_log('Table not found with ID: ' . $table_id);
            return array('error' => 'Table not found');
        }

        error_log('Found table for list: ' . $table_info->table_label . ' (ID: ' . $table_id . ')');

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table;
        if (!$table_exists) {
            error_log('Data table does not exist for list: ' . $data_table);
            return array('error' => 'Data table does not exist');
        }

        // Build query
        $select_fields = array('id', $title_field);

        if (!empty($description_field)) {
            $select_fields[] = $description_field;
        }

        if (!empty($icon_field)) {
            $select_fields[] = $icon_field;
        }

        $query = "SELECT " . implode(', ', $select_fields) . " FROM $data_table";

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $filter_field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$filter_field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(' AND ', $where_clauses);
            }
        }

        // Add limit
        $query .= " LIMIT $max_items";

        // Execute query
        $results = $wpdb->get_results($query);

        if (empty($results)) {
            return array('error' => 'No list items found');
        }



        // Format results
        $items = array();
        foreach ($results as $result) {
            $item = array(
                'id' => $result->id,
                'title' => $result->$title_field
            );

            if (!empty($description_field) && isset($result->$description_field)) {
                $item['description'] = $result->$description_field;
            }

            if (!empty($icon_field) && isset($result->$icon_field)) {
                $item['icon'] = $result->$icon_field;
            }

            $items[] = $item;
        }

        return array(
            'items' => $items
        );
    }

    /**
     * Get progress widget data
     *
     * @param array $settings Widget settings
     * @return array Widget data
     */
    private static function get_progress_widget_data($settings) {
        error_log('Getting progress widget data with settings: ' . print_r($settings, true));

        if (!isset($settings['table_id']) || empty($settings['table_id'])) {
            error_log('No table ID provided in progress settings');
            return array('error' => 'No table ID provided');
        }

        if (!isset($settings['value_field']) || empty($settings['value_field'])) {
            error_log('No value field provided in progress settings');
            return array('error' => 'No value field provided');
        }

        if (!isset($settings['max_field']) || empty($settings['max_field'])) {
            error_log('No max field provided in progress settings');
            return array('error' => 'No max field provided');
        }

        $table_id = (int) $settings['table_id'];
        if ($table_id <= 0) {
            error_log('Invalid table ID for progress: ' . $table_id);
            return array('error' => 'Invalid table ID');
        }

        $value_field = sanitize_text_field($settings['value_field']);
        $max_field = sanitize_text_field($settings['max_field']);
        $filters = isset($settings['filters']) ? $settings['filters'] : array();

        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            error_log('Table not found with ID: ' . $table_id);
            return array('error' => 'Table not found');
        }

        error_log('Found table for progress: ' . $table_info->table_label . ' (ID: ' . $table_id . ')');

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table;
        if (!$table_exists) {
            error_log('Data table does not exist for progress: ' . $data_table);
            return array('error' => 'Data table does not exist');
        }

        // Build query for current value
        $query = "SELECT SUM($value_field) as current_value, SUM($max_field) as max_value FROM $data_table";

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $filter_field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$filter_field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$filter_field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$filter_field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(' AND ', $where_clauses);
            }
        }

        // Execute query
        $result = $wpdb->get_row($query);

        if (!$result) {
            return array('error' => 'No progress data found');
        }

        // Get values
        $current_value = floatval($result->current_value);
        $max_value = floatval($result->max_value);

        // Calculate percentage
        $percentage = 0;
        if ($max_value > 0) {
            $percentage = min(100, round(($current_value / $max_value) * 100));
        }

        return array(
            'current_value' => $current_value,
            'max_value' => $max_value,
            'percentage' => $percentage,
            'label' => isset($settings['label']) ? $settings['label'] : '',
            'color' => isset($settings['color']) ? $settings['color'] : 'primary'
        );
    }
}

// Initialize the class
add_action('init', array('DAB_Simple_Dashboard_Manager', 'init'));
