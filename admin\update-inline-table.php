<?php
/**
 * Update Inline Table Field
 * 
 * This file adds the necessary columns to the database for inline table field options
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Add the new columns to the database
function dab_update_inline_table_columns() {
    global $wpdb;
    $fields_table = $wpdb->prefix . 'dab_fields';
    
    // Check if columns already exist
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $fields_table");
    $column_names = array_map(function($col) { return $col->Field; }, $columns);
    
    // Add inline_table_allow_add column if it doesn't exist
    if (!in_array('inline_table_allow_add', $column_names)) {
        $wpdb->query("ALTER TABLE $fields_table ADD COLUMN inline_table_allow_add TINYINT(1) DEFAULT 1");
    }
    
    // Add inline_table_allow_edit column if it doesn't exist
    if (!in_array('inline_table_allow_edit', $column_names)) {
        $wpdb->query("ALTER TABLE $fields_table ADD COLUMN inline_table_allow_edit TINYINT(1) DEFAULT 1");
    }
    
    // Add inline_table_allow_delete column if it doesn't exist
    if (!in_array('inline_table_allow_delete', $column_names)) {
        $wpdb->query("ALTER TABLE $fields_table ADD COLUMN inline_table_allow_delete TINYINT(1) DEFAULT 1");
    }
    
    return true;
}

// Run the update function
dab_update_inline_table_columns();

// Display success message
echo '<div class="notice notice-success is-dismissible"><p>Inline table field options have been added successfully.</p></div>';
