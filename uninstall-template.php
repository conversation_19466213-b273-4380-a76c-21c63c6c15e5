<?php
/**
 * Manual Template Uninstallation Script
 * 
 * Use this script to manually uninstall templates if the UI method fails
 * Run this from your WordPress admin or through WP-CLI
 */

// Load WordPress if available
if (file_exists('wp-config.php')) {
    require_once 'wp-config.php';
} else {
    echo "WordPress not found. This script should be run in a WordPress environment.\n";
    exit(1);
}

/**
 * Manually uninstall a template installation
 * 
 * @param int $installation_id The installation ID to remove
 * @return bool Success status
 */
function manual_uninstall_template($installation_id) {
    global $wpdb;
    
    $installations_table = $wpdb->prefix . 'dab_template_installations';
    
    // Get installation data
    $installation = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $installations_table WHERE id = %d",
        $installation_id
    ));
    
    if (!$installation) {
        echo "Installation with ID $installation_id not found.\n";
        return false;
    }
    
    echo "Found installation: {$installation->installation_name}\n";
    echo "Template: {$installation->template_key}\n";
    echo "Installed: {$installation->installed_at}\n\n";
    
    // Parse installation data
    $installation_data = json_decode($installation->installation_data, true);
    
    if (!$installation_data) {
        echo "Invalid installation data. Cannot proceed.\n";
        return false;
    }
    
    $deleted_items = array(
        'tables' => 0,
        'forms' => 0,
        'views' => 0,
        'data_tables' => 0
    );
    
    // Delete tables and related data
    if (isset($installation_data['tables'])) {
        foreach ($installation_data['tables'] as $table) {
            echo "Deleting table: {$table['table_slug']}\n";
            
            // Delete data table
            $data_table_name = $wpdb->prefix . 'dab_' . $table['table_slug'];
            $result = $wpdb->query("DROP TABLE IF EXISTS `$data_table_name`");
            if ($result !== false) {
                $deleted_items['data_tables']++;
                echo "  ✓ Data table dropped: $data_table_name\n";
            }
            
            // Delete table record
            $result = $wpdb->delete($wpdb->prefix . 'dab_tables', array('id' => $table['table_id']));
            if ($result) {
                $deleted_items['tables']++;
                echo "  ✓ Table record deleted\n";
            }
            
            // Delete fields
            $field_count = $wpdb->delete($wpdb->prefix . 'dab_fields', array('table_id' => $table['table_id']));
            echo "  ✓ Deleted $field_count field records\n";
        }
    }
    
    // Delete forms
    if (isset($installation_data['forms'])) {
        foreach ($installation_data['forms'] as $form) {
            echo "Deleting form: {$form['form_id']}\n";
            $result = $wpdb->delete($wpdb->prefix . 'dab_forms', array('id' => $form['form_id']));
            if ($result) {
                $deleted_items['forms']++;
                echo "  ✓ Form deleted\n";
            }
        }
    }
    
    // Delete views
    if (isset($installation_data['views'])) {
        foreach ($installation_data['views'] as $view) {
            echo "Deleting view: {$view['view_id']}\n";
            $result = $wpdb->delete($wpdb->prefix . 'dab_views', array('id' => $view['view_id']));
            if ($result) {
                $deleted_items['views']++;
                echo "  ✓ View deleted\n";
            }
        }
    }
    
    // Delete installation record
    $result = $wpdb->delete($installations_table, array('id' => $installation_id));
    if ($result) {
        echo "  ✓ Installation record deleted\n";
    }
    
    echo "\n=== Uninstallation Summary ===\n";
    echo "Data tables dropped: {$deleted_items['data_tables']}\n";
    echo "Table records deleted: {$deleted_items['tables']}\n";
    echo "Forms deleted: {$deleted_items['forms']}\n";
    echo "Views deleted: {$deleted_items['views']}\n";
    echo "Installation '{$installation->installation_name}' completely removed.\n";
    
    return true;
}

/**
 * List all template installations
 */
function list_template_installations() {
    global $wpdb;
    
    $installations_table = $wpdb->prefix . 'dab_template_installations';
    $installations = $wpdb->get_results("SELECT * FROM $installations_table ORDER BY installed_at DESC");
    
    if (empty($installations)) {
        echo "No template installations found.\n";
        return;
    }
    
    echo "=== Template Installations ===\n";
    foreach ($installations as $installation) {
        echo "ID: {$installation->id}\n";
        echo "Name: {$installation->installation_name}\n";
        echo "Template: {$installation->template_key}\n";
        echo "Installed: {$installation->installed_at}\n";
        echo "---\n";
    }
}

// Command line usage
if (php_sapi_name() === 'cli') {
    if ($argc < 2) {
        echo "Usage:\n";
        echo "  php uninstall-template.php list                    - List all installations\n";
        echo "  php uninstall-template.php uninstall <id>          - Uninstall by ID\n";
        echo "  php uninstall-template.php uninstall-name <name>   - Uninstall by name\n";
        exit(1);
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'list':
            list_template_installations();
            break;
            
        case 'uninstall':
            if (!isset($argv[2])) {
                echo "Please provide installation ID\n";
                exit(1);
            }
            $installation_id = intval($argv[2]);
            manual_uninstall_template($installation_id);
            break;
            
        case 'uninstall-name':
            if (!isset($argv[2])) {
                echo "Please provide installation name\n";
                exit(1);
            }
            $installation_name = $argv[2];
            global $wpdb;
            $installation = $wpdb->get_row($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}dab_template_installations WHERE installation_name = %s",
                $installation_name
            ));
            if ($installation) {
                manual_uninstall_template($installation->id);
            } else {
                echo "Installation '$installation_name' not found.\n";
            }
            break;
            
        default:
            echo "Unknown command: $command\n";
            exit(1);
    }
} else {
    echo "This script should be run from command line or WordPress admin.\n";
}
?>
