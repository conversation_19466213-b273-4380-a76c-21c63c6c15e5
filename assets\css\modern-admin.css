/**
 * Database App Builder Modern Admin UI
 * 
 * Modern admin interface styling for the Database App Builder plugin
 */

/* Import Modern UI Framework */
@import url('modern-ui.css');

/* Admin Container */
.dab-admin-wrap {
  padding: var(--dab-spacing-md);
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-sm);
  margin-top: var(--dab-spacing-md);
}

/* Admin Header */
.dab-admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--dab-spacing-lg);
  padding-bottom: var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
}

.dab-admin-title {
  font-size: var(--dab-font-size-2xl);
  font-weight: 600;
  color: var(--dab-gray-900);
  margin: 0;
}

.dab-admin-actions {
  display: flex;
  gap: var(--dab-spacing-sm);
}

/* Admin Content */
.dab-admin-content {
  margin-bottom: var(--dab-spacing-lg);
}

/* Admin Tabs */
.dab-admin-tabs {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: var(--dab-spacing-md);
  list-style: none;
  border-bottom: 1px solid var(--dab-gray-300);
}

.dab-admin-tab {
  margin-bottom: -1px;
}

.dab-admin-tab-link {
  display: block;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  text-decoration: none;
  color: var(--dab-gray-700);
  background-color: transparent;
  border: 1px solid transparent;
  border-top-left-radius: var(--dab-border-radius-md);
  border-top-right-radius: var(--dab-border-radius-md);
  transition: all var(--dab-transition-normal) ease;
}

.dab-admin-tab-link:hover {
  color: var(--dab-primary);
  border-color: var(--dab-gray-300) var(--dab-gray-300) transparent;
}

.dab-admin-tab-link.active {
  color: var(--dab-primary);
  background-color: var(--dab-white);
  border-color: var(--dab-gray-300) var(--dab-gray-300) var(--dab-white);
}

/* Admin Cards */
.dab-admin-card {
  margin-bottom: var(--dab-spacing-md);
}

.dab-admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dab-admin-card-title {
  margin: 0;
  font-size: var(--dab-font-size-lg);
  font-weight: 600;
}

/* Admin Forms */
.dab-admin-form {
  max-width: 800px;
}

.dab-admin-form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--dab-spacing-md) * -1);
  margin-left: calc(var(--dab-spacing-md) * -1);
  margin-bottom: var(--dab-spacing-md);
}

.dab-admin-form-group {
  flex: 0 0 100%;
  max-width: 100%;
  padding-right: var(--dab-spacing-md);
  padding-left: var(--dab-spacing-md);
}

@media (min-width: 768px) {
  .dab-admin-form-group {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .dab-admin-form-group-full {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.dab-admin-form-label {
  display: block;
  margin-bottom: var(--dab-spacing-sm);
  font-weight: 500;
}

.dab-admin-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: var(--dab-font-size-md);
  line-height: 1.5;
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  background-clip: padding-box;
  border: 1px solid var(--dab-gray-400);
  border-radius: var(--dab-border-radius-md);
  transition: border-color var(--dab-transition-normal) ease-in-out, box-shadow var(--dab-transition-normal) ease-in-out;
}

.dab-admin-form-control:focus {
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  border-color: var(--dab-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.dab-admin-form-help {
  display: block;
  margin-top: var(--dab-spacing-xs);
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
}

.dab-admin-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--dab-spacing-lg);
  padding-top: var(--dab-spacing-md);
  border-top: 1px solid var(--dab-gray-300);
}

/* Admin Tables */
.dab-admin-table {
  width: 100%;
  margin-bottom: var(--dab-spacing-md);
}

.dab-admin-table th {
  background-color: var(--dab-gray-100);
  font-weight: 600;
  text-align: left;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 2px solid var(--dab-gray-300);
}

.dab-admin-table td {
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
  vertical-align: middle;
}

.dab-admin-table tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

.dab-admin-table-actions {
  display: flex;
  gap: var(--dab-spacing-sm);
}

/* Admin Filters */
.dab-admin-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--dab-spacing-sm);
  margin-bottom: var(--dab-spacing-md);
  padding: var(--dab-spacing-md);
  background-color: var(--dab-gray-100);
  border-radius: var(--dab-border-radius-md);
}

.dab-admin-filter-item {
  flex: 1;
  min-width: 200px;
}

.dab-admin-filter-label {
  display: block;
  margin-bottom: var(--dab-spacing-xs);
  font-size: var(--dab-font-size-sm);
  font-weight: 500;
}

.dab-admin-filter-control {
  width: 100%;
}

.dab-admin-filter-actions {
  display: flex;
  align-items: flex-end;
  gap: var(--dab-spacing-sm);
}

/* Admin Pagination */
.dab-admin-pagination {
  display: flex;
  justify-content: center;
  padding-left: 0;
  list-style: none;
  margin-top: var(--dab-spacing-md);
}

.dab-admin-page-item {
  margin: 0 var(--dab-spacing-xs);
}

.dab-admin-page-link {
  display: block;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  color: var(--dab-primary);
  background-color: var(--dab-white);
  border: 1px solid var(--dab-gray-300);
  border-radius: var(--dab-border-radius-md);
  text-decoration: none;
  transition: all var(--dab-transition-normal) ease;
}

.dab-admin-page-link:hover {
  color: var(--dab-primary-dark);
  background-color: var(--dab-gray-200);
  border-color: var(--dab-gray-300);
}

.dab-admin-page-item.active .dab-admin-page-link {
  color: var(--dab-white);
  background-color: var(--dab-primary);
  border-color: var(--dab-primary);
}

.dab-admin-page-item.disabled .dab-admin-page-link {
  color: var(--dab-gray-500);
  pointer-events: none;
  background-color: var(--dab-white);
  border-color: var(--dab-gray-300);
}

/* Admin Notices */
.dab-admin-notice {
  padding: var(--dab-spacing-md);
  margin-bottom: var(--dab-spacing-md);
  border-radius: var(--dab-border-radius-md);
  border-left: 4px solid transparent;
  animation: dab-slide-down var(--dab-transition-normal) ease;
}

.dab-admin-notice-success {
  background-color: rgba(46, 196, 182, 0.1);
  border-left-color: var(--dab-success);
}

.dab-admin-notice-error {
  background-color: rgba(231, 29, 54, 0.1);
  border-left-color: var(--dab-danger);
}

.dab-admin-notice-warning {
  background-color: rgba(255, 159, 28, 0.1);
  border-left-color: var(--dab-warning);
}

.dab-admin-notice-info {
  background-color: rgba(76, 201, 240, 0.1);
  border-left-color: var(--dab-info);
}

/* Admin Loader */
.dab-admin-loader {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 0.2rem solid rgba(67, 97, 238, 0.2);
  border-top-color: var(--dab-primary);
  border-radius: 50%;
  animation: dab-spin 1s linear infinite;
}

@keyframes dab-spin {
  to { transform: rotate(360deg); }
}
