<?php
/**
 * Table Permissions Tab
 *
 * Displays the permissions tab in the table settings.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Get all WordPress roles
$wp_roles = wp_roles();
$all_roles = $wp_roles->get_names();

// Get current permissions for this table
$current_permissions = DAB_Role_Permissions_Manager::get_table_permissions($table_id);

// Convert to a more usable format
$role_permissions = [];
foreach ($current_permissions as $perm) {
    $role_permissions[$perm['role']] = [
        'can_view' => $perm['can_view'],
        'can_edit' => $perm['can_edit'],
        'can_delete' => $perm['can_delete']
    ];
}
?>

<style>
    .tooltip {
        position: relative;
        display: inline-block;
        margin-left: 5px;
        width: 16px;
        height: 16px;
        background-color: #f0f0f0;
        color: #666;
        border-radius: 50%;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        cursor: help;
    }

    .tooltip:hover::after {
        content: attr(title);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 100%;
        background-color: #333;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        white-space: nowrap;
        z-index: 1000;
        font-size: 12px;
        font-weight: normal;
    }
</style>

<div class="dab-tab-content" id="dab-tab-permissions">
    <h2>Role-Based Permissions</h2>
    <p class="description">Configure which user roles can view, edit, and delete records in this table. Hover over the question marks for more information.</p>

    <form id="dab-permissions-form" method="post">
        <input type="hidden" name="table_id" value="<?php echo esc_attr($table_id); ?>">
        <?php wp_nonce_field('dab_admin_nonce', 'dab_admin_nonce'); ?>

        <table class="widefat striped">
            <thead>
                <tr>
                    <th>Role</th>
                    <th>View Records</th>
                    <th>Edit Records</th>
                    <th>Delete Records</th>
                    <th>Export Records</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($all_roles as $role_id => $role_name): ?>
                    <?php
                    // Skip administrator role as they always have full access
                    if ($role_id === 'administrator') continue;

                    // Get current permissions for this role
                    $can_view = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_view'] ? true : false;
                    $can_edit = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_edit'] ? true : false;
                    $can_delete = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_delete'] ? true : false;
                    $can_export = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_export'] ? true : false;
                    ?>
                    <tr>
                        <td><?php echo esc_html($role_name); ?></td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_view]" value="1" <?php checked($can_view); ?>>
                            <span class="tooltip" title="Allow users with this role to view records in this table">?</span>
                        </td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_edit]" value="1" <?php checked($can_edit); ?>>
                            <span class="tooltip" title="Allow users with this role to edit records in this table">?</span>
                        </td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_delete]" value="1" <?php checked($can_delete); ?>>
                            <span class="tooltip" title="Allow users with this role to delete records in this table">?</span>
                        </td>
                        <td>
                            <input type="checkbox" name="permissions[<?php echo esc_attr($role_id); ?>][can_export]" value="1" <?php checked($can_export); ?>>
                            <span class="tooltip" title="Allow users with this role to export records from this table">?</span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <p class="description">Note: Administrators always have full access to all records.</p>
        <p class="description">Users can always view, edit, and delete their own records regardless of role permissions.</p>

        <p>
            <button type="submit" class="button button-primary" id="dab-save-permissions">Save Permissions</button>
            <span class="spinner" style="float: none; margin-top: 0;"></span>
        </p>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle form submission
    $('#dab-permissions-form').on('submit', function(e) {
        e.preventDefault();

        // Show spinner
        $(this).find('.spinner').addClass('is-active');

        // Get form data
        var formData = $(this).serialize();
        formData += '&action=dab_save_table_permissions';

        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Permissions saved successfully.');
                } else {
                    // Show error message
                    alert('Error: ' + (response.data || 'Failed to save permissions.'));
                }
            },
            error: function() {
                alert('An error occurred while saving permissions.');
            },
            complete: function() {
                // Hide spinner
                $('#dab-permissions-form').find('.spinner').removeClass('is-active');
            }
        });
    });
});
</script>
