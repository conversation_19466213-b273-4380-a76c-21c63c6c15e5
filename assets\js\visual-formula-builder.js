/**
 * Visual Formula Builder for Database App Builder
 * 
 * This script provides a visual interface for building formula expressions
 * by selecting fields and operators from a user-friendly interface.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the formula builder if the container exists
    const formulaBuilderRow = document.getElementById('formula_builder_row');
    if (!formulaBuilderRow) return;
    
    // Get the formula input field
    const formulaInput = document.getElementById('formula_expression');
    if (!formulaInput) return;
    
    // Create the visual builder container
    const visualBuilder = document.createElement('div');
    visualBuilder.className = 'dab-visual-formula-builder';
    formulaBuilderRow.querySelector('td').insertBefore(visualBuilder, formulaInput.nextSibling);
    
    // Create the formula preview
    const formulaPreview = document.createElement('div');
    formulaPreview.className = 'dab-formula-preview';
    formulaPreview.innerHTML = '<strong>Formula Preview:</strong> <span class="preview-text"></span>';
    visualBuilder.appendChild(formulaPreview);
    
    // Create the field selector
    createFieldSelector(visualBuilder);
    
    // Create the operator buttons
    createOperatorButtons(visualBuilder);
    
    // Create the function buttons
    createFunctionButtons(visualBuilder);
    
    // Create the clear button
    const clearButton = document.createElement('button');
    clearButton.type = 'button';
    clearButton.className = 'button dab-clear-formula';
    clearButton.textContent = 'Clear Formula';
    visualBuilder.appendChild(clearButton);
    
    // Add event listener to clear button
    clearButton.addEventListener('click', function() {
        formulaInput.value = '';
        updateFormulaPreview();
    });
    
    // Update the formula preview initially
    updateFormulaPreview();
    
    // Add event listener to formula input for manual changes
    formulaInput.addEventListener('input', updateFormulaPreview);
    
    /**
     * Create the field selector
     */
    function createFieldSelector(container) {
        // Get the table ID
        const tableIdInput = document.querySelector('input[name="table_id"]');
        if (!tableIdInput) return;
        
        const tableId = tableIdInput.value;
        if (!tableId) return;
        
        // Create the field selector container
        const fieldSelectorContainer = document.createElement('div');
        fieldSelectorContainer.className = 'dab-field-selector-container';
        container.appendChild(fieldSelectorContainer);
        
        // Create the field selector heading
        const fieldSelectorHeading = document.createElement('h4');
        fieldSelectorHeading.textContent = 'Available Fields';
        fieldSelectorContainer.appendChild(fieldSelectorHeading);
        
        // Create the field selector
        const fieldSelector = document.createElement('div');
        fieldSelector.className = 'dab-field-selector';
        fieldSelectorContainer.appendChild(fieldSelector);
        
        // Get all fields from the current table
        const fields = [];
        document.querySelectorAll('#field-list tr').forEach(function(row) {
            const fieldSlug = row.getAttribute('data-slug');
            const fieldLabel = row.querySelector('td:first-child').textContent.trim();
            const fieldType = row.querySelector('td:nth-child(2)').textContent.trim();
            
            // Only add numeric fields or fields that can be used in calculations
            if (fieldSlug && (fieldType === 'Number' || fieldType === 'Currency' || 
                fieldType === 'Formula' || fieldType === 'Rollup' || 
                fieldType === 'Checkbox' || fieldType === 'Rating')) {
                fields.push({
                    slug: fieldSlug,
                    label: fieldLabel,
                    type: fieldType
                });
            }
        });
        
        // Create field buttons
        fields.forEach(function(field) {
            const fieldButton = document.createElement('button');
            fieldButton.type = 'button';
            fieldButton.className = 'button dab-field-button';
            fieldButton.textContent = field.label;
            fieldButton.setAttribute('data-field', field.slug);
            fieldButton.setAttribute('data-type', field.type);
            fieldButton.title = `${field.label} (${field.type})`;
            
            // Add icon based on field type
            const icon = getFieldTypeIcon(field.type);
            if (icon) {
                fieldButton.innerHTML = icon + ' ' + field.label;
            }
            
            fieldSelector.appendChild(fieldButton);
            
            // Add event listener to field button
            fieldButton.addEventListener('click', function() {
                insertFieldReference(field.slug);
            });
        });
        
        // If no fields are available, show a message
        if (fields.length === 0) {
            const noFieldsMessage = document.createElement('p');
            noFieldsMessage.className = 'dab-no-fields-message';
            noFieldsMessage.textContent = 'No numeric fields available. Add number fields to use in formulas.';
            fieldSelector.appendChild(noFieldsMessage);
        }
    }
    
    /**
     * Create the operator buttons
     */
    function createOperatorButtons(container) {
        const operators = [
            { symbol: '+', label: 'Add' },
            { symbol: '-', label: 'Subtract' },
            { symbol: '*', label: 'Multiply' },
            { symbol: '/', label: 'Divide' },
            { symbol: '(', label: 'Open Parenthesis' },
            { symbol: ')', label: 'Close Parenthesis' }
        ];
        
        // Create the operator container
        const operatorContainer = document.createElement('div');
        operatorContainer.className = 'dab-operator-container';
        container.appendChild(operatorContainer);
        
        // Create the operator heading
        const operatorHeading = document.createElement('h4');
        operatorHeading.textContent = 'Operators';
        operatorContainer.appendChild(operatorHeading);
        
        // Create the operator buttons
        const operatorButtons = document.createElement('div');
        operatorButtons.className = 'dab-operator-buttons';
        operatorContainer.appendChild(operatorButtons);
        
        operators.forEach(function(operator) {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'button dab-operator-button';
            button.textContent = operator.symbol;
            button.title = operator.label;
            operatorButtons.appendChild(button);
            
            // Add event listener to operator button
            button.addEventListener('click', function() {
                insertOperator(operator.symbol);
            });
        });
    }
    
    /**
     * Create the function buttons
     */
    function createFunctionButtons(container) {
        const functions = [
            { name: 'SUM', description: 'Sum of values' },
            { name: 'AVG', description: 'Average of values' },
            { name: 'MIN', description: 'Minimum value' },
            { name: 'MAX', description: 'Maximum value' },
            { name: 'ROUND', description: 'Round to nearest integer' },
            { name: 'FLOOR', description: 'Round down' },
            { name: 'CEIL', description: 'Round up' },
            { name: 'ABS', description: 'Absolute value' }
        ];
        
        // Create the function container
        const functionContainer = document.createElement('div');
        functionContainer.className = 'dab-function-container';
        container.appendChild(functionContainer);
        
        // Create the function heading
        const functionHeading = document.createElement('h4');
        functionHeading.textContent = 'Functions';
        functionContainer.appendChild(functionHeading);
        
        // Create the function buttons
        const functionButtons = document.createElement('div');
        functionButtons.className = 'dab-function-buttons';
        functionContainer.appendChild(functionButtons);
        
        functions.forEach(function(func) {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'button dab-function-button';
            button.textContent = func.name;
            button.title = func.description;
            functionButtons.appendChild(button);
            
            // Add event listener to function button
            button.addEventListener('click', function() {
                insertFunction(func.name);
            });
        });
    }
    
    /**
     * Insert a field reference into the formula
     */
    function insertFieldReference(fieldSlug) {
        const cursorPos = formulaInput.selectionStart;
        const currentValue = formulaInput.value;
        const fieldReference = `{${fieldSlug}}`;
        
        // Insert the field reference at the cursor position
        formulaInput.value = currentValue.substring(0, cursorPos) + 
                            fieldReference + 
                            currentValue.substring(cursorPos);
        
        // Update the cursor position
        formulaInput.selectionStart = cursorPos + fieldReference.length;
        formulaInput.selectionEnd = cursorPos + fieldReference.length;
        
        // Focus the input
        formulaInput.focus();
        
        // Update the formula preview
        updateFormulaPreview();
    }
    
    /**
     * Insert an operator into the formula
     */
    function insertOperator(operator) {
        const cursorPos = formulaInput.selectionStart;
        const currentValue = formulaInput.value;
        
        // Insert the operator at the cursor position
        formulaInput.value = currentValue.substring(0, cursorPos) + 
                            operator + 
                            currentValue.substring(cursorPos);
        
        // Update the cursor position
        formulaInput.selectionStart = cursorPos + operator.length;
        formulaInput.selectionEnd = cursorPos + operator.length;
        
        // Focus the input
        formulaInput.focus();
        
        // Update the formula preview
        updateFormulaPreview();
    }
    
    /**
     * Insert a function into the formula
     */
    function insertFunction(functionName) {
        const cursorPos = formulaInput.selectionStart;
        const currentValue = formulaInput.value;
        const functionText = `${functionName}()`;
        
        // Insert the function at the cursor position
        formulaInput.value = currentValue.substring(0, cursorPos) + 
                            functionText + 
                            currentValue.substring(cursorPos);
        
        // Update the cursor position to be inside the parentheses
        formulaInput.selectionStart = cursorPos + functionName.length + 1;
        formulaInput.selectionEnd = cursorPos + functionName.length + 1;
        
        // Focus the input
        formulaInput.focus();
        
        // Update the formula preview
        updateFormulaPreview();
    }
    
    /**
     * Update the formula preview
     */
    function updateFormulaPreview() {
        const formula = formulaInput.value;
        const previewText = formulaPreview.querySelector('.preview-text');
        
        // Format the formula for display
        let formattedFormula = formula;
        
        // Replace field references with field labels
        formattedFormula = formattedFormula.replace(/{([^}]+)}/g, function(match, fieldSlug) {
            const fieldButton = document.querySelector(`.dab-field-button[data-field="${fieldSlug}"]`);
            if (fieldButton) {
                return `<span class="field-reference">${fieldButton.textContent}</span>`;
            }
            return match;
        });
        
        // Highlight operators
        formattedFormula = formattedFormula.replace(/([+\-*/()])/g, '<span class="operator">$1</span>');
        
        // Highlight functions
        const functions = ['SUM', 'AVG', 'MIN', 'MAX', 'ROUND', 'FLOOR', 'CEIL', 'ABS'];
        functions.forEach(function(func) {
            const regex = new RegExp(`(${func})\\(`, 'g');
            formattedFormula = formattedFormula.replace(regex, '<span class="function">$1</span>(');
        });
        
        // Update the preview
        previewText.innerHTML = formattedFormula || 'No formula yet';
    }
    
    /**
     * Get an icon for a field type
     */
    function getFieldTypeIcon(fieldType) {
        switch (fieldType) {
            case 'Number':
                return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg>';
            case 'Currency':
                return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg>';
            case 'Formula':
                return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline></svg>';
            case 'Checkbox':
                return '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path></svg>';
            default:
                return '';
        }
    }
});
