/**
 * Field Sortable JavaScript
 * Handles the drag-and-drop functionality for reordering form fields
 */

jQuery(document).ready(function($) {
    // Initialize sortable for field reordering
    if ($('#sortable-fields-list').length) {
        $('#sortable-fields-list').sortable({
            handle: '.dab-field-handle',
            placeholder: 'dab-field-item ui-sortable-placeholder',
            opacity: 0.7,
            update: function(event, ui) {
                // Update the hidden field with the new order
                updateFieldOrder();

                // Save the field order via AJAX
                saveFieldOrderViaAjax();
            }
        });

        // Initialize the field order on page load
        updateFieldOrder();
    }

    // Function to update the hidden field with the current field order
    function updateFieldOrder() {
        const fieldOrder = [];

        // Get all field IDs in the current order
        $('#sortable-fields-list .dab-field-item').each(function() {
            fieldOrder.push($(this).data('field-id'));
        });

        // Update the hidden input with the JSON string of field IDs
        $('#field-order-input').val(JSON.stringify(fieldOrder));
    }

    // Function to save the field order via AJAX
    function saveFieldOrderViaAjax() {
        // Get the table ID from the form
        const tableId = $('select[name="table_id"]').val();

        // Get the field order
        const fieldOrder = $('#field-order-input').val();

        // Make sure we have a table ID and field order
        if (!tableId || !fieldOrder) {
            return;
        }

        // Show a saving indicator
        const $saveIndicator = $('<div class="dab-save-indicator">Saving field order...</div>');
        $('#sortable-fields-list').before($saveIndicator);

        // Send AJAX request to save the field order
        $.ajax({
            url: dab_field_order.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_update_field_order',
                nonce: dab_field_order.nonce,
                table_id: tableId,
                field_order: fieldOrder
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    $saveIndicator.text('Field order saved successfully').addClass('dab-save-success');

                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $saveIndicator.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }, 2000);
                } else {
                    // Show error message
                    $saveIndicator.text('Error saving field order: ' + (response.data?.message || 'Unknown error')).addClass('dab-save-error');

                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $saveIndicator.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }, 3000);
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                $saveIndicator.text('Error saving field order: ' + error).addClass('dab-save-error');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $saveIndicator.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        });
    }

    // Save field order via AJAX when form is saved
    $('form').on('submit', function() {
        // Make sure field order is updated before form submission
        updateFieldOrder();

        // Continue with form submission
        return true;
    });
});
