<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Complete User Guide</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        h3 {
            color: #2980b9;
            border-left: 3px solid #3498db;
            padding-left: 15px;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .example-box {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-box h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        code {
            background-color: #f8f8f8;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
            border: 1px solid #ddd;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #34495e;
        }
        pre code {
            background: none;
            color: #ecf0f1;
            border: none;
            padding: 0;
        }
        .shortcode {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .toc {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border: 1px solid #dee2e6;
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            text-align: center;
        }
        .toc ul {
            list-style: none;
            padding: 0;
            columns: 2;
            column-gap: 30px;
        }
        .toc li {
            margin-bottom: 8px;
            break-inside: avoid;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
            font-weight: 500;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .toc a:hover {
            background: #3498db;
            color: white;
            transform: translateX(5px);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .screenshot-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin: 20px 0;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Database App Builder - Complete User Guide</h1>
        <p style="text-align: center; font-size: 1.2em; color: #6c757d; margin-bottom: 30px;">
            Master every feature and build powerful applications with confidence
        </p>

        <div class="toc">
            <h2>📚 Complete Tutorial Index</h2>
            <ul>
                <li><a href="#getting-started">🎯 Getting Started Guide</a></li>
                <li><a href="#tables-tutorial">🗃️ Tables & Database Management</a></li>
                <li><a href="#fields-tutorial">📝 Field Types & Configuration</a></li>
                <li><a href="#forms-tutorial">📋 Form Builder Mastery</a></li>
                <li><a href="#views-tutorial">👁️ Views & Data Display</a></li>
                <li><a href="#dashboards-tutorial">📊 Dashboard Creation</a></li>
                <li><a href="#workflows-tutorial">⚡ Workflow Automation</a></li>
                <li><a href="#analytics-tutorial">📈 Analytics & Reporting</a></li>
                <li><a href="#permissions-tutorial">🔐 User Management & Permissions</a></li>
                <li><a href="#integrations-tutorial">🔗 Integrations & Payments</a></li>
                <li><a href="#advanced-tutorial">🎓 Advanced Features</a></li>
                <li><a href="#troubleshooting">🔧 Troubleshooting Guide</a></li>
            </ul>
        </div>

        <div class="section" id="getting-started">
            <h2>🎯 Getting Started Guide</h2>
            <p>Welcome to Database App Builder! This comprehensive guide will take you from zero to expert in building powerful database applications.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>First Login & Overview</h4>
                <p>After activating the plugin, you'll see the "Database App Builder" menu in your WordPress admin.</p>
                
                <div class="example-box">
                    <h5>🎯 What You'll See:</h5>
                    <ul>
                        <li><strong>Dashboard:</strong> Overview of your applications</li>
                        <li><strong>Tables:</strong> Database structure management</li>
                        <li><strong>Fields:</strong> Field configuration</li>
                        <li><strong>Forms:</strong> Data collection interfaces</li>
                        <li><strong>Views:</strong> Data display components</li>
                        <li><strong>Dashboards:</strong> Analytics and visualization</li>
                        <li><strong>Workflows:</strong> Automation and approvals</li>
                        <li><strong>Analytics:</strong> Advanced reporting (Phase 3)</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Understanding the Application Building Process</h4>
                <p>Building applications follows a logical sequence:</p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>1. Create Tables</strong><br>
                        <small>Define data structure</small>
                    </div>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>2. Add Fields</strong><br>
                        <small>Configure data types</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>3. Build Forms</strong><br>
                        <small>Create data entry</small>
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>4. Create Views</strong><br>
                        <small>Display data</small>
                    </div>
                    <div style="background: #fce4ec; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>5. Build Dashboards</strong><br>
                        <small>Visualize insights</small>
                    </div>
                </div>
            </div>

            <div class="tip-box">
                <h5>💡 Pro Tip: Start Simple</h5>
                <p>Begin with a basic application (like a contact form) to understand the workflow, then gradually add complexity as you become more comfortable with the platform.</p>
            </div>
        </div>

        <div class="section" id="tables-tutorial">
            <h2>🗃️ Tables & Database Management</h2>
            <p>Tables are the foundation of your applications. They store all your data and define the structure of your database.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Creating Your First Table</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → Tables</strong></li>
                    <li>Click <strong>"Add New Table"</strong></li>
                    <li>Enter a descriptive table name (e.g., "Customer Contacts")</li>
                    <li>Add an optional description</li>
                    <li>Click <strong>"Create Table"</strong></li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Customer Management System</h5>
                    <p><strong>Table Name:</strong> Customer Contacts</p>
                    <p><strong>Description:</strong> Store customer information including contact details, preferences, and interaction history</p>
                    <p><strong>Generated Slug:</strong> customer_contacts</p>
                    <p><strong>Database Table:</strong> wp_dab_customer_contacts</p>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Table Management Features</h4>
                <p>Once created, you can manage your tables with these features:</p>
                
                <table>
                    <tr>
                        <th>Action</th>
                        <th>Description</th>
                        <th>Use Case</th>
                    </tr>
                    <tr>
                        <td><strong>Edit</strong></td>
                        <td>Modify table name and description</td>
                        <td>Update table information as needs change</td>
                    </tr>
                    <tr>
                        <td><strong>View Data</strong></td>
                        <td>Browse all records in the table</td>
                        <td>Quick data review and management</td>
                    </tr>
                    <tr>
                        <td><strong>Export</strong></td>
                        <td>Download data in CSV, Excel, or PDF</td>
                        <td>Backup, reporting, or data migration</td>
                    </tr>
                    <tr>
                        <td><strong>Duplicate</strong></td>
                        <td>Create a copy with same structure</td>
                        <td>Create similar tables quickly</td>
                    </tr>
                    <tr>
                        <td><strong>Delete</strong></td>
                        <td>Permanently remove table and data</td>
                        <td>Clean up unused tables</td>
                    </tr>
                </table>
            </div>

            <div class="warning-box">
                <h5>⚠️ Important: Table Deletion</h5>
                <p>Deleting a table permanently removes ALL data and cannot be undone. Always export your data before deletion if you might need it later.</p>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Planning Your Database Structure</h4>
                <p>Before creating multiple tables, plan your database structure:</p>
                
                <div class="example-box">
                    <h5>🏢 Example: Complete Business System</h5>
                    <ul>
                        <li><strong>Customers:</strong> Customer information and contacts</li>
                        <li><strong>Products:</strong> Product catalog and inventory</li>
                        <li><strong>Orders:</strong> Sales transactions and order details</li>
                        <li><strong>Support Tickets:</strong> Customer service requests</li>
                        <li><strong>Employees:</strong> Staff information and roles</li>
                        <li><strong>Projects:</strong> Project management and tracking</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="navigation-buttons">
            <a href="field-types-tutorial.html" class="nav-button">Next: Field Types Tutorial →</a>
        </div>
    </div>
</body>
</html>
