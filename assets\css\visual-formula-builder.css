/**
 * Visual Formula Builder Styles
 */

/* Formula help section */
.dab-formula-help {
    margin: 15px 0;
    padding: 15px;
    background-color: #f0f8ff;
    border-left: 4px solid #4a90e2;
    border-radius: 2px;
}

.dab-formula-help h4 {
    margin-top: 0;
    color: #4a90e2;
}

.dab-formula-help ol {
    margin-left: 20px;
}

.dab-formula-help li {
    margin-bottom: 5px;
}

/* Main container */
.dab-visual-formula-builder {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Formula preview */
.dab-formula-preview {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 40px;
}

.dab-formula-preview .preview-text {
    font-family: monospace;
    font-size: 14px;
}

.dab-formula-preview .field-reference {
    display: inline-block;
    padding: 2px 5px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 3px;
    color: #1890ff;
    margin: 0 2px;
}

.dab-formula-preview .operator {
    color: #f5222d;
    font-weight: bold;
    margin: 0 2px;
}

.dab-formula-preview .function {
    color: #722ed1;
    font-weight: bold;
}

/* Field selector */
.dab-field-selector-container,
.dab-operator-container,
.dab-function-container {
    margin-bottom: 15px;
}

.dab-field-selector-container h4,
.dab-operator-container h4,
.dab-function-container h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
}

.dab-field-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.dab-field-button {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background-color: #e6f7ff !important;
    border: 1px solid #91d5ff !important;
    color: #1890ff !important;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.dab-field-button:hover {
    background-color: #bae7ff !important;
    border-color: #69c0ff !important;
}

.dab-field-button svg {
    margin-right: 5px;
}

.dab-no-fields-message {
    color: #ff4d4f;
    font-style: italic;
}

/* Operator buttons */
.dab-operator-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.dab-operator-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    background-color: #fff1f0 !important;
    border: 1px solid #ffa39e !important;
    color: #f5222d !important;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.dab-operator-button:hover {
    background-color: #ffccc7 !important;
    border-color: #ff7875 !important;
}

/* Function buttons */
.dab-function-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.dab-function-button {
    padding: 6px 12px;
    background-color: #f9f0ff !important;
    border: 1px solid #d3adf7 !important;
    color: #722ed1 !important;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.dab-function-button:hover {
    background-color: #efdbff !important;
    border-color: #b37feb !important;
}

/* Clear button */
.dab-clear-formula {
    background-color: #f5f5f5 !important;
    border: 1px solid #d9d9d9 !important;
    color: #595959 !important;
}

.dab-clear-formula:hover {
    background-color: #e8e8e8 !important;
    border-color: #bfbfbf !important;
    color: #262626 !important;
}

/* Responsive adjustments */
@media (max-width: 782px) {
    .dab-field-selector,
    .dab-operator-buttons,
    .dab-function-buttons {
        justify-content: center;
    }

    .dab-field-button,
    .dab-operator-button,
    .dab-function-button {
        margin-bottom: 8px;
    }
}
