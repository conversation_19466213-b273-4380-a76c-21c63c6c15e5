# Payment Gateway Integration

This directory contains the payment gateway integrations for the Database App Builder plugin.

## Available Gateways

- **Stripe**: Accept credit card payments directly on your forms.
- **PayPal**: Accept payments through PayPal accounts and credit cards.
- **Paystack**: Accept payments through various methods including cards, bank transfers, and mobile money.

## How to Use

1. Go to **Database App Builder > Payment Settings** in the WordPress admin.
2. Configure the payment gateways you want to use by entering your API keys.
3. Create a form and add a "Payment" field.
4. Configure the payment field settings (gateway, amount, currency, etc.).
5. Publish your form and start accepting payments!

## Adding a New Gateway

To add a new payment gateway:

1. Create a new gateway class file in this directory (e.g., `class-new-gateway.php`).
2. Implement the required methods (see existing gateway classes for reference).
3. Register the gateway in `class-payment-gateway.php`.
4. Add the gateway to the main plugin file.

## Security Considerations

- Always use HTTPS on your website when accepting payments.
- Keep your API keys secure and never expose them in client-side code.
- Regularly update the plugin to ensure you have the latest security patches.
- Consider using webhooks for reliable payment status updates.

## Dependencies

- <PERSON><PERSON> requires the Stripe PHP SDK.
- PayPal uses the REST API.
- Paystack uses the Paystack API.

## Support

If you need help with payment gateway integration, please contact the plugin support team.
