/**
 * Multi-select Field Styles
 * 
 * Styles for multi-select dropdown fields in the Database App Builder plugin.
 */

/* Multi-select Field Container */
.dab-multiselect-field {
    margin-bottom: 15px;
    max-width: 100%;
}

/* Select2 Customization */
.dab-multiselect-field .select2-container {
    width: 100% !important;
    max-width: 100%;
}

/* Select2 Dropdown */
.dab-multiselect-field .select2-container--default .select2-selection--multiple {
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 38px;
    transition: border-color 0.3s;
}

.dab-multiselect-field .select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Selected Items */
.dab-multiselect-field .select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #f0f0f1;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px 8px;
    margin: 4px 4px 0 0;
    font-size: 0.9em;
}

/* Remove <PERSON><PERSON> */
.dab-multiselect-field .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #555;
    margin-right: 5px;
    font-weight: bold;
}

.dab-multiselect-field .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #d63638;
}

/* Placeholder */
.dab-multiselect-field .select2-container--default .select2-selection--multiple .select2-search__field::placeholder {
    color: #757575;
}

/* Dropdown Menu */
.select2-container--default .select2-results__option {
    padding: 8px 12px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0073aa;
}

/* Option Groups */
.select2-container--default .select2-results__group {
    padding: 8px 12px;
    font-weight: bold;
    color: #23282d;
    background-color: #f8f9fa;
}

/* Select All Option */
.select2-results__option[id*="select-all"] {
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
}

/* Search Box */
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 8px;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Dropdown Container */
.select2-container--default .select2-dropdown {
    border-color: #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Admin-specific styles */
.wp-admin .dab-multiselect-field {
    max-width: 500px;
}

/* Frontend form-specific styles */
.dab-form .dab-multiselect-field {
    margin-bottom: 20px;
}

/* Error state */
.dab-form .dab-multiselect-field.has-error .select2-container--default .select2-selection--multiple {
    border-color: #dc3545;
}

/* Disabled state */
.dab-multiselect-field.disabled .select2-container--default .select2-selection--multiple {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-multiselect-field .select2-container--default .select2-selection--multiple .select2-selection__choice {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* Read-only display */
.dab-multiselect-display {
    padding: 8px 0;
}

.dab-multiselect-display .dab-multiselect-item {
    display: inline-block;
    background-color: #f0f0f1;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px 8px;
    margin: 2px 4px 2px 0;
    font-size: 0.9em;
}

/* Select2 Dropdown Width Fix */
.select2-container--open .select2-dropdown {
    min-width: 200px;
}
