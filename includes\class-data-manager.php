<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Data_Manager {

    public static function get_records($table_id) {
        global $wpdb;

        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_slug = $wpdb->get_var($wpdb->prepare("SELECT table_slug FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_slug) {
            return [];
        }

        $table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        // Check if user has permission to view records
        $can_view = $is_admin;
        if (!$can_view && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
        }

        if ($is_admin || $can_view) {
            // Admins and users with view permission can see all records
            return $wpdb->get_results("SELECT * FROM $table_name ORDER BY id DESC");
        } else {
            // Other users can only see their own records
            return $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $table_name WHERE user_id = %d ORDER BY id DESC", $current_user_id
            ));
        }
    }

    public static function get_fields($table_id) {
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        return $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d ORDER BY id ASC", $table_id)
        );
    }

    public static function render_field_value($table_id, $field, $value, $record_id = null) {
        global $wpdb;

        if ($field->field_type === 'relation') {
            $relationship = DAB_Relationship_Manager::get_relationship_by_field($table_id, $field->field_slug);
            if ($relationship) {
                $records = DAB_Relationship_Manager::get_related_records($relationship->table_b_id, $relationship->display_column);
                foreach ($records as $record) {
                    if ($record->id == $value) {
                        return esc_html($record->{$relationship->display_column} ?? 'Record #' . $record->id);
                    }
                }
            }
        }

        // Handle lookup fields properly
        if ($field->field_type === 'lookup' && !empty($field->lookup_table_id) && !empty($field->lookup_display_column)) {
            $lookup_table_id = intval($field->lookup_table_id);
            $lookup_table_slug = $wpdb->get_var(
                $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
            );

            if ($lookup_table_slug && !empty($value)) {
                $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                $lookup_value = $wpdb->get_var(
                    $wpdb->prepare("SELECT `{$field->lookup_display_column}` FROM `$lookup_table` WHERE id = %d", intval($value))
                );
                return esc_html($lookup_value ?: '');
            }
        }

        // Handle inline table fields
        if ($field->field_type === 'inline_table' && $record_id) {
            return self::render_inline_table_value($field, $record_id, $table_id);
        }

        return esc_html($value);
    }

    public static function delete_record($table_id, $record_id) {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_slug = $wpdb->get_var($wpdb->prepare("SELECT table_slug FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_slug) {
            return false;
        }

        $table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        // Check if user has permission to delete this record
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_delete = DAB_Role_Permissions_Manager::can_user_delete_records($current_user_id, $table_id, $record_id);
            if (!$can_delete) {
                // Check if user is the owner of the record
                $record = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_name WHERE id = %d", $record_id
                ));

                if (!$record || $record->user_id != $current_user_id) {
                    return false; // User doesn't have permission to delete this record
                }
            }
        }

        return $wpdb->delete($table_name, array('id' => $record_id));
    }

    /**
     * Export records to CSV with improved formatting
     *
     * @param int $table_id The table ID
     * @param array $filter_params Optional filter parameters
     * @return void
     */
    public static function export_records_to_csv($table_id, $filter_params = []) {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Get table information
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));
        if (!$table_info) return;

        $table_slug = $table_info->table_slug;
        $table_label = $table_info->table_label;
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        // Get fields
        $fields = $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d ORDER BY id ASC", $table_id)
        );

        // Build query
        $where_clauses = [];
        $query_params = [];

        // Check user permissions
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin) {
            $where_clauses[] = "user_id = %d";
            $query_params[] = $current_user_id;
        }

        // Apply search filter if provided
        if (!empty($filter_params['search'])) {
            $search_term = '%' . $wpdb->esc_like($filter_params['search']) . '%';

            // If specific column is selected
            if (!empty($filter_params['column']) && is_numeric($filter_params['column'])) {
                $column_index = intval($filter_params['column']);
                if (isset($fields[$column_index])) {
                    $field_slug = $fields[$column_index]->field_slug;
                    $where_clauses[] = "`$field_slug` LIKE %s";
                    $query_params[] = $search_term;
                }
            } else {
                // Search all columns
                $search_clauses = [];
                foreach ($fields as $field) {
                    $search_clauses[] = "`{$field->field_slug}` LIKE %s";
                    $query_params[] = $search_term;
                }
                if (!empty($search_clauses)) {
                    $where_clauses[] = '(' . implode(' OR ', $search_clauses) . ')';
                }
            }
        }

        // Build the WHERE clause
        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }

        // Get records
        $query = "SELECT * FROM $data_table $where_sql";
        if (!empty($query_params)) {
            $query = $wpdb->prepare($query, $query_params);
        }
        $records = $wpdb->get_results($query);

        // Set filename with date and table name
        $date = date('Y-m-d');
        $filename = sanitize_file_name("{$table_label}_export_{$date}.csv");

        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Create a file pointer connected to the output stream
        $output = fopen('php://output', 'w');

        // Add UTF-8 BOM for Excel compatibility
        fputs($output, "\xEF\xBB\xBF");

        // Skip metadata - export only the table data

        // Add column headers
        $headers = [];
        foreach ($fields as $field) {
            $headers[] = $field->field_label;
        }
        fputcsv($output, $headers);

        // Add data rows
        foreach ($records as $record) {
            $row = [];
            foreach ($fields as $field) {
                $val = isset($record->{$field->field_slug}) ? $record->{$field->field_slug} : '';

                // Ensure $val is not null to avoid PHP deprecated warnings
                if ($val === null) {
                    $val = '';
                }

                // Format value based on field type
                switch ($field->field_type) {
                    case 'date':
                        $val = !empty($val) ? date('Y-m-d', strtotime($val)) : '';
                        break;
                    case 'datetime':
                        $val = !empty($val) ? date('Y-m-d H:i:s', strtotime($val)) : '';
                        break;
                    case 'boolean':
                        $val = $val ? 'Yes' : 'No';
                        break;
                    case 'lookup':
                        // Get the display value for lookup fields
                        $val = self::get_lookup_display_value($field, $val);
                        break;
                    case 'formula':
                        // Ensure numeric formatting
                        $val = is_numeric($val) ? number_format((float)$val, 2) : $val;
                        break;
                }

                $row[] = $val;
            }
            fputcsv($output, $row);
        }

        fclose($output);
        exit;
    }

    /**
     * Get the display value for a lookup field
     *
     * @param object $field The field object
     * @param mixed $value The field value (ID)
     * @return string The display value
     */
    public static function get_lookup_display_value($field, $value) {
        if (empty($value)) return '';

        global $wpdb;

        $lookup_table_id = $field->lookup_table_id ?? 0;
        $lookup_display_column = $field->lookup_display_column ?? '';

        if (!$lookup_table_id || !$lookup_display_column) return $value;

        $tables_table = $wpdb->prefix . 'dab_tables';
        $lookup_table_slug = $wpdb->get_var($wpdb->prepare(
            "SELECT table_slug FROM $tables_table WHERE id = %d",
            $lookup_table_id
        ));

        if (!$lookup_table_slug) return $value;

        $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);

        $display_value = $wpdb->get_var($wpdb->prepare(
            "SELECT `$lookup_display_column` FROM $lookup_table WHERE id = %d",
            $value
        ));

        return $display_value !== null ? $display_value : $value;
    }

    public static function update_record_field($table_id, $record_id, $field_slug, $field_value) {
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_slug = $wpdb->get_var($wpdb->prepare("SELECT table_slug FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_slug) {
            return false;
        }

        $table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);

        // Check if user has permission to edit this record
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);
        $is_record_owner = false;

        // Check if user is the owner of the record
        $record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d", $record_id
        ));

        if ($record && $record->user_id == $current_user_id) {
            $is_record_owner = true;
        }

        // Check table-level permissions
        $can_edit_record = $is_admin || $is_record_owner;
        if (!$can_edit_record && class_exists('DAB_Role_Permissions_Manager')) {
            $can_edit_record = DAB_Role_Permissions_Manager::can_user_edit_records($current_user_id, $table_id, $record_id);
        }

        if (!$can_edit_record) {
            return false; // User doesn't have permission to edit this record
        }

        // Check field-level permissions
        if (!$is_admin && !$is_record_owner && class_exists('DAB_Role_Permissions_Manager')) {
            // Get field ID
            $fields_table = $wpdb->prefix . 'dab_fields';
            $field_id = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $fields_table WHERE table_id = %d AND field_slug = %s",
                $table_id, $field_slug
            ));

            if ($field_id) {
                $can_edit_field = DAB_Role_Permissions_Manager::can_user_edit_field($current_user_id, $field_id);
                if (!$can_edit_field) {
                    return false; // User doesn't have permission to edit this field
                }
            }
        }

        return $wpdb->update(
            $table_name,
            array($field_slug => $field_value),
            array('id' => $record_id)
        );
    }
/**
 * Render inline table field
 *
 * @param object $field The field object
 * @param int $record_id The parent record ID
 * @param int $table_id The parent table ID
 * @return string HTML for the inline table
 */
public static function render_inline_table_value($field, $record_id, $table_id) {
    global $wpdb;

    // Get inline table settings
    $inline_table_id = intval($field->inline_table_id ?? 0);
    $foreign_key = sanitize_text_field($field->inline_table_foreign_key ?? '');
    $display_fields = json_decode($field->inline_table_display_fields ?? '[]', true);

    // Get inline table options with defaults
    $allow_add = isset($field->inline_table_allow_add) ? (bool)$field->inline_table_allow_add : true;
    $allow_edit = isset($field->inline_table_allow_edit) ? (bool)$field->inline_table_allow_edit : true;
    $allow_delete = isset($field->inline_table_allow_delete) ? (bool)$field->inline_table_allow_delete : true;

    if (empty($inline_table_id) || empty($foreign_key) || empty($display_fields)) {
        return '<div class="dab-inline-table-error">Inline table configuration is incomplete</div>';
    }

    // Get child table info
    $tables_table = $wpdb->prefix . 'dab_tables';
    $child_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $inline_table_id
    ));

    if (!$child_table) {
        return '<div class="dab-inline-table-error">Child table not found</div>';
    }

    // Get child table fields
    $fields_table = $wpdb->prefix . 'dab_fields';
    $child_fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d AND field_slug IN ('" . implode("','", array_map('esc_sql', $display_fields)) . "')",
        $inline_table_id
    ));

    if (empty($child_fields)) {
        return '<div class="dab-inline-table-error">No display fields found</div>';
    }

    // Get child records
    $child_table_name = $wpdb->prefix . 'dab_' . sanitize_title($child_table->table_slug);
    $child_records = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $child_table_name WHERE $foreign_key = %d ORDER BY id DESC",
        $record_id
    ));

    // Build the inline table HTML
    $output = '<div class="dab-inline-table-container"
        data-parent-id="' . esc_attr($record_id) . '"
        data-parent-table="' . esc_attr($table_id) . '"
        data-child-table="' . esc_attr($inline_table_id) . '"
        data-foreign-key="' . esc_attr($foreign_key) . '"
        data-allow-add="' . ($allow_add ? '1' : '0') . '"
        data-allow-edit="' . ($allow_edit ? '1' : '0') . '"
        data-allow-delete="' . ($allow_delete ? '1' : '0') . '">';

    if (empty($child_records)) {
        $output .= '<div class="dab-inline-table-empty">No records found</div>';
    } else {
        $output .= '<table class="dab-inline-table">';

        // Table header
        $output .= '<thead><tr>';
        foreach ($child_fields as $child_field) {
            $output .= '<th>' . esc_html($child_field->field_label) . '</th>';
        }
        $output .= '<th>Actions</th>';
        $output .= '</tr></thead>';

        // Table body
        $output .= '<tbody>';
        foreach ($child_records as $child_record) {
            $output .= '<tr data-id="' . esc_attr($child_record->id) . '">';

            foreach ($child_fields as $child_field) {
                $field_value = isset($child_record->{$child_field->field_slug}) ? $child_record->{$child_field->field_slug} : '';
                $output .= '<td class="dab-inline-field" data-field="' . esc_attr($child_field->field_slug) . '">';
                $output .= self::render_field_value($inline_table_id, $child_field, $field_value, $child_record->id);
                $output .= '</td>';
            }

            // Action buttons
            $output .= '<td class="dab-inline-actions">';
            if ($allow_edit) {
                $output .= '<button type="button" class="dab-inline-edit" title="Edit"><span class="dashicons dashicons-edit"></span></button>';
            }
            if ($allow_delete) {
                $output .= '<button type="button" class="dab-inline-delete" title="Delete"><span class="dashicons dashicons-trash"></span></button>';
            }
            $output .= '</td>';

            $output .= '</tr>';
        }
        $output .= '</tbody>';
        $output .= '</table>';
    }

    // Add button (only if allowed)
    if ($allow_add) {
        $output .= '<div class="dab-inline-add-container">';
        $output .= '<button type="button" class="dab-inline-add-btn button button-secondary">Add New ' . esc_html($child_table->table_label) . '</button>';
        $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
}

/**
 * Render enhanced inline table field for forms
 *
 * @param object $field The field object
 * @param string $value Current value (JSON string of existing records)
 * @param string $field_name The name of the field in the form
 * @return string HTML for the enhanced inline table
 */
public static function render_enhanced_inline_table_field($field, $value = '', $field_name = '') {
    global $wpdb;

    // Get inline table settings
    $inline_table_id = intval($field->inline_table_id ?? 0);
    $foreign_key = sanitize_text_field($field->inline_table_foreign_key ?? '');
    $display_fields = json_decode($field->inline_table_display_fields ?? '[]', true);

    // Get inline table options with defaults
    $allow_add = isset($field->inline_table_allow_add) ? (bool)$field->inline_table_allow_add : true;
    $allow_edit = isset($field->inline_table_allow_edit) ? (bool)$field->inline_table_allow_edit : true;
    $allow_delete = isset($field->inline_table_allow_delete) ? (bool)$field->inline_table_allow_delete : true;

    if (empty($inline_table_id) || empty($foreign_key) || empty($display_fields)) {
        return '<div class="dab-inline-table-error">Inline table configuration is incomplete</div>';
    }

    // Get child table info
    $tables_table = $wpdb->prefix . 'dab_tables';
    $child_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $inline_table_id
    ));

    if (!$child_table) {
        return '<div class="dab-inline-table-error">Child table not found</div>';
    }

    // Get child table fields
    $fields_table = $wpdb->prefix . 'dab_fields';
    $child_fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d AND field_slug IN ('" . implode("','", array_map('esc_sql', $display_fields)) . "')",
        $inline_table_id
    ));

    if (empty($child_fields)) {
        return '<div class="dab-inline-table-error">No display fields found</div>';
    }

    // Check if we're editing an existing record
    $parent_record_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

    // Parse existing data if any
    $existing_data = [];

    if ($parent_record_id > 0) {
        // We're editing an existing record - get child records from database
        $child_table_name = $wpdb->prefix . 'dab_' . sanitize_title($child_table->table_slug);
        $child_records = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $child_table_name WHERE $foreign_key = %d",
            $parent_record_id
        ), ARRAY_A);

        if (!empty($child_records)) {
            $existing_data = $child_records;
        }
    } else if (!empty($value)) {
        // We're handling a form submission with validation errors - use the submitted data
        $existing_data = json_decode($value, true);
        if (!is_array($existing_data)) {
            $existing_data = [];
        }
    }

    // Build the enhanced inline table HTML
    $output = '<div class="dab-enhanced-inline-table"
        data-child-table="' . esc_attr($inline_table_id) . '"
        data-foreign-key="' . esc_attr($foreign_key) . '"
        data-field-name="' . esc_attr($field_name) . '"
        data-allow-add="' . ($allow_add ? '1' : '0') . '"
        data-allow-edit="' . ($allow_edit ? '1' : '0') . '"
        data-allow-delete="' . ($allow_delete ? '1' : '0') . '"
        data-existing-data=\'' . json_encode($existing_data) . '\'>';

    // Add header with title and add button
    $output .= '<div class="dab-enhanced-inline-table-header">';
    $output .= '<h4 class="dab-enhanced-inline-table-title">' . esc_html($child_table->table_label) . '</h4>';
    $output .= '<button type="button" class="dab-inline-add-row"><span class="dashicons dashicons-plus-alt"></span> Add Row</button>';
    $output .= '</div>';

    // Create table template (will be used by JavaScript)
    $output .= '<script type="text/template" class="dab-inline-table-template">';
    $output .= '<table class="dab-enhanced-inline-table-grid">';
    $output .= '<thead><tr>';
    $output .= '<th class="dab-inline-row-number-col">#</th>';

    foreach ($child_fields as $child_field) {
        $output .= '<th>' . esc_html($child_field->field_label) . '</th>';
    }

    $output .= '<th class="dab-inline-actions-col">Actions</th>';
    $output .= '</tr></thead>';
    $output .= '<tbody></tbody>';
    $output .= '</table>';
    $output .= '</script>';

    // Create row template (will be used by JavaScript)
    $output .= '<script type="text/template" class="dab-inline-row-template">';
    $output .= '<tr>';
    $output .= '<td class="dab-inline-row-number-col"><span class="dab-inline-row-number">1</span></td>';

    foreach ($child_fields as $index => $child_field) {
        $output .= '<td>';

        // Create input field based on field type
        switch ($child_field->field_type) {
            case 'select':
                // Add select2 class for enhanced dropdowns
                $output .= '<select name="' . esc_attr($field_name) . '[0][' . esc_attr($child_field->field_slug) . ']" class="dab-select2">';
                $output .= '<option value="">-- Select --</option>';

                // Add options if available
                if (!empty($child_field->options)) {
                    $options = maybe_unserialize($child_field->options);
                    if (is_array($options)) {
                        foreach ($options as $option_text) {
                            // Check if this is a key:value pair
                            $parts = explode(':', $option_text, 2);
                            if (count($parts) > 1) {
                                $label = trim($parts[0]);
                                $value = trim($parts[1]);
                                $output .= '<option value="' . esc_attr($value) . '">' . esc_html($label) . '</option>';
                            } else {
                                $output .= '<option value="' . esc_attr($option_text) . '">' . esc_html($option_text) . '</option>';
                            }
                        }
                    }
                }

                $output .= '</select>';
                break;

            case 'number':
                $output .= '<input type="number" name="' . esc_attr($field_name) . '[0][' . esc_attr($child_field->field_slug) . ']" value="" step="any">';
                break;

            case 'textarea':
                $output .= '<textarea name="' . esc_attr($field_name) . '[0][' . esc_attr($child_field->field_slug) . ']" rows="2"></textarea>';
                break;

            default:
                $output .= '<input type="text" name="' . esc_attr($field_name) . '[0][' . esc_attr($child_field->field_slug) . ']" value="">';
                break;
        }

        $output .= '</td>';
    }

    $output .= '<td class="dab-inline-actions-col">';
    $output .= '<button type="button" class="dab-inline-delete-row" title="Delete Row"><span class="dashicons dashicons-trash"></span></button>';
    $output .= '</td>';
    $output .= '</tr>';
    $output .= '</script>';

    // Add hidden input to store the JSON data
    $output .= '<input type="hidden" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '">';

    $output .= '</div>';

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-enhanced-inline-table', plugin_dir_url(dirname(__FILE__)) . 'assets/css/enhanced-inline-table.css');
    wp_enqueue_script('dab-enhanced-inline-table', plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-inline-table.js', array('jquery'), null, true);

    // Enqueue Select2 for enhanced dropdowns
    wp_enqueue_style('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
    wp_enqueue_script('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), null, true);

    return $output;
}

/**
 * Process form submission with advanced dropdown fields
 */
public static function process_form_submission($form_id, $data) {
    global $wpdb;

    $forms_table = $wpdb->prefix . 'dab_forms';
    $tables_table = $wpdb->prefix . 'dab_tables';
    $fields_table = $wpdb->prefix . 'dab_fields';

    // Get form details
    $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));
    if (!$form) return false;

    // Get table details
    $table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $form->table_id));
    if (!$table) return false;

    // Get fields for this form
    $form_fields = maybe_unserialize($form->fields);
    if (empty($form_fields)) return false;

    // Get all fields for this table
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d",
        $table->id
    ));

    // Prepare data for insertion
    $insert_data = array();
    $errors = array();

    foreach ($fields as $field) {
        // Skip if field is not in the form
        if (!in_array($field->id, $form_fields)) continue;

        // Get field value from submitted data
        $field_value = isset($data[$field->field_slug]) ? $data[$field->field_slug] : '';

        // Validate required fields
        if ($field->is_required && empty($field_value)) {
            $errors[] = $field->field_label . ' is required.';
            continue;
        }

        // Process field based on type
        switch ($field->field_type) {
            case 'relationship':
            case 'lookup':
                // For advanced dropdowns, ensure the value is a valid ID
                if (!empty($field_value)) {
                    $insert_data[$field->field_slug] = intval($field_value);
                } else {
                    $insert_data[$field->field_slug] = null;
                }
                break;

            case 'number':
                if ($field_value !== '') {
                    $insert_data[$field->field_slug] = floatval($field_value);
                }
                break;

            case 'checkbox':
                // Handle multiple checkbox values
                if (is_array($field_value)) {
                    $insert_data[$field->field_slug] = implode(',', array_map('sanitize_text_field', $field_value));
                } else {
                    $insert_data[$field->field_slug] = sanitize_text_field($field_value);
                }
                break;

            case 'date':
            case 'datetime':
                if (!empty($field_value)) {
                    $insert_data[$field->field_slug] = sanitize_text_field($field_value);
                }
                break;

            case 'file':
            case 'image':
                // File uploads handled separately
                if (!empty($_FILES[$field->field_slug]['name'])) {
                    $attachment_id = self::handle_file_upload($field->field_slug, $field->field_type === 'image');
                    if ($attachment_id) {
                        $insert_data[$field->field_slug] = $attachment_id;
                    }
                }
                break;

            case 'inline_table':
                // Store the JSON data as is - we'll process it after the parent record is created
                if (!empty($field_value)) {
                    $insert_data[$field->field_slug] = $field_value;
                }
                break;

            default:
                if ($field_value !== '') {
                    $insert_data[$field->field_slug] = sanitize_text_field($field_value);
                }
                break;
        }
    }

    // If there are errors, return them
    if (!empty($errors)) {
        return array('success' => false, 'errors' => $errors);
    }

    // Add current user ID if user is logged in
    if (is_user_logged_in()) {
        $insert_data['user_id'] = get_current_user_id();
    }

    // Add timestamps
    $insert_data['created_at'] = current_time('mysql');
    $insert_data['updated_at'] = current_time('mysql');

    // Insert data into table
    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
    $result = $wpdb->insert($data_table, $insert_data);

    if ($result) {
        $record_id = $wpdb->insert_id;

        // Process inline table data if any
        foreach ($fields as $field) {
            if ($field->field_type === 'inline_table' && isset($data[$field->field_slug]) && !empty($data[$field->field_slug])) {
                self::process_inline_table_data($field, $data[$field->field_slug], $record_id);
            }
        }

        // Initialize approval workflow if enabled
        if (class_exists('DAB_Approval_Manager')) {
            DAB_Approval_Manager::initialize_approval($table->id, $record_id);
        }

        // Send email notification if configured
        if (!empty($form->notify_email) && !empty($form->notify_message)) {
            DAB_Email_Notifier::trigger_on_form_submission($form, $insert_data);
        }

        return array('success' => true, 'record_id' => $record_id);
    }

    return array('success' => false, 'errors' => array('Failed to save data.'));
}

/**
 * AJAX handler to get fields for a specific table
 */
public function get_table_fields() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_get_table_fields_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Check if table_id is provided
    if (!isset($_POST['table_id']) || empty($_POST['table_id'])) {
        wp_send_json_error('No table ID provided');
        return;
    }

    $table_id = intval($_POST['table_id']);

    global $wpdb;
    $fields_table = $wpdb->prefix . 'dab_fields';

    // Get all fields for the specified table
    $fields = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT id, field_label, field_slug, field_type FROM $fields_table WHERE table_id = %d ORDER BY field_label",
            $table_id
        )
    );

    if (empty($fields)) {
        wp_send_json_success(array()); // Return empty array if no fields found
    } else {
        wp_send_json_success($fields);
    }
}

/**
 * Register AJAX handlers
 */
public function register_ajax_handlers() {
    add_action('wp_ajax_dab_get_table_fields', array($this, 'get_table_fields'));
    // Add other AJAX handlers here
}

/**
 * Initialize the class
 */
public function init() {
    // Register post types, taxonomies, etc.

    // Register AJAX handlers
    $this->register_ajax_handlers();

    // Other initialization code
}

/**
 * Process inline table data
 *
 * @param object $field The inline table field
 * @param string $json_data JSON string of child records
 * @param int $parent_id Parent record ID
 * @return bool Success or failure
 */
public static function process_inline_table_data($field, $json_data, $parent_id) {
    global $wpdb;

    // Get inline table settings
    $inline_table_id = intval($field->inline_table_id ?? 0);
    $foreign_key = sanitize_text_field($field->inline_table_foreign_key ?? '');

    if (empty($inline_table_id) || empty($foreign_key)) {
        return false;
    }

    // Get child table info
    $tables_table = $wpdb->prefix . 'dab_tables';
    $child_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $inline_table_id
    ));

    if (!$child_table) {
        return false;
    }

    // Parse the JSON data
    $child_records = json_decode($json_data, true);
    if (!is_array($child_records)) {
        $child_records = [];
    }

    // Get the child table name
    $child_table_name = $wpdb->prefix . 'dab_' . sanitize_title($child_table->table_slug);

    // Get existing child records for this parent
    $existing_records = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $child_table_name WHERE $foreign_key = %d",
        $parent_id
    ), ARRAY_A);

    // Create a map of existing record IDs
    $existing_record_ids = [];
    foreach ($existing_records as $record) {
        $existing_record_ids[$record['id']] = $record;
    }

    // Track which records to keep
    $records_to_keep = [];
    $success = true;

    // Get child table fields for validation
    $fields_table = $wpdb->prefix . 'dab_fields';
    $child_fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d",
        $inline_table_id
    ));

    // Create a map of field slugs to field objects for validation
    $field_map = [];
    foreach ($child_fields as $child_field) {
        $field_map[$child_field->field_slug] = $child_field;
    }

    // Process each child record from the form
    foreach ($child_records as $record) {
        // Check if this record has an ID (existing record)
        $record_id = isset($record['id']) ? intval($record['id']) : 0;

        // Add the foreign key to link to the parent record
        $record[$foreign_key] = $parent_id;

        // Validate and sanitize each field value
        foreach ($record as $field_slug => $value) {
            // Skip ID and foreign key fields
            if ($field_slug === 'id' || $field_slug === $foreign_key) {
                continue;
            }

            // Skip fields that don't exist in the table
            if (!isset($field_map[$field_slug])) {
                unset($record[$field_slug]);
                continue;
            }

            $field_obj = $field_map[$field_slug];

            // Validate required fields
            if (!empty($field_obj->required) && empty($value)) {
                // Skip this record if a required field is empty
                continue 2; // Skip to the next record
            }

            // Sanitize based on field type
            switch ($field_obj->field_type) {
                case 'number':
                    $record[$field_slug] = is_numeric($value) ? floatval($value) : 0;
                    break;
                case 'checkbox':
                    $record[$field_slug] = !empty($value) ? 1 : 0;
                    break;
                case 'date':
                case 'datetime':
                    // Ensure date is in correct format
                    if (!empty($value)) {
                        $timestamp = strtotime($value);
                        if ($timestamp) {
                            $record[$field_slug] = date('Y-m-d H:i:s', $timestamp);
                        } else {
                            $record[$field_slug] = '';
                        }
                    }
                    break;
                default:
                    $record[$field_slug] = sanitize_text_field($value);
                    break;
            }
        }

        if ($record_id > 0 && isset($existing_record_ids[$record_id])) {
            // This is an existing record - update it
            $record['updated_at'] = current_time('mysql');

            // Remove id from the record data before update
            $update_data = $record;
            unset($update_data['id']);

            $result = $wpdb->update(
                $child_table_name,
                $update_data,
                ['id' => $record_id]
            );

            // Mark this record as processed
            $records_to_keep[] = $record_id;
        } else {
            // This is a new record - insert it
            $record['created_at'] = current_time('mysql');
            $record['updated_at'] = current_time('mysql');

            // Remove id if it exists but doesn't match an existing record
            if (isset($record['id'])) {
                unset($record['id']);
            }

            // Add user ID if user is logged in
            if (is_user_logged_in() && !isset($record['user_id'])) {
                $record['user_id'] = get_current_user_id();
            }

            $result = $wpdb->insert($child_table_name, $record);
            if ($result) {
                $records_to_keep[] = $wpdb->insert_id;
            }
        }

        if ($result === false) {
            $success = false;
        }
    }

    // Delete any existing records that weren't in the submitted data
    foreach ($existing_record_ids as $id => $record) {
        if (!in_array($id, $records_to_keep)) {
            $wpdb->delete($child_table_name, ['id' => $id]);
        }
    }

    return $success;
}
    }



















