<?php
/**
 * User Dashboard Manager
 *
 * Handles user-specific dashboards and data management
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_User_Dashboard_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Create tables if they don't exist
        self::create_tables();

        // Register AJAX handlers
        add_action('wp_ajax_dab_get_user_data', array(__CLASS__, 'ajax_get_user_data'));
        add_action('wp_ajax_nopriv_dab_get_user_data', array(__CLASS__, 'ajax_get_user_data'));

        add_action('wp_ajax_dab_save_user_data', array(__CLASS__, 'ajax_save_user_data'));
        add_action('wp_ajax_nopriv_dab_save_user_data', array(__CLASS__, 'ajax_save_user_data'));

        add_action('wp_ajax_dab_delete_user_data', array(__CLASS__, 'ajax_delete_user_data'));
        add_action('wp_ajax_nopriv_dab_delete_user_data', array(__CLASS__, 'ajax_delete_user_data'));

        add_action('wp_ajax_dab_get_user_stats', array(__CLASS__, 'ajax_get_user_stats'));
        add_action('wp_ajax_nopriv_dab_get_user_stats', array(__CLASS__, 'ajax_get_user_stats'));
    }

    /**
     * Create necessary tables
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // User dashboard configurations
        $dashboard_configs_table = $wpdb->prefix . 'dab_user_dashboard_configs';
        $sql_configs = "CREATE TABLE IF NOT EXISTS $dashboard_configs_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            dashboard_name VARCHAR(255) NOT NULL,
            dashboard_config LONGTEXT NULL,
            is_default TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";

        // User data access log
        $access_log_table = $wpdb->prefix . 'dab_user_access_log';
        $sql_access_log = "CREATE TABLE IF NOT EXISTS $access_log_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            table_id BIGINT(20) UNSIGNED NOT NULL,
            record_id BIGINT(20) UNSIGNED NULL,
            action VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY table_id (table_id),
            KEY action (action),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_configs);
        dbDelta($sql_access_log);
    }

    /**
     * Get user's accessible tables
     */
    public static function get_user_accessible_tables($user_id) {
        global $wpdb;

        $user = DAB_Frontend_Auth::get_user_by_id($user_id);
        if (!$user) {
            return array();
        }

        $tables_table = $wpdb->prefix . 'dab_tables';

        // If user is admin, return all tables
        if ($user->role === 'admin') {
            return $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");
        }

        // For regular users, get tables they have permission to access
        // This integrates with the existing role permissions system
        if (class_exists('DAB_Role_Permissions_Manager')) {
            $accessible_table_ids = DAB_Role_Permissions_Manager::get_user_accessible_tables($user_id);

            if (empty($accessible_table_ids)) {
                return array();
            }

            $placeholders = implode(',', array_fill(0, count($accessible_table_ids), '%d'));
            return $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id IN ($placeholders) ORDER BY table_label",
                $accessible_table_ids
            ));
        }

        return array();
    }

    /**
     * Get user's data from a specific table
     */
    public static function get_user_data($user_id, $table_id, $args = array()) {
        global $wpdb;

        // Verify user has access to this table
        $accessible_tables = self::get_user_accessible_tables($user_id);
        $has_access = false;

        foreach ($accessible_tables as $table) {
            if ($table->id == $table_id) {
                $has_access = true;
                break;
            }
        }

        if (!$has_access) {
            return new WP_Error('access_denied', 'You do not have access to this table.');
        }

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d", $table_id
        ));

        if (!$table_info) {
            return new WP_Error('table_not_found', 'Table not found.');
        }

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Default arguments
        $defaults = array(
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'search' => '',
            'filters' => array()
        );

        $args = wp_parse_args($args, $defaults);

        // Build WHERE clause
        $where = array("user_id = %d");
        $where_values = array($user_id);

        // Add search
        if (!empty($args['search'])) {
            $fields_table = $wpdb->prefix . 'dab_fields';
            $searchable_fields = $wpdb->get_col($wpdb->prepare(
                "SELECT field_slug FROM $fields_table WHERE table_id = %d AND field_type IN ('text', 'textarea', 'email')",
                $table_id
            ));

            if (!empty($searchable_fields)) {
                $search_conditions = array();
                foreach ($searchable_fields as $field) {
                    $search_conditions[] = "`$field` LIKE %s";
                    $where_values[] = '%' . $wpdb->esc_like($args['search']) . '%';
                }
                $where[] = '(' . implode(' OR ', $search_conditions) . ')';
            }
        }

        // Add filters
        foreach ($args['filters'] as $field => $value) {
            if (!empty($value)) {
                $where[] = "`$field` = %s";
                $where_values[] = $value;
            }
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where);
        $order_clause = sprintf('ORDER BY %s %s',
            sanitize_sql_orderby($args['orderby']),
            $args['order'] === 'ASC' ? 'ASC' : 'DESC'
        );
        $limit_clause = sprintf('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);

        $query = "SELECT * FROM $data_table $where_clause $order_clause $limit_clause";
        $records = $wpdb->get_results($wpdb->prepare($query, $where_values));

        // Get total count
        $count_query = "SELECT COUNT(*) FROM $data_table $where_clause";
        $total = $wpdb->get_var($wpdb->prepare($count_query, $where_values));

        // Log access
        self::log_user_access($user_id, $table_id, null, 'view');

        return array(
            'records' => $records,
            'total' => $total,
            'table_info' => $table_info
        );
    }

    /**
     * Save user data to a table
     */
    public static function save_user_data($user_id, $table_id, $data, $record_id = null) {
        global $wpdb;

        // Verify user has access to this table
        $accessible_tables = self::get_user_accessible_tables($user_id);
        $has_access = false;

        foreach ($accessible_tables as $table) {
            if ($table->id == $table_id) {
                $has_access = true;
                break;
            }
        }

        if (!$has_access) {
            return new WP_Error('access_denied', 'You do not have access to this table.');
        }

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d", $table_id
        ));

        if (!$table_info) {
            return new WP_Error('table_not_found', 'Table not found.');
        }

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Prepare data
        $save_data = array();
        foreach ($data as $field => $value) {
            $save_data[sanitize_key($field)] = sanitize_text_field($value);
        }

        // Add user_id and timestamps
        $save_data['user_id'] = $user_id;

        if ($record_id) {
            // Update existing record
            // Verify user owns this record
            $existing = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $data_table WHERE id = %d AND user_id = %d",
                $record_id, $user_id
            ));

            if (!$existing) {
                return new WP_Error('record_not_found', 'Record not found or you do not have permission to edit it.');
            }

            $save_data['updated_at'] = current_time('mysql');
            $result = $wpdb->update($data_table, $save_data, array('id' => $record_id));

            if ($result !== false) {
                self::log_user_access($user_id, $table_id, $record_id, 'update');
                return $record_id;
            }
        } else {
            // Insert new record
            $save_data['created_at'] = current_time('mysql');
            $result = $wpdb->insert($data_table, $save_data);

            if ($result !== false) {
                $new_record_id = $wpdb->insert_id;
                self::log_user_access($user_id, $table_id, $new_record_id, 'create');
                return $new_record_id;
            }
        }

        return new WP_Error('save_failed', 'Failed to save data.');
    }

    /**
     * Delete user data
     */
    public static function delete_user_data($user_id, $table_id, $record_id) {
        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d", $table_id
        ));

        if (!$table_info) {
            return new WP_Error('table_not_found', 'Table not found.');
        }

        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Verify user owns this record
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $data_table WHERE id = %d AND user_id = %d",
            $record_id, $user_id
        ));

        if (!$existing) {
            return new WP_Error('record_not_found', 'Record not found or you do not have permission to delete it.');
        }

        $result = $wpdb->delete($data_table, array('id' => $record_id, 'user_id' => $user_id));

        if ($result !== false) {
            self::log_user_access($user_id, $table_id, $record_id, 'delete');
            return true;
        }

        return new WP_Error('delete_failed', 'Failed to delete record.');
    }

    /**
     * Get user statistics
     */
    public static function get_user_stats($user_id) {
        global $wpdb;

        $accessible_tables = self::get_user_accessible_tables($user_id);
        $stats = array(
            'total_tables' => count($accessible_tables),
            'total_records' => 0,
            'recent_activity' => array(),
            'table_stats' => array()
        );

        foreach ($accessible_tables as $table) {
            $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $data_table WHERE user_id = %d", $user_id
            ));

            $stats['total_records'] += $count;
            $stats['table_stats'][] = array(
                'table_id' => $table->id,
                'table_name' => $table->table_label,
                'record_count' => $count
            );
        }

        // Get recent activity
        $access_log_table = $wpdb->prefix . 'dab_user_access_log';
        $stats['recent_activity'] = $wpdb->get_results($wpdb->prepare(
            "SELECT al.*, t.table_label
             FROM $access_log_table al
             LEFT JOIN {$wpdb->prefix}dab_tables t ON al.table_id = t.id
             WHERE al.user_id = %d
             ORDER BY al.created_at DESC
             LIMIT 10",
            $user_id
        ));

        return $stats;
    }

    /**
     * Log user access
     */
    private static function log_user_access($user_id, $table_id, $record_id, $action) {
        global $wpdb;

        $access_log_table = $wpdb->prefix . 'dab_user_access_log';

        $wpdb->insert($access_log_table, array(
            'user_id' => $user_id,
            'table_id' => $table_id,
            'record_id' => $record_id,
            'action' => $action,
            'ip_address' => self::get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
        ));
    }

    /**
     * Get client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }

    /**
     * AJAX handler for getting user data
     */
    public static function ajax_get_user_data() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $table_id = intval($_POST['table_id'] ?? 0);
        if (!$table_id) {
            wp_send_json_error('Table ID is required');
            return;
        }

        $args = array(
            'limit' => intval($_POST['limit'] ?? 20),
            'offset' => intval($_POST['offset'] ?? 0),
            'search' => sanitize_text_field($_POST['search'] ?? ''),
            'orderby' => sanitize_sql_orderby($_POST['orderby'] ?? 'created_at'),
            'order' => ($_POST['order'] ?? 'DESC') === 'ASC' ? 'ASC' : 'DESC'
        );

        $result = self::get_user_data($current_user->id, $table_id, $args);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success($result);
        }
    }

    /**
     * AJAX handler for saving user data
     */
    public static function ajax_save_user_data() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $table_id = intval($_POST['table_id'] ?? 0);
        $record_id = intval($_POST['record_id'] ?? 0);
        $data = $_POST['data'] ?? array();

        if (!$table_id) {
            wp_send_json_error('Table ID is required');
            return;
        }

        if (empty($data)) {
            wp_send_json_error('Data is required');
            return;
        }

        $result = self::save_user_data($current_user->id, $table_id, $data, $record_id ?: null);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => $record_id ? 'Record updated successfully' : 'Record created successfully',
                'record_id' => $result
            ));
        }
    }

    /**
     * AJAX handler for deleting user data
     */
    public static function ajax_delete_user_data() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $table_id = intval($_POST['table_id'] ?? 0);
        $record_id = intval($_POST['record_id'] ?? 0);

        if (!$table_id || !$record_id) {
            wp_send_json_error('Table ID and Record ID are required');
            return;
        }

        $result = self::delete_user_data($current_user->id, $table_id, $record_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Record deleted successfully');
        }
    }

    /**
     * AJAX handler for getting user statistics
     */
    public static function ajax_get_user_stats() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_frontend_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $stats = self::get_user_stats($current_user->id);
        wp_send_json_success($stats);
    }
}
