<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Get current date range
$date_range = isset($_GET['date_range']) ? sanitize_text_field($_GET['date_range']) : '30days';
$custom_start = isset($_GET['custom_start']) ? sanitize_text_field($_GET['custom_start']) : '';
$custom_end = isset($_GET['custom_end']) ? sanitize_text_field($_GET['custom_end']) : '';

// Get reports
global $wpdb;
$reports_table = $wpdb->prefix . 'dab_wc_custom_reports';
$available_reports = $wpdb->get_results("SELECT * FROM $reports_table WHERE is_active = 1 ORDER BY report_name ASC");

// Get selected report
$selected_report_id = isset($_GET['report_id']) ? intval($_GET['report_id']) : 0;
$selected_report = null;
$report_data = null;

if ($selected_report_id && !empty($available_reports)) {
    foreach ($available_reports as $report) {
        if ($report->id == $selected_report_id) {
            $selected_report = $report;
            break;
        }
    }
}

// If no report selected, use the first available report
if (!$selected_report && !empty($available_reports)) {
    $selected_report = $available_reports[0];
    $selected_report_id = $selected_report->id;
}

// Generate report data if we have a selected report
if ($selected_report) {
    $report_data = DAB_Enhanced_Reporting_Manager::generate_report_data($selected_report->id, $date_range);
}

// Get report statistics
$total_reports = count($available_reports);
$reports_generated_today = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->prefix}dab_wc_report_data 
    WHERE DATE(generated_at) = CURDATE()
");

$data_points_today = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->prefix}dab_wc_analytics_events 
    WHERE DATE(created_at) = CURDATE()
");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Enhanced Reports', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Advanced reporting and analytics with custom reports, data visualization, and business intelligence dashboards.', 'db-app-builder'); ?></p>
    </div>

    <!-- Report Statistics -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($total_reports); ?></div>
            <div class="dab-stat-label"><?php _e('Available Reports', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($reports_generated_today); ?></div>
            <div class="dab-stat-label"><?php _e('Reports Generated Today', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($data_points_today); ?></div>
            <div class="dab-stat-label"><?php _e('Data Points Collected Today', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo $selected_report ? date_i18n(get_option('time_format')) : '--'; ?></div>
            <div class="dab-stat-label"><?php _e('Last Updated', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Report Controls -->
            <div class="dab-card">
                <div class="dab-report-controls">
                    <div class="dab-control-group">
                        <label for="report-selector"><?php _e('Select Report:', 'db-app-builder'); ?></label>
                        <select id="report-selector" onchange="changeReport()">
                            <?php foreach ($available_reports as $report): ?>
                                <option value="<?php echo $report->id; ?>" <?php selected($selected_report_id, $report->id); ?>>
                                    <?php echo esc_html($report->report_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="dab-control-group">
                        <label for="date-range-selector"><?php _e('Date Range:', 'db-app-builder'); ?></label>
                        <select id="date-range-selector" onchange="changeDateRange()">
                            <option value="7days" <?php selected($date_range, '7days'); ?>><?php _e('Last 7 Days', 'db-app-builder'); ?></option>
                            <option value="30days" <?php selected($date_range, '30days'); ?>><?php _e('Last 30 Days', 'db-app-builder'); ?></option>
                            <option value="90days" <?php selected($date_range, '90days'); ?>><?php _e('Last 90 Days', 'db-app-builder'); ?></option>
                            <option value="this_month" <?php selected($date_range, 'this_month'); ?>><?php _e('This Month', 'db-app-builder'); ?></option>
                            <option value="last_month" <?php selected($date_range, 'last_month'); ?>><?php _e('Last Month', 'db-app-builder'); ?></option>
                            <option value="custom" <?php selected($date_range, 'custom'); ?>><?php _e('Custom Range', 'db-app-builder'); ?></option>
                        </select>
                    </div>
                    
                    <div class="dab-control-group dab-custom-date-range" style="display: <?php echo $date_range === 'custom' ? 'block' : 'none'; ?>;">
                        <input type="date" id="custom-start" value="<?php echo esc_attr($custom_start); ?>" placeholder="<?php _e('Start Date', 'db-app-builder'); ?>">
                        <input type="date" id="custom-end" value="<?php echo esc_attr($custom_end); ?>" placeholder="<?php _e('End Date', 'db-app-builder'); ?>">
                    </div>
                    
                    <div class="dab-control-group">
                        <button class="button button-primary" onclick="generateReport()"><?php _e('Generate Report', 'db-app-builder'); ?></button>
                        <button class="button" onclick="exportReport()"><?php _e('Export', 'db-app-builder'); ?></button>
                        <button class="button" onclick="scheduleReport()"><?php _e('Schedule', 'db-app-builder'); ?></button>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <?php if ($selected_report && $report_data): ?>
                <div class="dab-card">
                    <h2><?php echo esc_html($selected_report->report_name); ?></h2>
                    
                    <?php if ($selected_report->report_type === 'sales_dashboard'): ?>
                        <!-- Sales Dashboard Report -->
                        <div class="dab-report-metrics">
                            <div class="dab-metric-card">
                                <div class="dab-metric-value"><?php echo wc_price($report_data['total_sales']); ?></div>
                                <div class="dab-metric-label"><?php _e('Total Sales', 'db-app-builder'); ?></div>
                            </div>
                            
                            <div class="dab-metric-card">
                                <div class="dab-metric-value"><?php echo number_format($report_data['order_count']); ?></div>
                                <div class="dab-metric-label"><?php _e('Total Orders', 'db-app-builder'); ?></div>
                            </div>
                            
                            <div class="dab-metric-card">
                                <div class="dab-metric-value"><?php echo wc_price($report_data['average_order_value']); ?></div>
                                <div class="dab-metric-label"><?php _e('Average Order Value', 'db-app-builder'); ?></div>
                            </div>
                            
                            <div class="dab-metric-card">
                                <div class="dab-metric-value"><?php echo number_format($report_data['conversion_rate'], 2); ?>%</div>
                                <div class="dab-metric-label"><?php _e('Conversion Rate', 'db-app-builder'); ?></div>
                            </div>
                        </div>
                        
                        <div class="dab-chart-container">
                            <canvas id="sales-chart" width="400" height="200"></canvas>
                        </div>
                        
                    <?php else: ?>
                        <!-- Generic Report Display -->
                        <div class="dab-report-content">
                            <p><?php _e('Report data visualization will be implemented based on report type.', 'db-app-builder'); ?></p>
                            <pre><?php echo esc_html(print_r($report_data, true)); ?></pre>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="dab-card">
                    <h2><?php _e('No Report Selected', 'db-app-builder'); ?></h2>
                    <p><?php _e('Please select a report from the dropdown above to view its data.', 'db-app-builder'); ?></p>
                </div>
            <?php endif; ?>

            <!-- Available Reports -->
            <div class="dab-card">
                <h2><?php _e('Available Reports', 'db-app-builder'); ?></h2>
                
                <div class="dab-reports-grid">
                    <?php foreach ($available_reports as $report): ?>
                        <div class="dab-report-card <?php echo $selected_report_id == $report->id ? 'active' : ''; ?>" onclick="selectReport(<?php echo $report->id; ?>)">
                            <h3><?php echo esc_html($report->report_name); ?></h3>
                            <p class="dab-report-type"><?php echo esc_html(ucfirst(str_replace('_', ' ', $report->report_type))); ?></p>
                            <p class="dab-report-schedule">
                                <?php if ($report->schedule_frequency): ?>
                                    <?php printf(__('Scheduled: %s', 'db-app-builder'), ucfirst($report->schedule_frequency)); ?>
                                <?php else: ?>
                                    <?php _e('Manual generation', 'db-app-builder'); ?>
                                <?php endif; ?>
                            </p>
                            <div class="dab-report-actions">
                                <button class="button button-small" onclick="event.stopPropagation(); editReport(<?php echo $report->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                <button class="button button-small" onclick="event.stopPropagation(); duplicateReport(<?php echo $report->id; ?>)"><?php _e('Duplicate', 'db-app-builder'); ?></button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="dab-report-card dab-add-report" onclick="showCreateReportForm()">
                        <div class="dab-add-icon">+</div>
                        <h3><?php _e('Create New Report', 'db-app-builder'); ?></h3>
                        <p><?php _e('Build a custom report with your preferred metrics and visualizations', 'db-app-builder'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Report Insights -->
            <div class="dab-card">
                <h3><?php _e('Report Insights', 'db-app-builder'); ?></h3>
                <?php if ($selected_report): ?>
                    <div class="dab-insights">
                        <p><strong><?php _e('Report Type:', 'db-app-builder'); ?></strong> <?php echo esc_html(ucfirst(str_replace('_', ' ', $selected_report->report_type))); ?></p>
                        <p><strong><?php _e('Last Generated:', 'db-app-builder'); ?></strong> 
                            <?php echo $selected_report->last_generated ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($selected_report->last_generated)) : __('Never', 'db-app-builder'); ?>
                        </p>
                        <p><strong><?php _e('Schedule:', 'db-app-builder'); ?></strong> 
                            <?php echo $selected_report->schedule_frequency ? ucfirst($selected_report->schedule_frequency) : __('Manual', 'db-app-builder'); ?>
                        </p>
                    </div>
                <?php else: ?>
                    <p><?php _e('Select a report to view insights.', 'db-app-builder'); ?></p>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="#" onclick="createCustomReport()"><?php _e('Create Custom Report', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="manageScheduledReports()"><?php _e('Manage Scheduled Reports', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="exportAllReports()"><?php _e('Export All Reports', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="viewDataSources()"><?php _e('View Data Sources', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="setupReportAlerts()"><?php _e('Setup Report Alerts', 'db-app-builder'); ?></a></li>
                </ul>
            </div>

            <!-- Report Types -->
            <div class="dab-card">
                <h3><?php _e('Available Report Types', 'db-app-builder'); ?></h3>
                <ul class="dab-report-types">
                    <li><strong><?php _e('Sales Dashboard', 'db-app-builder'); ?></strong> - <?php _e('Comprehensive sales performance metrics', 'db-app-builder'); ?></li>
                    <li><strong><?php _e('Product Performance', 'db-app-builder'); ?></strong> - <?php _e('Product sales and inventory analysis', 'db-app-builder'); ?></li>
                    <li><strong><?php _e('Customer Analytics', 'db-app-builder'); ?></strong> - <?php _e('Customer behavior and lifetime value', 'db-app-builder'); ?></li>
                    <li><strong><?php _e('Inventory Report', 'db-app-builder'); ?></strong> - <?php _e('Stock levels and movement tracking', 'db-app-builder'); ?></li>
                    <li><strong><?php _e('Marketing Performance', 'db-app-builder'); ?></strong> - <?php _e('Campaign effectiveness and ROI', 'db-app-builder'); ?></li>
                </ul>
            </div>

            <!-- Export Options -->
            <div class="dab-card">
                <h3><?php _e('Export Options', 'db-app-builder'); ?></h3>
                <div class="dab-export-options">
                    <button class="button" onclick="exportToPDF()"><?php _e('Export to PDF', 'db-app-builder'); ?></button>
                    <button class="button" onclick="exportToExcel()"><?php _e('Export to Excel', 'db-app-builder'); ?></button>
                    <button class="button" onclick="exportToCSV()"><?php _e('Export to CSV', 'db-app-builder'); ?></button>
                    <button class="button" onclick="emailReport()"><?php _e('Email Report', 'db-app-builder'); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeReport() {
    var reportId = document.getElementById('report-selector').value;
    var currentUrl = new URL(window.location);
    currentUrl.searchParams.set('report_id', reportId);
    window.location.href = currentUrl.toString();
}

function changeDateRange() {
    var dateRange = document.getElementById('date-range-selector').value;
    var customDateRange = document.querySelector('.dab-custom-date-range');
    
    if (dateRange === 'custom') {
        customDateRange.style.display = 'block';
    } else {
        customDateRange.style.display = 'none';
        updateUrlWithDateRange(dateRange);
    }
}

function updateUrlWithDateRange(dateRange) {
    var currentUrl = new URL(window.location);
    currentUrl.searchParams.set('date_range', dateRange);
    window.location.href = currentUrl.toString();
}

function generateReport() {
    var reportId = document.getElementById('report-selector').value;
    var dateRange = document.getElementById('date-range-selector').value;
    var customStart = document.getElementById('custom-start').value;
    var customEnd = document.getElementById('custom-end').value;
    
    var currentUrl = new URL(window.location);
    currentUrl.searchParams.set('report_id', reportId);
    currentUrl.searchParams.set('date_range', dateRange);
    
    if (dateRange === 'custom') {
        currentUrl.searchParams.set('custom_start', customStart);
        currentUrl.searchParams.set('custom_end', customEnd);
    }
    
    window.location.href = currentUrl.toString();
}

function selectReport(reportId) {
    var currentUrl = new URL(window.location);
    currentUrl.searchParams.set('report_id', reportId);
    window.location.href = currentUrl.toString();
}

function exportReport() {
    // TODO: Implement report export
    alert('<?php _e('Report export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function scheduleReport() {
    // TODO: Implement report scheduling
    alert('<?php _e('Report scheduling will be implemented in the next update.', 'db-app-builder'); ?>');
}

function editReport(reportId) {
    // TODO: Implement report editing
    alert('<?php _e('Report editing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function duplicateReport(reportId) {
    // TODO: Implement report duplication
    alert('<?php _e('Report duplication will be implemented in the next update.', 'db-app-builder'); ?>');
}

function showCreateReportForm() {
    // TODO: Implement create report form
    alert('<?php _e('Create report form will be implemented in the next update.', 'db-app-builder'); ?>');
}

function createCustomReport() {
    // TODO: Implement custom report creation
    alert('<?php _e('Custom report creation will be implemented in the next update.', 'db-app-builder'); ?>');
}

function manageScheduledReports() {
    // TODO: Implement scheduled reports management
    alert('<?php _e('Scheduled reports management will be implemented in the next update.', 'db-app-builder'); ?>');
}

function exportAllReports() {
    // TODO: Implement all reports export
    alert('<?php _e('All reports export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function viewDataSources() {
    // TODO: Implement data sources view
    alert('<?php _e('Data sources view will be implemented in the next update.', 'db-app-builder'); ?>');
}

function setupReportAlerts() {
    // TODO: Implement report alerts setup
    alert('<?php _e('Report alerts setup will be implemented in the next update.', 'db-app-builder'); ?>');
}

function exportToPDF() {
    // TODO: Implement PDF export
    alert('<?php _e('PDF export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function exportToExcel() {
    // TODO: Implement Excel export
    alert('<?php _e('Excel export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function exportToCSV() {
    // TODO: Implement CSV export
    alert('<?php _e('CSV export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function emailReport() {
    // TODO: Implement email report
    alert('<?php _e('Email report will be implemented in the next update.', 'db-app-builder'); ?>');
}

// Initialize chart if we have sales dashboard data
<?php if ($selected_report && $selected_report->report_type === 'sales_dashboard' && $report_data): ?>
document.addEventListener('DOMContentLoaded', function() {
    // Chart initialization would go here
    console.log('Sales dashboard chart would be initialized here');
});
<?php endif; ?>
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-report-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.dab-control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dab-control-group label {
    font-weight: bold;
    font-size: 12px;
}

.dab-custom-date-range {
    display: flex;
    gap: 10px;
}

.dab-report-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-metric-card {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #2271b1;
    margin-bottom: 5px;
}

.dab-metric-label {
    font-size: 12px;
    color: #666;
}

.dab-chart-container {
    margin: 20px 0;
    height: 300px;
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-report-card {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dab-report-card:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dab-report-card.active {
    border-color: #2271b1;
    background: #f0f6fc;
}

.dab-report-card h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    border: none;
    padding: 0;
}

.dab-report-type {
    color: #666;
    font-size: 12px;
    margin: 5px 0;
}

.dab-report-schedule {
    color: #999;
    font-size: 11px;
    margin: 5px 0;
}

.dab-report-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.dab-add-report {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 2px dashed #c3c4c7;
    background: #fff;
}

.dab-add-icon {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 10px;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-report-types {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-report-types li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.dab-export-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-export-options .button {
    text-align: left;
}
</style>
