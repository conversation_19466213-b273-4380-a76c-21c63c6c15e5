<?php
/**
 * Advanced Report Builder Page
 * Phase 3: Data Intelligence & Analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('edit_posts')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Get report ID if editing
$report_id = isset($_GET['report_id']) ? intval($_GET['report_id']) : 0;
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';

?>
<div class="wrap dab-analytics-page">
    <h1 class="wp-heading-inline">
        <?php _e('Advanced Report Builder', 'db-app-builder'); ?>
        <span class="dab-phase-badge">Phase 3</span>
    </h1>
    
    <?php if ($action === 'list'): ?>
        <a href="<?php echo admin_url('admin.php?page=dab_report_builder&action=create'); ?>" class="page-title-action">
            <?php _e('Create New Report', 'db-app-builder'); ?>
        </a>
        
        <div class="dab-analytics-header">
            <div class="dab-analytics-stats">
                <div class="dab-stat-card">
                    <div class="dab-stat-number" id="total-reports">-</div>
                    <div class="dab-stat-label"><?php _e('Total Reports', 'db-app-builder'); ?></div>
                </div>
                <div class="dab-stat-card">
                    <div class="dab-stat-number" id="active-schedules">-</div>
                    <div class="dab-stat-label"><?php _e('Scheduled Reports', 'db-app-builder'); ?></div>
                </div>
                <div class="dab-stat-card">
                    <div class="dab-stat-number" id="executions-today">-</div>
                    <div class="dab-stat-label"><?php _e('Executions Today', 'db-app-builder'); ?></div>
                </div>
            </div>
        </div>

        <div class="dab-reports-toolbar">
            <div class="dab-toolbar-left">
                <select id="report-type-filter">
                    <option value=""><?php _e('All Report Types', 'db-app-builder'); ?></option>
                    <option value="table"><?php _e('Table Reports', 'db-app-builder'); ?></option>
                    <option value="chart"><?php _e('Chart Reports', 'db-app-builder'); ?></option>
                    <option value="dashboard"><?php _e('Dashboard Reports', 'db-app-builder'); ?></option>
                    <option value="pivot"><?php _e('Pivot Tables', 'db-app-builder'); ?></option>
                </select>
                
                <select id="data-source-filter">
                    <option value=""><?php _e('All Data Sources', 'db-app-builder'); ?></option>
                </select>
                
                <input type="text" id="search-reports" placeholder="<?php _e('Search reports...', 'db-app-builder'); ?>">
            </div>
            
            <div class="dab-toolbar-right">
                <button class="button" id="refresh-reports">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'db-app-builder'); ?>
                </button>
                
                <button class="button" id="bulk-actions-btn">
                    <span class="dashicons dashicons-admin-tools"></span>
                    <?php _e('Bulk Actions', 'db-app-builder'); ?>
                </button>
            </div>
        </div>

        <div class="dab-reports-grid" id="reports-grid">
            <div class="dab-loading-spinner">
                <div class="spinner is-active"></div>
                <p><?php _e('Loading reports...', 'db-app-builder'); ?></p>
            </div>
        </div>

    <?php elseif ($action === 'create' || $action === 'edit'): ?>
        <div class="dab-report-builder-container">
            <div class="dab-builder-header">
                <div class="dab-builder-tabs">
                    <button class="dab-tab-btn active" data-tab="data-source">
                        <span class="dashicons dashicons-database"></span>
                        <?php _e('Data Source', 'db-app-builder'); ?>
                    </button>
                    <button class="dab-tab-btn" data-tab="fields">
                        <span class="dashicons dashicons-list-view"></span>
                        <?php _e('Fields & Filters', 'db-app-builder'); ?>
                    </button>
                    <button class="dab-tab-btn" data-tab="visualization">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php _e('Visualization', 'db-app-builder'); ?>
                    </button>
                    <button class="dab-tab-btn" data-tab="settings">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Settings', 'db-app-builder'); ?>
                    </button>
                </div>
                
                <div class="dab-builder-actions">
                    <button class="button" id="preview-report">
                        <span class="dashicons dashicons-visibility"></span>
                        <?php _e('Preview', 'db-app-builder'); ?>
                    </button>
                    <button class="button button-primary" id="save-report">
                        <span class="dashicons dashicons-saved"></span>
                        <?php _e('Save Report', 'db-app-builder'); ?>
                    </button>
                </div>
            </div>

            <div class="dab-builder-content">
                <!-- Data Source Tab -->
                <div class="dab-tab-content active" id="tab-data-source">
                    <div class="dab-section">
                        <h3><?php _e('Select Data Source', 'db-app-builder'); ?></h3>
                        <div class="dab-data-sources-grid" id="data-sources-grid">
                            <!-- Data sources will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dab-section" id="table-schema-section" style="display: none;">
                        <h3><?php _e('Table Schema', 'db-app-builder'); ?></h3>
                        <div class="dab-schema-viewer" id="schema-viewer">
                            <!-- Schema will be displayed here -->
                        </div>
                    </div>
                </div>

                <!-- Fields & Filters Tab -->
                <div class="dab-tab-content" id="tab-fields">
                    <div class="dab-fields-container">
                        <div class="dab-available-fields">
                            <h3><?php _e('Available Fields', 'db-app-builder'); ?></h3>
                            <div class="dab-fields-search">
                                <input type="text" id="fields-search" placeholder="<?php _e('Search fields...', 'db-app-builder'); ?>">
                            </div>
                            <div class="dab-fields-list" id="available-fields-list">
                                <!-- Available fields will be loaded here -->
                            </div>
                        </div>
                        
                        <div class="dab-selected-fields">
                            <h3><?php _e('Selected Fields', 'db-app-builder'); ?></h3>
                            <div class="dab-fields-drop-zone" id="selected-fields-zone">
                                <p class="dab-drop-hint"><?php _e('Drag fields here or click to add', 'db-app-builder'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dab-filters-section">
                        <h3><?php _e('Filters & Conditions', 'db-app-builder'); ?></h3>
                        <div class="dab-filters-builder" id="filters-builder">
                            <button class="button dab-add-filter" id="add-filter-btn">
                                <span class="dashicons dashicons-plus"></span>
                                <?php _e('Add Filter', 'db-app-builder'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Visualization Tab -->
                <div class="dab-tab-content" id="tab-visualization">
                    <div class="dab-viz-options">
                        <h3><?php _e('Visualization Type', 'db-app-builder'); ?></h3>
                        <div class="dab-viz-types">
                            <div class="dab-viz-type active" data-type="table">
                                <span class="dashicons dashicons-list-view"></span>
                                <span><?php _e('Table', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-viz-type" data-type="bar">
                                <span class="dashicons dashicons-chart-bar"></span>
                                <span><?php _e('Bar Chart', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-viz-type" data-type="line">
                                <span class="dashicons dashicons-chart-line"></span>
                                <span><?php _e('Line Chart', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-viz-type" data-type="pie">
                                <span class="dashicons dashicons-chart-pie"></span>
                                <span><?php _e('Pie Chart', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-viz-type" data-type="pivot">
                                <span class="dashicons dashicons-grid-view"></span>
                                <span><?php _e('Pivot Table', 'db-app-builder'); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dab-viz-config" id="visualization-config">
                        <!-- Visualization configuration options will be loaded here -->
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="dab-tab-content" id="tab-settings">
                    <div class="dab-settings-grid">
                        <div class="dab-settings-section">
                            <h3><?php _e('Report Information', 'db-app-builder'); ?></h3>
                            <table class="form-table">
                                <tr>
                                    <th><label for="report-name"><?php _e('Report Name', 'db-app-builder'); ?></label></th>
                                    <td><input type="text" id="report-name" class="regular-text" required></td>
                                </tr>
                                <tr>
                                    <th><label for="report-description"><?php _e('Description', 'db-app-builder'); ?></label></th>
                                    <td><textarea id="report-description" rows="3" class="large-text"></textarea></td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="dab-settings-section">
                            <h3><?php _e('Access & Permissions', 'db-app-builder'); ?></h3>
                            <table class="form-table">
                                <tr>
                                    <th><label for="is-public"><?php _e('Public Access', 'db-app-builder'); ?></label></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" id="is-public">
                                            <?php _e('Allow public access to this report', 'db-app-builder'); ?>
                                        </label>
                                    </td>
                                </tr>
                                <tr>
                                    <th><label for="user-roles"><?php _e('User Roles', 'db-app-builder'); ?></label></th>
                                    <td>
                                        <select id="user-roles" multiple>
                                            <?php
                                            $roles = wp_roles()->get_names();
                                            foreach ($roles as $role_key => $role_name) {
                                                echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role_name) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="dab-settings-section">
                            <h3><?php _e('Scheduling', 'db-app-builder'); ?></h3>
                            <table class="form-table">
                                <tr>
                                    <th><label for="enable-scheduling"><?php _e('Enable Scheduling', 'db-app-builder'); ?></label></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" id="enable-scheduling">
                                            <?php _e('Enable automated report generation', 'db-app-builder'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Modal -->
            <div class="dab-modal" id="preview-modal" style="display: none;">
                <div class="dab-modal-content">
                    <div class="dab-modal-header">
                        <h2><?php _e('Report Preview', 'db-app-builder'); ?></h2>
                        <button class="dab-modal-close">&times;</button>
                    </div>
                    <div class="dab-modal-body" id="preview-content">
                        <!-- Preview content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

    <?php endif; ?>
</div>

<style>
.dab-analytics-page {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

.dab-phase-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 10px;
}

.dab-analytics-header {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-analytics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.dab-stat-card {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.dab-stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.dab-stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.dab-reports-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-toolbar-right {
    display: flex;
    gap: 10px;
}

.dab-reports-grid {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 400px;
}

.dab-loading-spinner {
    text-align: center;
    padding: 50px;
}

.dab-report-builder-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dab-builder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
    background: #f8f9fa;
}

.dab-builder-tabs {
    display: flex;
    gap: 5px;
}

.dab-tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dab-tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.dab-tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dab-builder-actions {
    display: flex;
    gap: 10px;
}

.dab-builder-content {
    padding: 30px;
}

.dab-tab-content {
    display: none;
}

.dab-tab-content.active {
    display: block;
}

.dab-section {
    margin-bottom: 30px;
}

.dab-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.2em;
}

.dab-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
}

.dab-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
}

.dab-modal-body {
    padding: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize the report builder
    DAB_ReportBuilder.init();
});
</script>
