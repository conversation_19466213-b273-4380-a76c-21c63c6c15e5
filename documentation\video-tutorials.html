<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Video Tutorials</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .video-container {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .video-container h3 {
            margin-top: 0;
            color: #3498db;
        }
        .video-container p {
            margin-bottom: 15px;
        }
        .video-frame {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
            max-width: 100%;
        }
        .video-frame iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .video-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .video-card-content {
            padding: 15px;
        }
        .video-card h3 {
            margin-top: 0;
            font-size: 18px;
        }
        .video-card p {
            font-size: 14px;
            color: #666;
        }
        .video-thumbnail {
            width: 100%;
            height: auto;
            display: block;
        }
        .note {
            background-color: #f1f9ff;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Database App Builder - Video Tutorials</h1>
    <p>These video tutorials will help you learn how to use Database App Builder effectively. Watch them in sequence for a complete understanding of the plugin, or jump to specific tutorials for the features you're interested in.</p>

    <div class="note">
        <p><strong>Note:</strong> These videos are placeholders. Replace them with actual tutorial videos for your plugin.</p>
    </div>

    <h2>Getting Started</h2>
    <div class="video-container">
        <h3>Introduction to Database App Builder</h3>
        <p>This video provides an overview of Database App Builder and its key features.</p>
        <div class="video-frame">
            <!-- Replace with your actual YouTube embed code -->
            <iframe width="560" height="315" src="https://www.youtube.com/embed/your-video-id" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
    </div>

    <div class="video-container">
        <h3>Installation and Setup</h3>
        <p>Learn how to install and set up Database App Builder on your WordPress site.</p>
        <div class="video-frame">
            <!-- Replace with your actual YouTube embed code -->
            <iframe width="560" height="315" src="https://www.youtube.com/embed/your-video-id" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
    </div>

    <h2>Core Features</h2>
    <div class="video-grid">
        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Creating+Tables" alt="Creating Tables" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Creating Tables</h3>
                <p>Learn how to create and manage database tables.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Managing+Fields" alt="Managing Fields" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Managing Fields</h3>
                <p>Learn how to add and configure fields for your tables.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Building+Forms" alt="Building Forms" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Building Forms</h3>
                <p>Learn how to create forms to collect data.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Creating+Views" alt="Creating Views" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Creating Views</h3>
                <p>Learn how to create views to display your data.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Building+Dashboards" alt="Building Dashboards" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Building Dashboards</h3>
                <p>Learn how to create interactive dashboards.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Setting+Up+Approval+Workflows" alt="Setting Up Approval Workflows" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Setting Up Approval Workflows</h3>
                <p>Learn how to create multi-level approval processes.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>
    </div>

    <h2>Advanced Features</h2>
    <div class="video-grid">
        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Role-Based+Permissions" alt="Role-Based Permissions" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Role-Based Permissions</h3>
                <p>Learn how to set up permissions for different user roles.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Conditional+Logic" alt="Conditional Logic" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Conditional Logic</h3>
                <p>Learn how to create dynamic forms with conditional logic.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Formula+Fields" alt="Formula Fields" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Formula Fields</h3>
                <p>Learn how to create calculated fields.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Payment+Integration" alt="Payment Integration" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Payment Integration</h3>
                <p>Learn how to accept payments through your forms.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>
    </div>

    <h2>Integrations</h2>
    <div class="video-grid">
        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Google+Sheets+Integration" alt="Google Sheets Integration" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Google Sheets Integration</h3>
                <p>Learn how to sync data with Google Sheets.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>

        <div class="video-card">
            <img src="https://via.placeholder.com/300x169?text=Zapier+Integration" alt="Zapier Integration" class="video-thumbnail">
            <div class="video-card-content">
                <h3>Zapier Integration</h3>
                <p>Learn how to connect with thousands of apps through Zapier.</p>
                <a href="#" onclick="alert('Replace with actual video link')">Watch Video</a>
            </div>
        </div>
    </div>

    <p><a href="index.html">Back to Full Documentation</a></p>
</body>
</html>
