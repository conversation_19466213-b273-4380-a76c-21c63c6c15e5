/**
 * Conditional Logic Field Styles
 *
 * Styles for conditional logic fields in the Database App Builder plugin.
 */

/* Conditional Logic Field Container */
.dab-conditional-logic-field {
    margin-bottom: 15px;
    max-width: 100%;
}

/* Value Display */
.dab-conditional-logic-value {
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    font-weight: 500;
}

.dab-boolean-true {
    color: #28a745;
    font-weight: bold;
}

.dab-boolean-false {
    color: #dc3545;
    font-weight: bold;
}

/* Toggle Button */
.dab-conditional-logic-toggle {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    background-color: #f0f0f1;
    color: #2271b1;
    border: 1px solid #2271b1;
    padding: 5px 10px;
    cursor: pointer;
    font-weight: 500;
}

/* Editor Panel */
.dab-conditional-logic-editor-panel {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    display: none;
    width: 100%;
    max-width: 800px;
    z-index: 100;
    position: relative;
}

/* Editor Header */
.dab-conditional-logic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
}

.dab-conditional-logic-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.dab-conditional-logic-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 0;
    font-size: 20px;
    line-height: 1;
}

.dab-conditional-logic-close:hover {
    color: #dc3545;
}

/* Editor Body */
.dab-conditional-logic-body {
    padding: 15px;
}

/* If-Else Logic */
.dab-conditional-logic-if-else {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-conditional-logic-rules {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-conditional-logic-rule {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-condition {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.dab-conditional-logic-if {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-field-select,
.dab-conditional-logic-operator,
.dab-conditional-logic-value-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
    min-width: 120px;
}

.dab-conditional-logic-result {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-top: 5px;
}

.dab-conditional-logic-then {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-result-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
}

.dab-conditional-logic-actions {
    display: flex;
    gap: 5px;
    margin-top: 10px;
    justify-content: flex-end;
}

.dab-conditional-logic-add-rule,
.dab-conditional-logic-remove-rule {
    padding: 5px !important;
    min-height: 30px;
    line-height: 1 !important;
}

.dab-conditional-logic-add-rule .dashicons,
.dab-conditional-logic-remove-rule .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.dab-conditional-logic-else {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-else-label {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-else-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
}

/* Switch-Case Logic */
.dab-conditional-logic-switch {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-conditional-logic-switch-field {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-switch-label {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-switch-field-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
}

.dab-conditional-logic-cases {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-conditional-logic-case {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-case-value,
.dab-conditional-logic-case-result {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.dab-conditional-logic-case-label,
.dab-conditional-logic-case-result-label {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-case-input,
.dab-conditional-logic-case-result-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
}

.dab-conditional-logic-case-actions {
    display: flex;
    gap: 5px;
}

.dab-conditional-logic-add-case,
.dab-conditional-logic-remove-case {
    padding: 5px !important;
    min-height: 30px;
    line-height: 1 !important;
}

.dab-conditional-logic-add-case .dashicons,
.dab-conditional-logic-remove-case .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.dab-conditional-logic-default {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-default-label {
    font-weight: 600;
    color: #0073aa;
}

.dab-conditional-logic-default-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
}

/* Formula-Based Logic */
.dab-conditional-logic-formula {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-conditional-logic-formula-editor {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-formula-input {
    width: 100%;
    min-height: 100px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
}

.dab-conditional-logic-formula-fields {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-conditional-logic-formula-fields h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.dab-conditional-logic-formula-fields ul {
    margin: 0;
    padding-left: 20px;
}

.dab-conditional-logic-formula-fields li {
    margin-bottom: 5px;
}

.dab-conditional-logic-formula-fields code {
    background-color: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Editor Footer */
.dab-conditional-logic-footer {
    padding: 10px 15px;
    border-top: 1px solid #ddd;
    background-color: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Admin-specific styles */
.wp-admin .dab-conditional-logic-field {
    max-width: 800px;
}

/* Frontend form-specific styles */
.dab-form .dab-conditional-logic-field {
    margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-conditional-logic-condition,
    .dab-conditional-logic-result,
    .dab-conditional-logic-else,
    .dab-conditional-logic-switch-field,
    .dab-conditional-logic-case,
    .dab-conditional-logic-default {
        flex-direction: column;
        align-items: flex-start;
    }

    .dab-conditional-logic-field-select,
    .dab-conditional-logic-operator,
    .dab-conditional-logic-value-input,
    .dab-conditional-logic-result-input,
    .dab-conditional-logic-else-input,
    .dab-conditional-logic-switch-field-select,
    .dab-conditional-logic-case-input,
    .dab-conditional-logic-case-result-input,
    .dab-conditional-logic-default-input {
        width: 100%;
    }
}
