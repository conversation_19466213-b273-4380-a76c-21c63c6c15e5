/**
 * Calendar View JavaScript
 * 
 * Handles calendar field functionality
 */

(function($) {
    'use strict';

    // Calendar View Object
    window.DABCalendarView = {
        currentDate: new Date(),
        selectedDate: null,
        events: [],
        
        init: function() {
            this.bindEvents();
            this.render();
            this.loadEvents();
        },

        bindEvents: function() {
            // Navigation buttons
            $(document).on('click', '.dab-calendar-nav-btn.prev', this.previousMonth.bind(this));
            $(document).on('click', '.dab-calendar-nav-btn.next', this.nextMonth.bind(this));
            
            // Add event button
            $(document).on('click', '.dab-add-event-btn', this.showAddEventModal.bind(this));
            
            // Day click
            $(document).on('click', '.dab-calendar-day', this.selectDay.bind(this));
            
            // Event click
            $(document).on('click', '.dab-calendar-event', this.showEventDetails.bind(this));
            
            // Modal events
            $(document).on('click', '.dab-event-modal-close', this.closeModal.bind(this));
            $(document).on('click', '.dab-event-modal', function(e) {
                if (e.target === this) {
                    window.DABCalendarView.closeModal();
                }
            });
            
            // Form submission
            $(document).on('submit', '#dab-event-form', this.saveEvent.bind(this));
            
            // Delete event
            $(document).on('click', '.dab-delete-event-btn', this.deleteEvent.bind(this));
        },

        render: function() {
            const calendar = $('.dab-calendar-view');
            if (calendar.length === 0) return;

            const monthYear = this.currentDate.toLocaleDateString('en-US', { 
                month: 'long', 
                year: 'numeric' 
            });
            
            calendar.find('.dab-calendar-month-year').text(monthYear);
            
            this.renderCalendarGrid();
        },

        renderCalendarGrid: function() {
            const grid = $('.dab-calendar-grid');
            if (grid.length === 0) return;

            // Clear existing content except headers
            grid.find('.dab-calendar-day').remove();

            const year = this.currentDate.getFullYear();
            const month = this.currentDate.getMonth();
            
            // Get first day of month and number of days
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const daysInMonth = lastDay.getDate();
            const startingDayOfWeek = firstDay.getDay();

            // Add previous month's trailing days
            const prevMonth = new Date(year, month - 1, 0);
            for (let i = startingDayOfWeek - 1; i >= 0; i--) {
                const day = prevMonth.getDate() - i;
                this.renderDay(day, true, new Date(year, month - 1, day));
            }

            // Add current month's days
            for (let day = 1; day <= daysInMonth; day++) {
                this.renderDay(day, false, new Date(year, month, day));
            }

            // Add next month's leading days
            const totalCells = grid.find('.dab-calendar-day').length;
            const remainingCells = 42 - totalCells; // 6 rows × 7 days
            for (let day = 1; day <= remainingCells; day++) {
                this.renderDay(day, true, new Date(year, month + 1, day));
            }
        },

        renderDay: function(dayNumber, isOtherMonth, date) {
            const grid = $('.dab-calendar-grid');
            const today = new Date();
            const isToday = date.toDateString() === today.toDateString();
            const isSelected = this.selectedDate && date.toDateString() === this.selectedDate.toDateString();

            let dayClass = 'dab-calendar-day';
            if (isOtherMonth) dayClass += ' other-month';
            if (isToday) dayClass += ' today';
            if (isSelected) dayClass += ' selected';

            const dayEvents = this.getEventsForDate(date);
            let eventsHtml = '';
            
            dayEvents.forEach(event => {
                const eventClass = `dab-calendar-event event-category-${event.category || 'default'}`;
                eventsHtml += `<div class="${eventClass}" data-event-id="${event.id}">${event.title}</div>`;
            });

            const dayHtml = `
                <div class="${dayClass}" data-date="${date.toISOString().split('T')[0]}">
                    <div class="dab-calendar-day-number">${dayNumber}</div>
                    <div class="dab-calendar-events">${eventsHtml}</div>
                </div>
            `;

            grid.append(dayHtml);
        },

        getEventsForDate: function(date) {
            const dateStr = date.toISOString().split('T')[0];
            return this.events.filter(event => {
                const eventDate = new Date(event.date).toISOString().split('T')[0];
                return eventDate === dateStr;
            });
        },

        previousMonth: function() {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.render();
        },

        nextMonth: function() {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.render();
        },

        selectDay: function(e) {
            const dayElement = $(e.currentTarget);
            const dateStr = dayElement.data('date');
            this.selectedDate = new Date(dateStr);
            
            // Update visual selection
            $('.dab-calendar-day').removeClass('selected');
            dayElement.addClass('selected');
        },

        showAddEventModal: function() {
            this.showEventModal();
        },

        showEventModal: function(event = null) {
            let modal = $('#dab-event-modal');
            
            if (modal.length === 0) {
                modal = this.createEventModal();
            }

            // Reset form
            const form = modal.find('#dab-event-form')[0];
            if (form) form.reset();

            if (event) {
                // Edit mode
                modal.find('.dab-event-modal-title').text('Edit Event');
                modal.find('#event-title').val(event.title);
                modal.find('#event-description').val(event.description);
                modal.find('#event-date').val(event.date);
                modal.find('#event-start-time').val(event.start_time);
                modal.find('#event-end-time').val(event.end_time);
                modal.find('#event-category').val(event.category);
                modal.find('#event-all-day').prop('checked', event.all_day);
                modal.find('#event-id').val(event.id);
            } else {
                // Add mode
                modal.find('.dab-event-modal-title').text('Add Event');
                if (this.selectedDate) {
                    modal.find('#event-date').val(this.selectedDate.toISOString().split('T')[0]);
                }
                modal.find('#event-id').val('');
            }

            modal.show();
        },

        createEventModal: function() {
            const modalHtml = `
                <div id="dab-event-modal" class="dab-event-modal">
                    <div class="dab-event-modal-content">
                        <div class="dab-event-modal-header">
                            <h3 class="dab-event-modal-title">Add Event</h3>
                            <button type="button" class="dab-event-modal-close">&times;</button>
                        </div>
                        <div class="dab-event-modal-body">
                            <form id="dab-event-form">
                                <input type="hidden" id="event-id" name="event_id">
                                
                                <div class="dab-event-form-group">
                                    <label for="event-title">Title</label>
                                    <input type="text" id="event-title" name="title" class="dab-event-form-control" required>
                                </div>
                                
                                <div class="dab-event-form-group">
                                    <label for="event-description">Description</label>
                                    <textarea id="event-description" name="description" class="dab-event-form-control textarea"></textarea>
                                </div>
                                
                                <div class="dab-event-form-group">
                                    <label for="event-date">Date</label>
                                    <input type="date" id="event-date" name="date" class="dab-event-form-control" required>
                                </div>
                                
                                <div class="dab-event-checkbox-group">
                                    <input type="checkbox" id="event-all-day" name="all_day" class="dab-event-checkbox">
                                    <label for="event-all-day">All Day</label>
                                </div>
                                
                                <div class="dab-event-form-group">
                                    <label for="event-start-time">Start Time</label>
                                    <input type="time" id="event-start-time" name="start_time" class="dab-event-form-control">
                                </div>
                                
                                <div class="dab-event-form-group">
                                    <label for="event-end-time">End Time</label>
                                    <input type="time" id="event-end-time" name="end_time" class="dab-event-form-control">
                                </div>
                                
                                <div class="dab-event-form-group">
                                    <label for="event-category">Category</label>
                                    <select id="event-category" name="category" class="dab-event-form-control">
                                        <option value="default">Default</option>
                                        <option value="work">Work</option>
                                        <option value="personal">Personal</option>
                                        <option value="important">Important</option>
                                        <option value="meeting">Meeting</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="dab-event-modal-footer">
                            <button type="button" class="dab-event-btn dab-event-btn-secondary dab-event-modal-close">Cancel</button>
                            <button type="button" class="dab-event-btn dab-event-btn-danger dab-delete-event-btn" style="display: none;">Delete</button>
                            <button type="submit" form="dab-event-form" class="dab-event-btn dab-event-btn-primary">Save Event</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            return $('#dab-event-modal');
        },

        closeModal: function() {
            $('#dab-event-modal').hide();
        },

        saveEvent: function(e) {
            e.preventDefault();
            
            const form = $('#dab-event-form');
            const formData = new FormData(form[0]);
            
            // Add AJAX data
            formData.append('action', 'dab_save_calendar_event');
            formData.append('nonce', dabCalendarData.nonce);

            $.ajax({
                url: dabCalendarData.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        window.DABCalendarView.loadEvents();
                        window.DABCalendarView.closeModal();
                        window.DABCalendarView.showNotification('Event saved successfully!');
                    } else {
                        window.DABCalendarView.showNotification('Error saving event: ' + response.data, 'error');
                    }
                },
                error: function() {
                    window.DABCalendarView.showNotification('Error saving event', 'error');
                }
            });
        },

        loadEvents: function() {
            if (typeof dabCalendarData === 'undefined') {
                console.warn('DAB Calendar: dabCalendarData not available');
                return;
            }

            $.ajax({
                url: dabCalendarData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_calendar_events',
                    nonce: dabCalendarData.nonce,
                    month: this.currentDate.getMonth() + 1,
                    year: this.currentDate.getFullYear()
                },
                success: function(response) {
                    if (response.success) {
                        window.DABCalendarView.events = response.data || [];
                        window.DABCalendarView.render();
                    }
                },
                error: function() {
                    console.warn('DAB Calendar: Error loading events');
                }
            });
        },

        showNotification: function(message, type = 'success') {
            const notification = $(`
                <div class="dab-calendar-notification dab-notification-${type}">
                    ${message}
                </div>
            `);

            $('body').append(notification);

            setTimeout(function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            }, 3000);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        if ($('.dab-calendar-view').length > 0) {
            window.DABCalendarView.init();
        }
    });

})(jQuery);
