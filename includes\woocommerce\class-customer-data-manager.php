<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Customer Data Manager
 *
 * Manages enhanced customer data and integrates with DAB user management
 */
class DAB_Customer_Data_Manager {

    /**
     * Initialize the Customer Data Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Add customer profile fields
        add_action('show_user_profile', array(__CLASS__, 'add_customer_profile_fields'));
        add_action('edit_user_profile', array(__CLASS__, 'add_customer_profile_fields'));

        // Save customer profile fields
        add_action('personal_options_update', array(__CLASS__, 'save_customer_profile_fields'));
        add_action('edit_user_profile_update', array(__CLASS__, 'save_customer_profile_fields'));

        // Add customer data to WooCommerce account page
        add_action('woocommerce_edit_account_form', array(__CLASS__, 'add_account_fields'));
        add_action('woocommerce_save_account_details', array(__CLASS__, 'save_account_fields'));

        // Customer registration fields
        add_action('woocommerce_register_form', array(__CLASS__, 'add_registration_fields'));
        add_action('woocommerce_created_customer', array(__CLASS__, 'save_registration_fields'));

        // Customer data sync with DAB
        add_action('woocommerce_checkout_update_user_meta', array(__CLASS__, 'sync_checkout_data'));

        // Add customer segments functionality
        add_action('init', array(__CLASS__, 'init_customer_segments'));

        // Handle AJAX requests
        add_action('wp_ajax_dab_get_customer_segments', array(__CLASS__, 'ajax_get_customer_segments'));
        add_action('wp_ajax_dab_create_customer_segment', array(__CLASS__, 'ajax_create_customer_segment'));
    }

    /**
     * Create database tables for customer data
     */
    public static function create_tables() {
        global $wpdb;

        // Customer extended data table
        $table_name = $wpdb->prefix . 'dab_wc_customer_data';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            field_name varchar(255) NOT NULL,
            field_value longtext,
            field_type varchar(50) DEFAULT 'text',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_field (user_id, field_name),
            KEY user_id (user_id),
            KEY field_name (field_name)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Customer segments table
        $segments_table = $wpdb->prefix . 'dab_wc_customer_segments';

        $sql_segments = "CREATE TABLE $segments_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            segment_name varchar(255) NOT NULL,
            segment_description text,
            segment_criteria longtext,
            segment_color varchar(7) DEFAULT '#007cba',
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY segment_name (segment_name)
        ) $charset_collate;";

        dbDelta($sql_segments);

        // Customer segment assignments table
        $assignments_table = $wpdb->prefix . 'dab_wc_customer_segment_assignments';

        $sql_assignments = "CREATE TABLE $assignments_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            segment_id mediumint(9) NOT NULL,
            assigned_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_segment (user_id, segment_id),
            KEY user_id (user_id),
            KEY segment_id (segment_id)
        ) $charset_collate;";

        dbDelta($sql_assignments);
    }

    /**
     * Add customer profile fields to admin user edit screen
     */
    public static function add_customer_profile_fields($user) {
        if (!current_user_can('edit_user', $user->ID)) {
            return;
        }

        $customer_data = self::get_customer_data($user->ID);
        $field_configs = self::get_customer_field_configs();

        echo '<h3>' . __('Customer Data (Database App Builder)', 'db-app-builder') . '</h3>';
        echo '<table class="form-table">';

        foreach ($field_configs as $field_config) {
            $field_name = $field_config['name'];
            $field_value = isset($customer_data[$field_name]) ? $customer_data[$field_name] : '';

            echo '<tr>';
            echo '<th><label for="dab_customer_' . esc_attr($field_name) . '">' . esc_html($field_config['label']) . '</label></th>';
            echo '<td>';

            self::render_customer_field($field_config, $field_value, 'dab_customer_' . $field_name);

            if (!empty($field_config['description'])) {
                echo '<p class="description">' . esc_html($field_config['description']) . '</p>';
            }

            echo '</td>';
            echo '</tr>';
        }

        echo '</table>';

        // Customer segments section
        echo '<h3>' . __('Customer Segments', 'db-app-builder') . '</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th>' . __('Assigned Segments', 'db-app-builder') . '</th>';
        echo '<td>';

        $user_segments = self::get_user_segments($user->ID);
        $all_segments = self::get_all_segments();

        foreach ($all_segments as $segment) {
            $checked = in_array($segment->id, $user_segments) ? 'checked' : '';
            echo '<label>';
            echo '<input type="checkbox" name="dab_customer_segments[]" value="' . esc_attr($segment->id) . '" ' . $checked . ' />';
            echo ' ' . esc_html($segment->segment_name);
            echo '</label><br>';
        }

        echo '</td>';
        echo '</tr>';
        echo '</table>';
    }

    /**
     * Save customer profile fields
     */
    public static function save_customer_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return;
        }

        $field_configs = self::get_customer_field_configs();

        foreach ($field_configs as $field_config) {
            $field_name = 'dab_customer_' . $field_config['name'];

            if (isset($_POST[$field_name])) {
                $field_value = sanitize_text_field($_POST[$field_name]);
                self::save_customer_field_value($user_id, $field_config['name'], $field_value, $field_config['type']);
            }
        }

        // Save customer segments
        if (isset($_POST['dab_customer_segments'])) {
            $segments = array_map('intval', $_POST['dab_customer_segments']);
            self::update_user_segments($user_id, $segments);
        } else {
            self::update_user_segments($user_id, array());
        }
    }

    /**
     * Get customer field configurations
     */
    public static function get_customer_field_configs() {
        $default_fields = array(
            array(
                'name' => 'company',
                'label' => __('Company', 'db-app-builder'),
                'type' => 'text',
                'description' => __('Customer company name', 'db-app-builder')
            ),
            array(
                'name' => 'phone_secondary',
                'label' => __('Secondary Phone', 'db-app-builder'),
                'type' => 'tel',
                'description' => __('Alternative phone number', 'db-app-builder')
            ),
            array(
                'name' => 'date_of_birth',
                'label' => __('Date of Birth', 'db-app-builder'),
                'type' => 'date',
                'description' => __('Customer date of birth', 'db-app-builder')
            ),
            array(
                'name' => 'customer_type',
                'label' => __('Customer Type', 'db-app-builder'),
                'type' => 'select',
                'options' => array(
                    'individual' => __('Individual', 'db-app-builder'),
                    'business' => __('Business', 'db-app-builder'),
                    'wholesale' => __('Wholesale', 'db-app-builder')
                ),
                'description' => __('Type of customer', 'db-app-builder')
            ),
            array(
                'name' => 'preferred_contact_method',
                'label' => __('Preferred Contact Method', 'db-app-builder'),
                'type' => 'select',
                'options' => array(
                    'email' => __('Email', 'db-app-builder'),
                    'phone' => __('Phone', 'db-app-builder'),
                    'sms' => __('SMS', 'db-app-builder')
                ),
                'description' => __('How the customer prefers to be contacted', 'db-app-builder')
            ),
            array(
                'name' => 'marketing_consent',
                'label' => __('Marketing Consent', 'db-app-builder'),
                'type' => 'checkbox',
                'description' => __('Customer has consented to marketing communications', 'db-app-builder')
            ),
            array(
                'name' => 'customer_notes',
                'label' => __('Customer Notes', 'db-app-builder'),
                'type' => 'textarea',
                'description' => __('Internal notes about the customer', 'db-app-builder')
            )
        );

        return apply_filters('dab_customer_field_configs', $default_fields);
    }

    /**
     * Render customer field
     */
    public static function render_customer_field($field_config, $value, $field_name) {
        $field_type = $field_config['type'];

        switch ($field_type) {
            case 'text':
            case 'tel':
            case 'email':
            case 'url':
                echo '<input type="' . esc_attr($field_type) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" class="regular-text" />';
                break;

            case 'date':
                echo '<input type="date" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" class="regular-text" />';
                break;

            case 'textarea':
                echo '<textarea name="' . esc_attr($field_name) . '" rows="4" class="large-text">' . esc_textarea($value) . '</textarea>';
                break;

            case 'select':
                echo '<select name="' . esc_attr($field_name) . '">';
                echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';
                if (!empty($field_config['options'])) {
                    foreach ($field_config['options'] as $option_value => $option_label) {
                        $selected = selected($value, $option_value, false);
                        echo '<option value="' . esc_attr($option_value) . '" ' . $selected . '>' . esc_html($option_label) . '</option>';
                    }
                }
                echo '</select>';
                break;

            case 'checkbox':
                $checked = checked($value, '1', false);
                echo '<input type="checkbox" name="' . esc_attr($field_name) . '" value="1" ' . $checked . ' />';
                break;
        }
    }

    /**
     * Get customer data for a user
     */
    public static function get_customer_data($user_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_data';

        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT field_name, field_value FROM $table_name WHERE user_id = %d",
                $user_id
            )
        );

        $data = array();
        foreach ($results as $result) {
            $data[$result->field_name] = $result->field_value;
        }

        return $data;
    }

    /**
     * Save customer field value
     */
    public static function save_customer_field_value($user_id, $field_name, $field_value, $field_type = 'text') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_data';

        $wpdb->replace(
            $table_name,
            array(
                'user_id' => $user_id,
                'field_name' => $field_name,
                'field_value' => $field_value,
                'field_type' => $field_type,
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%s')
        );
    }

    /**
     * Get user segments
     */
    public static function get_user_segments($user_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_segment_assignments';

        $results = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT segment_id FROM $table_name WHERE user_id = %d",
                $user_id
            )
        );

        return array_map('intval', $results);
    }

    /**
     * Get all segments
     */
    public static function get_all_segments() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_segments';

        return $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY segment_name ASC"
        );
    }

    /**
     * Update user segments
     */
    public static function update_user_segments($user_id, $segment_ids) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_segment_assignments';

        // Remove existing assignments
        $wpdb->delete($table_name, array('user_id' => $user_id), array('%d'));

        // Add new assignments
        foreach ($segment_ids as $segment_id) {
            $wpdb->insert(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'segment_id' => $segment_id,
                    'assigned_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s')
            );
        }
    }

    /**
     * Initialize customer segments
     */
    public static function init_customer_segments() {
        // Create default segments if they don't exist
        self::create_default_segments();
    }

    /**
     * Create default customer segments
     */
    public static function create_default_segments() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_customer_segments';

        $default_segments = array(
            array(
                'segment_name' => 'VIP Customers',
                'segment_description' => 'High-value customers with multiple orders',
                'segment_color' => '#FFD700',
                'segment_criteria' => serialize(array('min_orders' => 5, 'min_spent' => 500))
            ),
            array(
                'segment_name' => 'New Customers',
                'segment_description' => 'Customers who registered in the last 30 days',
                'segment_color' => '#00A32A',
                'segment_criteria' => serialize(array('registered_days' => 30))
            ),
            array(
                'segment_name' => 'Inactive Customers',
                'segment_description' => 'Customers who haven\'t ordered in the last 90 days',
                'segment_color' => '#D63638',
                'segment_criteria' => serialize(array('last_order_days' => 90))
            )
        );

        foreach ($default_segments as $segment) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE segment_name = %s",
                    $segment['segment_name']
                )
            );

            if (!$existing) {
                $wpdb->insert($table_name, $segment);
            }
        }
    }
}
