<?php
/**
 * Application Creation Wizard - Step 2: Table Structure
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get saved data
$table_name = isset($progress['data']['table_name']) ? $progress['data']['table_name'] : '';
$table_slug = isset($progress['data']['table_slug']) ? $progress['data']['table_slug'] : '';
$table_description = isset($progress['data']['table_description']) ? $progress['data']['table_description'] : '';
$app_type = isset($progress['data']['app_type']) ? $progress['data']['app_type'] : 'custom';

// Generate table name and slug based on app type if not provided
if (empty($table_name) && !empty($app_type) && $app_type !== 'custom') {
    $app_name = isset($progress['data']['app_name']) ? $progress['data']['app_name'] : '';

    if (!empty($app_name)) {
        switch ($app_type) {
            case 'crm':
                $table_name = $app_name . ' Contacts';
                break;
            case 'project':
                $table_name = $app_name . ' Tasks';
                break;
            case 'inventory':
                $table_name = $app_name . ' Products';
                break;
            default:
                $table_name = $app_name . ' Data';
        }

        $table_slug = sanitize_title($table_name);
    }
}

// Check if table already exists
$tables_table = $wpdb->prefix . 'dab_tables';
$existing_table = null;

if (!empty($table_slug)) {
    $existing_table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE table_slug = %s",
        $table_slug
    ));

    if ($existing_table) {
        $table_name = $existing_table->table_label;
        $table_description = $existing_table->description;
    }
}
?>

<div class="dab-wizard-form">
    <div class="dab-wizard-form-group">
        <label for="table_name" class="dab-wizard-form-label"><?php _e('Table Name', 'db-app-builder'); ?> <span class="required">*</span></label>
        <input type="text" id="table_name" name="table_name" class="dab-wizard-form-input" value="<?php echo esc_attr($table_name); ?>" required>
        <p class="dab-wizard-form-help"><?php _e('Enter a descriptive name for your table.', 'db-app-builder'); ?></p>
    </div>

    <div class="dab-wizard-form-group">
        <label for="table_slug" class="dab-wizard-form-label"><?php _e('Table Slug', 'db-app-builder'); ?></label>
        <input type="text" id="table_slug" name="table_slug" class="dab-wizard-form-input" value="<?php echo esc_attr($table_slug); ?>">
        <p class="dab-wizard-form-help"><?php _e('The slug is used in the database. Leave empty to generate automatically from the table name.', 'db-app-builder'); ?></p>
    </div>

    <div class="dab-wizard-form-group">
        <label for="table_description" class="dab-wizard-form-label"><?php _e('Table Description', 'db-app-builder'); ?></label>
        <textarea id="table_description" name="table_description" class="dab-wizard-form-textarea"><?php echo esc_textarea($table_description); ?></textarea>
        <p class="dab-wizard-form-help"><?php _e('Provide a brief description of what this table will be used for.', 'db-app-builder'); ?></p>
    </div>

    <?php if ($existing_table): ?>
        <div class="dab-wizard-notice dab-wizard-notice-info">
            <p><?php _e('This table already exists in the database. You can continue with this table or change the name/slug to create a new one.', 'db-app-builder'); ?></p>
        </div>
    <?php else: ?>
        <div class="dab-wizard-notice dab-wizard-notice-info">
            <p><?php _e('In the next step, you will be able to add fields to your table.', 'db-app-builder'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.dab-wizard-notice-info {
    background-color: #e5f5fa;
    border-left: 4px solid #00a0d2;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from table name
    $('#table_name').on('input', function() {
        const tableName = $(this).val();
        const tableSlug = tableName.toLowerCase()
            .replace(/[^a-z0-9]+/g, '_')
            .replace(/^_+|_+$/g, '');
        $('#table_slug').val(tableSlug);
    });
});</script>
