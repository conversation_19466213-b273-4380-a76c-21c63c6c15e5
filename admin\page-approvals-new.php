<?php
/**
 * Enhanced Approval Admin Page
 * 
 * This page provides a modern, user-friendly interface for administrators to manage approval workflows.
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$approval_levels_table = $wpdb->prefix . 'dab_approval_levels';

// Enqueue required styles and scripts
wp_enqueue_style('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/approval-dashboard.css', array(), DAB_VERSION);
wp_enqueue_script('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/approval-dashboard.js', array('jquery'), DAB_VERSION, true);

$message = '';
$current_user_id = get_current_user_id();

// Handle Approve/Reject Actions
if (isset($_POST['dab_approve_action']) && isset($_POST['record_id'], $_POST['table_id'])) {
    $action = sanitize_text_field($_POST['dab_approve_action']);
    $record_id = intval($_POST['record_id']);
    $table_id = intval($_POST['table_id']);
    $note = sanitize_textarea_field($_POST['approval_note'] ?? '');
    
    $status = ($action === 'approve') ? 'Approved' : 'Rejected';
    
    // Process the approval action
    if (DAB_Approval_Manager::can_user_approve($current_user_id, $table_id, $record_id)) {
        DAB_Approval_Manager::process_approval($table_id, $record_id, $status, $note, $current_user_id);
        $message = "Record has been " . strtolower($status) . " successfully.";
    } else {
        $message = "You don't have permission to approve this record at its current level.";
    }
}

// Get tables with approval workflows
$tables_with_approval = $wpdb->get_results(
    "SELECT DISTINCT t.* 
    FROM {$wpdb->prefix}dab_tables t
    INNER JOIN {$wpdb->prefix}dab_approval_levels l ON t.id = l.table_id
    ORDER BY t.table_label"
);

$selected_table_id = isset($_GET['table_id']) ? intval($_GET['table_id']) : 0;

// Get approval stats
$total_pending = 0;
$total_approved = 0;
$total_rejected = 0;

foreach ($tables_with_approval as $table) {
    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
    
    // Count records by status
    $pending = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Pending'");
    $approved = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Approved'");
    $rejected = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Rejected'");
    
    $total_pending += $pending;
    $total_approved += $approved;
    $total_rejected += $rejected;
}

// Get records pending approval that the current user can approve
$pending_records = [];
if ($selected_table_id) {
    $table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $selected_table_id
    ));
    
    if ($table) {
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
        
        // Get fields for this table
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order",
            $selected_table_id
        ));
        
        // Get records pending approval
        $records = $wpdb->get_results(
            "SELECT * FROM $data_table 
            WHERE approval_status = 'Pending' AND current_approval_level > 0
            ORDER BY id DESC"
        );
        
        // Filter records that the current user can approve
        foreach ($records as $record) {
            if (DAB_Approval_Manager::can_user_approve($current_user_id, $selected_table_id, $record->id)) {
                $pending_records[] = $record;
            }
        }
    }
}
?>

<!-- Include approval modal template -->
<?php include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/approval-modal.php'); ?>

<div class="wrap">
    <div class="dab-approval-dashboard">
        <div class="dab-dashboard-header">
            <h1 class="dab-dashboard-title">Approval Dashboard</h1>
            
            <!-- Display stats -->
            <div class="dab-dashboard-stats">
                <div class="dab-stat-card dab-stat-total">
                    <div class="dab-stat-number" id="dab-count-total"><?php echo ($total_pending + $total_approved + $total_rejected); ?></div>
                    <div class="dab-stat-label">Total Records</div>
                </div>
                
                <div class="dab-stat-card dab-stat-pending">
                    <div class="dab-stat-number" id="dab-count-pending"><?php echo $total_pending; ?></div>
                    <div class="dab-stat-label">Pending</div>
                </div>
                
                <div class="dab-stat-card dab-stat-approved">
                    <div class="dab-stat-number" id="dab-count-approved"><?php echo $total_approved; ?></div>
                    <div class="dab-stat-label">Approved</div>
                </div>
                
                <div class="dab-stat-card dab-stat-rejected">
                    <div class="dab-stat-number" id="dab-count-rejected"><?php echo $total_rejected; ?></div>
                    <div class="dab-stat-label">Rejected</div>
                </div>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
        <?php endif; ?>
        
        <div class="dab-dashboard-filters">
            <div class="dab-filter-group">
                <label class="dab-filter-label">Table:</label>
                <form method="get" style="display:inline;">
                    <input type="hidden" name="page" value="dab_approvals">
                    <select name="table_id" class="dab-filter-select" onchange="this.form.submit();">
                        <option value="">-- Select Table --</option>
                        <?php foreach ($tables_with_approval as $table): ?>
                            <option value="<?php echo esc_attr($table->id); ?>" <?php selected($selected_table_id, $table->id); ?>>
                                <?php echo esc_html($table->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </form>
            </div>
            
            <?php if ($selected_table_id): ?>
                <div class="dab-filter-group">
                    <label class="dab-filter-label">Status:</label>
                    <select id="dab-filter-status" class="dab-filter-select">
                        <option value="all">All</option>
                        <option value="Pending" selected>Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>
                
                <div class="dab-filter-search">
                    <input type="text" id="dab-approval-search" placeholder="Search records...">
                </div>
            <?php endif; ?>
        </div>
        
        <?php if ($selected_table_id && !empty($pending_records)): ?>
            <div class="dab-approval-records">
                <?php foreach ($pending_records as $record): ?>
                    <?php
                    // Get primary field for record title
                    $primary_field = reset($fields);
                    $record_title = $record->{$primary_field->field_slug} ?? 'Record #' . $record->id;
                    
                    // Format date
                    $created_date = !empty($record->created_at) ? date('Y-m-d', strtotime($record->created_at)) : '';
                    
                    // Get submitter info
                    $submitter = get_userdata($record->user_id);
                    $submitter_name = $submitter ? $submitter->display_name : 'Unknown';
                    
                    // Get current level
                    $level = DAB_Approval_Manager::get_current_level_info($selected_table_id, $record->id);
                    $level_name = $level ? $level->level_name : 'N/A';
                    ?>
                    
                    <div class="dab-approval-record" data-status="Pending" data-date="<?php echo esc_attr($created_date); ?>">
                        <!-- Record header -->
                        <div class="dab-record-header">
                            <div>
                                <h4 class="dab-record-title"><?php echo esc_html($record_title); ?></h4>
                                <div class="dab-record-meta">
                                    <div class="dab-record-submitter">Submitted by: <?php echo esc_html($submitter_name); ?></div>
                                    <div class="dab-record-date">Date: <?php echo esc_html($created_date); ?></div>
                                    <div class="dab-record-level">Current Level: <?php echo esc_html($level_name); ?></div>
                                </div>
                            </div>
                            
                            <!-- Status badge -->
                            <div class="dab-approval-status dab-status-pending" data-status="Pending">
                                Pending
                            </div>
                        </div>
                        
                        <!-- Record content -->
                        <div class="dab-record-content">
                            <?php
                            // Show up to 5 important fields
                            $display_fields = array_slice($fields, 0, 5);
                            foreach ($display_fields as $field):
                                $field_value = $record->{$field->field_slug} ?? '';
                                
                                // Format field value based on type
                                if ($field->field_type === 'lookup') {
                                    $lookup_table_id = intval($field->lookup_table_id);
                                    $display_column = sanitize_text_field($field->lookup_display_column);
                                    if ($lookup_table_id && $display_column) {
                                        $lookup_table_slug = $wpdb->get_var(
                                            $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                                        );
                                        if ($lookup_table_slug) {
                                            $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                                            $lookup_id = intval($field_value);
                                            $field_value = $wpdb->get_var(
                                                $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                                            );
                                        }
                                    }
                                }
                            ?>
                                <div class="dab-record-field">
                                    <div class="dab-field-label"><?php echo esc_html($field->field_label); ?></div>
                                    <div class="dab-field-value"><?php echo esc_html($field_value); ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Record actions -->
                        <div class="dab-record-actions">
                            <button type="button" class="dab-action-btn dab-view-btn dab-view-record-btn" 
                                    data-record="<?php echo $record->id; ?>" 
                                    data-table="<?php echo $selected_table_id; ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                View Details
                            </button>
                            
                            <button type="button" class="dab-action-btn dab-approve-btn" 
                                    data-record="<?php echo $record->id; ?>" 
                                    data-table="<?php echo $selected_table_id; ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Approve
                            </button>
                            
                            <button type="button" class="dab-action-btn dab-reject-btn" 
                                    data-record="<?php echo $record->id; ?>" 
                                    data-table="<?php echo $selected_table_id; ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Reject
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php elseif ($selected_table_id): ?>
            <div class="dab-empty-state">
                <div class="dab-empty-icon">📋</div>
                <h3>No Records Pending Your Approval</h3>
                <p>There are no records that require your approval at this time.</p>
            </div>
        <?php else: ?>
            <div class="dab-empty-state">
                <div class="dab-empty-icon">📊</div>
                <h3>Select a Table to View Approvals</h3>
                <p>Choose a table from the dropdown above to view records pending your approval.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Empty state styling */
.dab-empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.dab-empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.dab-empty-state h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #343a40;
}

.dab-empty-state p {
    font-size: 16px;
    color: #6c757d;
    max-width: 500px;
    margin: 0 auto;
}
</style>
