<?php
/**
 * Uninstall handler for Database App Builder
 *
 * This file runs when the plugin is uninstalled via the WordPress admin.
 * It provides options for the user to either keep or delete plugin data.
 *
 * @package    Database_App_Builder
 */

// If uninstall not called from WordPress, exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Check if the user has confirmed data deletion
$delete_data = get_option('dab_delete_data_on_uninstall', false);

// If user hasn't confirmed, exit without deleting data
if (!$delete_data) {
    // Store a flag indicating that uninstallation was attempted but data was preserved
    update_option('dab_uninstall_attempted', true);
    return;
}

// Begin data deletion process
global $wpdb;

// Delete plugin options
$options_to_delete = array(
    'dab_db_structure_check',
    'dab_delete_data_on_uninstall',
    'dab_uninstall_attempted',
    'dab_license_key',
    'dab_license_status',
    'dab_version'
);

foreach ($options_to_delete as $option) {
    delete_option($option);
}

// Get all plugin tables
$tables = array(
    $wpdb->prefix . 'dab_tables',
    $wpdb->prefix . 'dab_fields',
    $wpdb->prefix . 'dab_forms',
    $wpdb->prefix . 'dab_relationships',
    $wpdb->prefix . 'dab_views',
    $wpdb->prefix . 'dab_students',
    $wpdb->prefix . 'dab_approval_levels',
    $wpdb->prefix . 'dab_approval_history',
    $wpdb->prefix . 'dab_approval_comments'
);

// Get all data tables created by the plugin
$data_tables = $wpdb->get_results("SELECT table_slug FROM {$wpdb->prefix}dab_tables");
if (!empty($data_tables)) {
    foreach ($data_tables as $table) {
        $tables[] = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
    }
}

// Get dashboard tables
$dashboard_tables = array(
    $wpdb->prefix . 'dab_dashboards',
    $wpdb->prefix . 'dab_dashboard_widgets',
    $wpdb->prefix . 'dab_dashboard_permissions'
);
$tables = array_merge($tables, $dashboard_tables);

// Drop all tables
foreach ($tables as $table) {
    $wpdb->query("DROP TABLE IF EXISTS $table");
}

// Clear any cached data
wp_cache_flush();
