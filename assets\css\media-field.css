/**
 * Media Field Styles
 * 
 * Styles for audio/video media fields in the Database App Builder plugin.
 */

/* Media Field Container */
.dab-media-field {
    margin-bottom: 15px;
    max-width: 100%;
}

/* Media Tabs */
.dab-media-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.dab-media-tab {
    padding: 8px 15px;
    background-color: #f0f0f1;
    border: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.dab-media-tab:hover {
    background-color: #e0e0e0;
}

.dab-media-tab.active {
    background-color: #fff;
    border-bottom-color: #fff;
    color: #0073aa;
    border-top-color: #0073aa;
}

/* Tab Content */
.dab-media-tab-content {
    margin-bottom: 15px;
}

.dab-media-tab-pane {
    display: none;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.dab-media-tab-pane.active {
    display: block;
}

/* Upload Tab */
.dab-media-upload {
    width: 100%;
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    background-color: #fff;
    transition: border-color 0.3s;
}

.dab-media-upload:hover {
    border-color: #0073aa;
}

.dab-media-upload-info {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

/* Embed Tab */
.dab-media-embed-url {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
}

.dab-media-embed-preview {
    margin-bottom: 10px;
}

.dab-media-embed-preview-container {
    margin-top: 15px;
}

.dab-media-embed-preview-container iframe {
    max-width: 100%;
    border: 1px solid #ddd;
}

/* Record Tab */
.dab-media-recorder {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-media-recorder-preview {
    min-height: 50px;
    background-color: #f0f0f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-media-recorder-preview video {
    max-width: 100%;
    height: auto;
}

.dab-audio-recording-indicator {
    padding: 10px;
    color: #d63638;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dab-audio-recording-indicator:before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background-color: #d63638;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 1;
    }
}

.dab-media-recorder-controls {
    display: flex;
    gap: 10px;
}

.dab-media-record-start,
.dab-media-record-stop {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.dab-media-record-start {
    background-color: #007cba !important;
    color: #fff !important;
    border-color: #007cba !important;
}

.dab-media-record-stop {
    background-color: #d63638 !important;
    color: #fff !important;
    border-color: #d63638 !important;
}

/* Media Preview */
.dab-media-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.dab-media-preview audio,
.dab-media-preview video,
.dab-media-preview iframe {
    max-width: 100%;
    display: block;
    margin: 0 auto;
}

/* Admin-specific styles */
.wp-admin .dab-media-field {
    max-width: 800px;
}

/* Frontend form-specific styles */
.dab-form .dab-media-field {
    margin-bottom: 20px;
}

.dab-form .dab-media-embed-url {
    padding: 10px 15px;
    border-radius: 5px;
}

/* Error state */
.dab-form .dab-media-field.has-error .dab-media-upload,
.dab-form .dab-media-field.has-error .dab-media-embed-url {
    border-color: #dc3545;
}

/* Disabled state */
.dab-media-field.disabled .dab-media-upload,
.dab-media-field.disabled .dab-media-embed-url {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .dab-media-tabs {
        flex-wrap: wrap;
    }
    
    .dab-media-tab {
        flex: 1 1 auto;
        text-align: center;
    }
    
    .dab-media-preview audio,
    .dab-media-preview video,
    .dab-media-preview iframe {
        width: 100% !important;
        height: auto !important;
    }
}
