/**
 * Calendar View CSS
 * 
 * Styles for the calendar field component
 */

.dab-calendar-view {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dab-calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.dab-calendar-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.dab-calendar-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-calendar-nav {
    display: flex;
    gap: 5px;
}

.dab-calendar-nav-btn {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-calendar-nav-btn:hover {
    background: #f5f5f5;
    border-color: #999;
}

.dab-calendar-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.dab-calendar-month-year {
    font-weight: 600;
    color: #333;
    min-width: 150px;
    text-align: center;
}

.dab-add-event-btn {
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.dab-add-event-btn:hover {
    background: #005a87;
}

.dab-calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.dab-calendar-day-header {
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #666;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-size: 12px;
    text-transform: uppercase;
}

.dab-calendar-day {
    min-height: 100px;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 8px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dab-calendar-day:hover {
    background: #f8f9fa;
}

.dab-calendar-day.other-month {
    background: #f9f9f9;
    color: #999;
}

.dab-calendar-day.today {
    background: #e3f2fd;
}

.dab-calendar-day.selected {
    background: #bbdefb;
}

.dab-calendar-day-number {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px;
}

.dab-calendar-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.dab-calendar-event {
    background: #2196f3;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: background-color 0.2s ease;
}

.dab-calendar-event:hover {
    background: #1976d2;
}

.dab-calendar-event.event-category-work {
    background: #ff9800;
}

.dab-calendar-event.event-category-personal {
    background: #4caf50;
}

.dab-calendar-event.event-category-important {
    background: #f44336;
}

.dab-calendar-event.event-category-meeting {
    background: #9c27b0;
}

/* Event Modal */
.dab-event-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.dab-event-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.dab-event-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-event-modal-title {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.dab-event-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.dab-event-modal-close:hover {
    background: #f0f0f0;
}

.dab-event-modal-body {
    padding: 20px;
}

.dab-event-form-group {
    margin-bottom: 15px;
}

.dab-event-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.dab-event-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.dab-event-form-control:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-event-form-control.textarea {
    min-height: 80px;
    resize: vertical;
}

.dab-event-checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dab-event-checkbox {
    margin: 0;
}

.dab-event-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.dab-event-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dab-event-btn-primary {
    background: #0073aa;
    color: white;
}

.dab-event-btn-primary:hover {
    background: #005a87;
}

.dab-event-btn-secondary {
    background: #f0f0f0;
    color: #333;
}

.dab-event-btn-secondary:hover {
    background: #e0e0e0;
}

.dab-event-btn-danger {
    background: #dc3545;
    color: white;
}

.dab-event-btn-danger:hover {
    background: #c82333;
}

/* Loading State */
.dab-calendar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.dab-calendar-loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-calendar-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dab-calendar-controls {
        justify-content: space-between;
    }
    
    .dab-calendar-day {
        min-height: 80px;
        padding: 4px;
    }
    
    .dab-calendar-day-number {
        font-size: 12px;
    }
    
    .dab-calendar-event {
        font-size: 10px;
        padding: 1px 4px;
    }
    
    .dab-event-modal-content {
        width: 95%;
    }
}
