<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Product Fields Manager
 *
 * Manages custom fields for WooCommerce products using DAB form builder
 */
class DAB_Product_Fields_Manager {

    /**
     * Initialize the Product Fields Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Add product meta boxes
        add_action('add_meta_boxes', array(__CLASS__, 'add_product_meta_boxes'));

        // Save product custom fields
        add_action('woocommerce_process_product_meta', array(__CLASS__, 'save_product_custom_fields'));

        // Display custom fields on product page
        add_action('woocommerce_single_product_summary', array(__CLASS__, 'display_product_custom_fields'), 25);

        // Add custom fields to product data tabs
        add_filter('woocommerce_product_data_tabs', array(__CLASS__, 'add_product_data_tab'));
        add_action('woocommerce_product_data_panels', array(__CLASS__, 'add_product_data_panel'));

        // Register field types for products
        add_filter('dab_field_types', array(__CLASS__, 'add_product_field_types'));

        // Handle AJAX requests
        add_action('wp_ajax_dab_get_product_fields', array(__CLASS__, 'ajax_get_product_fields'));
        add_action('wp_ajax_dab_save_product_field_config', array(__CLASS__, 'ajax_save_product_field_config'));
        add_action('wp_ajax_dab_delete_product_field_config', array(__CLASS__, 'ajax_delete_product_field_config'));
    }

    /**
     * Create database tables for product fields
     */
    public static function create_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_product_fields';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            field_name varchar(255) NOT NULL,
            field_label varchar(255) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_options longtext,
            field_order int(11) DEFAULT 0,
            required tinyint(1) DEFAULT 0,
            show_on_frontend tinyint(1) DEFAULT 1,
            show_in_admin tinyint(1) DEFAULT 1,
            product_categories text,
            product_types text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY field_name (field_name)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Create product field values table
        $values_table = $wpdb->prefix . 'dab_wc_product_field_values';

        $sql_values = "CREATE TABLE $values_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            field_name varchar(255) NOT NULL,
            field_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY product_field (product_id, field_name),
            KEY product_id (product_id),
            KEY field_name (field_name)
        ) $charset_collate;";

        dbDelta($sql_values);
    }

    /**
     * Add product-specific field types
     */
    public static function add_product_field_types($field_types) {
        $field_types['product_gallery'] = __('Product Gallery', 'db-app-builder');
        $field_types['product_attributes'] = __('Product Attributes', 'db-app-builder');
        $field_types['product_variations'] = __('Product Variations', 'db-app-builder');
        $field_types['product_shipping'] = __('Shipping Information', 'db-app-builder');
        $field_types['product_seo'] = __('SEO Information', 'db-app-builder');

        return $field_types;
    }

    /**
     * Add meta boxes to product edit screen
     */
    public static function add_product_meta_boxes() {
        add_meta_box(
            'dab_product_custom_fields',
            __('Custom Product Fields', 'db-app-builder'),
            array(__CLASS__, 'render_product_meta_box'),
            'product',
            'normal',
            'high'
        );
    }

    /**
     * Render product meta box
     */
    public static function render_product_meta_box($post) {
        $product_id = $post->ID;
        $custom_fields = self::get_product_custom_fields($product_id);
        $field_configs = self::get_product_field_configs();

        wp_nonce_field('dab_product_custom_fields', 'dab_product_custom_fields_nonce');

        echo '<div class="dab-product-fields-container">';

        if (empty($field_configs)) {
            echo '<p>' . __('No custom fields configured for products.', 'db-app-builder') . '</p>';
            echo '<p><a href="' . admin_url('admin.php?page=dab_woocommerce_product_fields') . '" class="button">' . __('Configure Product Fields', 'db-app-builder') . '</a></p>';
        } else {
            foreach ($field_configs as $field_config) {
                $field_name = $field_config->field_name;
                $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';

                echo '<div class="dab-field-wrapper">';
                echo '<label for="dab_' . esc_attr($field_name) . '">' . esc_html($field_config->field_label);
                if ($field_config->required) {
                    echo ' <span class="required">*</span>';
                }
                echo '</label>';

                self::render_product_field($field_config, $field_value);

                echo '</div>';
            }
        }

        echo '</div>';

        // Add CSS for styling
        echo '<style>
        .dab-product-fields-container .dab-field-wrapper {
            margin-bottom: 15px;
        }
        .dab-product-fields-container label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .dab-product-fields-container .required {
            color: #d63638;
        }
        .dab-product-fields-container input,
        .dab-product-fields-container textarea,
        .dab-product-fields-container select {
            width: 100%;
            max-width: 500px;
        }
        </style>';
    }

    /**
     * Render individual product field
     */
    public static function render_product_field($field_config, $value) {
        $field_name = 'dab_' . $field_config->field_name;
        $field_type = $field_config->field_type;
        $field_options = maybe_unserialize($field_config->field_options);
        $required = $field_config->required ? 'required' : '';

        switch ($field_type) {
            case 'text':
                echo '<input type="text" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" ' . $required . ' />';
                break;

            case 'textarea':
                echo '<textarea name="' . esc_attr($field_name) . '" rows="4" ' . $required . '>' . esc_textarea($value) . '</textarea>';
                break;

            case 'number':
                echo '<input type="number" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" ' . $required . ' />';
                break;

            case 'email':
                echo '<input type="email" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" ' . $required . ' />';
                break;

            case 'url':
                echo '<input type="url" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" ' . $required . ' />';
                break;

            case 'date':
                echo '<input type="date" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '" ' . $required . ' />';
                break;

            case 'select':
                echo '<select name="' . esc_attr($field_name) . '" ' . $required . '>';
                echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';
                if (!empty($field_options['options'])) {
                    foreach ($field_options['options'] as $option_value => $option_label) {
                        $selected = selected($value, $option_value, false);
                        echo '<option value="' . esc_attr($option_value) . '" ' . $selected . '>' . esc_html($option_label) . '</option>';
                    }
                }
                echo '</select>';
                break;

            case 'checkbox':
                $checked = checked($value, '1', false);
                echo '<input type="checkbox" name="' . esc_attr($field_name) . '" value="1" ' . $checked . ' ' . $required . ' />';
                break;

            case 'file':
                echo '<input type="file" name="' . esc_attr($field_name) . '" ' . $required . ' />';
                if ($value) {
                    echo '<p>' . __('Current file:', 'db-app-builder') . ' <a href="' . esc_url($value) . '" target="_blank">' . basename($value) . '</a></p>';
                }
                break;

            default:
                // Allow custom field types to be rendered via action
                do_action('dab_render_product_field_' . $field_type, $field_config, $value);
                break;
        }
    }

    /**
     * Save product custom fields
     */
    public static function save_product_custom_fields($product_id) {
        if (!isset($_POST['dab_product_custom_fields_nonce']) ||
            !wp_verify_nonce($_POST['dab_product_custom_fields_nonce'], 'dab_product_custom_fields')) {
            return;
        }

        if (!current_user_can('edit_post', $product_id)) {
            return;
        }

        $field_configs = self::get_product_field_configs();

        foreach ($field_configs as $field_config) {
            $field_name = 'dab_' . $field_config->field_name;

            if (isset($_POST[$field_name])) {
                $field_value = sanitize_text_field($_POST[$field_name]);
                self::save_product_field_value($product_id, $field_config->field_name, $field_value);
            }
        }
    }

    /**
     * Get product field configurations
     */
    public static function get_product_field_configs() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_product_fields';

        return $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY field_order ASC, id ASC"
        );
    }

    /**
     * Get custom fields for a specific product
     */
    public static function get_product_custom_fields($product_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_product_field_values';

        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT field_name, field_value FROM $table_name WHERE product_id = %d",
                $product_id
            )
        );

        $fields = array();
        foreach ($results as $result) {
            $fields[$result->field_name] = $result->field_value;
        }

        return $fields;
    }

    /**
     * Save product field value
     */
    public static function save_product_field_value($product_id, $field_name, $field_value) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_product_field_values';

        $wpdb->replace(
            $table_name,
            array(
                'product_id' => $product_id,
                'field_name' => $field_name,
                'field_value' => $field_value,
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s')
        );
    }

    /**
     * AJAX handler to get product fields
     */
    public static function ajax_get_product_fields() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $product_id = intval($_POST['product_id']);
        $custom_fields = self::get_product_custom_fields($product_id);

        wp_send_json_success($custom_fields);
    }

    /**
     * AJAX handler to save product field configuration
     */
    public static function ajax_save_product_field_config() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $field_data = array(
            'field_name' => sanitize_key($_POST['field_name']),
            'field_label' => sanitize_text_field($_POST['field_label']),
            'field_type' => sanitize_text_field($_POST['field_type']),
            'field_order' => intval($_POST['field_order']),
            'required' => isset($_POST['required']) ? 1 : 0,
            'show_on_frontend' => isset($_POST['show_on_frontend']) ? 1 : 0,
            'show_in_admin' => isset($_POST['show_in_admin']) ? 1 : 0,
        );

        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_wc_product_fields';

        if (isset($_POST['field_id']) && !empty($_POST['field_id'])) {
            $result = $wpdb->update(
                $table_name,
                $field_data,
                array('id' => intval($_POST['field_id']))
            );
        } else {
            $result = $wpdb->insert($table_name, $field_data);
        }

        if ($result !== false) {
            wp_send_json_success('Field saved successfully');
        } else {
            wp_send_json_error('Error saving field');
        }
    }

    /**
     * AJAX handler to delete product field configuration
     */
    public static function ajax_delete_product_field_config() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $field_id = intval($_POST['field_id']);

        global $wpdb;
        $table_name = $wpdb->prefix . 'dab_wc_product_fields';

        $result = $wpdb->delete($table_name, array('id' => $field_id), array('%d'));

        if ($result !== false) {
            wp_send_json_success('Field deleted successfully');
        } else {
            wp_send_json_error('Error deleting field');
        }
    }

    /**
     * Display custom fields on product page
     */
    public static function display_product_custom_fields() {
        global $product;

        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $custom_fields = self::get_product_custom_fields($product_id);
        $field_configs = self::get_product_field_configs();

        $frontend_fields = array_filter($field_configs, function($field) {
            return $field->show_on_frontend;
        });

        if (empty($frontend_fields) || empty($custom_fields)) {
            return;
        }

        echo '<div class="dab-product-custom-fields">';
        echo '<h3>' . __('Additional Information', 'db-app-builder') . '</h3>';
        echo '<table class="dab-product-fields-table">';

        foreach ($frontend_fields as $field_config) {
            $field_name = $field_config->field_name;
            $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';

            if (!empty($field_value)) {
                echo '<tr>';
                echo '<td class="dab-field-label"><strong>' . esc_html($field_config->field_label) . ':</strong></td>';
                echo '<td class="dab-field-value">' . esc_html($field_value) . '</td>';
                echo '</tr>';
            }
        }

        echo '</table>';
        echo '</div>';
    }

    /**
     * Add product data tab
     */
    public static function add_product_data_tab($tabs) {
        $tabs['dab_custom_fields'] = array(
            'label' => __('Custom Fields', 'db-app-builder'),
            'target' => 'dab_custom_fields_data',
            'class' => array('show_if_simple', 'show_if_variable'),
            'priority' => 80,
        );

        return $tabs;
    }

    /**
     * Add product data panel
     */
    public static function add_product_data_panel() {
        global $post;

        $product_id = $post->ID;
        $custom_fields = self::get_product_custom_fields($product_id);
        $field_configs = self::get_product_field_configs();

        echo '<div id="dab_custom_fields_data" class="panel woocommerce_options_panel">';

        if (empty($field_configs)) {
            echo '<p>' . __('No custom fields configured for products.', 'db-app-builder') . '</p>';
            echo '<p><a href="' . admin_url('admin.php?page=dab_woocommerce_product_fields') . '" class="button">' . __('Configure Product Fields', 'db-app-builder') . '</a></p>';
        } else {
            foreach ($field_configs as $field_config) {
                $field_name = $field_config->field_name;
                $field_value = isset($custom_fields[$field_name]) ? $custom_fields[$field_name] : '';

                woocommerce_wp_text_input(array(
                    'id' => 'dab_' . $field_name,
                    'label' => $field_config->field_label,
                    'value' => $field_value,
                    'description' => $field_config->required ? __('Required field', 'db-app-builder') : '',
                ));
            }
        }

        echo '</div>';
    }
}
