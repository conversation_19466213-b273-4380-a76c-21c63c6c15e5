<?php
// Remove the inline table field options section
// Keep only the lookup field options section

// Example of lookup field options section (keep this part):
?>
<div class="field-options lookup-options" style="display: none;">
    <div class="form-group">
        <label for="lookup_table_id"><?php _e('Reference Table', 'db-app-builder'); ?></label>
        <select id="lookup_table_id" name="lookup_table_id" class="form-control">
            <option value=""><?php _e('Select a table', 'db-app-builder'); ?></option>
            <?php foreach ($tables as $table) : ?>
                <option value="<?php echo $table->id; ?>"><?php echo $table->table_label; ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="form-group">
        <label for="lookup_display_field"><?php _e('Display Field', 'db-app-builder'); ?></label>
        <select id="lookup_display_field" name="lookup_display_field" class="form-control">
            <option value=""><?php _e('Select a table first', 'db-app-builder'); ?></option>
        </select>
    </div>
    <div class="form-group">
        <label for="lookup_value_field"><?php _e('Value Field', 'db-app-builder'); ?></label>
        <select id="lookup_value_field" name="lookup_value_field" class="form-control">
            <option value=""><?php _e('Select a table first', 'db-app-builder'); ?></option>
        </select>
    </div>
</div>

<?php
// Remove any inline table field options section that might exist
?>