<?php
/**
 * Conditional Logic Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Conditional_Logic_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the conditional logic field type
        add_filter('dab_field_types', array($this, 'register_conditional_logic_field_type'));

        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_conditional_logic_field_options'), 10, 2);

        // Register field renderer
        add_action('dab_render_field_conditional_logic', array($this, 'render_conditional_logic_field'), 10, 2);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Format conditional logic value for display
        add_filter('dab_format_field_value', array($this, 'format_conditional_logic_value'), 10, 3);

        // AJAX handler for fetching field options
        add_action('wp_ajax_dab_get_field_options', array($this, 'ajax_get_field_options'));
        add_action('wp_ajax_nopriv_dab_get_field_options', array($this, 'ajax_get_field_options'));

        // Save conditional logic rules
        add_action('dab_save_form_data', array($this, 'save_conditional_logic_rules'), 10, 3);
    }

    /**
     * Register the conditional logic field type
     *
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_conditional_logic_field_type($field_types) {
        $field_types['conditional_logic'] = __('Conditional Logic', 'db-app-builder');
        return $field_types;
    }

    /**
     * Add conditional logic field options
     *
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_conditional_logic_field_options($options, $field_type) {
        if ($field_type === 'conditional_logic') {
            $options = array(
                'default_value' => array(
                    'label' => __('Default Value', 'db-app-builder'),
                    'type' => 'text',
                    'description' => __('Default value when no conditions are met.', 'db-app-builder'),
                ),
                'output_type' => array(
                    'label' => __('Output Type', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'text' => __('Text', 'db-app-builder'),
                        'number' => __('Number', 'db-app-builder'),
                        'boolean' => __('Boolean (Yes/No)', 'db-app-builder'),
                        'date' => __('Date', 'db-app-builder'),
                    ),
                    'default' => 'text',
                    'description' => __('Type of output value.', 'db-app-builder'),
                ),
                'logic_type' => array(
                    'label' => __('Logic Type', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'if_else' => __('If-Else Chain', 'db-app-builder'),
                        'switch' => __('Switch-Case', 'db-app-builder'),
                        'formula' => __('Formula-Based', 'db-app-builder'),
                    ),
                    'default' => 'if_else',
                    'description' => __('Type of conditional logic to use.', 'db-app-builder'),
                ),
                'auto_update' => array(
                    'label' => __('Auto Update', 'db-app-builder'),
                    'type' => 'checkbox',
                    'default' => true,
                    'description' => __('Automatically update when dependent fields change.', 'db-app-builder'),
                ),
                'show_editor' => array(
                    'label' => __('Show Logic Editor', 'db-app-builder'),
                    'type' => 'checkbox',
                    'default' => true,
                    'description' => __('Show the logic editor in the form.', 'db-app-builder'),
                ),
            );
        }

        return $options;
    }

    /**
     * Render the conditional logic field
     *
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_conditional_logic_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        // Get field settings with defaults
        $default_value = isset($options['default_value']) ? $options['default_value'] : '';
        $output_type = isset($options['output_type']) ? $options['output_type'] : 'text';
        $logic_type = isset($options['logic_type']) ? $options['logic_type'] : 'if_else';
        $auto_update = isset($options['auto_update']) ? (bool)$options['auto_update'] : true;
        $show_editor = isset($options['show_editor']) ? (bool)$options['show_editor'] : true;

        // Parse the value
        $logic_value = !empty($value) ? $value : $default_value;

        // Get all fields in the form
        global $wpdb;
        $form_id = isset($field->form_id) ? $field->form_id : 0;
        $table_id = isset($field->table_id) ? $field->table_id : 0;

        $fields = array();
        if ($table_id) {
            $fields_table = $wpdb->prefix . 'dab_fields';
            $fields = $wpdb->get_results($wpdb->prepare(
                "SELECT id, field_label, field_slug, field_type, options FROM $fields_table
                WHERE table_id = %d AND id != %d ORDER BY field_order ASC",
                $table_id, $field->id
            ));
        }

        // Load field metadata (rules)
        $meta_table = $wpdb->prefix . 'dab_field_meta';
        $field_meta = array();

        // Check if meta table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$meta_table'") == $meta_table) {
            $meta_value = $wpdb->get_var($wpdb->prepare(
                "SELECT meta_value FROM $meta_table WHERE field_id = %d AND meta_key = 'rules'",
                $field->id
            ));

            if ($meta_value) {
                $field_meta['rules'] = $meta_value;
            }
        }

        // Attach meta to field object
        $field->meta = $field_meta;

        // Generate a unique ID for the field
        $field_id = 'dab-conditional-logic-' . $field->id . '-' . uniqid();

        // Output the field HTML
        ?>
        <div class="dab-conditional-logic-field"
             id="<?php echo esc_attr($field_id); ?>"
             data-field-slug="<?php echo esc_attr($field->field_slug); ?>"
             data-output-type="<?php echo esc_attr($output_type); ?>"
             data-logic-type="<?php echo esc_attr($logic_type); ?>"
             data-auto-update="<?php echo $auto_update ? 'true' : 'false'; ?>"
             data-table-id="<?php echo esc_attr($table_id); ?>"
             data-field-id="<?php echo esc_attr($field->id); ?>">

            <!-- Display the current value -->
            <div class="dab-conditional-logic-value">
                <?php if ($output_type === 'boolean'): ?>
                    <span class="dab-conditional-logic-display"><?php echo $logic_value ? __('Yes', 'db-app-builder') : __('No', 'db-app-builder'); ?></span>
                <?php else: ?>
                    <span class="dab-conditional-logic-display"><?php echo esc_html($logic_value); ?></span>
                <?php endif; ?>
            </div>

            <?php if ($show_editor): ?>
            <!-- Logic editor -->
            <div class="dab-conditional-logic-editor">
                <button type="button" class="button button-primary dab-conditional-logic-toggle" onclick="dabToggleConditionalLogicEditor('<?php echo esc_attr($field_id); ?>')">
                    <span class="dashicons dashicons-editor-code"></span> <?php _e('Edit Logic', 'db-app-builder'); ?>
                </button>

                <div class="dab-conditional-logic-editor-panel">
                    <div class="dab-conditional-logic-header">
                        <h3><?php _e('Conditional Logic Editor', 'db-app-builder'); ?></h3>
                        <button type="button" class="dab-conditional-logic-close">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>

                    <div class="dab-conditional-logic-body">
                        <?php if ($logic_type === 'if_else'): ?>
                        <!-- If-Else Logic -->
                        <div class="dab-conditional-logic-if-else">
                            <div class="dab-conditional-logic-rules">
                                <div class="dab-conditional-logic-rule">
                                    <div class="dab-conditional-logic-condition">
                                        <span class="dab-conditional-logic-if"><?php _e('If', 'db-app-builder'); ?></span>
                                        <select class="dab-conditional-logic-field-select">
                                            <option value=""><?php _e('Select Field', 'db-app-builder'); ?></option>
                                            <?php foreach ($fields as $form_field): ?>
                                            <option value="<?php echo esc_attr($form_field->field_slug); ?>" data-type="<?php echo esc_attr($form_field->field_type); ?>">
                                                <?php echo esc_html($form_field->field_label); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <select class="dab-conditional-logic-operator">
                                            <option value="equals"><?php _e('equals', 'db-app-builder'); ?></option>
                                            <option value="not_equals"><?php _e('does not equal', 'db-app-builder'); ?></option>
                                            <option value="contains"><?php _e('contains', 'db-app-builder'); ?></option>
                                            <option value="not_contains"><?php _e('does not contain', 'db-app-builder'); ?></option>
                                            <option value="greater_than"><?php _e('is greater than', 'db-app-builder'); ?></option>
                                            <option value="less_than"><?php _e('is less than', 'db-app-builder'); ?></option>
                                            <option value="is_empty"><?php _e('is empty', 'db-app-builder'); ?></option>
                                            <option value="is_not_empty"><?php _e('is not empty', 'db-app-builder'); ?></option>
                                        </select>

                                        <div class="dab-conditional-logic-value-wrapper">
                                            <input type="text" class="dab-conditional-logic-value-input" placeholder="<?php esc_attr_e('Value', 'db-app-builder'); ?>">
                                        </div>
                                    </div>

                                    <div class="dab-conditional-logic-result">
                                        <span class="dab-conditional-logic-then"><?php _e('then output', 'db-app-builder'); ?></span>
                                        <input type="text" class="dab-conditional-logic-result-input" placeholder="<?php esc_attr_e('Result', 'db-app-builder'); ?>">
                                    </div>

                                    <div class="dab-conditional-logic-actions">
                                        <button type="button" class="button dab-conditional-logic-add-rule">
                                            <span class="dashicons dashicons-plus"></span>
                                        </button>
                                        <button type="button" class="button dab-conditional-logic-remove-rule">
                                            <span class="dashicons dashicons-minus"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="dab-conditional-logic-else">
                                <span class="dab-conditional-logic-else-label"><?php _e('Else output', 'db-app-builder'); ?></span>
                                <input type="text" class="dab-conditional-logic-else-input" value="<?php echo esc_attr($default_value); ?>" placeholder="<?php esc_attr_e('Default Value', 'db-app-builder'); ?>">
                            </div>
                        </div>
                        <?php elseif ($logic_type === 'switch'): ?>
                        <!-- Switch-Case Logic -->
                        <div class="dab-conditional-logic-switch">
                            <div class="dab-conditional-logic-switch-field">
                                <span class="dab-conditional-logic-switch-label"><?php _e('Switch on field', 'db-app-builder'); ?></span>
                                <select class="dab-conditional-logic-switch-field-select">
                                    <option value=""><?php _e('Select Field', 'db-app-builder'); ?></option>
                                    <?php foreach ($fields as $form_field): ?>
                                    <option value="<?php echo esc_attr($form_field->field_slug); ?>" data-type="<?php echo esc_attr($form_field->field_type); ?>">
                                        <?php echo esc_html($form_field->field_label); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="dab-conditional-logic-cases">
                                <div class="dab-conditional-logic-case">
                                    <div class="dab-conditional-logic-case-value">
                                        <span class="dab-conditional-logic-case-label"><?php _e('Case', 'db-app-builder'); ?></span>
                                        <input type="text" class="dab-conditional-logic-case-input" placeholder="<?php esc_attr_e('Value', 'db-app-builder'); ?>">
                                    </div>

                                    <div class="dab-conditional-logic-case-result">
                                        <span class="dab-conditional-logic-case-result-label"><?php _e('Output', 'db-app-builder'); ?></span>
                                        <input type="text" class="dab-conditional-logic-case-result-input" placeholder="<?php esc_attr_e('Result', 'db-app-builder'); ?>">
                                    </div>

                                    <div class="dab-conditional-logic-case-actions">
                                        <button type="button" class="button dab-conditional-logic-add-case">
                                            <span class="dashicons dashicons-plus"></span>
                                        </button>
                                        <button type="button" class="button dab-conditional-logic-remove-case">
                                            <span class="dashicons dashicons-minus"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="dab-conditional-logic-default">
                                <span class="dab-conditional-logic-default-label"><?php _e('Default output', 'db-app-builder'); ?></span>
                                <input type="text" class="dab-conditional-logic-default-input" value="<?php echo esc_attr($default_value); ?>" placeholder="<?php esc_attr_e('Default Value', 'db-app-builder'); ?>">
                            </div>
                        </div>
                        <?php elseif ($logic_type === 'formula'): ?>
                        <!-- Formula-Based Logic -->
                        <div class="dab-conditional-logic-formula">
                            <div class="dab-conditional-logic-formula-editor">
                                <textarea class="dab-conditional-logic-formula-input" placeholder="<?php esc_attr_e('Enter formula using {field_slug} syntax', 'db-app-builder'); ?>"></textarea>
                            </div>

                            <div class="dab-conditional-logic-formula-fields">
                                <h4><?php _e('Available Fields', 'db-app-builder'); ?></h4>
                                <ul>
                                    <?php foreach ($fields as $form_field): ?>
                                    <li>
                                        <code>{<?php echo esc_html($form_field->field_slug); ?>}</code> - <?php echo esc_html($form_field->field_label); ?>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="dab-conditional-logic-footer">
                        <button type="button" class="button button-primary dab-conditional-logic-apply">
                            <?php _e('Apply Logic', 'db-app-builder'); ?>
                        </button>
                        <button type="button" class="button dab-conditional-logic-cancel">
                            <?php _e('Cancel', 'db-app-builder'); ?>
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Hidden field to store the logic rules -->
            <?php
            // Get existing rules from field metadata
            $existing_rules = '';
            if (isset($field->meta) && !empty($field->meta)) {
                $meta = maybe_unserialize($field->meta);
                if (isset($meta['rules']) && !empty($meta['rules'])) {
                    $existing_rules = $meta['rules'];
                    error_log('Found existing rules for field ' . $field->id . ': ' . $existing_rules);
                }
            }
            ?>
            <input type="hidden"
                   name="<?php echo esc_attr($field->field_slug); ?>_rules"
                   id="<?php echo esc_attr($field_id . '-rules'); ?>"
                   class="dab-conditional-logic-rules-input"
                   value="<?php echo esc_attr($existing_rules); ?>">

            <!-- Hidden field to store the output value -->
            <input type="hidden"
                   name="<?php echo esc_attr($field->field_slug); ?>"
                   id="<?php echo esc_attr($field_id . '-value'); ?>"
                   value="<?php echo esc_attr($logic_value); ?>"
                   <?php echo $field->required ? 'required' : ''; ?>>
        </div>
        <?php
    }

    /**
     * Format conditional logic value for display
     *
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_conditional_logic_value($value, $field, $context) {
        if ($field->field_type !== 'conditional_logic') {
            return $value;
        }

        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        $output_type = isset($options['output_type']) ? $options['output_type'] : 'text';

        // Format based on output type
        if ($output_type === 'boolean') {
            if ($context === 'html') {
                return $value ? '<span class="dab-boolean-true">✓</span>' : '<span class="dab-boolean-false">✗</span>';
            } else {
                return $value ? __('Yes', 'db-app-builder') : __('No', 'db-app-builder');
            }
        } else if ($output_type === 'number') {
            return is_numeric($value) ? number_format_i18n((float)$value, 2) : $value;
        } else if ($output_type === 'date') {
            return !empty($value) ? date_i18n(get_option('date_format'), strtotime($value)) : '';
        }

        return $value;
    }

    /**
     * AJAX handler for fetching field options
     */
    public function ajax_get_field_options() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_conditional_logic')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check required parameters
        if (!isset($_POST['field_slug']) || !isset($_POST['table_id'])) {
            wp_send_json_error('Missing required parameters');
        }

        $field_slug = sanitize_text_field($_POST['field_slug']);
        $table_id = intval($_POST['table_id']);

        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Get the field
        $field = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d AND field_slug = %s",
            $table_id, $field_slug
        ));

        if (!$field) {
            wp_send_json_error('Field not found');
        }

        $options = array();

        // Get options based on field type
        if (in_array($field->field_type, array('select', 'radio', 'checkbox'))) {
            $field_options = json_decode($field->options, true);
            if (isset($field_options['options']) && is_array($field_options['options'])) {
                $options = $field_options['options'];
            }
        }

        wp_send_json_success(array(
            'field_type' => $field->field_type,
            'options' => $options
        ));
    }

    /**
     * Save conditional logic rules
     *
     * @param int $record_id Record ID
     * @param array $data Form data
     * @param array $fields Form fields
     */
    public function save_conditional_logic_rules($record_id, $data, $fields) {
        global $wpdb;

        // Loop through fields to find conditional logic fields
        foreach ($fields as $field) {
            if ($field->field_type !== 'conditional_logic') {
                continue;
            }

            // Check if rules were submitted
            $rules_key = $field->field_slug . '_rules';
            if (!isset($data[$rules_key]) || empty($data[$rules_key])) {
                error_log('No rules found for field: ' . $field->field_slug);
                continue;
            }

            // Get the rules
            $rules = $data[$rules_key];
            error_log('Saving rules for field ' . $field->field_slug . ': ' . $rules);

            // Save rules to field metadata
            $meta_table = $wpdb->prefix . 'dab_field_meta';

            // Check if meta table exists, create it if not
            if ($wpdb->get_var("SHOW TABLES LIKE '$meta_table'") != $meta_table) {
                $charset_collate = $wpdb->get_charset_collate();
                $sql = "CREATE TABLE $meta_table (
                    id bigint(20) NOT NULL AUTO_INCREMENT,
                    field_id bigint(20) NOT NULL,
                    meta_key varchar(255) NOT NULL,
                    meta_value longtext NOT NULL,
                    PRIMARY KEY  (id),
                    KEY field_id (field_id),
                    KEY meta_key (meta_key)
                ) $charset_collate;";

                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                dbDelta($sql);
            }

            // Check if meta already exists
            $existing_meta = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $meta_table WHERE field_id = %d AND meta_key = 'rules'",
                $field->id
            ));

            if ($existing_meta) {
                // Update existing meta
                $wpdb->update(
                    $meta_table,
                    array('meta_value' => $rules),
                    array('field_id' => $field->id, 'meta_key' => 'rules')
                );
            } else {
                // Insert new meta
                $wpdb->insert(
                    $meta_table,
                    array(
                        'field_id' => $field->id,
                        'meta_key' => 'rules',
                        'meta_value' => $rules
                    )
                );
            }
        }
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue custom styles
        wp_enqueue_style(
            'dab-conditional-logic-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/conditional-logic-field.css',
            array(),
            DAB_VERSION
        );

        // Enqueue custom scripts
        wp_enqueue_script(
            'dab-conditional-logic-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/conditional-logic-field.js',
            array('jquery'),
            DAB_VERSION,
            true
        );

        // Localize script
        wp_localize_script('dab-conditional-logic-field', 'dabConditionalLogic', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_conditional_logic'),
            'i18n' => array(
                'selectField' => __('Select Field', 'db-app-builder'),
                'equals' => __('equals', 'db-app-builder'),
                'notEquals' => __('does not equal', 'db-app-builder'),
                'contains' => __('contains', 'db-app-builder'),
                'notContains' => __('does not contain', 'db-app-builder'),
                'greaterThan' => __('is greater than', 'db-app-builder'),
                'lessThan' => __('is less than', 'db-app-builder'),
                'isEmpty' => __('is empty', 'db-app-builder'),
                'isNotEmpty' => __('is not empty', 'db-app-builder'),
                'value' => __('Value', 'db-app-builder'),
                'result' => __('Result', 'db-app-builder'),
                'defaultValue' => __('Default Value', 'db-app-builder'),
                'yes' => __('Yes', 'db-app-builder'),
                'no' => __('No', 'db-app-builder'),
            )
        ));
    }
}

// Initialize the class
new DAB_Conditional_Logic_Field();
