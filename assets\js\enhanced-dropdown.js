/**
 * Enhanced Dropdown functionality for Database App Builder
 * 
 * This script provides improved dropdown functionality with:
 * - Better search capabilities
 * - Keyboard navigation
 * - Improved accessibility
 * - Visual indicators and animations
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all searchable dropdowns
    const searchableDropdowns = document.querySelectorAll('.dab-searchable');
    
    searchableDropdowns.forEach(function(dropdown) {
        enhanceDropdown(dropdown);
    });
    
    // Initialize all advanced dropdowns (relationship fields)
    const advancedDropdowns = document.querySelectorAll('.dab-advanced-dropdown-field');
    
    advancedDropdowns.forEach(function(dropdown) {
        initializeAdvancedDropdown(dropdown);
    });
    
    /**
     * Enhance a dropdown with search functionality
     */
    function enhanceDropdown(dropdown) {
        // Create wrapper if not already wrapped
        let wrapper = dropdown.closest('.dab-enhanced-select-wrapper');
        if (!wrapper) {
            wrapper = document.createElement('div');
            wrapper.className = 'dab-enhanced-select-wrapper';
            dropdown.parentNode.insertBefore(wrapper, dropdown);
            wrapper.appendChild(dropdown);
        }
        
        // Create search input
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'dab-dropdown-search';
        searchInput.placeholder = 'Search...';
        
        // Insert search input before dropdown
        wrapper.insertBefore(searchInput, dropdown);
        
        // Hide search input initially
        searchInput.style.display = 'none';
        
        // Add toggle button for search
        const searchToggle = document.createElement('div');
        searchToggle.className = 'dab-search-toggle';
        searchToggle.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>';
        searchToggle.title = 'Search options';
        wrapper.appendChild(searchToggle);
        
        // Toggle search input when button is clicked
        searchToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            if (searchInput.style.display === 'none') {
                searchInput.style.display = 'block';
                searchInput.focus();
            } else {
                searchInput.style.display = 'none';
                dropdown.focus();
            }
        });
        
        // Add event listener for search
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            
            // Get all options including those in optgroups
            const allOptions = Array.from(dropdown.querySelectorAll('option'));
            
            // Filter options
            allOptions.forEach(function(option, index) {
                // Skip the first placeholder option
                if (index === 0 && option.value === '') return;
                
                const optionText = option.textContent.toLowerCase();
                const match = optionText.includes(searchText);
                
                // Hide/show option
                option.style.display = match ? '' : 'none';
            });
            
            // Show/hide optgroups based on whether they have visible options
            const optgroups = dropdown.querySelectorAll('optgroup');
            optgroups.forEach(function(optgroup) {
                const visibleOptions = Array.from(optgroup.querySelectorAll('option')).filter(function(option) {
                    return option.style.display !== 'none';
                });
                
                optgroup.style.display = visibleOptions.length > 0 ? '' : 'none';
            });
        });
        
        // Close search when clicking outside
        document.addEventListener('click', function(e) {
            if (!wrapper.contains(e.target)) {
                searchInput.style.display = 'none';
            }
        });
        
        // Add keyboard navigation
        dropdown.addEventListener('keydown', function(e) {
            // Press S to open search
            if (e.key === 's' && !e.ctrlKey && !e.altKey && !e.metaKey) {
                e.preventDefault();
                searchInput.style.display = 'block';
                searchInput.focus();
            }
        });
    }
    
    /**
     * Initialize a single advanced dropdown (relationship field)
     */
    function initializeAdvancedDropdown(dropdown) {
        const targetTableId = dropdown.getAttribute('data-target-table');
        const parentFieldId = dropdown.getAttribute('data-parent-field');
        
        // If this dropdown depends on a parent field
        if (parentFieldId) {
            const parentField = document.getElementById(parentFieldId);
            
            if (parentField) {
                // Update options when parent field changes
                parentField.addEventListener('change', function() {
                    updateDropdownOptions(dropdown, parentField.value);
                });
                
                // Initial load of options based on parent value
                if (parentField.value) {
                    updateDropdownOptions(dropdown, parentField.value);
                }
            }
        }
    }
    
    /**
     * Update dropdown options based on parent field value
     */
    function updateDropdownOptions(dropdown, parentValue) {
        const targetTableId = dropdown.getAttribute('data-target-table');
        
        // Save current selection if any
        const currentValue = dropdown.value;
        
        // Show loading indicator
        const wrapper = dropdown.closest('.dab-enhanced-select-wrapper');
        let loadingIndicator = wrapper.querySelector('.dab-loading');
        
        if (!loadingIndicator) {
            loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'dab-loading';
            wrapper.appendChild(loadingIndicator);
        } else {
            loadingIndicator.style.display = 'inline-block';
        }
        
        // Disable dropdown during loading
        dropdown.disabled = true;
        
        // AJAX request to get filtered options
        const xhr = new XMLHttpRequest();
        xhr.open('POST', dab_vars.ajax_url, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        xhr.onload = function() {
            // Hide loading indicator
            loadingIndicator.style.display = 'none';
            
            // Enable dropdown
            dropdown.disabled = false;
            
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    
                    if (response.success) {
                        // Clear existing options except the first placeholder
                        while (dropdown.options.length > 1) {
                            dropdown.remove(1);
                        }
                        
                        // Group options if there are more than 10
                        const options = response.data.options;
                        const groupOptions = options.length > 10;
                        
                        if (groupOptions) {
                            // Group options by first letter
                            const groups = {};
                            
                            options.forEach(function(option) {
                                const firstChar = option.label.charAt(0).toUpperCase();
                                if (/[A-Z]/.test(firstChar)) {
                                    if (!groups[firstChar]) groups[firstChar] = [];
                                    groups[firstChar].push(option);
                                } else {
                                    if (!groups['#']) groups['#'] = [];
                                    groups['#'].push(option);
                                }
                            });
                            
                            // Add options with optgroups
                            Object.keys(groups).sort().forEach(function(group) {
                                const optgroup = document.createElement('optgroup');
                                optgroup.label = group;
                                dropdown.appendChild(optgroup);
                                
                                groups[group].forEach(function(option) {
                                    const optElement = document.createElement('option');
                                    optElement.value = option.value;
                                    optElement.textContent = option.label;
                                    
                                    // Restore selection if it still exists
                                    if (option.value == currentValue) {
                                        optElement.selected = true;
                                    }
                                    
                                    optgroup.appendChild(optElement);
                                });
                            });
                        } else {
                            // Add options without grouping
                            options.forEach(function(option) {
                                const optElement = document.createElement('option');
                                optElement.value = option.value;
                                optElement.textContent = option.label;
                                
                                // Restore selection if it still exists
                                if (option.value == currentValue) {
                                    optElement.selected = true;
                                }
                                
                                dropdown.appendChild(optElement);
                            });
                        }
                        
                        // Trigger change event to cascade to child dropdowns
                        const event = new Event('change');
                        dropdown.dispatchEvent(event);
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                    
                    // Show error message
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'dab-error-message';
                    errorMsg.textContent = 'Error loading options';
                    wrapper.appendChild(errorMsg);
                    
                    // Remove error after 3 seconds
                    setTimeout(function() {
                        errorMsg.remove();
                    }, 3000);
                }
            }
        };
        
        xhr.send(
            'action=dab_filter_dropdown' + 
            '&nonce=' + encodeURIComponent(dab_vars.nonce) + 
            '&parent_value=' + encodeURIComponent(parentValue) + 
            '&target_table=' + encodeURIComponent(targetTableId)
        );
    }
});
