/**
 * Form validation script
 * 
 * Handles client-side form validation for the Database App Builder forms
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all forms with the dab-form class
    const forms = document.querySelectorAll('form.dab-form');
    
    if (!forms.length) return;
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Get all required fields
            const requiredFields = form.querySelectorAll('[required]');
            let hasErrors = false;
            
            // Remove any existing error messages
            const existingErrors = form.querySelectorAll('.dab-field-error');
            existingErrors.forEach(error => error.remove());
            
            // Validate each required field
            requiredFields.forEach(field => {
                const fieldWrapper = field.closest('.dab-form-field');
                
                // Check if field is empty
                if (!field.value.trim()) {
                    e.preventDefault();
                    hasErrors = true;
                    
                    // Add error message
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'dab-field-error';
                    errorMessage.textContent = 'This field is required';
                    fieldWrapper.appendChild(errorMessage);
                    
                    // Add error class to field
                    field.classList.add('dab-field-has-error');
                }
                
                // Validate email fields
                if (field.type === 'email' && field.value.trim() && !isValidEmail(field.value)) {
                    e.preventDefault();
                    hasErrors = true;
                    
                    // Add error message
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'dab-field-error';
                    errorMessage.textContent = 'Please enter a valid email address';
                    fieldWrapper.appendChild(errorMessage);
                    
                    // Add error class to field
                    field.classList.add('dab-field-has-error');
                }
                
                // Validate number fields
                if (field.type === 'number' && field.value.trim() && isNaN(parseFloat(field.value))) {
                    e.preventDefault();
                    hasErrors = true;
                    
                    // Add error message
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'dab-field-error';
                    errorMessage.textContent = 'Please enter a valid number';
                    fieldWrapper.appendChild(errorMessage);
                    
                    // Add error class to field
                    field.classList.add('dab-field-has-error');
                }
            });
            
            // Check file inputs for image fields
            const imageFields = form.querySelectorAll('input[type="file"][accept="image/*"]');
            imageFields.forEach(field => {
                if (field.files.length > 0) {
                    const file = field.files[0];
                    const fieldWrapper = field.closest('.dab-form-field');
                    
                    // Check file type
                    const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                    if (!validImageTypes.includes(file.type)) {
                        e.preventDefault();
                        hasErrors = true;
                        
                        // Add error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'dab-field-error';
                        errorMessage.textContent = 'Please upload a valid image file (JPG, PNG, or GIF)';
                        fieldWrapper.appendChild(errorMessage);
                        
                        // Add error class to field
                        field.classList.add('dab-field-has-error');
                    }
                    
                    // Check file size (max 5MB)
                    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                    if (file.size > maxSize) {
                        e.preventDefault();
                        hasErrors = true;
                        
                        // Add error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'dab-field-error';
                        errorMessage.textContent = 'File size exceeds the maximum limit of 5MB';
                        fieldWrapper.appendChild(errorMessage);
                        
                        // Add error class to field
                        field.classList.add('dab-field-has-error');
                    }
                }
            });
            
            // If there are errors, scroll to the first error
            if (hasErrors) {
                const firstError = form.querySelector('.dab-field-has-error');
                if (firstError) {
                    firstError.focus();
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
        
        // Clear error when field is changed
        form.querySelectorAll('input, select, textarea').forEach(field => {
            field.addEventListener('input', function() {
                const fieldWrapper = field.closest('.dab-form-field');
                const errorMessage = fieldWrapper.querySelector('.dab-field-error');
                
                if (errorMessage) {
                    errorMessage.remove();
                }
                
                field.classList.remove('dab-field-has-error');
            });
        });
    });
    
    // Helper function to validate email
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
