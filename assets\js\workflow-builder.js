/**
 * Workflow Builder JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Global variables
    let currentWorkflow = null;
    let workflowSteps = [];
    let stepCounter = 0;

    // Initialize workflow builder
    function initWorkflowBuilder() {
        bindEvents();
        initDragAndDrop();

        // Load existing workflow if in edit mode
        if (window.dabWorkflowData.isEditMode && window.dabWorkflowData.currentWorkflowId > 0) {
            loadWorkflow(window.dabWorkflowData.currentWorkflowId);
        }
    }

    // Bind event handlers
    function bindEvents() {
        // Create workflow buttons
        $(document).on('click', '#create-workflow-btn, #create-first-workflow-btn', showCreateWorkflowModal);

        // Modal events
        $(document).on('click', '#cancel-workflow-creation, .dab-modal-close', hideCreateWorkflowModal);
        $(document).on('click', '#confirm-workflow-creation', createWorkflow);

        // Workflow list events
        $(document).on('click', '.edit-workflow-btn', editWorkflow);
        $(document).on('click', '.duplicate-workflow-btn', duplicateWorkflow);
        $(document).on('click', '.delete-workflow-btn', deleteWorkflow);

        // Editor events
        $(document).on('click', '#back-to-list-btn', backToList);
        $(document).on('click', '#save-workflow-btn', saveWorkflow);
        $(document).on('click', '#test-workflow-btn', testWorkflow);

        // Trigger type change
        $(document).on('change', '#trigger-type', handleTriggerTypeChange);

        // Webhook key generation
        $(document).on('click', '#generate-webhook-key', generateWebhookKey);

        // Workflow step events
        $(document).on('click', '.edit-step-btn', editWorkflowStep);
        $(document).on('click', '.delete-step-btn', deleteWorkflowStep);

        // Step configuration modal events
        $(document).on('click', '#save-step-config', saveStepConfiguration);
        $(document).on('click', '#cancel-step-config, .dab-step-modal-close', hideStepConfigModal);

        // Close modal on outside click
        $(document).on('click', '.dab-modal', function(e) {
            if (e.target === this) {
                hideCreateWorkflowModal();
                hideStepConfigModal();
            }
        });
    }

    // Initialize drag and drop functionality
    function initDragAndDrop() {
        // Make action items draggable
        $('.dab-action-item').draggable({
            helper: 'clone',
            revert: 'invalid',
            zIndex: 1000,
            start: function(event, ui) {
                ui.helper.addClass('dab-dragging');
            }
        });

        // Make canvas droppable
        $('#workflow-canvas').droppable({
            accept: '.dab-action-item',
            drop: function(event, ui) {
                const actionType = ui.draggable.data('action');
                const position = {
                    x: event.pageX - $(this).offset().left - 100,
                    y: event.pageY - $(this).offset().top - 50
                };
                addWorkflowStep(actionType, position);
            }
        });
    }

    // Show create workflow modal
    function showCreateWorkflowModal() {
        $('#workflow-creation-modal').fadeIn(300);
        $('#new-workflow-name').focus();
    }

    // Hide create workflow modal
    function hideCreateWorkflowModal() {
        $('#workflow-creation-modal').fadeOut(300);
        $('#new-workflow-name').val('');
        $('#new-workflow-description').val('');
    }

    // Create new workflow
    function createWorkflow() {
        const name = $('#new-workflow-name').val().trim();
        const description = $('#new-workflow-description').val().trim();

        if (!name) {
            alert('Please enter a workflow name.');
            return;
        }

        const data = {
            action: 'dab_save_workflow',
            nonce: window.dabWorkflowData.nonce,
            name: name,
            description: description,
            trigger_type: 'manual',
            trigger_config: {},
            workflow_steps: [],
            is_active: true
        };

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    hideCreateWorkflowModal();
                    // Redirect to edit mode
                    window.location.href = window.location.href + '&action=edit&workflow_id=' + response.data.workflow_id;
                } else {
                    alert('Error creating workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error creating workflow. Please try again.');
            }
        });
    }

    // Edit workflow
    function editWorkflow() {
        const workflowId = $(this).data('workflow-id');
        window.location.href = window.location.href + '&action=edit&workflow_id=' + workflowId;
    }

    // Duplicate workflow
    function duplicateWorkflow() {
        const workflowId = $(this).data('workflow-id');

        if (!confirm('Are you sure you want to duplicate this workflow?')) {
            return;
        }

        // Load workflow data and create a copy
        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_load_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    const workflow = response.data;
                    const data = {
                        action: 'dab_save_workflow',
                        nonce: window.dabWorkflowData.nonce,
                        name: workflow.name + ' (Copy)',
                        description: workflow.description,
                        trigger_type: workflow.trigger_type,
                        trigger_config: workflow.trigger_config,
                        workflow_steps: workflow.workflow_steps,
                        is_active: false
                    };

                    $.ajax({
                        url: window.dabWorkflowData.ajaxUrl,
                        type: 'POST',
                        data: data,
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert('Error duplicating workflow: ' + (response.data || 'Unknown error'));
                            }
                        }
                    });
                }
            }
        });
    }

    // Delete workflow
    function deleteWorkflow() {
        const workflowId = $(this).data('workflow-id');

        if (!confirm('Are you sure you want to delete this workflow? This action cannot be undone.')) {
            return;
        }

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_delete_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error deleting workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error deleting workflow. Please try again.');
            }
        });
    }

    // Back to workflows list
    function backToList() {
        const url = new URL(window.location);
        url.searchParams.delete('action');
        url.searchParams.delete('workflow_id');
        window.location.href = url.toString();
    }

    // Handle trigger type change
    function handleTriggerTypeChange() {
        const triggerType = $(this).val();

        // Hide all config sections
        $('.dab-trigger-config-section').hide();

        if (triggerType) {
            $('#trigger-config').show();
            $('#' + triggerType.replace('_', '-') + '-config').show();

            // Generate webhook key if webhook trigger
            if (triggerType === 'webhook') {
                generateWebhookKey();
            }
        } else {
            $('#trigger-config').hide();
        }
    }

    // Generate webhook key
    function generateWebhookKey() {
        const key = 'wh_' + Math.random().toString(36).substr(2, 16);
        $('#webhook-key').val(key);

        const webhookUrl = window.location.origin + '/wp-admin/admin-ajax.php?action=dab_webhook_trigger&key=' + key;
        $('#webhook-url').val(webhookUrl);
    }

    // Add workflow step
    function addWorkflowStep(actionType, position, existingConfig = {}) {
        stepCounter++;

        const stepId = 'step-' + stepCounter;
        const actionConfig = getActionConfig(actionType);

        // Determine if step is configured
        const isConfigured = existingConfig && Object.keys(existingConfig).length > 0;
        const stepContent = isConfigured
            ? actionConfig.description + '<br><small style="color: #666;">Configured</small>'
            : actionConfig.description;

        const stepHtml = `
            <div class="dab-workflow-step" id="${stepId}" data-action="${actionType}" style="left: ${position.x}px; top: ${position.y}px;">
                <div class="dab-workflow-step-header">
                    <div class="dab-workflow-step-title dab-drag-handle">
                        <span class="dashicons ${actionConfig.icon}"></span>
                        ${actionConfig.label}
                    </div>
                    <div class="dab-workflow-step-actions">
                        <button type="button" class="dab-workflow-step-btn edit-step-btn" title="Edit">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="dab-workflow-step-btn delete-step-btn" title="Delete">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
                <div class="dab-workflow-step-content">
                    ${stepContent}
                </div>
            </div>
        `;

        $('#workflow-canvas').append(stepHtml);
        $('.dab-canvas-placeholder').hide();

        // Make step draggable
        $('#' + stepId).draggable({
            containment: '#workflow-canvas',
            handle: '.dab-drag-handle'
        });

        // Add to steps array
        workflowSteps.push({
            id: stepId,
            action: actionType,
            config: existingConfig,
            position: position
        });
    }

    // Get action configuration
    function getActionConfig(actionType) {
        const configs = {
            send_email: {
                label: 'Send Email',
                icon: 'dashicons-email-alt',
                description: 'Send an email notification'
            },
            create_record: {
                label: 'Create Record',
                icon: 'dashicons-plus-alt',
                description: 'Create a new record in a table'
            },
            update_record: {
                label: 'Update Record',
                icon: 'dashicons-edit',
                description: 'Update an existing record'
            },
            delete_record: {
                label: 'Delete Record',
                icon: 'dashicons-trash',
                description: 'Delete a record from a table'
            },
            api_call: {
                label: 'API Call',
                icon: 'dashicons-cloud',
                description: 'Make an HTTP API request'
            },
            condition: {
                label: 'Condition',
                icon: 'dashicons-randomize',
                description: 'Branch workflow based on conditions'
            },
            delay: {
                label: 'Delay',
                icon: 'dashicons-clock',
                description: 'Wait for a specified time'
            },
            calculate: {
                label: 'Calculate',
                icon: 'dashicons-calculator',
                description: 'Perform calculations and store results'
            }
        };

        return configs[actionType] || {
            label: 'Unknown Action',
            icon: 'dashicons-admin-generic',
            description: 'Unknown action type'
        };
    }

    // Load workflow
    function loadWorkflow(workflowId) {
        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_load_workflow',
                workflow_id: workflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    currentWorkflow = response.data;
                    populateWorkflowForm(currentWorkflow);
                } else {
                    alert('Error loading workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error loading workflow. Please try again.');
            }
        });
    }

    // Populate workflow form
    function populateWorkflowForm(workflow) {
        $('#workflow-name').val(workflow.name);
        $('#workflow-description').val(workflow.description);
        $('#trigger-type').val(workflow.trigger_type).trigger('change');
        $('#workflow-active').prop('checked', workflow.is_active == 1);

        // Populate trigger config
        if (workflow.trigger_config) {
            Object.keys(workflow.trigger_config).forEach(key => {
                $('#trigger-' + key.replace('_', '-')).val(workflow.trigger_config[key]);
            });
        }

        // Load workflow steps
        if (workflow.workflow_steps && workflow.workflow_steps.length > 0) {
            workflow.workflow_steps.forEach((step, index) => {
                const position = step.position || { x: 50 + (index * 220), y: 50 };
                addWorkflowStep(step.action, position, step.config || {});
            });
        }
    }

    // Save workflow
    function saveWorkflow() {
        const name = $('#workflow-name').val().trim();
        const description = $('#workflow-description').val().trim();
        const triggerType = $('#trigger-type').val();
        const isActive = $('#workflow-active').is(':checked');

        if (!name) {
            alert('Please enter a workflow name.');
            return;
        }

        if (!triggerType) {
            alert('Please select a trigger type.');
            return;
        }

        // Collect trigger config
        const triggerConfig = {};
        $('#trigger-config input, #trigger-config select').each(function() {
            const key = $(this).attr('id').replace('trigger-', '').replace('-', '_');
            triggerConfig[key] = $(this).val();
        });

        const data = {
            action: 'dab_save_workflow',
            nonce: window.dabWorkflowData.nonce,
            workflow_id: window.dabWorkflowData.currentWorkflowId,
            name: name,
            description: description,
            trigger_type: triggerType,
            trigger_config: triggerConfig,
            workflow_steps: workflowSteps,
            is_active: isActive
        };

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    alert('Workflow saved successfully!');
                } else {
                    alert('Error saving workflow: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error saving workflow. Please try again.');
            }
        });
    }

    // Test workflow
    function testWorkflow() {
        if (!currentWorkflow) {
            alert('Please save the workflow first.');
            return;
        }

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_test_workflow',
                workflow_id: window.dabWorkflowData.currentWorkflowId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('Workflow test completed successfully!');
                } else {
                    alert('Workflow test failed: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error testing workflow. Please try again.');
            }
        });
    }

    // Edit workflow step
    function editWorkflowStep() {
        const stepElement = $(this).closest('.dab-workflow-step');
        const stepId = stepElement.attr('id');
        const actionType = stepElement.data('action');

        // Find step in workflowSteps array
        const step = workflowSteps.find(s => s.id === stepId);
        if (!step) {
            alert('Step not found.');
            return;
        }

        showStepConfigModal(step, actionType);
    }

    // Delete workflow step
    function deleteWorkflowStep() {
        if (!confirm('Are you sure you want to delete this step?')) {
            return;
        }

        const stepElement = $(this).closest('.dab-workflow-step');
        const stepId = stepElement.attr('id');

        // Remove from DOM
        stepElement.remove();

        // Remove from workflowSteps array
        workflowSteps = workflowSteps.filter(s => s.id !== stepId);

        // Show placeholder if no steps left
        if (workflowSteps.length === 0) {
            $('.dab-canvas-placeholder').show();
        }
    }

    // Show step configuration modal
    function showStepConfigModal(step, actionType) {
        const actionConfig = getActionConfig(actionType);

        // Set modal title
        $('#step-config-modal-title').text('Configure ' + actionConfig.label);

        // Clear previous form content
        $('#step-config-form').empty();

        // Generate form fields based on action type
        const formFields = generateStepConfigForm(actionType, step.config);
        $('#step-config-form').html(formFields);

        // Store current step for saving
        window.currentEditingStep = step;

        // Add event handlers for table selection
        if (actionType === 'create_record' || actionType === 'update_record') {
            $('#step-table-id').on('change', function() {
                const tableId = $(this).val();
                if (tableId) {
                    loadTableFieldsForMapping(tableId, step.config.field_mappings || {});
                } else {
                    $('#field-mappings-container').html('<p class="dab-form-text">Select a table to configure field mappings</p>');
                }
            });

            // Load fields if table is already selected
            const selectedTableId = $('#step-table-id').val();
            if (selectedTableId) {
                loadTableFieldsForMapping(selectedTableId, step.config.field_mappings || {});
            }
        }

        // Show modal
        $('#step-config-modal').fadeIn(300);
    }

    // Hide step configuration modal
    function hideStepConfigModal() {
        $('#step-config-modal').fadeOut(300);
        window.currentEditingStep = null;
    }

    // Load table fields for mapping
    function loadTableFieldsForMapping(tableId, existingMappings = {}) {
        $('#field-mappings-container').html('<p class="dab-form-text">Loading fields...</p>');

        $.ajax({
            url: window.dabWorkflowData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_get_table_fields_for_workflow',
                table_id: tableId,
                nonce: window.dabWorkflowData.nonce
            },
            success: function(response) {
                if (response.success) {
                    generateFieldMappingInterface(response.data, existingMappings);
                } else {
                    $('#field-mappings-container').html('<p class="dab-form-text error">Error loading fields: ' + (response.data || 'Unknown error') + '</p>');
                }
            },
            error: function() {
                $('#field-mappings-container').html('<p class="dab-form-text error">Error loading fields. Please try again.</p>');
            }
        });
    }

    // Generate field mapping interface
    function generateFieldMappingInterface(fields, existingMappings = {}) {
        let mappingHtml = '<div class="dab-field-mappings">';

        if (fields.length === 0) {
            mappingHtml += '<p class="dab-form-text">No fields found in this table.</p>';
        } else {
            mappingHtml += '<div class="dab-field-mappings-header">';
            mappingHtml += '<p class="dab-form-text">Configure how data should be mapped to each field. You can use static values or dynamic references like <code>{field_name}</code> from the trigger data.</p>';
            mappingHtml += '</div>';

            fields.forEach(function(field) {
                const existingValue = existingMappings[field.slug] || '';
                mappingHtml += `
                    <div class="dab-field-mapping-row" data-field-slug="${field.slug}">
                        <div class="dab-field-mapping-label">
                            <label for="mapping-${field.slug}">
                                <strong>${field.label}</strong>
                                <span class="dab-field-type">(${field.type})</span>
                            </label>
                        </div>
                        <div class="dab-field-mapping-input">
                            ${generateFieldMappingInput(field, existingValue)}
                        </div>
                    </div>
                `;
            });
        }

        mappingHtml += '</div>';
        $('#field-mappings-container').html(mappingHtml);
    }

    // Generate appropriate input for field mapping
    function generateFieldMappingInput(field, existingValue) {
        const fieldId = `mapping-${field.slug}`;
        const placeholder = getFieldPlaceholder(field.type);

        switch (field.type) {
            case 'Select':
            case 'Radio':
                if (field.options && field.options.options) {
                    let selectHtml = `<select id="${fieldId}" class="dab-form-control dab-field-mapping-value" data-field-slug="${field.slug}">`;
                    selectHtml += '<option value="">Select a value</option>';
                    field.options.options.forEach(function(option) {
                        const selected = existingValue === option ? 'selected' : '';
                        selectHtml += `<option value="${option}" ${selected}>${option}</option>`;
                    });
                    selectHtml += '</select>';
                    return selectHtml;
                }
                break;

            case 'Checkbox':
                const checked = existingValue === 'true' || existingValue === '1' ? 'checked' : '';
                return `
                    <label class="dab-checkbox-label">
                        <input type="checkbox" id="${fieldId}" class="dab-field-mapping-value" data-field-slug="${field.slug}" ${checked}>
                        Check to set as true
                    </label>
                `;

            case 'Textarea':
                return `<textarea id="${fieldId}" class="dab-form-control dab-field-mapping-value" data-field-slug="${field.slug}" rows="3" placeholder="${placeholder}">${existingValue}</textarea>`;

            default:
                return `<input type="text" id="${fieldId}" class="dab-form-control dab-field-mapping-value" data-field-slug="${field.slug}" value="${existingValue}" placeholder="${placeholder}">`;
        }

        // Fallback to text input
        return `<input type="text" id="${fieldId}" class="dab-form-control dab-field-mapping-value" data-field-slug="${field.slug}" value="${existingValue}" placeholder="${placeholder}">`;
    }

    // Get placeholder text for field types
    function getFieldPlaceholder(fieldType) {
        const placeholders = {
            'Text': 'Enter text value or {field_reference}',
            'Number': 'Enter number or {field_reference}',
            'Email': 'Enter email or {field_reference}',
            'URL': 'Enter URL or {field_reference}',
            'Date': 'Enter date (YYYY-MM-DD) or {field_reference}',
            'DateTime': 'Enter datetime or {field_reference}',
            'Currency': 'Enter amount or {field_reference}',
            'Textarea': 'Enter text content or {field_reference}',
            'Select': 'Select a value',
            'Radio': 'Select a value',
            'Checkbox': 'Check to set as true',
            'File': 'Enter file URL or {field_reference}'
        };

        return placeholders[fieldType] || 'Enter value or {field_reference}';
    }

    // Generate step configuration form
    function generateStepConfigForm(actionType, config) {
        let formHtml = '';

        switch (actionType) {
            case 'send_email':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-email-to">To Email</label>
                        <input type="email" id="step-email-to" class="dab-form-control" value="${config.to || ''}" placeholder="<EMAIL>">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-email-subject">Subject</label>
                        <input type="text" id="step-email-subject" class="dab-form-control" value="${config.subject || ''}" placeholder="Email subject">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-email-message">Message</label>
                        <textarea id="step-email-message" class="dab-form-control" rows="4" placeholder="Email message">${config.message || ''}</textarea>
                    </div>
                `;
                break;

            case 'create_record':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-table-id">Target Table</label>
                        <select id="step-table-id" class="dab-form-control">
                            <option value="">Select a table</option>
                            ${window.dabWorkflowData.tables.map(table =>
                                `<option value="${table.id}" ${config.table_id == table.id ? 'selected' : ''}>${table.table_label}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="dab-form-group">
                        <label>Field Mappings</label>
                        <div id="field-mappings-container">
                            <p class="dab-form-text">Select a table to configure field mappings</p>
                        </div>
                    </div>
                `;
                break;

            case 'update_record':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-table-id">Target Table</label>
                        <select id="step-table-id" class="dab-form-control">
                            <option value="">Select a table</option>
                            ${window.dabWorkflowData.tables.map(table =>
                                `<option value="${table.id}" ${config.table_id == table.id ? 'selected' : ''}>${table.table_label}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="dab-form-group">
                        <label for="step-record-id">Record ID</label>
                        <input type="text" id="step-record-id" class="dab-form-control" value="${config.record_id || ''}" placeholder="Record ID or field reference">
                    </div>
                    <div class="dab-form-group">
                        <label>Field Mappings</label>
                        <div id="field-mappings-container">
                            <p class="dab-form-text">Select a table to configure field mappings</p>
                        </div>
                    </div>
                `;
                break;

            case 'delete_record':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-table-id">Target Table</label>
                        <select id="step-table-id" class="dab-form-control">
                            <option value="">Select a table</option>
                            ${window.dabWorkflowData.tables.map(table =>
                                `<option value="${table.id}" ${config.table_id == table.id ? 'selected' : ''}>${table.table_label}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="dab-form-group">
                        <label for="step-record-id">Record ID</label>
                        <input type="text" id="step-record-id" class="dab-form-control" value="${config.record_id || ''}" placeholder="Record ID or field reference">
                    </div>
                `;
                break;

            case 'api_call':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-api-url">API URL</label>
                        <input type="url" id="step-api-url" class="dab-form-control" value="${config.url || ''}" placeholder="https://api.example.com/endpoint">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-api-method">HTTP Method</label>
                        <select id="step-api-method" class="dab-form-control">
                            <option value="GET" ${config.method === 'GET' ? 'selected' : ''}>GET</option>
                            <option value="POST" ${config.method === 'POST' ? 'selected' : ''}>POST</option>
                            <option value="PUT" ${config.method === 'PUT' ? 'selected' : ''}>PUT</option>
                            <option value="DELETE" ${config.method === 'DELETE' ? 'selected' : ''}>DELETE</option>
                        </select>
                    </div>
                    <div class="dab-form-group">
                        <label for="step-api-headers">Headers (JSON)</label>
                        <textarea id="step-api-headers" class="dab-form-control" rows="3" placeholder='{"Content-Type": "application/json"}'>${JSON.stringify(config.headers || {}, null, 2)}</textarea>
                    </div>
                    <div class="dab-form-group">
                        <label for="step-api-body">Request Body (JSON)</label>
                        <textarea id="step-api-body" class="dab-form-control" rows="4" placeholder='{"key": "value"}'>${JSON.stringify(config.body || {}, null, 2)}</textarea>
                    </div>
                `;
                break;

            case 'condition':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-condition-field">Field to Check</label>
                        <input type="text" id="step-condition-field" class="dab-form-control" value="${config.field || ''}" placeholder="field_name">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-condition-operator">Operator</label>
                        <select id="step-condition-operator" class="dab-form-control">
                            <option value="equals" ${config.operator === 'equals' ? 'selected' : ''}>Equals</option>
                            <option value="not_equals" ${config.operator === 'not_equals' ? 'selected' : ''}>Not Equals</option>
                            <option value="greater_than" ${config.operator === 'greater_than' ? 'selected' : ''}>Greater Than</option>
                            <option value="less_than" ${config.operator === 'less_than' ? 'selected' : ''}>Less Than</option>
                            <option value="contains" ${config.operator === 'contains' ? 'selected' : ''}>Contains</option>
                            <option value="empty" ${config.operator === 'empty' ? 'selected' : ''}>Is Empty</option>
                            <option value="not_empty" ${config.operator === 'not_empty' ? 'selected' : ''}>Is Not Empty</option>
                        </select>
                    </div>
                    <div class="dab-form-group">
                        <label for="step-condition-value">Value to Compare</label>
                        <input type="text" id="step-condition-value" class="dab-form-control" value="${config.value || ''}" placeholder="comparison value">
                    </div>
                `;
                break;

            case 'delay':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-delay-amount">Delay Amount</label>
                        <input type="number" id="step-delay-amount" class="dab-form-control" value="${config.amount || ''}" placeholder="5" min="1">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-delay-unit">Time Unit</label>
                        <select id="step-delay-unit" class="dab-form-control">
                            <option value="minutes" ${config.unit === 'minutes' ? 'selected' : ''}>Minutes</option>
                            <option value="hours" ${config.unit === 'hours' ? 'selected' : ''}>Hours</option>
                            <option value="days" ${config.unit === 'days' ? 'selected' : ''}>Days</option>
                        </select>
                    </div>
                `;
                break;

            case 'calculate':
                formHtml = `
                    <div class="dab-form-group">
                        <label for="step-calculate-formula">Formula</label>
                        <input type="text" id="step-calculate-formula" class="dab-form-control" value="${config.formula || ''}" placeholder="field1 + field2">
                    </div>
                    <div class="dab-form-group">
                        <label for="step-calculate-store">Store Result In</label>
                        <input type="text" id="step-calculate-store" class="dab-form-control" value="${config.store_in || ''}" placeholder="result_field">
                    </div>
                `;
                break;

            default:
                formHtml = '<p>No configuration options available for this action type.</p>';
        }

        return formHtml;
    }

    // Save step configuration
    function saveStepConfiguration() {
        if (!window.currentEditingStep) {
            alert('No step selected for editing.');
            return;
        }

        const step = window.currentEditingStep;
        const actionType = step.action;
        const config = {};

        // Collect configuration based on action type
        switch (actionType) {
            case 'send_email':
                config.to = $('#step-email-to').val();
                config.subject = $('#step-email-subject').val();
                config.message = $('#step-email-message').val();
                break;

            case 'create_record':
            case 'update_record':
                config.table_id = $('#step-table-id').val();
                if (actionType === 'update_record') {
                    config.record_id = $('#step-record-id').val();
                }

                // Collect field mappings from the visual interface
                config.field_mappings = {};
                $('.dab-field-mapping-value').each(function() {
                    const fieldSlug = $(this).data('field-slug');
                    let value = '';

                    if ($(this).is(':checkbox')) {
                        value = $(this).is(':checked') ? 'true' : 'false';
                    } else {
                        value = $(this).val();
                    }

                    // Only include non-empty values
                    if (value && value.trim() !== '') {
                        config.field_mappings[fieldSlug] = value;
                    }
                });
                break;

            case 'delete_record':
                config.table_id = $('#step-table-id').val();
                config.record_id = $('#step-record-id').val();
                break;

            case 'api_call':
                config.url = $('#step-api-url').val();
                config.method = $('#step-api-method').val();
                try {
                    config.headers = JSON.parse($('#step-api-headers').val() || '{}');
                    config.body = JSON.parse($('#step-api-body').val() || '{}');
                } catch (e) {
                    alert('Invalid JSON in headers or body.');
                    return;
                }
                break;

            case 'condition':
                config.field = $('#step-condition-field').val();
                config.operator = $('#step-condition-operator').val();
                config.value = $('#step-condition-value').val();
                break;

            case 'delay':
                config.amount = parseInt($('#step-delay-amount').val());
                config.unit = $('#step-delay-unit').val();
                break;

            case 'calculate':
                config.formula = $('#step-calculate-formula').val();
                config.store_in = $('#step-calculate-store').val();
                break;
        }

        // Update step configuration
        step.config = config;

        // Update visual representation if needed
        const stepElement = $('#' + step.id);
        const actionConfig = getActionConfig(actionType);

        // Update step content to show it's configured
        stepElement.find('.dab-workflow-step-content').html(
            actionConfig.description + '<br><small style="color: #666;">Configured</small>'
        );

        hideStepConfigModal();

        alert('Step configuration saved successfully!');
    }

    // Initialize the workflow builder
    initWorkflowBuilder();
});
