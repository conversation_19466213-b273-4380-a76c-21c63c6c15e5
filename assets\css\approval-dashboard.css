/**
 * Enhanced Approval Dashboard Styles
 */

/* Loading Overlay */
.dab-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.dab-loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.dab-loading-text {
    color: white;
    font-size: 16px;
    margin-top: 10px;
    font-weight: bold;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.dab-notice {
    position: relative;
    padding: 15px 20px;
    margin: 15px 0;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

.dab-notice p {
    margin: 0;
    padding-right: 30px;
    font-size: 14px;
}

.dab-notice-success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.dab-notice-error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.dab-notice-dismiss {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.dab-notice-dismiss:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Main Dashboard Container */
.dab-approval-dashboard {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
    animation: fadeIn 0.5s ease-in-out;
}

/* Dashboard Header */
.dab-dashboard-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.dab-dashboard-title {
    font-size: 20px;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

/* Dashboard Stats */
.dab-dashboard-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    width: 100%;
}

.dab-stat-card {
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    flex: 1;
    min-width: 150px;
    text-align: center;
}

.dab-stat-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.dab-counter-highlight {
    animation: counterPulse 1s ease;
}

@keyframes counterPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.dab-stat-label {
    font-size: 14px;
    color: #6c757d;
}

.dab-stat-pending .dab-stat-number {
    color: #fd7e14;
}

.dab-stat-approved .dab-stat-number {
    color: #28a745;
}

.dab-stat-rejected .dab-stat-number {
    color: #dc3545;
}

.dab-stat-user-pending {
    font-size: 12px;
    margin-top: 5px;
    padding: 3px 8px;
    background-color: #fff;
    border-radius: 10px;
    display: inline-block;
    color: #856404;
    font-weight: 600;
    transition: all 0.3s ease;
}

.dab-highlight-count {
    background-color: #fd7e14;
    color: white;
    animation: pulse 2s infinite;
}

/* Dashboard Filters */
.dab-dashboard-filters {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.dab-filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dab-filter-label {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.dab-filter-select,
.dab-filter-input,
.dab-filter-date {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    z-index: 100;
    height: auto;
    appearance: menulist;
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
}

.dab-filter-select:focus,
.dab-filter-input:focus,
.dab-filter-date:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dab-filter-search {
    flex: 1;
    min-width: 200px;
    position: relative;
}

.dab-filter-search input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.dab-filter-search input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dab-filter-search::before {
    content: "🔍";
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Active Filters */
.dab-active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px 20px;
    background-color: #e9f5ff;
    border-bottom: 1px solid #cce5ff;
    align-items: center;
    animation: slideIn 0.3s ease-out;
}

.dab-filter-badge {
    display: inline-block;
    padding: 5px 10px;
    background-color: #007bff;
    color: white;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.dab-clear-filters {
    margin-left: auto;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dab-clear-filters:hover {
    background-color: #e9ecef;
}

/* Approval Records */
.dab-approval-records {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
}

.dab-approval-record {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-left: 4px solid transparent;
    margin-bottom: 15px;
    animation: slideIn 0.3s ease-out;
}

.dab-approval-record:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Highlight records assigned to current user */
.dab-approval-record[data-assigned="true"] {
    border-left: 4px solid #4caf50;
    background-color: #f9fff9;
}

.dab-approval-record[data-assigned="true"]::before {
    content: "Assigned to You";
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #4caf50;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
    animation: pulse 2s infinite;
}

/* Status-based styling */
.dab-approval-record[data-status="Pending"] {
    border-left: 4px solid #ff9800;
}

.dab-approval-record[data-status="Approved"] {
    border-left: 4px solid #4caf50;
    background-color: #f9fff9;
}

.dab-approval-record[data-status="Rejected"] {
    border-left: 4px solid #f44336;
    background-color: #fff9f9;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.dab-record-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.dab-record-title {
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
    margin: 0 0 5px 0;
}

.dab-record-meta {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: #6c757d;
}

.dab-record-submitter, .dab-record-date, .dab-record-level {
    display: flex;
    align-items: center;
}

.dab-record-submitter::before {
    content: "\f110";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-record-date::before {
    content: "\f508";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-record-level::before {
    content: "\f313";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-approval-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.dab-status-pending::before {
    content: "\f469";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-status-approved {
    background-color: #d4edda;
    color: #155724;
}

.dab-status-approved::before {
    content: "\f147";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.dab-status-rejected::before {
    content: "\f335";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-record-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.dab-record-field {
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
}

.dab-field-label {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 3px;
    font-weight: 600;
}

.dab-field-value {
    font-size: 15px;
    color: #212529;
    word-break: break-word;
}

.dab-record-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dab-action-btn {
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-view-btn {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
}

.dab-view-btn:hover {
    background-color: #e9ecef;
}

.dab-view-btn::before {
    content: "\f177";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-approve-btn {
    background-color: #28a745;
    color: white;
}

.dab-approve-btn:hover {
    background-color: #218838;
}

.dab-approve-btn::before {
    content: "\f147";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-reject-btn {
    background-color: #dc3545;
    color: white;
}

.dab-reject-btn:hover {
    background-color: #c82333;
}

.dab-reject-btn::before {
    content: "\f335";
    font-family: dashicons;
    margin-right: 5px;
}

/* Record Details Modal */
.dab-record-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dab-record-meta-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.dab-record-meta-info > div {
    margin-bottom: 5px;
}

.dab-section-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
    color: #343a40;
}

/* Approval History */
.dab-approval-history {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.dab-history-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.dab-history-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dab-history-item {
    padding: 15px;
    border-left: 3px solid #6c757d;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border-radius: 0 5px 5px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dab-history-item.approved {
    border-left-color: #28a745;
}

.dab-history-item.rejected {
    border-left-color: #dc3545;
}

.dab-history-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    align-items: center;
}

.dab-history-user {
    font-weight: 500;
    color: #343a40;
    display: flex;
    align-items: center;
}

.dab-history-user::before {
    content: "\f110";
    font-family: dashicons;
    margin-right: 5px;
    color: #6c757d;
}

.dab-history-date {
    font-size: 12px;
    color: #6c757d;
}

.dab-history-status {
    display: inline-flex;
    align-items: center;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 12px;
    margin-right: 5px;
    font-weight: 600;
}

.dab-history-status.approved {
    background-color: #d4edda;
    color: #155724;
}

.dab-history-status.approved::before {
    content: "\f147";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-history-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.dab-history-status.rejected::before {
    content: "\f335";
    font-family: dashicons;
    margin-right: 5px;
}

.dab-history-notes {
    font-style: italic;
    color: #495057;
    margin-top: 10px;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border-left: 2px solid #dee2e6;
}

.dab-no-history {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

/* Approval Modal */
.dab-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
    backdrop-filter: blur(3px);
    animation: fadeIn 0.3s ease-out;
}

.dab-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 600px;
    z-index: 1001;
    display: none;
    animation: slideDown 0.3s ease-out;
    max-height: 90vh;
    overflow: hidden;
    flex-direction: column;
}

.dab-modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.dab-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.dab-modal-close:hover {
    background-color: #e9ecef;
    color: #343a40;
}

.dab-modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.dab-form-group {
    margin-bottom: 20px;
}

.dab-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.dab-form-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.dab-form-textarea:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dab-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #f8f9fa;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translate(-50%, -60%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%);
        opacity: 1;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dab-dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .dab-dashboard-stats {
        margin-top: 15px;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .dab-stat-card {
        min-width: 45%;
        margin-bottom: 10px;
        flex: 0 0 45%;
    }

    .dab-dashboard-filters {
        flex-direction: column;
        align-items: stretch;
        padding: 12px 15px;
    }

    .dab-filter-group {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        margin-bottom: 15px;
    }

    .dab-filter-select,
    .dab-filter-input,
    .dab-filter-date {
        width: 100%;
        margin-bottom: 10px;
        padding: 10px;
        font-size: 16px; /* Larger font for mobile */
    }

    .dab-filter-search {
        width: 100%;
    }

    .dab-filter-search input {
        width: 100%;
        padding: 10px 12px 10px 35px;
        font-size: 16px; /* Larger font for mobile */
    }

    .dab-record-header {
        flex-direction: column;
    }

    .dab-record-meta {
        margin-top: 10px;
        flex-direction: column;
        gap: 8px;
    }

    .dab-record-actions {
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 10px;
        margin-top: 15px;
    }

    .dab-action-btn {
        flex: 1 1 auto;
        min-width: 120px;
        margin: 0;
        padding: 12px 15px; /* Larger touch targets */
        font-size: 16px; /* Larger font for mobile */
    }

    .dab-modal {
        width: 95%;
        max-width: 95%;
        top: 5%;
        left: 2.5%;
        transform: none;
        height: 90vh;
        overflow-y: auto;
    }

    .dab-modal-body {
        max-height: none;
        overflow-y: auto;
        padding: 15px;
    }

    .dab-modal-header {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .dab-modal-footer {
        flex-direction: column;
        gap: 10px;
        position: sticky;
        bottom: 0;
        z-index: 10;
    }

    .dab-modal-footer button {
        width: 100%;
        margin: 0;
        padding: 12px 15px; /* Larger touch targets */
        font-size: 16px; /* Larger font for mobile */
    }

    .dab-record-field {
        flex-direction: column;
    }

    .dab-field-label {
        width: 100%;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .dab-field-value {
        width: 100%;
        font-size: 16px;
    }

    /* Improve record display on mobile */
    .dab-approval-record {
        padding: 15px;
        margin-bottom: 15px;
    }

    .dab-record-content {
        grid-template-columns: 1fr;
        padding: 12px;
    }

    /* Improve status badges on mobile */
    .dab-approval-status {
        margin-top: 10px;
        align-self: flex-start;
        width: 100%;
        text-align: center;
        padding: 8px 0;
    }

    /* Animation for mobile */
    .dab-approval-record {
        transition: transform 0.2s ease;
    }

    .dab-approval-record:active {
        transform: scale(0.98);
    }
}
