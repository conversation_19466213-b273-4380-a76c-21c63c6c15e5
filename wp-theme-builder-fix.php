<?php
/**
 * Temporary fix for WP Theme Builder plugin errors
 * Add this code to your theme's functions.php file
 */

// Prevent WP Theme Builder from loading problematic scripts on frontend pages
add_action('wp_enqueue_scripts', 'fix_wp_theme_builder_conflicts', 999);

// Global script conflict resolver
add_action('wp_enqueue_scripts', 'dab_resolve_script_conflicts', 1000);

function fix_wp_theme_builder_conflicts() {
    // Check if we're on a frontend page that doesn't need WP Theme Builder
    if (is_page(array('login', 'register', 'user-dashboard', 'user-profile', 'chat'))) {

        // Dequeue problematic scripts
        wp_dequeue_script('conditional-logic');
        wp_dequeue_script('popup-builder');
        wp_dequeue_script('mobile-detect');
        wp_dequeue_script('share-modal'); // Dequeue any external share-modal script

        // Dequeue problematic styles
        wp_dequeue_style('popup-builder');

        // Add fallback for MobileDetect if needed
        add_action('wp_footer', 'add_mobile_detect_fallback');
    }
}

function dab_resolve_script_conflicts() {
    // List of problematic scripts that might conflict
    $problematic_scripts = array(
        'share-modal',
        'share-modal-js',
        'social-share-modal',
        'modal-share',
        'wp-share-modal'
    );

    // Dequeue any problematic scripts
    foreach ($problematic_scripts as $script) {
        if (wp_script_is($script, 'enqueued')) {
            wp_dequeue_script($script);
        }
    }

    // Ensure our error handler loads first
    if (!wp_script_is('dab-error-handler', 'enqueued')) {
        wp_enqueue_script('dab-error-handler', plugin_dir_url(dirname(__FILE__)) . 'assets/js/error-handler.js', array('jquery'), DAB_VERSION, true);
    }
}

function add_mobile_detect_fallback() {
    ?>
    <script>
    // Fallback for MobileDetect if not loaded
    if (typeof MobileDetect === 'undefined') {
        window.MobileDetect = function() {
            return {
                mobile: function() { return false; },
                tablet: function() { return false; },
                phone: function() { return false; }
            };
        };
    }
    </script>
    <?php
}

// Fix autocomplete attributes for password fields
add_action('wp_footer', 'fix_password_autocomplete');

// Add JavaScript error prevention
add_action('wp_footer', 'dab_add_error_prevention_script', 5);

function dab_add_error_prevention_script() {
    ?>
    <script>
    // DAB Error Prevention Script
    (function() {
        'use strict';

        // Prevent share-modal.js errors
        if (typeof window.shareModal === 'undefined') {
            window.shareModal = {
                init: function() {},
                show: function() {},
                hide: function() {},
                addEventListener: function() {}
            };
        }

        // Prevent null reference errors on common elements
        document.addEventListener('DOMContentLoaded', function() {
            // Create placeholder elements if they don't exist
            const requiredElements = [
                { id: 'share-modal', tag: 'div' },
                { class: 'dab-modal', tag: 'div' },
                { class: 'share-btn', tag: 'button' }
            ];

            requiredElements.forEach(function(element) {
                let exists = false;

                if (element.id) {
                    exists = document.getElementById(element.id) !== null;
                } else if (element.class) {
                    exists = document.querySelector('.' + element.class) !== null;
                }

                if (!exists) {
                    const placeholder = document.createElement(element.tag);
                    placeholder.style.display = 'none';
                    placeholder.className = 'dab-error-prevention-placeholder';

                    if (element.id) {
                        placeholder.id = element.id;
                    }
                    if (element.class) {
                        placeholder.className += ' ' + element.class;
                    }

                    document.body.appendChild(placeholder);
                }
            });
        });

        // Override addEventListener to prevent passive event warnings
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove', 'touchend'];

            if (passiveEvents.includes(type)) {
                if (typeof options === 'boolean') {
                    options = { capture: options, passive: true };
                } else if (typeof options === 'object' && options !== null) {
                    options.passive = true;
                } else {
                    options = { passive: true };
                }
            }

            return originalAddEventListener.call(this, type, listener, options);
        };

    })();
    </script>
    <?php
}

function fix_password_autocomplete() {
    if (is_page(array('login', 'register', 'user-profile'))) {
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add autocomplete attributes to password fields
            const passwordFields = document.querySelectorAll('input[type="password"]');
            passwordFields.forEach(function(field) {
                if (field.name === 'password' && field.closest('form').querySelector('input[name="username"]')) {
                    field.setAttribute('autocomplete', 'current-password');
                } else if (field.name === 'password' || field.name === 'new_password') {
                    field.setAttribute('autocomplete', 'new-password');
                } else if (field.name === 'confirm_password') {
                    field.setAttribute('autocomplete', 'new-password');
                }
            });
        });
        </script>
        <?php
    }
}
