/* Enhanced Dropdown Styles */
.dab-enhanced-select-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
}

.dab-enhanced-dropdown,
.dab-lookup-field,
.dab-relationship-field {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

.dab-enhanced-dropdown:focus,
.dab-lookup-field:focus,
.dab-relationship-field:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.dab-select-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #666;
}

.dab-select-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #666;
}

.dab-select-icon + select {
    padding-left: 35px;
}

/* Dropdown options styling */
.dab-enhanced-dropdown option,
.dab-lookup-field option,
.dab-relationship-field option {
    padding: 8px;
}

/* Optgroup styling */
.dab-enhanced-dropdown optgroup,
.dab-lookup-field optgroup,
.dab-relationship-field optgroup {
    font-weight: bold;
    color: #333;
    background-color: #f5f5f5;
}

/* Error state */
.dab-enhanced-select-wrapper.has-error .dab-enhanced-dropdown,
.dab-enhanced-select-wrapper.has-error .dab-lookup-field,
.dab-enhanced-select-wrapper.has-error .dab-relationship-field {
    border-color: #d63638;
}

/* Disabled state */
.dab-enhanced-dropdown:disabled,
.dab-lookup-field:disabled,
.dab-relationship-field:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Loading indicator */
.dab-select-loading {
    position: absolute;
    right: 35px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: dab-spin 1s linear infinite;
}

@keyframes dab-spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Responsive styles */
@media screen and (max-width: 782px) {
    .dab-enhanced-dropdown,
    .dab-lookup-field,
    .dab-relationship-field {
        padding: 10px 35px 10px 12px;
        font-size: 16px; /* Larger font size for mobile */
    }
    
    .dab-select-icon + select {
        padding-left: 40px;
    }
}
