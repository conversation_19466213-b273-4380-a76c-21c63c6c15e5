<?php
/**
 * User Login Shortcode
 *
 * Provides frontend login functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register login shortcode
 */
function dab_user_login_shortcode($atts) {
    $atts = shortcode_atts(array(
        'redirect' => '',
        'show_register_link' => 'true',
        'show_forgot_password' => 'true'
    ), $atts, 'dab_user_login');

    // Check if user is already logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    if ($current_user) {
        $dashboard_url = home_url('/user-dashboard/');
        return '<div class="dab-login-message">
                    <p>You are already logged in as <strong>' . esc_html($current_user->username) . '</strong></p>
                    <p><a href="' . esc_url($dashboard_url) . '" class="dab-btn dab-btn-primary">Go to Dashboard</a></p>
                </div>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-frontend-auth', plugin_dir_url(dirname(__FILE__)) . 'assets/css/frontend-auth.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-frontend-auth', plugin_dir_url(dirname(__FILE__)) . 'assets/js/frontend-auth.js', array('jquery'), DAB_VERSION, true);

    // Localize script
    wp_localize_script('dab-frontend-auth', 'dab_auth', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_frontend_nonce'),
        'redirect_url' => !empty($atts['redirect']) ? $atts['redirect'] : home_url('/user-dashboard/'),
        'messages' => array(
            'login_success' => __('Login successful! Redirecting...', 'db-app-builder'),
            'login_error' => __('Login failed. Please try again.', 'db-app-builder'),
            'required_fields' => __('Please fill in all required fields.', 'db-app-builder')
        )
    ));

    // Check for verification message
    $verification_message = '';
    if (isset($_GET['verified']) && $_GET['verified'] == '1') {
        $verification_message = '<div class="dab-alert dab-alert-success">
            <p>' . __('Email verified successfully! You can now log in.', 'db-app-builder') . '</p>
        </div>';
    }

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-login-container">
        <?php echo $verification_message; ?>
        
        <div class="dab-login-form-wrapper">
            <h2 class="dab-login-title"><?php _e('Login to Your Account', 'db-app-builder'); ?></h2>
            
            <form id="dab-login-form" class="dab-login-form">
                <div class="dab-form-group">
                    <label for="username_or_email"><?php _e('Username or Email', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="text" id="username_or_email" name="username_or_email" class="dab-form-control" required>
                </div>

                <div class="dab-form-group">
                    <label for="password"><?php _e('Password', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="password" id="password" name="password" class="dab-form-control" required>
                </div>

                <div class="dab-form-group dab-form-checkbox">
                    <label>
                        <input type="checkbox" id="remember" name="remember" value="1">
                        <?php _e('Remember me', 'db-app-builder'); ?>
                    </label>
                </div>

                <div class="dab-form-group">
                    <button type="submit" class="dab-btn dab-btn-primary dab-btn-block">
                        <span class="dab-btn-text"><?php _e('Login', 'db-app-builder'); ?></span>
                        <span class="dab-btn-loading" style="display: none;">
                            <span class="dab-spinner"></span>
                            <?php _e('Logging in...', 'db-app-builder'); ?>
                        </span>
                    </button>
                </div>

                <div class="dab-form-messages">
                    <div class="dab-alert dab-alert-error" id="dab-login-error" style="display: none;"></div>
                    <div class="dab-alert dab-alert-success" id="dab-login-success" style="display: none;"></div>
                </div>
            </form>

            <div class="dab-login-links">
                <?php if ($atts['show_forgot_password'] === 'true'): ?>
                <p class="dab-forgot-password">
                    <a href="#" id="dab-forgot-password-link"><?php _e('Forgot your password?', 'db-app-builder'); ?></a>
                </p>
                <?php endif; ?>

                <?php if ($atts['show_register_link'] === 'true'): ?>
                <p class="dab-register-link">
                    <?php _e("Don't have an account?", 'db-app-builder'); ?> 
                    <a href="<?php echo home_url('/register/'); ?>"><?php _e('Register here', 'db-app-builder'); ?></a>
                </p>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($atts['show_forgot_password'] === 'true'): ?>
        <!-- Forgot Password Modal -->
        <div id="dab-forgot-password-modal" class="dab-modal" style="display: none;">
            <div class="dab-modal-content">
                <div class="dab-modal-header">
                    <h3><?php _e('Reset Password', 'db-app-builder'); ?></h3>
                    <button type="button" class="dab-modal-close" id="dab-close-forgot-modal">&times;</button>
                </div>
                <div class="dab-modal-body">
                    <form id="dab-forgot-password-form">
                        <div class="dab-form-group">
                            <label for="forgot_email"><?php _e('Email Address', 'db-app-builder'); ?> <span class="required">*</span></label>
                            <input type="email" id="forgot_email" name="email" class="dab-form-control" required>
                            <small class="dab-form-help"><?php _e('Enter your email address and we will send you a password reset link.', 'db-app-builder'); ?></small>
                        </div>

                        <div class="dab-form-group">
                            <button type="submit" class="dab-btn dab-btn-primary">
                                <span class="dab-btn-text"><?php _e('Send Reset Link', 'db-app-builder'); ?></span>
                                <span class="dab-btn-loading" style="display: none;">
                                    <span class="dab-spinner"></span>
                                    <?php _e('Sending...', 'db-app-builder'); ?>
                                </span>
                            </button>
                        </div>

                        <div class="dab-form-messages">
                            <div class="dab-alert dab-alert-error" id="dab-forgot-error" style="display: none;"></div>
                            <div class="dab-alert dab-alert-success" id="dab-forgot-success" style="display: none;"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <style>
    .dab-login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
    }

    .dab-login-form-wrapper {
        background: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
    }

    .dab-login-title {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
        font-size: 24px;
        font-weight: 600;
    }

    .dab-form-group {
        margin-bottom: 20px;
    }

    .dab-form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }

    .dab-form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .dab-form-control:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    }

    .dab-form-checkbox label {
        display: flex;
        align-items: center;
        font-weight: normal;
    }

    .dab-form-checkbox input[type="checkbox"] {
        margin-right: 8px;
        width: auto;
    }

    .dab-btn {
        display: inline-block;
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s;
        text-align: center;
    }

    .dab-btn-primary {
        background-color: #007cba;
        color: white;
    }

    .dab-btn-primary:hover {
        background-color: #005a87;
    }

    .dab-btn-block {
        width: 100%;
    }

    .dab-btn-loading {
        display: none;
    }

    .dab-btn.loading .dab-btn-text {
        display: none;
    }

    .dab-btn.loading .dab-btn-loading {
        display: inline-block;
    }

    .dab-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
        margin-right: 8px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .dab-alert {
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .dab-alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .dab-alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .dab-login-links {
        text-align: center;
        margin-top: 20px;
    }

    .dab-login-links p {
        margin: 10px 0;
    }

    .dab-login-links a {
        color: #007cba;
        text-decoration: none;
    }

    .dab-login-links a:hover {
        text-decoration: underline;
    }

    .required {
        color: #dc3545;
    }

    .dab-form-help {
        display: block;
        margin-top: 5px;
        color: #6c757d;
        font-size: 12px;
    }

    /* Modal Styles */
    .dab-modal {
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .dab-modal-content {
        background-color: #fff;
        margin: 10% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .dab-modal-header {
        padding: 20px 30px;
        border-bottom: 1px solid #e1e5e9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dab-modal-header h3 {
        margin: 0;
        color: #333;
    }

    .dab-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .dab-modal-close:hover {
        color: #333;
    }

    .dab-modal-body {
        padding: 30px;
    }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_user_login', 'dab_user_login_shortcode');
