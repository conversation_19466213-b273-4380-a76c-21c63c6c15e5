/**
 * Enhanced Data Management Dashboard Styles
 */

/* Main Dashboard Container */
.dab-data-dashboard-wrap {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
}

.dab-dashboard-title {
    font-size: 28px;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 10px;
}

.dab-dashboard-description {
    color: #6c757d;
    margin-bottom: 20px;
    font-size: 16px;
}

.dab-dashboard-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Table Selection Panel */
.dab-table-selection-panel {
    flex: 0 0 250px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.dab-panel-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-panel-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
}

.dab-panel-actions {
    display: flex;
    gap: 5px;
}

.dab-panel-actions button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    transition: all 0.2s ease;
}

.dab-panel-actions button:hover {
    background-color: #e9ecef;
    color: #343a40;
}

.dab-panel-actions button span {
    font-size: 18px;
}

.dab-panel-body {
    padding: 15px;
}

.dab-search-box {
    position: relative;
    margin-bottom: 15px;
}

.dab-search-box input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.dab-search-box .dashicons {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.dab-tables-list {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.dab-no-tables-message {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    margin-bottom: 10px;
}

.dab-no-tables-message p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    text-align: center;
}

.dab-table-item {
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.2s ease;
}

.dab-table-item:hover {
    background-color: #f8f9fa;
}

.dab-table-item.active {
    background-color: #e9ecef;
    font-weight: 600;
}

.dab-table-item .dashicons {
    color: #6c757d;
}

.dab-table-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Data Display Panel */
.dab-data-display-panel {
    flex: 1;
    min-width: 0;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.dab-panel-actions button {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-panel-actions button:hover {
    background-color: #e9ecef;
}

.dab-add-record {
    background-color: #28a745 !important;
    color: white !important;
    border-color: #28a745 !important;
}

.dab-add-record:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}

/* Filters Section */
.dab-filters-section {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* Filter Tabs */
.dab-filter-tabs,
.dab-import-export-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

.dab-tab-button {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-tab-button:hover {
    background-color: #e9ecef;
}

.dab-tab-button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.dab-filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.dab-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dab-filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
}

.dab-filter-group select,
.dab-filter-group input {
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

.dab-filter-actions {
    display: flex;
    align-items: flex-end;
}

.dab-add-filter,
.dab-apply-advanced-filters,
.dab-save-advanced-filters,
.dab-import-data,
.dab-export-data {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-add-filter:hover,
.dab-apply-advanced-filters:hover,
.dab-save-advanced-filters:hover,
.dab-import-data:hover,
.dab-export-data:hover {
    background-color: #0069d9;
}

.dab-import-data:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Import Options */
.dab-import-options {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.dab-import-options h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
}

.dab-import-option {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.dab-import-option input[type="checkbox"] {
    margin-right: 10px;
}

.dab-import-option label {
    font-size: 14px;
}

/* Advanced Filter Builder */
.dab-advanced-filter-builder {
    margin-bottom: 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 15px;
    background-color: #fff;
}

.dab-filter-group-container {
    margin-bottom: 10px;
}

.dab-filter-group-header {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.dab-filter-group-operator {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-weight: 600;
}

.dab-add-filter-group,
.dab-add-filter-condition {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.dab-add-filter-group:hover,
.dab-add-filter-condition:hover {
    background-color: #e9ecef;
}

.dab-filter-conditions {
    padding-left: 20px;
}

.dab-filter-condition {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
    align-items: flex-end;
}

.dab-remove-condition {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-remove-condition:hover {
    background-color: #c82333;
}

.dab-advanced-filter-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.dab-save-advanced-filters {
    background-color: #28a745;
}

.dab-save-advanced-filters:hover {
    background-color: #218838;
}

/* Saved Filters */
.dab-saved-filters-list {
    margin-bottom: 15px;
}

.dab-no-saved-filters {
    color: #6c757d;
    font-style: italic;
}

.dab-saved-filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #fff;
}

.dab-saved-filter-name {
    font-weight: 600;
}

.dab-saved-filter-actions {
    display: flex;
    gap: 5px;
}

.dab-saved-filter-actions button {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-saved-filter-actions button:hover {
    background-color: #e9ecef;
}

.dab-active-filters {
    margin-top: 15px;
}

.dab-active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dab-active-filters-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #343a40;
}

.dab-clear-all-filters {
    background-color: transparent;
    border: none;
    color: #dc3545;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.dab-clear-all-filters:hover {
    text-decoration: underline;
}

.dab-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.dab-filter-tag {
    background-color: #e9ecef;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dab-filter-tag-remove {
    cursor: pointer;
    color: #dc3545;
}

/* Visualization Section */
.dab-visualization-section {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.dab-visualization-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.dab-visualization-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dab-visualization-group label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
}

.dab-visualization-group select {
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

.dab-visualization-actions {
    display: flex;
    align-items: flex-end;
}

.dab-generate-chart {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-generate-chart:hover {
    background-color: #0069d9;
}

.dab-chart-container {
    height: 300px;
    margin-top: 15px;
}

/* Data Table Section */
.dab-data-table-section {
    flex: 1;
    padding: 15px;
    position: relative;
    overflow: auto;
}

.dab-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    display: none;
}

.dab-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dab-loading-text {
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
}

/* Modal Styles */
.dab-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.dab-modal-content {
    background-color: #fff;
    margin: 50px auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.dab-modal-header {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
}

.dab-modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.dab-modal-close:hover {
    color: #000;
}

.dab-modal-body {
    padding: 15px;
    overflow-y: auto;
    flex: 1;
}

/* Form Tabs */
.dab-form-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

.dab-form-tab {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-form-tab:hover {
    background-color: #e9ecef;
}

.dab-form-tab.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.dab-form-validation-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    display: none;
}

/* Form Fields */
.dab-form-field {
    margin-bottom: 15px;
}

.dab-form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
}

.dab-form-field input[type="text"],
.dab-form-field input[type="email"],
.dab-form-field input[type="number"],
.dab-form-field input[type="date"],
.dab-form-field textarea,
.dab-form-field select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.dab-form-field input[type="text"]:focus,
.dab-form-field input[type="email"]:focus,
.dab-form-field input[type="number"]:focus,
.dab-form-field input[type="date"]:focus,
.dab-form-field textarea:focus,
.dab-form-field select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.dab-form-field textarea {
    min-height: 100px;
    resize: vertical;
}

.dab-form-field .dab-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dab-form-field .dab-radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.dab-form-field input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.dab-form-field .dab-field-description {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.dab-form-field.required label:after {
    content: " *";
    color: #dc3545;
}

.dab-modal-footer {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.dab-modal-footer button {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.dab-modal-cancel {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    color: #6c757d;
}

.dab-modal-cancel:hover {
    background-color: #e9ecef;
}

.dab-modal-save {
    background-color: #007bff;
    color: white;
    border: none;
}

.dab-modal-save:hover {
    background-color: #0069d9;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .dab-dashboard-container {
        flex-direction: column;
    }

    .dab-table-selection-panel {
        flex: 0 0 auto;
        width: 100%;
    }

    .dab-tables-list {
        max-height: 200px;
    }

    .dab-modal-content {
        width: 95%;
    }
}

@media (max-width: 768px) {
    .dab-filter-controls,
    .dab-visualization-controls {
        flex-direction: column;
    }

    .dab-filter-group,
    .dab-visualization-group {
        width: 100%;
    }

    .dab-filter-actions,
    .dab-visualization-actions {
        margin-top: 10px;
    }

    .dab-panel-actions button {
        padding: 5px;
    }

    .dab-panel-actions button span {
        margin-right: 0;
    }

    .dab-panel-actions button span + span {
        display: none;
    }
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-left: 5px;
}

.dataTables_wrapper .dataTables_length select {
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.dataTables_wrapper .dataTables_info {
    padding-top: 15px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 5px 10px;
    border-radius: 4px;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef !important;
    border-color: #ced4da !important;
    color: #343a40 !important;
}

/* Import/Export Section */
.dab-import-export-section {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.dab-import-instructions,
.dab-export-instructions {
    margin-bottom: 15px;
}

.dab-import-options,
.dab-export-options {
    margin-bottom: 15px;
}

.dab-import-option,
.dab-export-option {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.dab-export-option-group {
    margin-bottom: 15px;
}

.dab-export-format-options {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.dab-export-format-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.dab-file-upload {
    position: relative;
    border: 2px dashed #ced4da;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dab-file-upload:hover {
    border-color: #007bff;
}

.dab-file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.dab-file-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #6c757d;
}

.dab-file-upload-label .dashicons {
    font-size: 24px;
}

.dab-import-preview {
    margin-bottom: 15px;
}

.dab-import-preview h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #343a40;
}

.dab-preview-container {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 15px;
    background-color: #fff;
    max-height: 200px;
    overflow-y: auto;
}

.dab-preview-placeholder {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    margin: 0;
}

.dab-preview-table {
    width: 100%;
    border-collapse: collapse;
}

.dab-preview-table th,
.dab-preview-table td {
    padding: 8px;
    border: 1px solid #ced4da;
    text-align: left;
}

.dab-preview-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.dab-import-actions,
.dab-export-actions {
    display: flex;
    justify-content: flex-end;
}

/* Bulk Actions */
.dab-bulk-actions-dropdown {
    position: relative;
}

.dab-bulk-actions-toggle {
    background-color: #6c757d !important;
    color: white !important;
    border-color: #6c757d !important;
}

.dab-bulk-actions-toggle:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
}

.dab-bulk-actions-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    min-width: 150px;
    display: none;
}

.dab-bulk-actions-dropdown:hover .dab-bulk-actions-menu {
    display: block;
}

.dab-bulk-actions-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 8px 15px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dab-bulk-actions-menu button:hover {
    background-color: #f8f9fa;
}

.dab-bulk-delete {
    color: #dc3545;
}

.dab-bulk-export {
    color: #007bff;
}



/* Action Buttons */
.dab-action-btn {
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.dab-edit-btn {
    background-color: #007bff;
    color: white;
    border: none;
}

.dab-edit-btn:hover {
    background-color: #0069d9;
}

.dab-delete-btn {
    background-color: #dc3545;
    color: white;
    border: none;
}

.dab-delete-btn:hover {
    background-color: #c82333;
}

.dab-view-btn {
    background-color: #6c757d;
    color: white;
    border: none;
}

.dab-view-btn:hover {
    background-color: #5a6268;
}

.dab-approve-btn {
    background-color: #28a745;
    color: white;
    border: none;
}

.dab-approve-btn:hover {
    background-color: #218838;
}

.dab-reject-btn {
    background-color: #dc3545;
    color: white;
    border: none;
}

.dab-reject-btn:hover {
    background-color: #c82333;
}
