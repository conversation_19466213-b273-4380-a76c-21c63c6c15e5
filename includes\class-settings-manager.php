<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Settings_Manager {

    public static function get($key, $default = '') {
        $value = function_exists('dab_safe_get_option') ? dab_safe_get_option('dab_' . $key, $default) : get_option('dab_' . $key, $default);
        return ($value !== null) ? $value : $default;
    }

    public static function set($key, $value) {
        return update_option('dab_' . $key, $value);
    }

    public static function delete($key) {
        return delete_option('dab_' . $key);
    }
}
?>
