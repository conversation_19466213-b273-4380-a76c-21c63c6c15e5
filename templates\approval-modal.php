<?php
/**
 * Approval Modal Template
 *
 * This template displays the approval modal for reviewing and approving/rejecting records.
 */
if (!defined('ABSPATH')) exit;
?>

<!-- Approval Modal Overlay -->
<div class="dab-modal-overlay"></div>

<!-- Loading Overlay -->
<div class="dab-loading-overlay">
    <div class="dab-loading-spinner"></div>
    <div class="dab-loading-text">Processing...</div>
</div>

<!-- Approval Modal -->
<div id="dab-approval-modal" class="dab-modal">
    <div class="dab-modal-header">
        <h3 id="dab-approval-modal-title" class="dab-modal-title">Approve Record</h3>
        <button type="button" class="dab-modal-close">&times;</button>
    </div>

    <div class="dab-approval-form">
        <div class="dab-modal-body">
            <p>You are about to take action on: <strong id="dab-approval-record-title"></strong></p>

            <div class="dab-form-group">
                <label for="dab-approval-note" class="dab-form-label">Add a note (optional):</label>
                <textarea id="dab-approval-note" name="approval_note" class="dab-form-textarea" rows="4" placeholder="Enter any comments or notes about this approval decision..."></textarea>
            </div>

            <input type="hidden" id="dab-approval-action" name="dab_approve_action" value="">
            <input type="hidden" id="dab-approval-record-id" name="record_id" value="">
            <input type="hidden" id="dab-approval-table-id" name="table_id" value="">
            <input type="hidden" name="action" value="dab_process_approval">
            <?php
            // Create and output the nonce
            $approval_nonce = wp_create_nonce('dab_approval_nonce');
            $general_nonce = wp_create_nonce('dab_nonce');
            ?>
            <input type="hidden" name="nonce" value="<?php echo $approval_nonce; ?>">
            <script>
                // Make the nonce available to JavaScript
                if (typeof dab_vars === 'undefined') {
                    var dab_vars = {};
                }
                dab_vars.nonce = '<?php echo $general_nonce; ?>';
                dab_vars.approval_nonce = '<?php echo $approval_nonce; ?>';
            </script>
        </div>

        <div class="dab-modal-footer">
            <button type="button" class="dab-modal-close-btn dab-action-btn">Cancel</button>
            <button type="button" class="dab-modal-approve-btn dab-action-btn dab-approve-btn" id="dab-modal-approve-button">Approve</button>
            <button type="button" class="dab-modal-reject-btn dab-action-btn dab-reject-btn" id="dab-modal-reject-button">Reject</button>

        </div>
    </div>
</div>

<!-- Record Details Modal -->
<div id="dab-record-details-modal" class="dab-modal">
    <div class="dab-modal-header">
        <h3 class="dab-modal-title">Record Details</h3>
        <button type="button" class="dab-modal-close">&times;</button>
    </div>

    <div class="dab-modal-body">
        <div id="dab-record-details-content"></div>
    </div>

    <div class="dab-modal-footer">
        <button type="button" class="dab-modal-close-btn dab-action-btn">Close</button>
    </div>
</div>
