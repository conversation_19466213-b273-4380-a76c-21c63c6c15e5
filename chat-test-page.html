<!DOCTYPE html>
<html>
<head>
    <title>Chat System Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f9f9f9;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .shortcode-example {
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .error {
            background: #f8d7da;
            padding: 15px;
            border-left: 4px solid #dc3545;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Database App Builder - Chat System Test</h1>
    
    <div class="instructions">
        <h3>📋 Instructions</h3>
        <p>This is a test page to help you debug the chat system. Follow these steps:</p>
        <ol>
            <li>Create a new WordPress page</li>
            <li>Copy and paste the shortcodes below into your page</li>
            <li>Publish the page and view it on the frontend</li>
            <li>Check the browser console (F12) for any JavaScript errors</li>
            <li>Look at the debug information to identify issues</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔍 Step 1: Debug Information</h3>
        <p>Add this shortcode first to see debug information (only visible to administrators):</p>
        <div class="shortcode-example">[dab_chat_debug]</div>
        <p>This will show you:</p>
        <ul>
            <li>Whether the frontend user is logged in</li>
            <li>If database tables exist</li>
            <li>If AJAX handlers are registered</li>
            <li>If assets are loading properly</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>💬 Step 2: Chat System</h3>
        <p>Add this shortcode to display the chat interface:</p>
        <div class="shortcode-example">[dab_chat]</div>
        <p>Optional parameters:</p>
        <div class="shortcode-example">[dab_chat height="500px" show_groups="true" show_users="true" default_view="conversations"]</div>
    </div>

    <div class="test-section">
        <h3>👤 Step 3: User Authentication</h3>
        <p>If the chat shows "You must be logged in", add these shortcodes for user management:</p>
        
        <h4>Login Form:</h4>
        <div class="shortcode-example">[dab_user_login]</div>
        
        <h4>Registration Form:</h4>
        <div class="shortcode-example">[dab_user_register]</div>
        
        <h4>User Dashboard:</h4>
        <div class="shortcode-example">[dab_user_dashboard]</div>
    </div>

    <div class="warning">
        <h3>⚠️ Common Issues</h3>
        <ul>
            <li><strong>Chat not showing:</strong> Check if frontend user is logged in</li>
            <li><strong>JavaScript errors:</strong> Check browser console (F12)</li>
            <li><strong>AJAX errors:</strong> Check if nonce is valid and AJAX handlers are registered</li>
            <li><strong>Database errors:</strong> Check if chat tables exist in database</li>
            <li><strong>Asset loading:</strong> Check if CSS/JS files are accessible</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🛠️ Troubleshooting Steps</h3>
        <ol>
            <li><strong>Check Browser Console:</strong> Press F12 and look for JavaScript errors</li>
            <li><strong>Check Network Tab:</strong> Look for failed AJAX requests</li>
            <li><strong>Verify User Login:</strong> Make sure frontend user is properly authenticated</li>
            <li><strong>Check Database:</strong> Verify chat tables exist in your database</li>
            <li><strong>Test AJAX Endpoints:</strong> Try accessing AJAX URLs directly</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>📝 Complete Test Page Example</h3>
        <p>Copy this entire content into a new WordPress page:</p>
        <div class="shortcode-example">
<h2>Chat System Test</h2>

<h3>Debug Information</h3>
[dab_chat_debug]

<h3>User Login</h3>
[dab_user_login]

<h3>User Registration</h3>
[dab_user_register]

<h3>Chat Interface</h3>
[dab_chat]
        </div>
    </div>

    <div class="error">
        <h3>🚨 If Chat Still Not Working</h3>
        <p>If the chat is still not showing after following all steps:</p>
        <ol>
            <li>Check the debug information for any red "Missing" or "Not Registered" items</li>
            <li>Look at the browser console for JavaScript errors</li>
            <li>Verify that the frontend user authentication is working</li>
            <li>Check if the chat database tables were created properly</li>
            <li>Make sure the plugin is fully activated and all files are present</li>
        </ol>
    </div>

    <div class="instructions">
        <h3>📞 Need Help?</h3>
        <p>If you're still having issues:</p>
        <ul>
            <li>Share the debug information output</li>
            <li>Share any JavaScript console errors</li>
            <li>Let me know what you see when you try to use the chat</li>
        </ul>
    </div>
</body>
</html>
