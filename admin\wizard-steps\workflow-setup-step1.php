<?php
/**
 * Workflow Setup Wizard - Step 1: Select Table
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get tables
$tables = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}dab_tables ORDER BY table_label");

// Get saved data
$selected_table_id = isset($progress['data']['table_id']) ? $progress['data']['table_id'] : '';
?>

<div class="dab-wizard-form">
    <div class="dab-wizard-form-group">
        <label for="table_id" class="dab-wizard-form-label"><?php _e('Select Table', 'db-app-builder'); ?> <span class="required">*</span></label>
        
        <?php if (empty($tables)): ?>
            <div class="dab-wizard-notice dab-wizard-notice-warning">
                <p><?php _e('No tables found. Please create a table first.', 'db-app-builder'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-btn dab-btn-outline-primary">
                    <?php _e('Create Table', 'db-app-builder'); ?>
                </a>
            </div>
        <?php else: ?>
            <select id="table_id" name="table_id" class="dab-wizard-form-select" required>
                <option value=""><?php _e('-- Select Table --', 'db-app-builder'); ?></option>
                <?php foreach ($tables as $table): ?>
                    <option value="<?php echo esc_attr($table->id); ?>" <?php selected($selected_table_id, $table->id); ?>>
                        <?php echo esc_html($table->table_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <p class="dab-wizard-form-help"><?php _e('Select the table you want to set up an approval workflow for.', 'db-app-builder'); ?></p>
            
            <div id="table-info" class="dab-wizard-table-info" style="display: <?php echo empty($selected_table_id) ? 'none' : 'block'; ?>;">
                <h3><?php _e('Table Information', 'db-app-builder'); ?></h3>
                
                <?php if (!empty($selected_table_id)): 
                    $selected_table = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
                        $selected_table_id
                    ));
                    
                    $fields_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}dab_fields WHERE table_id = %d",
                        $selected_table_id
                    ));
                    
                    $records_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}dab_data_{$selected_table->table_slug}",
                        $selected_table_id
                    ));
                    
                    // Check if approval workflow already exists
                    $approval_exists = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}dab_approval_levels WHERE table_id = %d",
                        $selected_table_id
                    ));
                ?>
                    <div class="dab-wizard-table-details">
                        <div class="dab-wizard-table-detail">
                            <span class="dab-wizard-table-detail-label"><?php _e('Table Name:', 'db-app-builder'); ?></span>
                            <span class="dab-wizard-table-detail-value"><?php echo esc_html($selected_table->table_label); ?></span>
                        </div>
                        
                        <div class="dab-wizard-table-detail">
                            <span class="dab-wizard-table-detail-label"><?php _e('Table Slug:', 'db-app-builder'); ?></span>
                            <span class="dab-wizard-table-detail-value"><?php echo esc_html($selected_table->table_slug); ?></span>
                        </div>
                        
                        <div class="dab-wizard-table-detail">
                            <span class="dab-wizard-table-detail-label"><?php _e('Fields:', 'db-app-builder'); ?></span>
                            <span class="dab-wizard-table-detail-value"><?php echo esc_html($fields_count); ?></span>
                        </div>
                        
                        <div class="dab-wizard-table-detail">
                            <span class="dab-wizard-table-detail-label"><?php _e('Records:', 'db-app-builder'); ?></span>
                            <span class="dab-wizard-table-detail-value"><?php echo esc_html($records_count); ?></span>
                        </div>
                        
                        <?php if ($approval_exists > 0): ?>
                            <div class="dab-wizard-notice dab-wizard-notice-info">
                                <p><?php _e('This table already has an approval workflow configured. Continuing will update the existing workflow.', 'db-app-builder'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="dab-wizard-form-group">
        <label class="dab-wizard-form-label"><?php _e('Workflow Type', 'db-app-builder'); ?></label>
        
        <div class="dab-wizard-workflow-types">
            <div class="dab-wizard-workflow-type <?php echo (!isset($progress['data']['workflow_type']) || $progress['data']['workflow_type'] === 'sequential') ? 'selected' : ''; ?>">
                <input type="radio" id="workflow_type_sequential" name="workflow_type" value="sequential" <?php checked(!isset($progress['data']['workflow_type']) || $progress['data']['workflow_type'] === 'sequential'); ?>>
                <label for="workflow_type_sequential">
                    <div class="dab-wizard-workflow-icon">
                        <span class="dashicons dashicons-list-view"></span>
                    </div>
                    <div class="dab-wizard-workflow-content">
                        <h4><?php _e('Sequential Approval', 'db-app-builder'); ?></h4>
                        <p><?php _e('Approvals must follow a specific sequence of levels. Each level must be approved before moving to the next.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>
            
            <div class="dab-wizard-workflow-type <?php echo (isset($progress['data']['workflow_type']) && $progress['data']['workflow_type'] === 'parallel') ? 'selected' : ''; ?>">
                <input type="radio" id="workflow_type_parallel" name="workflow_type" value="parallel" <?php checked(isset($progress['data']['workflow_type']) && $progress['data']['workflow_type'] === 'parallel'); ?>>
                <label for="workflow_type_parallel">
                    <div class="dab-wizard-workflow-icon">
                        <span class="dashicons dashicons-grid-view"></span>
                    </div>
                    <div class="dab-wizard-workflow-content">
                        <h4><?php _e('Parallel Approval', 'db-app-builder'); ?></h4>
                        <p><?php _e('Approvals can happen simultaneously across different levels. All levels must approve for final approval.', 'db-app-builder'); ?></p>
                    </div>
                </label>
            </div>
        </div>
    </div>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 15px;
}

.dab-wizard-notice p {
    margin-top: 0;
}

.dab-wizard-notice-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
}

.dab-wizard-notice-info {
    background-color: #e5f5fa;
    border-left: 4px solid #00a0d2;
}

.dab-wizard-table-info {
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.dab-wizard-table-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.dab-wizard-table-detail {
    margin-bottom: 10px;
}

.dab-wizard-table-detail-label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.dab-wizard-workflow-types {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 10px;
}

.dab-wizard-workflow-type {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.dab-wizard-workflow-type:hover {
    border-color: #c0c0c0;
}

.dab-wizard-workflow-type.selected {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

.dab-wizard-workflow-type input[type="radio"] {
    display: none;
}

.dab-wizard-workflow-type label {
    display: flex;
    cursor: pointer;
    padding: 15px;
}

.dab-wizard-workflow-icon {
    margin-right: 15px;
}

.dab-wizard-workflow-icon .dashicons {
    font-size: 30px;
    width: 30px;
    height: 30px;
    color: #2271b1;
}

.dab-wizard-workflow-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.dab-wizard-workflow-content p {
    margin: 0;
    font-size: 13px;
    color: #666;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Table selection change
    $('#table_id').on('change', function() {
        const tableId = $(this).val();
        
        if (tableId) {
            // Show table info
            $('#table-info').show();
            
            // Load table info via AJAX
            $.ajax({
                url: dab_wizard.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_table_info',
                    table_id: tableId,
                    nonce: dab_wizard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update table info
                        $('#table-info').html(response.data.html);
                    }
                }
            });
        } else {
            // Hide table info
            $('#table-info').hide();
        }
    });
    
    // Workflow type selection
    $('.dab-wizard-workflow-type').on('click', function() {
        $('.dab-wizard-workflow-type').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
    });
});
</script>
