<?php
/**
 * Uninstall Settings Page
 *
 * Provides options for handling data during plugin uninstallation
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/admin
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Render the uninstall settings page
 */
function dab_render_uninstall_settings_page() {
    // Check if user has appropriate permissions
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.', 'db-app-builder'));
    }

    // Process form submission
    if (isset($_POST['dab_uninstall_settings_nonce']) && wp_verify_nonce($_POST['dab_uninstall_settings_nonce'], 'dab_uninstall_settings')) {
        // Update the delete data option
        $delete_data = isset($_POST['dab_delete_data']) ? true : false;
        update_option('dab_delete_data_on_uninstall', $delete_data);

        // Show success message
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Uninstall settings saved successfully.', 'db-app-builder') . '</p></div>';
    }

    // Get current setting
    $delete_data = get_option('dab_delete_data_on_uninstall', false);
    ?>
    <div class="wrap">
        <h1><?php _e('Uninstall Settings', 'db-app-builder'); ?></h1>
        
        <div class="dab-settings-section">
            <h2><?php _e('Data Management', 'db-app-builder'); ?></h2>
            <p><?php _e('Configure how your data should be handled when the plugin is uninstalled.', 'db-app-builder'); ?></p>
            
            <form method="post" action="">
                <?php wp_nonce_field('dab_uninstall_settings', 'dab_uninstall_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Data Deletion', 'db-app-builder'); ?></th>
                        <td>
                            <label for="dab_delete_data">
                                <input type="checkbox" name="dab_delete_data" id="dab_delete_data" <?php checked($delete_data); ?>>
                                <?php _e('Delete all plugin data when uninstalling', 'db-app-builder'); ?>
                            </label>
                            <p class="description"><?php _e('When checked, all plugin data (tables, settings, etc.) will be permanently deleted when the plugin is uninstalled. If unchecked, data will be preserved.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <div class="dab-export-section">
                    <h3><?php _e('Export Settings', 'db-app-builder'); ?></h3>
                    <p><?php _e('You can export your settings to preserve them outside of WordPress.', 'db-app-builder'); ?></p>
                    <button type="button" class="button" id="dab-export-settings"><?php _e('Export Settings', 'db-app-builder'); ?></button>
                </div>
                
                <?php submit_button(__('Save Settings', 'db-app-builder')); ?>
            </form>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        $('#dab-export-settings').on('click', function() {
            // AJAX call to export settings
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'dab_export_settings',
                    nonce: '<?php echo wp_create_nonce('dab_export_settings'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Create download link
                        var blob = new Blob([JSON.stringify(response.data)], {type: 'application/json'});
                        var url = URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = 'dab-settings-export.json';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } else {
                        alert('<?php _e('Error exporting settings.', 'db-app-builder'); ?>');
                    }
                }
            });
        });
    });
    </script>
    <?php
}

/**
 * AJAX handler for exporting settings
 */
function dab_export_settings_ajax() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_export_settings')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }
    
    // Get all plugin settings
    $settings = array(
        'delete_data_on_uninstall' => get_option('dab_delete_data_on_uninstall', false),
        // Add other settings as needed
    );
    
    wp_send_json_success($settings);
}
add_action('wp_ajax_dab_export_settings', 'dab_export_settings_ajax');
