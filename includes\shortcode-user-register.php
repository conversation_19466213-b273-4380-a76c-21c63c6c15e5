<?php
/**
 * User Registration Shortcode
 *
 * Provides frontend registration functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register registration shortcode
 */
function dab_user_register_shortcode($atts) {
    $atts = shortcode_atts(array(
        'redirect' => '',
        'show_login_link' => 'true',
        'require_email_verification' => 'true'
    ), $atts, 'dab_user_register');

    // Check if user is already logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    if ($current_user) {
        $dashboard_url = home_url('/user-dashboard/');
        return '<div class="dab-register-message">
                    <p>You are already logged in as <strong>' . esc_html($current_user->username) . '</strong></p>
                    <p><a href="' . esc_url($dashboard_url) . '" class="dab-btn dab-btn-primary">Go to Dashboard</a></p>
                </div>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-frontend-auth', plugin_dir_url(dirname(__FILE__)) . 'assets/css/frontend-auth.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-frontend-auth', plugin_dir_url(dirname(__FILE__)) . 'assets/js/frontend-auth.js', array('jquery'), DAB_VERSION, true);

    // Localize script
    wp_localize_script('dab-frontend-auth', 'dab_auth', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_frontend_nonce'),
        'redirect_url' => !empty($atts['redirect']) ? $atts['redirect'] : home_url('/login/'),
        'messages' => array(
            'register_success' => __('Registration successful! Please check your email to verify your account.', 'db-app-builder'),
            'register_error' => __('Registration failed. Please try again.', 'db-app-builder'),
            'required_fields' => __('Please fill in all required fields.', 'db-app-builder'),
            'password_mismatch' => __('Passwords do not match.', 'db-app-builder'),
            'weak_password' => __('Password must be at least 6 characters long.', 'db-app-builder')
        )
    ));

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-register-container">
        <div class="dab-register-form-wrapper">
            <h2 class="dab-register-title"><?php _e('Create Your Account', 'db-app-builder'); ?></h2>
            
            <form id="dab-register-form" class="dab-register-form">
                <div class="dab-form-row">
                    <div class="dab-form-group dab-form-col-6">
                        <label for="first_name"><?php _e('First Name', 'db-app-builder'); ?></label>
                        <input type="text" id="first_name" name="first_name" class="dab-form-control">
                    </div>
                    <div class="dab-form-group dab-form-col-6">
                        <label for="last_name"><?php _e('Last Name', 'db-app-builder'); ?></label>
                        <input type="text" id="last_name" name="last_name" class="dab-form-control">
                    </div>
                </div>

                <div class="dab-form-group">
                    <label for="username"><?php _e('Username', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="text" id="username" name="username" class="dab-form-control" required>
                    <small class="dab-form-help"><?php _e('Choose a unique username for your account.', 'db-app-builder'); ?></small>
                </div>

                <div class="dab-form-group">
                    <label for="email"><?php _e('Email Address', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="email" id="email" name="email" class="dab-form-control" required>
                    <small class="dab-form-help"><?php _e('We will send a verification email to this address.', 'db-app-builder'); ?></small>
                </div>

                <div class="dab-form-group">
                    <label for="password"><?php _e('Password', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="password" id="password" name="password" class="dab-form-control" required>
                    <small class="dab-form-help"><?php _e('Password must be at least 6 characters long.', 'db-app-builder'); ?></small>
                </div>

                <div class="dab-form-group">
                    <label for="confirm_password"><?php _e('Confirm Password', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="password" id="confirm_password" name="confirm_password" class="dab-form-control" required>
                </div>

                <div class="dab-form-group dab-form-checkbox">
                    <label>
                        <input type="checkbox" id="agree_terms" name="agree_terms" value="1" required>
                        <?php _e('I agree to the', 'db-app-builder'); ?> 
                        <a href="#" target="_blank"><?php _e('Terms of Service', 'db-app-builder'); ?></a> 
                        <?php _e('and', 'db-app-builder'); ?> 
                        <a href="#" target="_blank"><?php _e('Privacy Policy', 'db-app-builder'); ?></a>
                        <span class="required">*</span>
                    </label>
                </div>

                <div class="dab-form-group">
                    <button type="submit" class="dab-btn dab-btn-primary dab-btn-block">
                        <span class="dab-btn-text"><?php _e('Create Account', 'db-app-builder'); ?></span>
                        <span class="dab-btn-loading" style="display: none;">
                            <span class="dab-spinner"></span>
                            <?php _e('Creating account...', 'db-app-builder'); ?>
                        </span>
                    </button>
                </div>

                <div class="dab-form-messages">
                    <div class="dab-alert dab-alert-error" id="dab-register-error" style="display: none;"></div>
                    <div class="dab-alert dab-alert-success" id="dab-register-success" style="display: none;"></div>
                </div>
            </form>

            <?php if ($atts['show_login_link'] === 'true'): ?>
            <div class="dab-register-links">
                <p class="dab-login-link">
                    <?php _e('Already have an account?', 'db-app-builder'); ?> 
                    <a href="<?php echo home_url('/login/'); ?>"><?php _e('Login here', 'db-app-builder'); ?></a>
                </p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <style>
    .dab-register-container {
        max-width: 500px;
        margin: 0 auto;
        padding: 20px;
    }

    .dab-register-form-wrapper {
        background: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
    }

    .dab-register-title {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
        font-size: 24px;
        font-weight: 600;
    }

    .dab-form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 0;
    }

    .dab-form-col-6 {
        flex: 1;
    }

    .dab-form-group {
        margin-bottom: 20px;
    }

    .dab-form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
    }

    .dab-form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
        box-sizing: border-box;
    }

    .dab-form-control:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    }

    .dab-form-checkbox label {
        display: flex;
        align-items: flex-start;
        font-weight: normal;
        line-height: 1.4;
    }

    .dab-form-checkbox input[type="checkbox"] {
        margin-right: 8px;
        margin-top: 2px;
        width: auto;
        flex-shrink: 0;
    }

    .dab-btn {
        display: inline-block;
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s;
        text-align: center;
    }

    .dab-btn-primary {
        background-color: #007cba;
        color: white;
    }

    .dab-btn-primary:hover {
        background-color: #005a87;
    }

    .dab-btn-block {
        width: 100%;
    }

    .dab-btn-loading {
        display: none;
    }

    .dab-btn.loading .dab-btn-text {
        display: none;
    }

    .dab-btn.loading .dab-btn-loading {
        display: inline-block;
    }

    .dab-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
        margin-right: 8px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .dab-alert {
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    .dab-alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .dab-alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .dab-register-links {
        text-align: center;
        margin-top: 20px;
    }

    .dab-register-links p {
        margin: 10px 0;
    }

    .dab-register-links a {
        color: #007cba;
        text-decoration: none;
    }

    .dab-register-links a:hover {
        text-decoration: underline;
    }

    .required {
        color: #dc3545;
    }

    .dab-form-help {
        display: block;
        margin-top: 5px;
        color: #6c757d;
        font-size: 12px;
    }

    /* Responsive */
    @media (max-width: 600px) {
        .dab-form-row {
            flex-direction: column;
            gap: 0;
        }
        
        .dab-register-container {
            padding: 10px;
        }
        
        .dab-register-form-wrapper {
            padding: 20px;
        }
    }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_user_register', 'dab_user_register_shortcode');
