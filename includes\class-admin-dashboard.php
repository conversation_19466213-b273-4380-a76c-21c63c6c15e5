<?php
/**
 * Admin Dashboard Widgets
 *
 * This class adds widgets to the WordPress admin dashboard to show pending approvals.
 */
if (!defined('ABSPATH')) exit;

class DAB_Admin_Dashboard {

    /**
     * Initialize the class
     */
    public function __construct() {
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widgets'));
    }

    /**
     * Add dashboard widgets
     */
    public function add_dashboard_widgets() {
        // Only add for users who can approve records
        if ($this->user_has_approval_role()) {
            wp_add_dashboard_widget(
                'dab_pending_approvals',
                'Pending Approvals',
                array($this, 'pending_approvals_widget')
            );
        }
    }

    /**
     * Check if current user has a role that can approve records
     */
    private function user_has_approval_role() {
        global $wpdb;

        // Administrators can always approve
        if (current_user_can('administrator')) {
            return true;
        }

        // Get current user roles
        $current_user = wp_get_current_user();
        if (!$current_user->exists()) {
            return false;
        }

        $user_roles = $current_user->roles;

        // Get all approval levels
        $approval_levels_table = $wpdb->prefix . 'dab_approval_levels';
        $levels = $wpdb->get_results("SELECT * FROM $approval_levels_table");

        // Check if user has any role that can approve
        foreach ($levels as $level) {
            $approver_roles = maybe_unserialize($level->approver_roles);
            if (!empty($approver_roles) && is_array($approver_roles)) {
                foreach ($approver_roles as $role) {
                    if (in_array($role, $user_roles)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Pending approvals widget content
     */
    public function pending_approvals_widget() {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $is_admin = current_user_can('administrator');

        // Get tables with approval workflows
        $tables_with_approval = $wpdb->get_results(
            "SELECT DISTINCT t.*
            FROM {$wpdb->prefix}dab_tables t
            INNER JOIN {$wpdb->prefix}dab_approval_levels l ON t.id = l.table_id
            ORDER BY t.table_label"
        );

        if (empty($tables_with_approval)) {
            echo '<p>No tables with approval workflows found.</p>';
            return;
        }

        $pending_count = 0;
        $pending_items = array();

        foreach ($tables_with_approval as $table) {
            $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

            // Get records pending approval
            $records = $wpdb->get_results(
                "SELECT * FROM $data_table
                WHERE approval_status = 'Pending'
                ORDER BY id DESC
                LIMIT 10"
            );

            if (empty($records)) {
                continue;
            }

            foreach ($records as $record) {
                // Check if user can approve this record
                $can_approve = DAB_Approval_Manager::can_user_approve($current_user_id, $table->id, $record->id);

                // Administrators see all workflows, others only see what they can approve
                if ($is_admin || $can_approve) {
                    $pending_count++;

                    // Get current approval level
                    $level_name = 'Unknown';
                    if (!empty($record->current_approval_level)) {
                        $level = $wpdb->get_var($wpdb->prepare(
                            "SELECT level_name FROM {$wpdb->prefix}dab_approval_levels WHERE id = %d",
                            $record->current_approval_level
                        ));
                        if ($level) {
                            $level_name = $level;
                        }
                    }

                    // Get submitter info
                    $submitter = 'Unknown';
                    if (!empty($record->user_id)) {
                        $user = get_userdata($record->user_id);
                        if ($user) {
                            $submitter = $user->display_name;
                        }
                    }

                    // Add a flag to indicate if this record is directly assigned to the current user
                    $is_assigned = $can_approve && !$is_admin;

                    $pending_items[] = array(
                        'table' => $table,
                        'record' => $record,
                        'level' => $level_name,
                        'submitter' => $submitter,
                        'is_assigned' => $is_assigned
                    );
                }
            }
        }

        if ($pending_count === 0) {
            echo '<p>No records pending your approval.</p>';
            return;
        }

        echo '<p><strong>' . $pending_count . ' record(s) pending your approval:</strong></p>';
        echo '<table class="widefat" style="margin-bottom: 15px;">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Table</th>';
        echo '<th>Record ID</th>';
        echo '<th>Submitted By</th>';
        echo '<th>Level</th>';
        echo '<th>Status</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($pending_items as $item) {
            // Add a class to highlight rows assigned to the current user
            $row_class = isset($item['is_assigned']) && $item['is_assigned'] ? ' class="dab-assigned-workflow"' : '';

            echo '<tr' . $row_class . '>';
            echo '<td>' . esc_html($item['table']->table_label) . '</td>';
            echo '<td>#' . esc_html($item['record']->id) . '</td>';
            echo '<td>' . esc_html($item['submitter']) . '</td>';
            echo '<td>' . esc_html($item['level']) . '</td>';
            echo '<td>';

            // Show assignment status
            if (isset($item['is_assigned']) && $item['is_assigned']) {
                echo '<span class="dab-status-assigned">Assigned to you</span>';
            } else if (current_user_can('administrator')) {
                echo '<span class="dab-status-admin">Admin access</span>';
            } else {
                echo '<span class="dab-status-pending">Pending</span>';
            }

            echo '</td>';
            echo '<td>';
            echo '<a href="' . admin_url('admin.php?page=dab_approvals&table_id=' . $item['table']->id) . '" class="button button-small">Review</a>';
            echo '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';

        echo '<p><a href="' . admin_url('admin.php?page=dab_approvals') . '" class="button">View All Pending Approvals</a></p>';
    }
}

// Initialize the class
new DAB_Admin_Dashboard();
