/**
 * Table Export Functionality
 *
 * This script provides export functionality for data tables.
 */
jQuery(document).ready(function($) {


    // Direct event binding for export button
    $(document).on('click', '.dab-export-button', function(e) {

        e.preventDefault();

        // Find the associated options container
        const options = $(this).next('.dab-export-options');
        if (options.length) {
            options.toggle();
        }
    });

    // Close export options when clicking outside
    $(document).on('click', function(event) {
        if (!$(event.target).closest('.dab-export-container').length) {
            $('.dab-export-options').hide();
        }
    });

    // Print table functionality
    $(document).on('click', '.dab-print-table', function(e) {

        e.preventDefault();

        // Find the table
        let tableElement = null;
        const viewContainer = $(this).closest('.dab-enhanced-search').next('.dab-view-container');
        if (viewContainer.length) {
            const table = viewContainer.find('.dab-view-table');
            if (table.length) {
                tableElement = table[0];
            } else {

            }
        } else {
            // Try a different approach to find the table
            const table = $('.dab-view-table');
            if (table.length) {
                tableElement = table[0];
            } else {
                // Try to find DataTable
                const dataTable = $('#dab-data-table');
                if (dataTable.length) {
                    tableElement = dataTable[0];
                } else {

                    return;
                }
            }
        }

        // If table found, show print options modal
        if (tableElement) {
            showPrintOptionsModal(tableElement);
        }
    });

    /**
     * Show print options modal
     *
     * @param {HTMLElement} table The table element to print
     */
    function showPrintOptionsModal(table) {
        // Store the table element for later use
        $('#dab-print-modal').data('table', table);

        // Show the modal
        $('#dab-print-modal').show();

        // Add event listeners
        $('.dab-modal-close, .dab-modal-cancel').off('click').on('click', function() {
            $('#dab-print-modal').hide();
        });

        $('.dab-print-proceed').off('click').on('click', function() {
            const format = $('input[name="print_format"]:checked').val();
            const includeHeader = $('#dab-print-include-header').is(':checked');
            const includeDate = $('#dab-print-include-date').is(':checked');
            const selectedOnly = $('#dab-print-selected-only').is(':checked');

            // Get the table element
            const tableElement = $('#dab-print-modal').data('table');

            // Print the table with the selected options
            printTable(tableElement, format, includeHeader, includeDate, selectedOnly);

            // Hide the modal
            $('#dab-print-modal').hide();
        });
    }

    /**
     * Print a table
     *
     * @param {HTMLElement} table The table element to print
     * @param {string} format The print format (standard, invoice, thermal)
     * @param {boolean} includeHeader Whether to include header/logo
     * @param {boolean} includeDate Whether to include date/time
     * @param {boolean} selectedOnly Whether to print only selected records
     */
    function printTable(table, format = 'standard', includeHeader = true, includeDate = true, selectedOnly = false) {


        const printWindow = window.open('', '_blank');
        const currentDate = new Date().toLocaleString();
        const tableTitle = getTableTitle(table);

        // Create the HTML content for the print window
        let printContent = '<!DOCTYPE html><html><head>';
        printContent += '<title>Print - ' + tableTitle + '</title>';
        printContent += '<style>';

        // Common styles
        printContent += 'body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }';

        // Format-specific styles
        switch (format) {
            case 'invoice':
                printContent += getInvoiceStyles();
                break;
            case 'thermal':
                printContent += getThermalStyles();
                break;
            default: // standard
                printContent += getStandardStyles();
        }

        printContent += '</style>';
        printContent += '</head><body>';

        // If we need to print only selected records, create a filtered table clone
        let tableToUse = table;
        if (selectedOnly) {
            tableToUse = createFilteredTable(table);

            // If no records are selected, show a message
            if (!tableToUse) {
                printWindow.document.write('<h2>No records selected</h2><p>Please select at least one record to print.</p>');
                printWindow.document.close();
                return;
            }
        }

        // Format-specific content
        switch (format) {
            case 'invoice':
                printContent += getInvoiceContent(tableToUse, tableTitle, includeHeader, includeDate, currentDate);
                break;
            case 'thermal':
                printContent += getThermalContent(tableToUse, tableTitle, includeHeader, includeDate, currentDate);
                break;
            default: // standard
                printContent += getStandardContent(tableToUse, tableTitle, includeHeader, includeDate, currentDate);
        }

        printContent += '<script>';
        printContent += 'window.onload = function() {';
        printContent += '  window.print();';
        printContent += '  window.setTimeout(function() { window.close(); }, 500);';
        printContent += '};';
        printContent += '<\/script>';
        printContent += '</body></html>';

        printWindow.document.write(printContent);
        printWindow.document.close();
    }

    /**
     * Create a filtered table with only selected records
     *
     * @param {HTMLElement} table The original table element
     * @return {HTMLElement|null} A new table with only selected records, or null if no records selected
     */
    function createFilteredTable(table) {
        console.log('Creating filtered table with selected records');

        // Get selected records from DataTable if available
        let selectedRows = [];

        // Check if this is a DataTable
        if ($.fn.DataTable.isDataTable('#' + table.id)) {
            console.log('Table is a DataTable, getting selected rows');

            // Get all checked checkboxes in the table
            const checkboxes = $(table).find('tbody input[type="checkbox"]:checked');
            console.log('Found', checkboxes.length, 'selected rows');

            if (checkboxes.length === 0) {
                return null; // No records selected
            }

            // Get the rows containing the checked checkboxes
            checkboxes.each(function() {
                selectedRows.push($(this).closest('tr')[0]);
            });
        } else {
            // For regular tables, look for a class or data attribute that marks selected rows
            selectedRows = $(table).find('tr.selected, tr[data-selected="true"]');

            if (selectedRows.length === 0) {
                return null; // No records selected
            }
        }

        // Create a new table with the same structure
        const newTable = table.cloneNode(false);

        // Copy the thead
        const thead = table.querySelector('thead');
        if (thead) {
            newTable.appendChild(thead.cloneNode(true));
        }

        // Create a new tbody
        const tbody = document.createElement('tbody');

        // Add selected rows to the new tbody
        selectedRows.forEach(function(row) {
            tbody.appendChild(row.cloneNode(true));
        });

        // Add the tbody to the new table
        newTable.appendChild(tbody);

        console.log('Created filtered table with', selectedRows.length, 'rows');
        return newTable;
    }

    /**
     * Get the title of the table
     *
     * @param {HTMLElement} table The table element
     * @return {string} The table title
     */
    function getTableTitle(table) {
        // Try to get the table name from the current table name in the dashboard
        const tableName = $('#dab-current-table-name').text();
        if (tableName && tableName !== 'Select a Table') {
            return tableName;
        }

        // Try to get the table name from the closest container
        const containerTitle = $(table).closest('.dab-view-container').prev('.dab-enhanced-search').find('.dab-view-title').text();
        if (containerTitle) {
            return containerTitle;
        }

        // Default title
        return 'Table Data';
    }

    /**
     * Get styles for standard report format
     *
     * @return {string} CSS styles
     */
    function getStandardStyles() {
        let styles = '';
        styles += 'table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }';
        styles += 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
        styles += 'th { background-color: #f2f2f2; font-weight: bold; }';
        styles += 'tr:nth-child(even) { background-color: #f9f9f9; }';
        styles += '.report-header { margin-bottom: 20px; }';
        styles += '.report-title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }';
        styles += '.report-date { color: #666; margin-bottom: 15px; }';
        styles += '@media print {';
        styles += '  body { margin: 0; padding: 15px; }';
        styles += '  table { page-break-inside: auto; }';
        styles += '  tr { page-break-inside: avoid; page-break-after: auto; }';
        styles += '  thead { display: table-header-group; }';
        styles += '}';
        return styles;
    }

    /**
     * Get styles for invoice format
     *
     * @return {string} CSS styles
     */
    function getInvoiceStyles() {
        let styles = '';
        styles += 'body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; }';
        styles += '.invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; }';
        styles += '.company-info { max-width: 50%; }';
        styles += '.company-name { font-size: 24px; font-weight: bold; margin-bottom: 5px; }';
        styles += '.company-details { color: #666; }';
        styles += '.invoice-info { text-align: right; }';
        styles += '.invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }';
        styles += '.invoice-details { color: #666; }';
        styles += 'table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }';
        styles += 'th { background-color: #f2f2f2; border-bottom: 2px solid #ddd; padding: 10px; text-align: left; }';
        styles += 'td { padding: 10px; border-bottom: 1px solid #ddd; }';
        styles += '.totals { margin-top: 20px; text-align: right; }';
        styles += '.total-row { display: flex; justify-content: flex-end; margin-bottom: 5px; }';
        styles += '.total-label { font-weight: bold; margin-right: 20px; }';
        styles += '.grand-total { font-size: 18px; font-weight: bold; margin-top: 10px; }';
        styles += '.footer { margin-top: 30px; text-align: center; color: #666; font-size: 12px; }';
        styles += '@media print {';
        styles += '  body { margin: 0; padding: 15px; }';
        styles += '  table { page-break-inside: auto; }';
        styles += '  tr { page-break-inside: avoid; page-break-after: auto; }';
        styles += '  thead { display: table-header-group; }';
        styles += '}';
        return styles;
    }

    /**
     * Get styles for thermal receipt format
     *
     * @return {string} CSS styles
     */
    function getThermalStyles() {
        let styles = '';
        styles += 'body { font-family: "Courier New", Courier, monospace; max-width: 300px; margin: 0 auto; font-size: 12px; }';
        styles += '.receipt-header { text-align: center; margin-bottom: 10px; }';
        styles += '.company-name { font-size: 16px; font-weight: bold; margin-bottom: 5px; }';
        styles += '.receipt-title { font-weight: bold; margin-bottom: 5px; }';
        styles += '.receipt-date { margin-bottom: 10px; }';
        styles += '.divider { border-top: 1px dashed #000; margin: 10px 0; }';
        styles += 'table { width: 100%; border-collapse: collapse; margin-bottom: 10px; }';
        styles += 'th, td { padding: 5px 2px; text-align: left; vertical-align: top; }';
        styles += 'th { border-bottom: 1px dashed #000; }';
        styles += '.totals { margin-top: 10px; }';
        styles += '.total-row { display: flex; justify-content: space-between; }';
        styles += '.grand-total { font-weight: bold; margin-top: 5px; }';
        styles += '.footer { margin-top: 20px; text-align: center; font-size: 10px; }';
        styles += '@media print {';
        styles += '  @page { size: 80mm 297mm; margin: 0; }';
        styles += '  body { margin: 5mm; }';
        styles += '}';
        return styles;
    }

    /**
     * Get content for standard report format
     *
     * @param {HTMLElement} table The table element
     * @param {string} tableTitle The table title
     * @param {boolean} includeHeader Whether to include header
     * @param {boolean} includeDate Whether to include date
     * @param {string} currentDate The current date string
     * @return {string} HTML content
     */
    function getStandardContent(table, tableTitle, includeHeader, includeDate, currentDate) {
        let content = '';

        if (includeHeader) {
            content += '<div class="report-header">';
            content += '<div class="report-title">' + tableTitle + ' Report</div>';
            if (includeDate) {
                content += '<div class="report-date">Generated on: ' + currentDate + '</div>';
            }
            content += '</div>';
        }

        content += table.outerHTML;

        return content;
    }

    /**
     * Get content for invoice format
     *
     * @param {HTMLElement} table The table element
     * @param {string} tableTitle The table title
     * @param {boolean} includeHeader Whether to include header
     * @param {boolean} includeDate Whether to include date
     * @param {string} currentDate The current date string
     * @return {string} HTML content
     */
    function getInvoiceContent(table, tableTitle, includeHeader, includeDate, currentDate) {
        let content = '';

        if (includeHeader) {
            content += '<div class="invoice-header">';
            content += '<div class="company-info">';
            content += '<div class="company-name">Your Company Name</div>';
            content += '<div class="company-details">';
            content += '123 Business Street<br>';
            content += 'City, State ZIP<br>';
            content += 'Phone: (*************<br>';
            content += 'Email: <EMAIL>';
            content += '</div>';
            content += '</div>';

            content += '<div class="invoice-info">';
            content += '<div class="invoice-title">INVOICE</div>';
            if (includeDate) {
                content += '<div class="invoice-details">';
                content += 'Invoice #: INV-' + Math.floor(Math.random() * 10000) + '<br>';
                content += 'Date: ' + currentDate + '<br>';
                content += '</div>';
            }
            content += '</div>';
            content += '</div>';
        }

        // Process the table to make it look like an invoice
        const tableClone = $(table).clone();

        // Add invoice-specific content
        content += tableClone[0].outerHTML;

        // Add totals section (for demonstration)
        content += '<div class="totals">';
        content += '<div class="total-row"><div class="total-label">Subtotal:</div><div>$1,000.00</div></div>';
        content += '<div class="total-row"><div class="total-label">Tax (10%):</div><div>$100.00</div></div>';
        content += '<div class="total-row grand-total"><div class="total-label">TOTAL:</div><div>$1,100.00</div></div>';
        content += '</div>';

        content += '<div class="footer">';
        content += 'Thank you for your business!';
        content += '</div>';

        return content;
    }

    /**
     * Get content for thermal receipt format
     *
     * @param {HTMLElement} table The table element
     * @param {string} tableTitle The table title
     * @param {boolean} includeHeader Whether to include header
     * @param {boolean} includeDate Whether to include date
     * @param {string} currentDate The current date string
     * @return {string} HTML content
     */
    function getThermalContent(table, tableTitle, includeHeader, includeDate, currentDate) {
        let content = '';

        if (includeHeader) {
            content += '<div class="receipt-header">';
            content += '<div class="company-name">YOUR COMPANY NAME</div>';
            content += '<div>123 Business Street</div>';
            content += '<div>City, State ZIP</div>';
            content += '<div>Tel: (*************</div>';
            content += '</div>';
        }

        content += '<div class="divider"></div>';

        content += '<div class="receipt-title">' + tableTitle + '</div>';

        if (includeDate) {
            content += '<div class="receipt-date">' + currentDate + '</div>';
        }

        content += '<div class="divider"></div>';

        // Process the table to make it fit thermal receipt format
        const tableClone = $(table).clone();

        // Simplify the table for thermal receipt format
        $(tableClone).find('th, td').css('border', 'none');

        content += tableClone[0].outerHTML;

        content += '<div class="divider"></div>';

        // Add totals section (for demonstration)
        content += '<div class="totals">';
        content += '<div class="total-row"><div>Subtotal:</div><div>$1,000.00</div></div>';
        content += '<div class="total-row"><div>Tax (10%):</div><div>$100.00</div></div>';
        content += '<div class="divider"></div>';
        content += '<div class="total-row grand-total"><div>TOTAL:</div><div>$1,100.00</div></div>';
        content += '</div>';

        content += '<div class="divider"></div>';

        content += '<div class="footer">';
        content += 'Thank you for your business!<br>';
        content += 'Please come again';
        content += '</div>';

        return content;
    }
});
