/**
 * WooCommerce Integration JavaScript
 * Handles frontend functionality for DAB WooCommerce integration
 */

(function($) {
    'use strict';

    // Global object for WooCommerce integration
    window.DAB_WooCommerce = {
        init: function() {
            this.initProductSelector();
            this.initCustomerSegments();
            this.initOrderFields();
            this.initSalesDashboard();
            this.initFormValidation();
        },

        /**
         * Initialize product selector functionality
         */
        initProductSelector: function() {
            $('.dab-wc-product-selector').each(function() {
                const $container = $(this);
                const $search = $container.find('.dab-wc-product-search');
                const $results = $container.find('.dab-wc-product-results');
                const $selected = $container.find('.dab-wc-selected-products');
                const $hiddenInput = $container.find('input[type="hidden"]');
                
                let searchTimeout;
                let selectedProducts = [];

                // Parse existing selected products
                if ($hiddenInput.val()) {
                    try {
                        selectedProducts = JSON.parse($hiddenInput.val());
                        this.renderSelectedProducts();
                    } catch (e) {
                        selectedProducts = [];
                    }
                }

                // Search products
                $search.on('input', function() {
                    const query = $(this).val().trim();
                    
                    clearTimeout(searchTimeout);
                    
                    if (query.length < 2) {
                        $results.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        DAB_WooCommerce.searchProducts(query, function(products) {
                            DAB_WooCommerce.renderProductResults(products, $results, function(product) {
                                if (!selectedProducts.find(p => p.id === product.id)) {
                                    selectedProducts.push(product);
                                    DAB_WooCommerce.updateSelectedProducts();
                                }
                                $search.val('');
                                $results.hide();
                            });
                        });
                    }, 300);
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$container.is(e.target) && $container.has(e.target).length === 0) {
                        $results.hide();
                    }
                });

                // Update selected products display and hidden input
                this.updateSelectedProducts = function() {
                    this.renderSelectedProducts();
                    $hiddenInput.val(JSON.stringify(selectedProducts));
                };

                // Render selected products
                this.renderSelectedProducts = function() {
                    $selected.empty();
                    selectedProducts.forEach(function(product, index) {
                        const $product = $('<div class="dab-wc-selected-product">')
                            .html(product.name + ' <span class="remove" data-index="' + index + '">&times;</span>');
                        $selected.append($product);
                    });
                };

                // Remove selected product
                $selected.on('click', '.remove', function() {
                    const index = $(this).data('index');
                    selectedProducts.splice(index, 1);
                    DAB_WooCommerce.updateSelectedProducts();
                });
            });
        },

        /**
         * Search products via AJAX
         */
        searchProducts: function(query, callback) {
            $.ajax({
                url: dab_wc_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_search_products',
                    query: query,
                    nonce: dab_wc_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data);
                    } else {
                        callback([]);
                    }
                },
                error: function() {
                    callback([]);
                }
            });
        },

        /**
         * Render product search results
         */
        renderProductResults: function(products, $container, onSelect) {
            $container.empty();
            
            if (products.length === 0) {
                $container.html('<div class="dab-wc-product-result">' + dab_wc_vars.i18n.no_products + '</div>');
            } else {
                products.forEach(function(product) {
                    const $result = $('<div class="dab-wc-product-result">')
                        .html('<strong>' + product.name + '</strong><br><small>' + product.sku + '</small>')
                        .on('click', function() {
                            onSelect(product);
                        });
                    $container.append($result);
                });
            }
            
            $container.show();
        },

        /**
         * Initialize customer segments functionality
         */
        initCustomerSegments: function() {
            // Customer segment assignment
            $('.dab-customer-segment-assign').on('click', function() {
                const customerId = $(this).data('customer-id');
                const segmentId = $(this).data('segment-id');
                
                DAB_WooCommerce.assignCustomerToSegment(customerId, segmentId);
            });

            // Bulk segment operations
            $('#dab-bulk-segment-action').on('click', function() {
                const action = $('#dab-segment-action').val();
                const segmentId = $('#dab-target-segment').val();
                const customerIds = [];
                
                $('.dab-customer-checkbox:checked').each(function() {
                    customerIds.push($(this).val());
                });

                if (customerIds.length === 0) {
                    alert(dab_wc_vars.i18n.select_customers);
                    return;
                }

                DAB_WooCommerce.bulkSegmentOperation(action, segmentId, customerIds);
            });
        },

        /**
         * Assign customer to segment
         */
        assignCustomerToSegment: function(customerId, segmentId) {
            $.ajax({
                url: dab_wc_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_assign_customer_segment',
                    customer_id: customerId,
                    segment_id: segmentId,
                    nonce: dab_wc_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        DAB_WooCommerce.showNotice(dab_wc_vars.i18n.segment_assigned, 'success');
                        location.reload();
                    } else {
                        DAB_WooCommerce.showNotice(response.data.message, 'error');
                    }
                }
            });
        },

        /**
         * Initialize order fields functionality
         */
        initOrderFields: function() {
            // Conditional field display
            $('.dab-order-field-conditional').each(function() {
                const $field = $(this);
                const condition = $field.data('condition');
                
                if (condition) {
                    DAB_WooCommerce.evaluateFieldCondition($field, condition);
                }
            });

            // Field dependencies
            $('[data-dab-depends]').each(function() {
                const $field = $(this);
                const depends = $field.data('dab-depends');
                
                $(depends).on('change', function() {
                    DAB_WooCommerce.updateFieldVisibility($field, $(this));
                });
            });

            // Order field validation
            $('.dab-order-field input, .dab-order-field select, .dab-order-field textarea').on('blur', function() {
                DAB_WooCommerce.validateOrderField($(this));
            });
        },

        /**
         * Validate order field
         */
        validateOrderField: function($field) {
            const value = $field.val();
            const required = $field.prop('required');
            const type = $field.attr('type') || $field.prop('tagName').toLowerCase();
            
            let isValid = true;
            let message = '';

            // Required validation
            if (required && !value.trim()) {
                isValid = false;
                message = dab_wc_vars.i18n.field_required;
            }

            // Type-specific validation
            if (isValid && value.trim()) {
                switch (type) {
                    case 'email':
                        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                            isValid = false;
                            message = dab_wc_vars.i18n.invalid_email;
                        }
                        break;
                    case 'url':
                        if (!/^https?:\/\/.+/.test(value)) {
                            isValid = false;
                            message = dab_wc_vars.i18n.invalid_url;
                        }
                        break;
                    case 'number':
                        if (isNaN(value)) {
                            isValid = false;
                            message = dab_wc_vars.i18n.invalid_number;
                        }
                        break;
                }
            }

            // Update field appearance
            $field.toggleClass('dab-field-error', !isValid);
            
            const $error = $field.siblings('.dab-field-error-message');
            if (!isValid) {
                if ($error.length === 0) {
                    $field.after('<div class="dab-field-error-message">' + message + '</div>');
                } else {
                    $error.text(message);
                }
            } else {
                $error.remove();
            }

            return isValid;
        },

        /**
         * Initialize sales dashboard functionality
         */
        initSalesDashboard: function() {
            // Chart period selector
            $('#dab-chart-period').on('change', function() {
                const period = $(this).val();
                DAB_WooCommerce.updateSalesChart(period);
            });

            // Metric refresh
            $('.dab-refresh-metrics').on('click', function() {
                DAB_WooCommerce.refreshSalesMetrics();
            });

            // Export functionality
            $('.dab-export-sales').on('click', function() {
                const format = $(this).data('format');
                DAB_WooCommerce.exportSalesData(format);
            });

            // Auto-refresh setup
            if (dab_wc_vars.auto_refresh) {
                setInterval(function() {
                    DAB_WooCommerce.refreshSalesMetrics();
                }, dab_wc_vars.refresh_interval * 1000);
            }
        },

        /**
         * Update sales chart
         */
        updateSalesChart: function(period) {
            const $chartContainer = $('.dab-sales-chart-container');
            $chartContainer.addClass('dab-loading');

            $.ajax({
                url: dab_wc_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_sales_chart_data',
                    period: period,
                    nonce: dab_wc_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        DAB_WooCommerce.renderSalesChart(response.data);
                    }
                },
                complete: function() {
                    $chartContainer.removeClass('dab-loading');
                }
            });
        },

        /**
         * Refresh sales metrics
         */
        refreshSalesMetrics: function() {
            $.ajax({
                url: dab_wc_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'dab_get_sales_metrics',
                    nonce: dab_wc_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        DAB_WooCommerce.updateMetricsDisplay(response.data);
                    }
                }
            });
        },

        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            // Real-time validation
            $('.dab-wc-form input, .dab-wc-form select, .dab-wc-form textarea').on('input change', function() {
                DAB_WooCommerce.validateField($(this));
            });

            // Form submission validation
            $('.dab-wc-form').on('submit', function(e) {
                const $form = $(this);
                let isValid = true;

                $form.find('input, select, textarea').each(function() {
                    if (!DAB_WooCommerce.validateField($(this))) {
                        isValid = false;
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    DAB_WooCommerce.showNotice(dab_wc_vars.i18n.form_validation_failed, 'error');
                }
            });
        },

        /**
         * Validate individual field
         */
        validateField: function($field) {
            // Implementation similar to validateOrderField
            return DAB_WooCommerce.validateOrderField($field);
        },

        /**
         * Show notification message
         */
        showNotice: function(message, type) {
            const $notice = $('<div class="dab-notice dab-notice-' + type + '">')
                .html(message)
                .hide()
                .fadeIn();

            $('.dab-notices-container').append($notice);

            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Utility function to debounce function calls
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = function() {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * Format currency value
         */
        formatCurrency: function(amount) {
            return new Intl.NumberFormat(dab_wc_vars.locale, {
                style: 'currency',
                currency: dab_wc_vars.currency
            }).format(amount);
        },

        /**
         * Format date
         */
        formatDate: function(date) {
            return new Intl.DateTimeFormat(dab_wc_vars.locale).format(new Date(date));
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        DAB_WooCommerce.init();
    });

    // Handle AJAX errors globally
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        if (settings.url.indexOf(dab_wc_vars.ajax_url) !== -1) {
            console.error('DAB WooCommerce AJAX Error:', thrownError);
            DAB_WooCommerce.showNotice(dab_wc_vars.i18n.ajax_error, 'error');
        }
    });

})(jQuery);
