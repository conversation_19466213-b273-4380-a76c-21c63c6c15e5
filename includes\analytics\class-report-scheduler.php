<?php
/**
 * Report Scheduler Manager
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles automated report generation, scheduling, and distribution
 * with email notifications and various export formats.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Report_Scheduler {
    
    /**
     * Initialize the Report Scheduler
     */
    public static function init() {
        add_action('wp_ajax_dab_schedule_report', array(__CLASS__, 'schedule_report'));
        add_action('wp_ajax_dab_update_schedule', array(__CLASS__, 'update_schedule'));
        add_action('wp_ajax_dab_delete_schedule', array(__CLASS__, 'delete_schedule'));
        add_action('wp_ajax_dab_get_scheduled_reports', array(__CLASS__, 'get_scheduled_reports'));
        add_action('wp_ajax_dab_run_scheduled_report', array(__CLASS__, 'run_scheduled_report'));
        add_action('wp_ajax_dab_pause_schedule', array(__CLASS__, 'pause_schedule'));
        add_action('wp_ajax_dab_resume_schedule', array(__CLASS__, 'resume_schedule'));
        
        // Cron hooks for scheduled reports
        add_action('dab_run_scheduled_report', array(__CLASS__, 'execute_scheduled_report'), 10, 1);
        add_action('dab_cleanup_old_reports', array(__CLASS__, 'cleanup_old_reports'));
        
        // Schedule cleanup job
        if (!wp_next_scheduled('dab_cleanup_old_reports')) {
            wp_schedule_event(time(), 'daily', 'dab_cleanup_old_reports');
        }
    }
    
    /**
     * Create database tables for report scheduling
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        // Scheduled reports table
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        $sql = "CREATE TABLE IF NOT EXISTS $schedules_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            report_id BIGINT(20) UNSIGNED NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            schedule_type ENUM('once', 'daily', 'weekly', 'monthly', 'custom') NOT NULL,
            schedule_config TEXT,
            export_format ENUM('pdf', 'excel', 'csv', 'html') DEFAULT 'pdf',
            email_config TEXT,
            filter_config TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_run DATETIME NULL,
            next_run DATETIME NULL,
            run_count INT DEFAULT 0,
            PRIMARY KEY (id),
            KEY idx_report_id (report_id),
            KEY idx_schedule_type (schedule_type),
            KEY idx_is_active (is_active),
            KEY idx_next_run (next_run)
        ) $charset_collate;";
        
        // Report executions history table
        $executions_table = $wpdb->prefix . 'dab_scheduled_executions';
        $sql .= "CREATE TABLE IF NOT EXISTS $executions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            schedule_id BIGINT(20) UNSIGNED NOT NULL,
            execution_status ENUM('success', 'failed', 'partial') NOT NULL,
            execution_time FLOAT,
            row_count INT,
            file_path VARCHAR(500),
            file_size BIGINT,
            error_message TEXT,
            email_sent TINYINT(1) DEFAULT 0,
            email_recipients TEXT,
            executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_schedule_id (schedule_id),
            KEY idx_execution_status (execution_status),
            KEY idx_executed_at (executed_at),
            FOREIGN KEY (schedule_id) REFERENCES $schedules_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // Email templates table
        $templates_table = $wpdb->prefix . 'dab_email_templates';
        $sql .= "CREATE TABLE IF NOT EXISTS $templates_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            subject VARCHAR(500) NOT NULL,
            body_html LONGTEXT,
            body_text LONGTEXT,
            template_type ENUM('report_success', 'report_failed', 'custom') DEFAULT 'custom',
            is_system TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_type (template_type),
            KEY idx_is_system (is_system)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Insert default email templates
        self::insert_default_email_templates();
    }
    
    /**
     * Insert default email templates
     */
    private static function insert_default_email_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_email_templates';
        
        $default_templates = array(
            array(
                'name' => 'Report Success Notification',
                'subject' => 'Scheduled Report: {{report_name}} - Generated Successfully',
                'body_html' => '<h2>Report Generated Successfully</h2>
                    <p>Your scheduled report "{{report_name}}" has been generated successfully.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Generated: {{execution_date}}</li>
                        <li>Records: {{row_count}}</li>
                        <li>Format: {{export_format}}</li>
                        <li>File Size: {{file_size}}</li>
                    </ul>
                    <p>The report is attached to this email.</p>',
                'body_text' => 'Report Generated Successfully
                    
                    Your scheduled report "{{report_name}}" has been generated successfully.
                    
                    Details:
                    - Generated: {{execution_date}}
                    - Records: {{row_count}}
                    - Format: {{export_format}}
                    - File Size: {{file_size}}
                    
                    The report is attached to this email.',
                'template_type' => 'report_success',
                'is_system' => 1
            ),
            array(
                'name' => 'Report Failed Notification',
                'subject' => 'Scheduled Report: {{report_name}} - Generation Failed',
                'body_html' => '<h2>Report Generation Failed</h2>
                    <p>Your scheduled report "{{report_name}}" failed to generate.</p>
                    <p><strong>Error Details:</strong></p>
                    <p>{{error_message}}</p>
                    <p><strong>Execution Details:</strong></p>
                    <ul>
                        <li>Attempted: {{execution_date}}</li>
                        <li>Schedule: {{schedule_type}}</li>
                    </ul>
                    <p>Please check your report configuration and try again.</p>',
                'body_text' => 'Report Generation Failed
                    
                    Your scheduled report "{{report_name}}" failed to generate.
                    
                    Error Details:
                    {{error_message}}
                    
                    Execution Details:
                    - Attempted: {{execution_date}}
                    - Schedule: {{schedule_type}}
                    
                    Please check your report configuration and try again.',
                'template_type' => 'report_failed',
                'is_system' => 1
            )
        );
        
        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $templates_table WHERE template_type = %s AND is_system = 1",
                $template['template_type']
            ));
            
            if (!$existing) {
                $wpdb->insert($templates_table, $template);
            }
        }
    }
    
    /**
     * Schedule a report
     */
    public static function schedule_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        
        $report_id = intval($_POST['report_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $schedule_type = sanitize_text_field($_POST['schedule_type']);
        $schedule_config = $_POST['schedule_config'];
        $export_format = sanitize_text_field($_POST['export_format']);
        $email_config = $_POST['email_config'];
        $filter_config = $_POST['filter_config'];
        
        // Calculate next run time
        $next_run = self::calculate_next_run($schedule_type, $schedule_config);
        
        $data = array(
            'report_id' => $report_id,
            'name' => $name,
            'description' => $description,
            'schedule_type' => $schedule_type,
            'schedule_config' => json_encode($schedule_config),
            'export_format' => $export_format,
            'email_config' => json_encode($email_config),
            'filter_config' => json_encode($filter_config),
            'created_by' => get_current_user_id(),
            'next_run' => $next_run
        );
        
        $result = $wpdb->insert($schedules_table, $data);
        
        if ($result !== false) {
            $schedule_id = $wpdb->insert_id;
            
            // Schedule the WordPress cron job
            wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));
            
            wp_send_json_success(array(
                'message' => 'Report scheduled successfully',
                'schedule_id' => $schedule_id,
                'next_run' => $next_run
            ));
        } else {
            wp_send_json_error('Failed to schedule report');
        }
    }
    
    /**
     * Update schedule configuration
     */
    public static function update_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        
        $schedule_id = intval($_POST['schedule_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $schedule_type = sanitize_text_field($_POST['schedule_type']);
        $schedule_config = $_POST['schedule_config'];
        $export_format = sanitize_text_field($_POST['export_format']);
        $email_config = $_POST['email_config'];
        $filter_config = $_POST['filter_config'];
        
        // Calculate new next run time
        $next_run = self::calculate_next_run($schedule_type, $schedule_config);
        
        $data = array(
            'name' => $name,
            'description' => $description,
            'schedule_type' => $schedule_type,
            'schedule_config' => json_encode($schedule_config),
            'export_format' => $export_format,
            'email_config' => json_encode($email_config),
            'filter_config' => json_encode($filter_config),
            'next_run' => $next_run
        );
        
        $result = $wpdb->update($schedules_table, $data, array('id' => $schedule_id));
        
        if ($result !== false) {
            // Clear old cron job and schedule new one
            wp_clear_scheduled_hook('dab_run_scheduled_report', array($schedule_id));
            wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));
            
            wp_send_json_success(array(
                'message' => 'Schedule updated successfully',
                'next_run' => $next_run
            ));
        } else {
            wp_send_json_error('Failed to update schedule');
        }
    }
    
    /**
     * Delete a schedule
     */
    public static function delete_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $schedule_id = intval($_POST['schedule_id']);
        
        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        
        // Clear scheduled cron job
        wp_clear_scheduled_hook('dab_run_scheduled_report', array($schedule_id));
        
        $result = $wpdb->delete($schedules_table, array('id' => $schedule_id));
        
        if ($result !== false) {
            wp_send_json_success('Schedule deleted successfully');
        } else {
            wp_send_json_error('Failed to delete schedule');
        }
    }
    
    /**
     * Get all scheduled reports
     */
    public static function get_scheduled_reports() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';
        
        $schedules = $wpdb->get_results("
            SELECT s.*, r.name as report_name, r.description as report_description
            FROM $schedules_table s
            LEFT JOIN $reports_table r ON s.report_id = r.id
            ORDER BY s.created_at DESC
        ");
        
        // Decode JSON fields
        foreach ($schedules as $schedule) {
            $schedule->schedule_config = json_decode($schedule->schedule_config, true);
            $schedule->email_config = json_decode($schedule->email_config, true);
            $schedule->filter_config = json_decode($schedule->filter_config, true);
        }
        
        wp_send_json_success($schedules);
    }
}
