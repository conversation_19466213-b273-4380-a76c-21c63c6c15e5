<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Troubleshooting Guide - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #dc3545;
        }
        .faq-section {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .faq-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .faq-question {
            font-weight: bold;
            color: #721c24;
            font-size: 1.1em;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        .faq-question::before {
            content: "❓";
            margin-right: 10px;
            font-size: 1.2em;
        }
        .faq-answer {
            color: #495057;
            margin-left: 30px;
        }
        .solution-box {
            background: #d4edda;
            border: 1px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution-box h5 {
            color: #155724;
            margin-top: 0;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .solution-box h5::before {
            content: "✅";
            margin-right: 10px;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .error-box h5 {
            color: #721c24;
            margin-top: 0;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .error-box h5::before {
            content: "❌";
            margin-right: 10px;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .warning-box h5::before {
            content: "⚠️";
            margin-right: 10px;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .diagnostic-steps {
            background: #e2e3e5;
            border: 1px solid #6c757d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .diagnostic-steps h5 {
            color: #495057;
            margin-top: 0;
            font-weight: bold;
        }
        .diagnostic-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .diagnostic-steps li {
            margin-bottom: 10px;
            font-weight: 500;
        }
        .quick-fixes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .quick-fix-card {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 10px;
            padding: 20px;
        }
        .quick-fix-card h4 {
            color: #0c5460;
            margin-top: 0;
            border-bottom: 2px solid #17a2b8;
            padding-bottom: 10px;
        }
        .contact-support {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        .contact-support h3 {
            margin-top: 0;
            color: white;
        }
        .support-button {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .support-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,255,255,0.3);
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .system-requirements {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Troubleshooting Guide & FAQ</h1>

        <h2>🚨 Common Issues & Quick Fixes</h2>

        <div class="quick-fixes">
            <div class="quick-fix-card">
                <h4>Plugin Not Activating</h4>
                <ul>
                    <li>Check PHP version (7.4+ required)</li>
                    <li>Verify WordPress version (5.0+ required)</li>
                    <li>Check for plugin conflicts</li>
                    <li>Review error logs</li>
                </ul>
            </div>

            <div class="quick-fix-card">
                <h4>Forms Not Submitting</h4>
                <ul>
                    <li>Clear browser cache</li>
                    <li>Check JavaScript console for errors</li>
                    <li>Verify nonce settings</li>
                    <li>Test with default theme</li>
                </ul>
            </div>

            <div class="quick-fix-card">
                <h4>Database Tables Missing</h4>
                <ul>
                    <li>Deactivate and reactivate plugin</li>
                    <li>Check database permissions</li>
                    <li>Run manual table creation</li>
                    <li>Contact hosting provider</li>
                </ul>
            </div>

            <div class="quick-fix-card">
                <h4>Slow Performance</h4>
                <ul>
                    <li>Enable caching plugins</li>
                    <li>Optimize database queries</li>
                    <li>Reduce form field count</li>
                    <li>Check server resources</li>
                </ul>
            </div>
        </div>

        <h2>❓ Frequently Asked Questions</h2>

        <div class="faq-section">
            <div class="faq-item">
                <div class="faq-question">Why can't I see the Database App Builder menu?</div>
                <div class="faq-answer">
                    <p>This usually happens due to insufficient user permissions. Ensure you're logged in as an Administrator or have the required capabilities.</p>
                    
                    <div class="solution-box">
                        <h5>Solution Steps:</h5>
                        <ol>
                            <li>Log in as WordPress Administrator</li>
                            <li>Go to Users → Your Profile</li>
                            <li>Verify your role is "Administrator"</li>
                            <li>If not, ask site admin to update your role</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Forms are not saving data to the database</div>
                <div class="faq-answer">
                    <p>This is often caused by database connection issues, incorrect table structure, or validation errors.</p>
                    
                    <div class="diagnostic-steps">
                        <h5>Diagnostic Steps:</h5>
                        <ol>
                            <li>Check if the target table exists in Database → Tables</li>
                            <li>Verify all required fields are filled</li>
                            <li>Check browser console for JavaScript errors</li>
                            <li>Review WordPress debug logs</li>
                            <li>Test with a simple form first</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Email notifications are not being sent</div>
                <div class="faq-answer">
                    <p>Email delivery issues are common and usually related to server configuration or SMTP settings.</p>
                    
                    <div class="solution-box">
                        <h5>Email Troubleshooting:</h5>
                        <ol>
                            <li>Test WordPress email functionality with a simple contact form</li>
                            <li>Install an SMTP plugin (WP Mail SMTP recommended)</li>
                            <li>Configure proper SMTP settings</li>
                            <li>Check spam folders</li>
                            <li>Verify email addresses are correct</li>
                            <li>Test with different email providers</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">File uploads are failing</div>
                <div class="faq-answer">
                    <p>File upload issues are typically related to server limits, permissions, or file type restrictions.</p>
                    
                    <div class="warning-box">
                        <h5>Check These Settings:</h5>
                        <ul>
                            <li><strong>File Size Limit:</strong> Check PHP max_file_size setting</li>
                            <li><strong>Upload Directory:</strong> Verify wp-content/uploads is writable</li>
                            <li><strong>File Types:</strong> Ensure file type is allowed</li>
                            <li><strong>Server Space:</strong> Check available disk space</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Workflows are not triggering</div>
                <div class="faq-answer">
                    <p>Workflow automation requires proper trigger configuration and WordPress cron functionality.</p>
                    
                    <div class="diagnostic-steps">
                        <h5>Workflow Debugging:</h5>
                        <ol>
                            <li>Verify workflow is activated</li>
                            <li>Check trigger conditions are met</li>
                            <li>Test WordPress cron functionality</li>
                            <li>Review workflow logs</li>
                            <li>Test with simple actions first</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 Advanced Troubleshooting</h2>

        <div class="error-box">
            <h5>Database Connection Errors</h5>
            <p>If you're experiencing database connectivity issues:</p>
            
            <div class="code-block">
// Add to wp-config.php for debugging
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// Check error logs at:
// wp-content/debug.log
</div>
        </div>

        <div class="solution-box">
            <h5>Performance Optimization</h5>
            <p>For better performance with large datasets:</p>
            <ul>
                <li>Enable object caching (Redis/Memcached)</li>
                <li>Use pagination for large data sets</li>
                <li>Optimize database queries</li>
                <li>Enable GZIP compression</li>
                <li>Use a CDN for static assets</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>Security Considerations</h5>
            <p>Important security practices:</p>
            <ul>
                <li>Keep WordPress and plugins updated</li>
                <li>Use strong passwords and 2FA</li>
                <li>Limit file upload types</li>
                <li>Sanitize all user inputs</li>
                <li>Regular security scans</li>
            </ul>
        </div>

        <h2>⚙️ System Requirements</h2>

        <div class="system-requirements">
            <table>
                <tr>
                    <th>Component</th>
                    <th>Minimum Requirement</th>
                    <th>Recommended</th>
                </tr>
                <tr>
                    <td>WordPress Version</td>
                    <td>5.0+</td>
                    <td>Latest version</td>
                </tr>
                <tr>
                    <td>PHP Version</td>
                    <td>7.4+</td>
                    <td>8.0+</td>
                </tr>
                <tr>
                    <td>MySQL Version</td>
                    <td>5.6+</td>
                    <td>8.0+</td>
                </tr>
                <tr>
                    <td>Memory Limit</td>
                    <td>256MB</td>
                    <td>512MB+</td>
                </tr>
                <tr>
                    <td>Max Execution Time</td>
                    <td>30 seconds</td>
                    <td>60 seconds+</td>
                </tr>
                <tr>
                    <td>Upload Max Size</td>
                    <td>32MB</td>
                    <td>128MB+</td>
                </tr>
            </table>
        </div>

        <h2>🛠️ Debug Mode & Logging</h2>

        <div class="diagnostic-steps">
            <h5>Enable Debug Mode:</h5>
            <ol>
                <li>Navigate to <strong>Database App Builder → Settings</strong></li>
                <li>Enable <strong>"Debug Mode"</strong></li>
                <li>Set log level to <strong>"Detailed"</strong></li>
                <li>Reproduce the issue</li>
                <li>Check logs in <strong>Settings → Debug Logs</strong></li>
                <li>Share relevant log entries with support</li>
            </ol>
        </div>

        <div class="code-block">
// Manual debug logging
error_log('DAB Debug: ' . print_r($data, true));

// Check WordPress debug log
tail -f wp-content/debug.log

// Database query debugging
define('SAVEQUERIES', true);
</div>

        <h2>🔄 Plugin Conflicts</h2>

        <div class="diagnostic-steps">
            <h5>Identifying Plugin Conflicts:</h5>
            <ol>
                <li>Deactivate all other plugins</li>
                <li>Test if Database App Builder works correctly</li>
                <li>Reactivate plugins one by one</li>
                <li>Test after each activation</li>
                <li>Identify the conflicting plugin</li>
                <li>Check for compatibility updates</li>
                <li>Contact plugin authors if needed</li>
            </ol>
        </div>

        <div class="warning-box">
            <h5>Known Conflicts</h5>
            <p>Some plugins that may cause conflicts:</p>
            <ul>
                <li>Aggressive caching plugins</li>
                <li>Security plugins with strict settings</li>
                <li>Other form builder plugins</li>
                <li>Database optimization plugins</li>
                <li>JavaScript minification plugins</li>
            </ul>
        </div>

        <div class="contact-support">
            <h3>🆘 Still Need Help?</h3>
            <p>If you can't resolve the issue using this guide, our support team is here to help!</p>
            
            <a href="mailto:<EMAIL>" class="support-button">📧 Email Support</a>
            <a href="#" class="support-button">💬 Live Chat</a>
            <a href="#" class="support-button">📋 Submit Ticket</a>
            
            <p style="margin-top: 20px; font-size: 0.9em;">
                <strong>Before contacting support, please:</strong><br>
                • Include your WordPress and plugin versions<br>
                • Describe steps to reproduce the issue<br>
                • Share any error messages or logs<br>
                • Mention what you've already tried
            </p>
        </div>

        <div class="navigation-buttons">
            <a href="analytics-tutorial.html" class="nav-button">← Previous: Analytics Tutorial</a>
            <a href="complete-user-guide.html" class="nav-button">Back to Main Guide →</a>
        </div>
    </div>
</body>
</html>
