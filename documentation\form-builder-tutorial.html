<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Builder Tutorial - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .example-form {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .example-form h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
            font-size: 1.2em;
        }
        .form-preview {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .form-field {
            margin-bottom: 15px;
        }
        .form-field label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }
        .form-field input, .form-field select, .form-field textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .required {
            color: #dc3545;
        }
        .configuration-panel {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .configuration-panel h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .shortcode-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        .shortcode-box code {
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h5 {
            color: #721c24;
            margin-top: 0;
            font-weight: bold;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Form Builder Tutorial - Create Powerful Forms</h1>

        <div class="step-by-step">
            <h4><span class="step-number">1</span>Creating Your First Form</h4>
            <ol>
                <li>Navigate to <strong>Database App Builder → Forms</strong></li>
                <li>Click <strong>"Add New Form"</strong></li>
                <li>Enter a descriptive form name (e.g., "Customer Registration")</li>
                <li>Select the table where data will be stored</li>
                <li>Choose fields to include in your form</li>
                <li>Arrange field order by dragging and dropping</li>
                <li>Configure form settings and styling</li>
                <li>Click <strong>"Create Form"</strong></li>
            </ol>
        </div>

        <div class="example-form">
            <h5>📝 Example: Customer Registration Form</h5>
            <div class="form-preview">
                <h3>Customer Registration</h3>
                <p>Please fill out all required fields to create your account.</p>
                
                <div class="form-field">
                    <label>First Name <span class="required">*</span></label>
                    <input type="text" placeholder="Enter your first name">
                </div>
                
                <div class="form-field">
                    <label>Last Name <span class="required">*</span></label>
                    <input type="text" placeholder="Enter your last name">
                </div>
                
                <div class="form-field">
                    <label>Email Address <span class="required">*</span></label>
                    <input type="email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-field">
                    <label>Phone Number</label>
                    <input type="tel" placeholder="(*************">
                </div>
                
                <div class="form-field">
                    <label>Company</label>
                    <input type="text" placeholder="Your company name">
                </div>
                
                <div class="form-field">
                    <label>How did you hear about us?</label>
                    <select>
                        <option>Select an option</option>
                        <option>Google Search</option>
                        <option>Social Media</option>
                        <option>Referral</option>
                        <option>Advertisement</option>
                    </select>
                </div>
                
                <button style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Submit Registration</button>
            </div>
        </div>

        <h2>⚙️ Form Configuration Options</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>📝 Basic Settings</h4>
                <ul>
                    <li><strong>Form Title:</strong> Display title above form</li>
                    <li><strong>Description:</strong> Instructions for users</li>
                    <li><strong>Submit Button Text:</strong> Customize button label</li>
                    <li><strong>Required Fields:</strong> Mark mandatory fields</li>
                    <li><strong>Field Order:</strong> Drag-and-drop arrangement</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>🎨 Styling & Layout</h4>
                <ul>
                    <li><strong>Form Width:</strong> Full width or fixed size</li>
                    <li><strong>Column Layout:</strong> Single or multi-column</li>
                    <li><strong>Field Spacing:</strong> Adjust field margins</li>
                    <li><strong>Color Scheme:</strong> Match your brand</li>
                    <li><strong>Custom CSS:</strong> Advanced styling</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>📧 Notifications</h4>
                <ul>
                    <li><strong>Admin Email:</strong> Notify on submissions</li>
                    <li><strong>User Confirmation:</strong> Send receipt email</li>
                    <li><strong>Email Templates:</strong> Custom message design</li>
                    <li><strong>Multiple Recipients:</strong> CC/BCC support</li>
                    <li><strong>Conditional Emails:</strong> Based on form data</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>🔄 After Submission</h4>
                <ul>
                    <li><strong>Success Message:</strong> Thank you message</li>
                    <li><strong>Redirect URL:</strong> Send to specific page</li>
                    <li><strong>Show Submitted Data:</strong> Confirmation display</li>
                    <li><strong>Reset Form:</strong> Clear for new entry</li>
                    <li><strong>Download Receipt:</strong> PDF generation</li>
                </ul>
            </div>
        </div>

        <h2>🧠 Conditional Logic & Smart Forms</h2>

        <div class="step-by-step">
            <h4><span class="step-number">2</span>Setting Up Conditional Logic</h4>
            <p>Make your forms intelligent by showing/hiding fields based on user selections:</p>
            <ol>
                <li>In the form editor, click <strong>"Conditional Logic"</strong> tab</li>
                <li>Click <strong>"Add Rule"</strong></li>
                <li>Select the <strong>target field</strong> (field to show/hide)</li>
                <li>Choose the <strong>condition</strong> (equals, not equals, contains, etc.)</li>
                <li>Select the <strong>source field</strong> and trigger value</li>
                <li>Set the <strong>action</strong> (show or hide)</li>
                <li>Test your logic before publishing</li>
            </ol>
        </div>

        <div class="example-form">
            <h5>🎯 Example: Event Registration with Conditional Logic</h5>
            <div class="configuration-panel">
                <h5>Logic Rules:</h5>
                <table>
                    <tr>
                        <th>If Field</th>
                        <th>Condition</th>
                        <th>Value</th>
                        <th>Then</th>
                        <th>Target Field</th>
                    </tr>
                    <tr>
                        <td>Event Type</td>
                        <td>equals</td>
                        <td>"Workshop"</td>
                        <td>Show</td>
                        <td>Skill Level</td>
                    </tr>
                    <tr>
                        <td>Attendance</td>
                        <td>equals</td>
                        <td>"Virtual"</td>
                        <td>Hide</td>
                        <td>Parking Needed</td>
                    </tr>
                    <tr>
                        <td>Dietary Restrictions</td>
                        <td>equals</td>
                        <td>"Yes"</td>
                        <td>Show</td>
                        <td>Dietary Details</td>
                    </tr>
                </table>
            </div>
        </div>

        <h2>🔐 Form Security & Validation</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>🛡️ Security Features</h4>
                <ul>
                    <li><strong>CAPTCHA Protection:</strong> Prevent spam submissions</li>
                    <li><strong>Honeypot Fields:</strong> Hidden bot detection</li>
                    <li><strong>Rate Limiting:</strong> Prevent form abuse</li>
                    <li><strong>IP Blocking:</strong> Block malicious users</li>
                    <li><strong>Nonce Verification:</strong> CSRF protection</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>✅ Validation Rules</h4>
                <ul>
                    <li><strong>Required Fields:</strong> Mandatory validation</li>
                    <li><strong>Email Format:</strong> Valid email checking</li>
                    <li><strong>Phone Numbers:</strong> Format validation</li>
                    <li><strong>Custom Regex:</strong> Pattern matching</li>
                    <li><strong>File Types:</strong> Upload restrictions</li>
                </ul>
            </div>
        </div>

        <h2>📱 Multi-Step Forms (Phase 2 Feature)</h2>

        <div class="step-by-step">
            <h4><span class="step-number">3</span>Creating Multi-Step Forms</h4>
            <p>Break long forms into manageable steps for better user experience:</p>
            <ol>
                <li>Enable <strong>"Multi-Step Mode"</strong> in form settings</li>
                <li>Group related fields into logical steps</li>
                <li>Add step titles and descriptions</li>
                <li>Configure progress indicators</li>
                <li>Set validation for each step</li>
                <li>Customize navigation buttons</li>
                <li>Test the complete flow</li>
            </ol>
        </div>

        <div class="example-form">
            <h5>📋 Example: Job Application Multi-Step Form</h5>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 20px 0;">
                <div style="background: #28a745; color: white; padding: 10px; text-align: center; border-radius: 5px;">
                    <strong>Step 1</strong><br>Personal Info
                </div>
                <div style="background: #6c757d; color: white; padding: 10px; text-align: center; border-radius: 5px;">
                    <strong>Step 2</strong><br>Experience
                </div>
                <div style="background: #6c757d; color: white; padding: 10px; text-align: center; border-radius: 5px;">
                    <strong>Step 3</strong><br>Documents
                </div>
                <div style="background: #6c757d; color: white; padding: 10px; text-align: center; border-radius: 5px;">
                    <strong>Step 4</strong><br>Review
                </div>
            </div>
        </div>

        <h2>🚀 Publishing Your Form</h2>

        <div class="shortcode-box">
            <h3>📋 Form Shortcode</h3>
            <p>Use this shortcode to display your form anywhere on your site:</p>
            <code>[dab_form id="123"]</code>
            <p><small>Replace "123" with your actual form ID</small></p>
        </div>

        <div class="configuration-panel">
            <h5>📍 Where to Use Forms:</h5>
            <ul>
                <li><strong>Pages:</strong> Contact, registration, application pages</li>
                <li><strong>Posts:</strong> Embed in blog posts and articles</li>
                <li><strong>Widgets:</strong> Sidebar and footer forms</li>
                <li><strong>Popups:</strong> Modal and overlay forms</li>
                <li><strong>Landing Pages:</strong> Dedicated conversion pages</li>
            </ul>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tips for Better Forms</h5>
            <ul>
                <li><strong>Keep it short:</strong> Only ask for essential information</li>
                <li><strong>Clear labels:</strong> Use descriptive field labels</li>
                <li><strong>Logical flow:</strong> Group related fields together</li>
                <li><strong>Mobile-first:</strong> Test on mobile devices</li>
                <li><strong>Progress indicators:</strong> Show completion status</li>
                <li><strong>Error messages:</strong> Provide helpful validation feedback</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Data Privacy</h5>
            <p>Always inform users about data collection and usage. Include privacy policy links and obtain necessary consents, especially for GDPR compliance.</p>
        </div>

        <div class="navigation-buttons">
            <a href="advanced-fields-tutorial.html" class="nav-button">← Previous: Advanced Fields</a>
            <a href="views-tutorial.html" class="nav-button">Next: Views & Data Display →</a>
        </div>
    </div>
</body>
</html>
