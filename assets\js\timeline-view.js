/**
 * Timeline View JavaScript
 * 
 * Handles timeline field functionality
 */

(function($) {
    'use strict';

    // Timeline View Object
    window.DABTimelineView = {
        items: [],
        
        init: function() {
            this.bindEvents();
            this.loadItems();
        },

        bindEvents: function() {
            // Add timeline item button
            $(document).on('click', '.dab-add-timeline-item', this.showAddItemModal.bind(this));
            
            // Edit timeline item
            $(document).on('click', '.dab-timeline-action-btn.edit', this.editItem.bind(this));
            
            // Delete timeline item
            $(document).on('click', '.dab-timeline-action-btn.delete', this.deleteItem.bind(this));
            
            // Modal events
            $(document).on('click', '.dab-timeline-modal-close', this.closeModal.bind(this));
            $(document).on('click', '.dab-timeline-modal', function(e) {
                if (e.target === this) {
                    window.DABTimelineView.closeModal();
                }
            });
            
            // Form submission
            $(document).on('submit', '#dab-timeline-form', this.saveItem.bind(this));
        },

        render: function() {
            const container = $('.dab-timeline');
            if (container.length === 0) return;

            if (this.items.length === 0) {
                container.html(`
                    <div class="dab-timeline-empty">
                        <div class="dab-timeline-empty-icon">📅</div>
                        <h3>No Timeline Items</h3>
                        <p>Add your first timeline item to get started.</p>
                    </div>
                `);
                return;
            }

            let html = '';
            this.items.forEach(item => {
                html += this.renderItem(item);
            });

            container.html(html);
        },

        renderItem: function(item) {
            const statusClass = item.status || 'pending';
            const date = new Date(item.date).toLocaleDateString();
            
            return `
                <div class="dab-timeline-item ${statusClass}" data-item-id="${item.id}">
                    <div class="dab-timeline-item-header">
                        <h4 class="dab-timeline-item-title">${item.title}</h4>
                        <div class="dab-timeline-item-meta">
                            <span class="dab-timeline-item-date">${date}</span>
                            <span class="dab-timeline-item-status ${statusClass}">${this.getStatusLabel(statusClass)}</span>
                        </div>
                    </div>
                    ${item.description ? `<div class="dab-timeline-item-description">${item.description}</div>` : ''}
                    <div class="dab-timeline-item-actions">
                        <button type="button" class="dab-timeline-action-btn edit" data-item-id="${item.id}">
                            <span class="dashicons dashicons-edit"></span> Edit
                        </button>
                        <button type="button" class="dab-timeline-action-btn delete" data-item-id="${item.id}">
                            <span class="dashicons dashicons-trash"></span> Delete
                        </button>
                    </div>
                </div>
            `;
        },

        getStatusLabel: function(status) {
            const labels = {
                'completed': 'Completed',
                'in-progress': 'In Progress',
                'pending': 'Pending',
                'milestone': 'Milestone'
            };
            return labels[status] || 'Pending';
        },

        showAddItemModal: function() {
            this.showItemModal();
        },

        editItem: function(e) {
            const itemId = $(e.currentTarget).data('item-id');
            const item = this.items.find(i => i.id == itemId);
            if (item) {
                this.showItemModal(item);
            }
        },

        showItemModal: function(item = null) {
            let modal = $('#dab-timeline-modal');
            
            if (modal.length === 0) {
                modal = this.createItemModal();
            }

            // Reset form
            const form = modal.find('#dab-timeline-form')[0];
            if (form) form.reset();

            if (item) {
                // Edit mode
                modal.find('.dab-timeline-modal-title').text('Edit Timeline Item');
                modal.find('#timeline-title').val(item.title);
                modal.find('#timeline-description').val(item.description);
                modal.find('#timeline-date').val(item.date);
                modal.find('#timeline-status').val(item.status);
                modal.find('#timeline-id').val(item.id);
                modal.find('.dab-delete-timeline-btn').show();
            } else {
                // Add mode
                modal.find('.dab-timeline-modal-title').text('Add Timeline Item');
                modal.find('#timeline-id').val('');
                modal.find('.dab-delete-timeline-btn').hide();
            }

            modal.show();
        },

        createItemModal: function() {
            const modalHtml = `
                <div id="dab-timeline-modal" class="dab-timeline-modal">
                    <div class="dab-timeline-modal-content">
                        <div class="dab-timeline-modal-header">
                            <h3 class="dab-timeline-modal-title">Add Timeline Item</h3>
                            <button type="button" class="dab-timeline-modal-close">&times;</button>
                        </div>
                        <div class="dab-timeline-modal-body">
                            <form id="dab-timeline-form">
                                <input type="hidden" id="timeline-id" name="item_id">
                                
                                <div class="dab-timeline-form-group">
                                    <label for="timeline-title">Title</label>
                                    <input type="text" id="timeline-title" name="title" class="dab-timeline-form-control" required>
                                </div>
                                
                                <div class="dab-timeline-form-group">
                                    <label for="timeline-description">Description</label>
                                    <textarea id="timeline-description" name="description" class="dab-timeline-form-control textarea"></textarea>
                                </div>
                                
                                <div class="dab-timeline-form-group">
                                    <label for="timeline-date">Date</label>
                                    <input type="date" id="timeline-date" name="date" class="dab-timeline-form-control" required>
                                </div>
                                
                                <div class="dab-timeline-form-group">
                                    <label for="timeline-status">Status</label>
                                    <select id="timeline-status" name="status" class="dab-timeline-form-control">
                                        <option value="pending">Pending</option>
                                        <option value="in-progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                        <option value="milestone">Milestone</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="dab-timeline-modal-footer">
                            <button type="button" class="dab-timeline-btn dab-timeline-btn-secondary dab-timeline-modal-close">Cancel</button>
                            <button type="button" class="dab-timeline-btn dab-timeline-btn-danger dab-delete-timeline-btn" style="display: none;">Delete</button>
                            <button type="submit" form="dab-timeline-form" class="dab-timeline-btn dab-timeline-btn-primary">Save Item</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            
            // Bind delete button
            $('#dab-timeline-modal').on('click', '.dab-delete-timeline-btn', this.deleteItemFromModal.bind(this));
            
            return $('#dab-timeline-modal');
        },

        closeModal: function() {
            $('#dab-timeline-modal').hide();
        },

        saveItem: function(e) {
            e.preventDefault();
            
            const form = $('#dab-timeline-form');
            const formData = new FormData(form[0]);
            
            // Add AJAX data
            formData.append('action', 'dab_save_timeline_item');
            formData.append('nonce', dabTimelineData.nonce);

            $.ajax({
                url: dabTimelineData.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        window.DABTimelineView.loadItems();
                        window.DABTimelineView.closeModal();
                        window.DABTimelineView.showNotification('Timeline item saved successfully!');
                    } else {
                        window.DABTimelineView.showNotification('Error saving timeline item: ' + response.data, 'error');
                    }
                },
                error: function() {
                    window.DABTimelineView.showNotification('Error saving timeline item', 'error');
                }
            });
        },

        deleteItem: function(e) {
            const itemId = $(e.currentTarget).data('item-id');
            
            if (confirm('Are you sure you want to delete this timeline item?')) {
                this.performDelete(itemId);
            }
        },

        deleteItemFromModal: function() {
            const itemId = $('#timeline-id').val();
            
            if (confirm('Are you sure you want to delete this timeline item?')) {
                this.performDelete(itemId);
            }
        },

        performDelete: function(itemId) {
            if (typeof dabTimelineData === 'undefined') {
                console.warn('DAB Timeline: dabTimelineData not available');
                return;
            }

            $.ajax({
                url: dabTimelineData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_delete_timeline_item',
                    nonce: dabTimelineData.nonce,
                    item_id: itemId
                },
                success: function(response) {
                    if (response.success) {
                        window.DABTimelineView.loadItems();
                        window.DABTimelineView.closeModal();
                        window.DABTimelineView.showNotification('Timeline item deleted successfully!');
                    } else {
                        window.DABTimelineView.showNotification('Error deleting timeline item: ' + response.data, 'error');
                    }
                },
                error: function() {
                    window.DABTimelineView.showNotification('Error deleting timeline item', 'error');
                }
            });
        },

        loadItems: function() {
            if (typeof dabTimelineData === 'undefined') {
                console.warn('DAB Timeline: dabTimelineData not available');
                return;
            }

            $.ajax({
                url: dabTimelineData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_timeline_items',
                    nonce: dabTimelineData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        window.DABTimelineView.items = response.data || [];
                        window.DABTimelineView.render();
                    }
                },
                error: function() {
                    console.warn('DAB Timeline: Error loading timeline items');
                }
            });
        },

        showNotification: function(message, type = 'success') {
            const notification = $(`
                <div class="dab-timeline-notification dab-notification-${type}">
                    ${message}
                </div>
            `);

            // Add notification styles if not present
            if (!$('#dab-timeline-notification-styles').length) {
                $('head').append(`
                    <style id="dab-timeline-notification-styles">
                        .dab-timeline-notification {
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            padding: 12px 20px;
                            border-radius: 4px;
                            z-index: 10001;
                            font-size: 14px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        }
                        .dab-notification-success {
                            background: #4CAF50;
                            color: white;
                        }
                        .dab-notification-error {
                            background: #f44336;
                            color: white;
                        }
                    </style>
                `);
            }

            $('body').append(notification);

            setTimeout(function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            }, 3000);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        if ($('.dab-timeline-view').length > 0) {
            window.DABTimelineView.init();
        }
    });

})(jQuery);
