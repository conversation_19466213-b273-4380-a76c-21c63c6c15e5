/**
 * Database App Builder Wizard Styles
 * 
 * Styles for the guided setup wizards
 */

/* Wizard Dashboard */
.dab-wizard-dashboard {
  margin-top: 20px;
}

.dab-wizard-intro {
  margin-bottom: 30px;
  text-align: center;
}

.dab-wizard-description {
  font-size: 16px;
  color: #555;
  max-width: 800px;
  margin: 0 auto;
}

/* Wizard Cards */
.dab-wizard-card-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.dab-wizard-card-icon .dashicons {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #2271b1;
}

.dab-wizard-card-description {
  text-align: center;
  margin-bottom: 20px;
}

.dab-wizard-features {
  list-style-type: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.dab-wizard-features li {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 25px;
}

.dab-wizard-features li:before {
  content: "\f147";
  font-family: dashicons;
  position: absolute;
  left: 0;
  color: #2271b1;
}

.dab-wizard-features li:last-child {
  border-bottom: none;
}

/* Wizard Container */
.dab-wizard-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  overflow: hidden;
  display: flex;
  min-height: 600px;
}

/* Wizard Steps */
.dab-wizard-steps {
  width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 30px 0;
}

.dab-wizard-steps-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.dab-wizard-step {
  display: flex;
  padding: 15px 20px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

.dab-wizard-step.active {
  background-color: rgba(34, 113, 177, 0.05);
  border-left-color: #2271b1;
}

.dab-wizard-step.completed {
  border-left-color: #46b450;
}

.dab-wizard-step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-weight: bold;
  flex-shrink: 0;
}

.dab-wizard-step.active .dab-wizard-step-number {
  background-color: #2271b1;
  color: #fff;
}

.dab-wizard-step.completed .dab-wizard-step-number {
  background-color: #46b450;
  color: #fff;
}

.dab-wizard-step-content {
  flex-grow: 1;
}

.dab-wizard-step-title {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
}

.dab-wizard-step-description {
  margin: 0;
  font-size: 12px;
  color: #666;
}

/* Wizard Content */
.dab-wizard-content {
  flex-grow: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
}

.dab-wizard-header {
  margin-bottom: 30px;
}

.dab-wizard-title {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.dab-wizard-description {
  margin: 0;
  color: #666;
}

.dab-wizard-body {
  flex-grow: 1;
  margin-bottom: 30px;
}

.dab-wizard-footer {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dab-wizard-actions {
  display: flex;
  gap: 10px;
}

.dab-wizard-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.dab-wizard-progress-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.dab-wizard-progress-bar {
  width: 200px;
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.dab-wizard-progress-bar-inner {
  height: 100%;
  background-color: #2271b1;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Wizard Form Elements */
.dab-wizard-form-group {
  margin-bottom: 20px;
}

.dab-wizard-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
}

.dab-wizard-form-input,
.dab-wizard-form-select,
.dab-wizard-form-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.dab-wizard-form-textarea {
  min-height: 100px;
}

.dab-wizard-form-help {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* Wizard Save Indicator */
.dab-wizard-save-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #333;
  color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 9999;
}

.dab-wizard-save-indicator.active {
  opacity: 1;
}

/* Wizard Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.dab-animate-fade-in {
  animation: fadeIn 0.5s ease;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.dab-animate-slide-up {
  animation: slideUp 0.5s ease;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .dab-wizard-container {
    flex-direction: column;
  }
  
  .dab-wizard-steps {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 0;
  }
  
  .dab-wizard-steps-list {
    display: flex;
    overflow-x: auto;
    padding: 0 15px;
  }
  
  .dab-wizard-step {
    flex-direction: column;
    align-items: center;
    text-align: center;
    border-left: none;
    border-bottom: 3px solid transparent;
    padding: 10px 15px;
    min-width: 120px;
  }
  
  .dab-wizard-step.active {
    border-left-color: transparent;
    border-bottom-color: #2271b1;
  }
  
  .dab-wizard-step.completed {
    border-left-color: transparent;
    border-bottom-color: #46b450;
  }
  
  .dab-wizard-step-number {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .dab-wizard-step-description {
    display: none;
  }
}
