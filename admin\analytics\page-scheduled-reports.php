<?php
/**
 * Scheduled Reports Page
 * Phase 3: Data Intelligence & Analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
$schedule_id = isset($_GET['schedule_id']) ? intval($_GET['schedule_id']) : 0;

?>
<div class="wrap dab-scheduled-reports-page">
    <h1 class="wp-heading-inline">
        <?php _e('Scheduled Reports', 'db-app-builder'); ?>
        <span class="dab-phase-badge">Phase 3</span>
    </h1>
    
    <?php if ($action === 'list'): ?>
        <a href="<?php echo admin_url('admin.php?page=dab_scheduled_reports&action=create'); ?>" class="page-title-action">
            <?php _e('Schedule New Report', 'db-app-builder'); ?>
        </a>
        
        <div class="dab-schedules-overview">
            <div class="dab-overview-stats">
                <div class="dab-stat-box">
                    <div class="dab-stat-icon">
                        <span class="dashicons dashicons-clock"></span>
                    </div>
                    <div class="dab-stat-content">
                        <div class="dab-stat-number" id="total-schedules">-</div>
                        <div class="dab-stat-label"><?php _e('Total Schedules', 'db-app-builder'); ?></div>
                    </div>
                </div>
                
                <div class="dab-stat-box">
                    <div class="dab-stat-icon">
                        <span class="dashicons dashicons-yes-alt"></span>
                    </div>
                    <div class="dab-stat-content">
                        <div class="dab-stat-number" id="active-schedules">-</div>
                        <div class="dab-stat-label"><?php _e('Active Schedules', 'db-app-builder'); ?></div>
                    </div>
                </div>
                
                <div class="dab-stat-box">
                    <div class="dab-stat-icon">
                        <span class="dashicons dashicons-email"></span>
                    </div>
                    <div class="dab-stat-content">
                        <div class="dab-stat-number" id="reports-sent">-</div>
                        <div class="dab-stat-label"><?php _e('Reports Sent Today', 'db-app-builder'); ?></div>
                    </div>
                </div>
                
                <div class="dab-stat-box">
                    <div class="dab-stat-icon">
                        <span class="dashicons dashicons-calendar-alt"></span>
                    </div>
                    <div class="dab-stat-content">
                        <div class="dab-stat-number" id="next-execution">-</div>
                        <div class="dab-stat-label"><?php _e('Next Execution', 'db-app-builder'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dab-schedules-toolbar">
            <div class="dab-toolbar-filters">
                <select id="schedule-status-filter">
                    <option value=""><?php _e('All Statuses', 'db-app-builder'); ?></option>
                    <option value="active"><?php _e('Active', 'db-app-builder'); ?></option>
                    <option value="paused"><?php _e('Paused', 'db-app-builder'); ?></option>
                    <option value="failed"><?php _e('Failed', 'db-app-builder'); ?></option>
                </select>
                
                <select id="schedule-frequency-filter">
                    <option value=""><?php _e('All Frequencies', 'db-app-builder'); ?></option>
                    <option value="daily"><?php _e('Daily', 'db-app-builder'); ?></option>
                    <option value="weekly"><?php _e('Weekly', 'db-app-builder'); ?></option>
                    <option value="monthly"><?php _e('Monthly', 'db-app-builder'); ?></option>
                    <option value="custom"><?php _e('Custom', 'db-app-builder'); ?></option>
                </select>
                
                <input type="text" id="search-schedules" placeholder="<?php _e('Search schedules...', 'db-app-builder'); ?>">
            </div>
            
            <div class="dab-toolbar-actions">
                <button class="button" id="refresh-schedules">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'db-app-builder'); ?>
                </button>
                
                <button class="button" id="bulk-actions">
                    <span class="dashicons dashicons-admin-tools"></span>
                    <?php _e('Bulk Actions', 'db-app-builder'); ?>
                </button>
            </div>
        </div>

        <div class="dab-schedules-table-container">
            <table class="wp-list-table widefat fixed striped" id="schedules-table">
                <thead>
                    <tr>
                        <th class="check-column">
                            <input type="checkbox" id="select-all-schedules">
                        </th>
                        <th><?php _e('Schedule Name', 'db-app-builder'); ?></th>
                        <th><?php _e('Report', 'db-app-builder'); ?></th>
                        <th><?php _e('Frequency', 'db-app-builder'); ?></th>
                        <th><?php _e('Format', 'db-app-builder'); ?></th>
                        <th><?php _e('Status', 'db-app-builder'); ?></th>
                        <th><?php _e('Last Run', 'db-app-builder'); ?></th>
                        <th><?php _e('Next Run', 'db-app-builder'); ?></th>
                        <th><?php _e('Actions', 'db-app-builder'); ?></th>
                    </tr>
                </thead>
                <tbody id="schedules-tbody">
                    <tr>
                        <td colspan="9" class="dab-loading-row">
                            <div class="spinner is-active"></div>
                            <span><?php _e('Loading scheduled reports...', 'db-app-builder'); ?></span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    <?php elseif ($action === 'create' || $action === 'edit'): ?>
        <div class="dab-schedule-form-container">
            <form id="schedule-form" class="dab-schedule-form">
                <div class="dab-form-sections">
                    <div class="dab-form-section">
                        <h3><?php _e('Schedule Information', 'db-app-builder'); ?></h3>
                        
                        <div class="dab-form-row">
                            <div class="dab-form-group">
                                <label for="schedule-name"><?php _e('Schedule Name', 'db-app-builder'); ?></label>
                                <input type="text" id="schedule-name" class="dab-input" required>
                            </div>
                            
                            <div class="dab-form-group">
                                <label for="report-select"><?php _e('Report to Schedule', 'db-app-builder'); ?></label>
                                <select id="report-select" class="dab-select" required>
                                    <option value=""><?php _e('Select a report', 'db-app-builder'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="dab-form-group">
                            <label for="schedule-description"><?php _e('Description', 'db-app-builder'); ?></label>
                            <textarea id="schedule-description" class="dab-textarea" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="dab-form-section">
                        <h3><?php _e('Schedule Configuration', 'db-app-builder'); ?></h3>
                        
                        <div class="dab-form-row">
                            <div class="dab-form-group">
                                <label for="schedule-type"><?php _e('Schedule Type', 'db-app-builder'); ?></label>
                                <select id="schedule-type" class="dab-select" required>
                                    <option value="once"><?php _e('Run Once', 'db-app-builder'); ?></option>
                                    <option value="daily"><?php _e('Daily', 'db-app-builder'); ?></option>
                                    <option value="weekly"><?php _e('Weekly', 'db-app-builder'); ?></option>
                                    <option value="monthly"><?php _e('Monthly', 'db-app-builder'); ?></option>
                                    <option value="custom"><?php _e('Custom', 'db-app-builder'); ?></option>
                                </select>
                            </div>
                            
                            <div class="dab-form-group">
                                <label for="export-format"><?php _e('Export Format', 'db-app-builder'); ?></label>
                                <select id="export-format" class="dab-select" required>
                                    <option value="pdf"><?php _e('PDF', 'db-app-builder'); ?></option>
                                    <option value="excel"><?php _e('Excel', 'db-app-builder'); ?></option>
                                    <option value="csv"><?php _e('CSV', 'db-app-builder'); ?></option>
                                    <option value="html"><?php _e('HTML', 'db-app-builder'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div id="schedule-config-details">
                            <!-- Schedule configuration details will be loaded here based on type -->
                        </div>
                    </div>
                    
                    <div class="dab-form-section">
                        <h3><?php _e('Email Configuration', 'db-app-builder'); ?></h3>
                        
                        <div class="dab-form-group">
                            <label for="email-recipients"><?php _e('Email Recipients', 'db-app-builder'); ?></label>
                            <textarea id="email-recipients" class="dab-textarea" rows="2" 
                                placeholder="<?php _e('Enter email addresses separated by commas', 'db-app-builder'); ?>"></textarea>
                        </div>
                        
                        <div class="dab-form-row">
                            <div class="dab-form-group">
                                <label for="email-subject"><?php _e('Email Subject', 'db-app-builder'); ?></label>
                                <input type="text" id="email-subject" class="dab-input" 
                                    placeholder="<?php _e('Scheduled Report: {{report_name}}', 'db-app-builder'); ?>">
                            </div>
                            
                            <div class="dab-form-group">
                                <label for="email-template"><?php _e('Email Template', 'db-app-builder'); ?></label>
                                <select id="email-template" class="dab-select">
                                    <option value=""><?php _e('Default Template', 'db-app-builder'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="dab-form-group">
                            <label for="email-message"><?php _e('Custom Message', 'db-app-builder'); ?></label>
                            <textarea id="email-message" class="dab-textarea" rows="4" 
                                placeholder="<?php _e('Optional custom message to include in the email', 'db-app-builder'); ?>"></textarea>
                        </div>
                        
                        <div class="dab-form-group">
                            <label>
                                <input type="checkbox" id="send-on-failure">
                                <?php _e('Send notification on failure', 'db-app-builder'); ?>
                            </label>
                        </div>
                    </div>
                    
                    <div class="dab-form-section">
                        <h3><?php _e('Advanced Options', 'db-app-builder'); ?></h3>
                        
                        <div class="dab-form-group">
                            <label for="report-filters"><?php _e('Report Filters', 'db-app-builder'); ?></label>
                            <div id="filters-builder">
                                <button type="button" class="button" id="add-filter">
                                    <span class="dashicons dashicons-plus"></span>
                                    <?php _e('Add Filter', 'db-app-builder'); ?>
                                </button>
                            </div>
                        </div>
                        
                        <div class="dab-form-row">
                            <div class="dab-form-group">
                                <label for="max-rows"><?php _e('Maximum Rows', 'db-app-builder'); ?></label>
                                <input type="number" id="max-rows" class="dab-input" min="1" max="100000" value="10000">
                            </div>
                            
                            <div class="dab-form-group">
                                <label for="timeout"><?php _e('Timeout (seconds)', 'db-app-builder'); ?></label>
                                <input type="number" id="timeout" class="dab-input" min="30" max="3600" value="300">
                            </div>
                        </div>
                        
                        <div class="dab-form-group">
                            <label>
                                <input type="checkbox" id="compress-file">
                                <?php _e('Compress large files', 'db-app-builder'); ?>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="dab-form-actions">
                    <button type="button" class="button" id="test-schedule">
                        <span class="dashicons dashicons-admin-tools"></span>
                        <?php _e('Test Schedule', 'db-app-builder'); ?>
                    </button>
                    
                    <button type="button" class="button" id="cancel-schedule">
                        <?php _e('Cancel', 'db-app-builder'); ?>
                    </button>
                    
                    <button type="submit" class="button button-primary">
                        <span class="dashicons dashicons-saved"></span>
                        <?php _e('Save Schedule', 'db-app-builder'); ?>
                    </button>
                </div>
            </form>
        </div>

    <?php elseif ($action === 'history'): ?>
        <div class="dab-execution-history">
            <h2><?php _e('Execution History', 'db-app-builder'); ?></h2>
            
            <div class="dab-history-filters">
                <select id="history-schedule-filter">
                    <option value=""><?php _e('All Schedules', 'db-app-builder'); ?></option>
                </select>
                
                <select id="history-status-filter">
                    <option value=""><?php _e('All Statuses', 'db-app-builder'); ?></option>
                    <option value="success"><?php _e('Success', 'db-app-builder'); ?></option>
                    <option value="failed"><?php _e('Failed', 'db-app-builder'); ?></option>
                    <option value="partial"><?php _e('Partial', 'db-app-builder'); ?></option>
                </select>
                
                <input type="date" id="history-date-from" class="dab-input">
                <input type="date" id="history-date-to" class="dab-input">
                
                <button class="button" id="apply-history-filters">
                    <?php _e('Apply Filters', 'db-app-builder'); ?>
                </button>
            </div>
            
            <div class="dab-history-table-container">
                <table class="wp-list-table widefat fixed striped" id="history-table">
                    <thead>
                        <tr>
                            <th><?php _e('Schedule', 'db-app-builder'); ?></th>
                            <th><?php _e('Execution Time', 'db-app-builder'); ?></th>
                            <th><?php _e('Status', 'db-app-builder'); ?></th>
                            <th><?php _e('Duration', 'db-app-builder'); ?></th>
                            <th><?php _e('Rows', 'db-app-builder'); ?></th>
                            <th><?php _e('File Size', 'db-app-builder'); ?></th>
                            <th><?php _e('Email Sent', 'db-app-builder'); ?></th>
                            <th><?php _e('Actions', 'db-app-builder'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="history-tbody">
                        <tr>
                            <td colspan="8" class="dab-loading-row">
                                <div class="spinner is-active"></div>
                                <span><?php _e('Loading execution history...', 'db-app-builder'); ?></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    <?php endif; ?>
</div>

<style>
.dab-scheduled-reports-page {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

.dab-phase-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 10px;
}

.dab-schedules-overview {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.dab-stat-box {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.dab-stat-icon {
    font-size: 2.5em;
    margin-right: 20px;
    opacity: 0.8;
}

.dab-stat-number {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.dab-stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.dab-schedules-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-toolbar-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-toolbar-actions {
    display: flex;
    gap: 10px;
}

.dab-schedules-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dab-loading-row {
    text-align: center;
    padding: 50px !important;
}

.dab-schedule-form-container {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-form-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.dab-form-section {
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
}

.dab-form-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.2em;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.dab-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.dab-form-group {
    margin-bottom: 15px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.dab-input, .dab-textarea, .dab-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.dab-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

.dab-execution-history {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-history-filters {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dab-history-table-container {
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    overflow: hidden;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize the scheduled reports page
    DAB_ScheduledReports.init();
});
</script>
