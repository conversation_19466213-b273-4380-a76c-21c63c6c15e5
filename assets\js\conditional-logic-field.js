/**
 * Conditional Logic Field Scripts
 *
 * JavaScript for conditional logic fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all conditional logic fields
    function initConditionalLogicFields() {
        console.log('Initializing conditional logic fields');
        $('.dab-conditional-logic-field').each(function() {
            var $field = $(this);
            console.log('Found conditional logic field:', $field);

            // Skip if already initialized
            if ($field.data('initialized')) {
                console.log('Field already initialized, skipping');
                return;
            }

            // Mark as initialized
            console.log('Initializing field');
            $field.data('initialized', true);

            var $value = $field.find('input[type="hidden"]').last();
            var $rulesInput = $field.find('.dab-conditional-logic-rules-input');
            var $display = $field.find('.dab-conditional-logic-display');
            var $toggleBtn = $field.find('.dab-conditional-logic-toggle');
            var $editorPanel = $field.find('.dab-conditional-logic-editor-panel');
            var $closeBtn = $field.find('.dab-conditional-logic-close');
            var $applyBtn = $field.find('.dab-conditional-logic-apply');
            var $cancelBtn = $field.find('.dab-conditional-logic-cancel');

            // Debug elements
            console.log('Value element found:', $value.length > 0);
            console.log('Display element found:', $display.length > 0);
            console.log('Toggle button found:', $toggleBtn.length > 0);
            console.log('Editor panel found:', $editorPanel.length > 0);
            console.log('Close button found:', $closeBtn.length > 0);
            console.log('Apply button found:', $applyBtn.length > 0);
            console.log('Cancel button found:', $cancelBtn.length > 0);

            var fieldId = $field.attr('id');
            var outputType = $field.data('output-type') || 'text';
            var logicType = $field.data('logic-type') || 'if_else';
            var autoUpdate = $field.data('auto-update') || true;
            var tableId = $field.data('table-id') || 0;
            var fieldId = $field.data('field-id') || 0;

            // Logic rules
            var logicRules = {
                type: logicType,
                rules: [],
                default: ''
            };

            // Try to load existing rules
            console.log('Field ID:', fieldId);
            console.log('Rules input element found:', $rulesInput.length > 0);

            // Check if rules input is found
            if ($rulesInput.length === 0) {
                console.error('Rules input element not found for field:', fieldId);
                // Try to find it by ID
                $rulesInput = $('#' + fieldId + '-rules');
                console.log('Trying to find by ID:', fieldId + '-rules', 'Found:', $rulesInput.length > 0);

                // If still not found, create it
                if ($rulesInput.length === 0) {
                    console.log('Creating rules input element');
                    $field.append('<input type="hidden" name="' + $field.data('field-slug') + '_rules" id="' + fieldId + '-rules" class="dab-conditional-logic-rules-input" value="">');
                    $rulesInput = $field.find('.dab-conditional-logic-rules-input');
                }
            }

            var existingRules = $rulesInput.val();
            console.log('Rules input value:', existingRules);
            console.log('Rules input element:', $rulesInput[0]);

            if (existingRules) {
                try {
                    logicRules = JSON.parse(existingRules);
                    console.log('Loaded existing rules:', logicRules);

                    // Populate UI with existing rules
                    populateRulesUI(logicRules);
                } catch (e) {
                    console.error('Error parsing conditional logic rules:', e);
                    console.error('Raw rules string:', existingRules);
                }
            } else {
                console.log('No existing rules found for this field');
            }

            // Toggle editor panel
            $toggleBtn.on('click', function(e) {
                e.preventDefault();
                console.log('Toggle button clicked');
                console.log('Editor panel element:', $editorPanel.length > 0);
                console.log('Editor panel visibility:', $editorPanel.is(':visible'));
                $editorPanel.slideToggle(200);
            });

            // Close editor panel
            $closeBtn.on('click', function() {
                $editorPanel.slideUp(200);
            });

            // Apply logic
            $applyBtn.on('click', function() {
                // Build logic rules based on logic type
                if (logicType === 'if_else') {
                    buildIfElseRules();
                } else if (logicType === 'switch') {
                    buildSwitchRules();
                } else if (logicType === 'formula') {
                    buildFormulaRules();
                }

                // Save rules to hidden input
                var rulesJson = JSON.stringify(logicRules);
                console.log('Saving rules:', logicRules);
                console.log('Rules JSON:', rulesJson);
                $rulesInput.val(rulesJson);

                // Evaluate rules
                evaluateRules();

                // Close editor panel
                $editorPanel.slideUp(200);
            });

            // Cancel editing
            $cancelBtn.on('click', function() {
                $editorPanel.slideUp(200);
            });

            // Build If-Else rules
            function buildIfElseRules() {
                logicRules.type = 'if_else';
                logicRules.rules = [];

                // Get all rule rows
                $field.find('.dab-conditional-logic-rule').each(function() {
                    var $rule = $(this);
                    var $fieldSelect = $rule.find('.dab-conditional-logic-field-select');
                    var $operator = $rule.find('.dab-conditional-logic-operator');
                    var $valueInput = $rule.find('.dab-conditional-logic-value-input');
                    var $resultInput = $rule.find('.dab-conditional-logic-result-input');

                    var fieldSlug = $fieldSelect.val();
                    var operator = $operator.val();
                    var value = $valueInput.val();
                    var result = $resultInput.val();

                    if (fieldSlug && operator && result) {
                        logicRules.rules.push({
                            field: fieldSlug,
                            operator: operator,
                            value: value,
                            result: result
                        });
                    }
                });

                // Get else value
                logicRules.default = $field.find('.dab-conditional-logic-else-input').val();
            }

            // Build Switch rules
            function buildSwitchRules() {
                logicRules.type = 'switch';
                logicRules.field = $field.find('.dab-conditional-logic-switch-field-select').val();
                logicRules.cases = [];

                // Get all case rows
                $field.find('.dab-conditional-logic-case').each(function() {
                    var $case = $(this);
                    var $valueInput = $case.find('.dab-conditional-logic-case-input');
                    var $resultInput = $case.find('.dab-conditional-logic-case-result-input');

                    var value = $valueInput.val();
                    var result = $resultInput.val();

                    if (value && result) {
                        logicRules.cases.push({
                            value: value,
                            result: result
                        });
                    }
                });

                // Get default value
                logicRules.default = $field.find('.dab-conditional-logic-default-input').val();
            }

            // Build Formula rules
            function buildFormulaRules() {
                logicRules.type = 'formula';
                logicRules.formula = $field.find('.dab-conditional-logic-formula-input').val();
            }

            // Evaluate rules based on current form values
            function evaluateRules() {
                var result = '';

                if (logicRules.type === 'if_else') {
                    result = evaluateIfElseRules();
                } else if (logicRules.type === 'switch') {
                    result = evaluateSwitchRules();
                } else if (logicRules.type === 'formula') {
                    result = evaluateFormulaRules();
                }

                // Format result based on output type
                if (outputType === 'boolean') {
                    // Convert to boolean
                    var boolValue = Boolean(result);
                    result = boolValue ? '1' : '0';
                    $display.text(boolValue ? dabConditionalLogic.i18n.yes : dabConditionalLogic.i18n.no);
                } else if (outputType === 'number') {
                    // Ensure it's a number
                    var numValue = parseFloat(result);
                    result = isNaN(numValue) ? '0' : numValue.toString();
                    $display.text(result);
                } else {
                    $display.text(result);
                }

                // Update hidden value
                $value.val(result);
            }

            // Evaluate If-Else rules
            function evaluateIfElseRules() {
                if (!logicRules.rules || !logicRules.rules.length) {
                    return logicRules.default || '';
                }

                // Check each rule in order
                for (var i = 0; i < logicRules.rules.length; i++) {
                    var rule = logicRules.rules[i];
                    var fieldValue = getFieldValue(rule.field);

                    if (evaluateCondition(fieldValue, rule.operator, rule.value)) {
                        return rule.result;
                    }
                }

                // If no rules match, return default
                return logicRules.default || '';
            }

            // Evaluate Switch rules
            function evaluateSwitchRules() {
                if (!logicRules.field || !logicRules.cases || !logicRules.cases.length) {
                    return logicRules.default || '';
                }

                var fieldValue = getFieldValue(logicRules.field);

                // Check each case
                for (var i = 0; i < logicRules.cases.length; i++) {
                    var caseItem = logicRules.cases[i];

                    if (fieldValue == caseItem.value) {
                        return caseItem.result;
                    }
                }

                // If no cases match, return default
                return logicRules.default || '';
            }

            // Evaluate Formula rules
            function evaluateFormulaRules() {
                if (!logicRules.formula) {
                    return '';
                }

                var formula = logicRules.formula;

                // Replace field references with values
                formula = formula.replace(/{([^}]+)}/g, function(match, fieldName) {
                    return getFieldValue(fieldName);
                });

                // Evaluate the formula
                try {
                    // Use Function constructor to evaluate the formula safely
                    return new Function('return ' + formula)();
                } catch (e) {
                    console.error('Error evaluating formula:', e);
                    return '';
                }
            }

            // Get field value from the form
            function getFieldValue(fieldSlug) {
                var $formField = $('input[name="' + fieldSlug + '"], select[name="' + fieldSlug + '"], textarea[name="' + fieldSlug + '"]');

                if (!$formField.length) {
                    return '';
                }

                var fieldType = $formField.attr('type');

                if (fieldType === 'checkbox') {
                    return $formField.is(':checked') ? '1' : '0';
                } else if (fieldType === 'radio') {
                    return $('input[name="' + fieldSlug + '"]:checked').val() || '';
                } else {
                    return $formField.val() || '';
                }
            }

            // Evaluate a single condition
            function evaluateCondition(fieldValue, operator, compareValue) {
                switch (operator) {
                    case 'equals':
                        return fieldValue == compareValue;
                    case 'not_equals':
                        return fieldValue != compareValue;
                    case 'contains':
                        return String(fieldValue).indexOf(String(compareValue)) !== -1;
                    case 'not_contains':
                        return String(fieldValue).indexOf(String(compareValue)) === -1;
                    case 'greater_than':
                        return parseFloat(fieldValue) > parseFloat(compareValue);
                    case 'less_than':
                        return parseFloat(fieldValue) < parseFloat(compareValue);
                    case 'is_empty':
                        return !fieldValue;
                    case 'is_not_empty':
                        return !!fieldValue;
                    default:
                        return false;
                }
            }

            // Add rule button
            $field.on('click', '.dab-conditional-logic-add-rule', function() {
                var $rule = $(this).closest('.dab-conditional-logic-rule');
                var $newRule = $rule.clone();

                // Clear values in the new rule
                $newRule.find('input[type="text"]').val('');
                $newRule.find('select').each(function() {
                    $(this).val($(this).find('option:first').val());
                });

                // Insert after current rule
                $rule.after($newRule);
            });

            // Remove rule button
            $field.on('click', '.dab-conditional-logic-remove-rule', function() {
                var $rule = $(this).closest('.dab-conditional-logic-rule');
                var $rules = $field.find('.dab-conditional-logic-rule');

                // Don't remove if it's the only rule
                if ($rules.length > 1) {
                    $rule.remove();
                }
            });

            // Add case button
            $field.on('click', '.dab-conditional-logic-add-case', function() {
                var $case = $(this).closest('.dab-conditional-logic-case');
                var $newCase = $case.clone();

                // Clear values in the new case
                $newCase.find('input[type="text"]').val('');

                // Insert after current case
                $case.after($newCase);
            });

            // Remove case button
            $field.on('click', '.dab-conditional-logic-remove-case', function() {
                var $case = $(this).closest('.dab-conditional-logic-case');
                var $cases = $field.find('.dab-conditional-logic-case');

                // Don't remove if it's the only case
                if ($cases.length > 1) {
                    $case.remove();
                }
            });

            // Field select change - load field options
            $field.on('change', '.dab-conditional-logic-field-select', function() {
                var $select = $(this);
                var fieldSlug = $select.val();

                if (!fieldSlug) return;

                // Get field options via AJAX
                $.ajax({
                    url: dabConditionalLogic.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'dab_get_field_options',
                        nonce: dabConditionalLogic.nonce,
                        field_slug: fieldSlug,
                        table_id: tableId
                    },
                    success: function(response) {
                        if (response.success) {
                            var fieldType = response.data.field_type;
                            var options = response.data.options;

                            // Update value input based on field type
                            var $valueWrapper = $select.closest('.dab-conditional-logic-condition').find('.dab-conditional-logic-value-wrapper');

                            if (fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                                // Create select dropdown for options
                                var $select = $('<select class="dab-conditional-logic-value-input"></select>');
                                $select.append('<option value="">' + dabConditionalLogic.i18n.selectField + '</option>');

                                for (var key in options) {
                                    $select.append('<option value="' + key + '">' + options[key] + '</option>');
                                }

                                $valueWrapper.html($select);
                            } else {
                                // Default text input
                                $valueWrapper.html('<input type="text" class="dab-conditional-logic-value-input" placeholder="' + dabConditionalLogic.i18n.value + '">');
                            }
                        }
                    }
                });
            });

            // Auto-update when form fields change
            if (autoUpdate) {
                $(document).on('change', 'input, select, textarea', function() {
                    // Don't trigger for the conditional logic field itself
                    if ($(this).closest('.dab-conditional-logic-field').length === 0) {
                        evaluateRules();
                    }
                });
            }

            // Populate UI with existing rules
            function populateRulesUI(rules) {
                if (!rules || !rules.type) return;

                // Populate based on logic type
                if (rules.type === 'if_else') {
                    populateIfElseUI(rules);
                } else if (rules.type === 'switch') {
                    populateSwitchUI(rules);
                } else if (rules.type === 'formula') {
                    populateFormulaUI(rules);
                }
            }

            // Populate If-Else UI
            function populateIfElseUI(rules) {
                if (!rules.rules || !rules.rules.length) return;

                // Clear existing rules except the first one
                var $firstRule = $field.find('.dab-conditional-logic-rule').first();
                $field.find('.dab-conditional-logic-rule').not($firstRule).remove();

                // Populate the first rule
                var firstRule = rules.rules[0];
                populateRuleRow($firstRule, firstRule);

                // Add and populate additional rules
                for (var i = 1; i < rules.rules.length; i++) {
                    var $newRule = $firstRule.clone();
                    $firstRule.after($newRule);
                    populateRuleRow($newRule, rules.rules[i]);
                }

                // Set default value
                $field.find('.dab-conditional-logic-else-input').val(rules.default || '');
            }

            // Populate a single rule row
            function populateRuleRow($row, rule) {
                $row.find('.dab-conditional-logic-field-select').val(rule.field);
                $row.find('.dab-conditional-logic-operator').val(rule.operator);

                // Set value input - might need to load field options first
                var $valueInput = $row.find('.dab-conditional-logic-value-input');
                $valueInput.val(rule.value);

                // Set result input
                $row.find('.dab-conditional-logic-result-input').val(rule.result);
            }

            // Populate Switch UI
            function populateSwitchUI(rules) {
                if (!rules.field || !rules.cases || !rules.cases.length) return;

                // Set switch field
                $field.find('.dab-conditional-logic-switch-field-select').val(rules.field);

                // Clear existing cases except the first one
                var $firstCase = $field.find('.dab-conditional-logic-case').first();
                $field.find('.dab-conditional-logic-case').not($firstCase).remove();

                // Populate the first case
                var firstCase = rules.cases[0];
                $firstCase.find('.dab-conditional-logic-case-input').val(firstCase.value);
                $firstCase.find('.dab-conditional-logic-case-result-input').val(firstCase.result);

                // Add and populate additional cases
                for (var i = 1; i < rules.cases.length; i++) {
                    var $newCase = $firstCase.clone();
                    $firstCase.after($newCase);
                    $newCase.find('.dab-conditional-logic-case-input').val(rules.cases[i].value);
                    $newCase.find('.dab-conditional-logic-case-result-input').val(rules.cases[i].result);
                }

                // Set default value
                $field.find('.dab-conditional-logic-default-input').val(rules.default || '');
            }

            // Populate Formula UI
            function populateFormulaUI(rules) {
                if (!rules.formula) return;

                // Set formula
                $field.find('.dab-conditional-logic-formula-input').val(rules.formula);
            }

            // Initialize
            evaluateRules();
        });
    }

    // Function to manually toggle the editor panel
    window.dabToggleConditionalLogicEditor = function(fieldId) {
        console.log('Manual toggle for field:', fieldId);
        var $field = $('#' + fieldId);
        if ($field.length === 0) {
            console.error('Field not found:', fieldId);
            return;
        }

        var $editorPanel = $field.find('.dab-conditional-logic-editor-panel');
        if ($editorPanel.length === 0) {
            console.error('Editor panel not found for field:', fieldId);
            return;
        }

        console.log('Toggling editor panel');
        $editorPanel.slideToggle(200);
    };

    // Initialize on document ready
    $(document).ready(function() {
        console.log('Document ready - initializing conditional logic fields');
        console.log('Found conditional logic fields:', $('.dab-conditional-logic-field').length);
        initConditionalLogicFields();

        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            console.log('Content loaded event - reinitializing conditional logic fields');
            initConditionalLogicFields();
        });

        // Also initialize after a short delay to catch any dynamically loaded fields
        setTimeout(function() {
            console.log('Delayed initialization - checking for conditional logic fields');
            console.log('Found conditional logic fields:', $('.dab-conditional-logic-field').length);
            initConditionalLogicFields();
        }, 1000);

        // Add direct click handler for toggle buttons
        $(document).on('click', '.dab-conditional-logic-toggle', function(e) {
            e.preventDefault();
            var $field = $(this).closest('.dab-conditional-logic-field');
            var fieldId = $field.attr('id');
            console.log('Toggle button clicked directly for field:', fieldId);
            var $editorPanel = $field.find('.dab-conditional-logic-editor-panel');
            $editorPanel.slideToggle(200);
        });

        // Add form submission debugging
        $(document).on('submit', 'form', function() {
            console.log('Form submitted');
            // Log all conditional logic rule inputs
            $('.dab-conditional-logic-rules-input').each(function() {
                console.log('Rule input name:', $(this).attr('name'));
                console.log('Rule input value:', $(this).val());
            });
        });

        // Trigger initialization when the DOM is modified (for dynamically loaded fields)
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes contain conditional logic fields
                    for (var i = 0; i < mutation.addedNodes.length; i++) {
                        var node = mutation.addedNodes[i];
                        if (node.nodeType === 1 && (node.classList && node.classList.contains('dab-conditional-logic-field') || $(node).find('.dab-conditional-logic-field').length > 0)) {
                            console.log('Conditional logic field added to DOM, reinitializing');
                            initConditionalLogicFields();
                            break;
                        }
                    }
                }
            });
        });

        // Start observing the document
        observer.observe(document.body, { childList: true, subtree: true });
    });

})(jQuery);
