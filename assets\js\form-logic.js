/**
 * Form Logic Script
 *
 * Handles conditional logic for the Database App Builder forms
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all forms with the dab-form class
    const forms = document.querySelectorAll('form.dab-form');

    if (!forms.length) return;

    // Check if we have conditional logic rules
    if (typeof dabLogic === 'undefined' || !dabLogic.rules) return;

    const rules = dabLogic.rules;

    // Support both old and new format
    const isLegacyFormat = Array.isArray(rules) && rules.length > 0 && 'if' in rules[0];

    forms.forEach(form => {
        // Store rules on the form element for later use
        form.dabLogicRules = rules;

        // Initial evaluation of all rules
        if (isLegacyFormat) {
            evaluateAllLegacyRules(form, rules);
        } else {
            evaluateAllRules(form, rules);
        }

        // Add event listeners to all form fields
        const formFields = form.querySelectorAll('input, select, textarea');
        formFields.forEach(field => {
            field.addEventListener('change', function() {
                if (isLegacyFormat) {
                    evaluateAllLegacyRules(form, rules);
                } else {
                    evaluateAllRules(form, rules);
                }
            });

            if (field.tagName !== 'SELECT' && field.type !== 'checkbox' && field.type !== 'radio') {
                field.addEventListener('keyup', function() {
                    if (isLegacyFormat) {
                        evaluateAllLegacyRules(form, rules);
                    } else {
                        evaluateAllRules(form, rules);
                    }
                });
            }
        });

        // Add submit event listener to handle conditional email
        form.addEventListener('submit', function(e) {
            // Re-evaluate all rules before submission
            if (isLegacyFormat) {
                evaluateAllLegacyRules(form, rules);
            } else {
                evaluateAllRules(form, rules);
            }

            // Check for conditional email
            const conditionalEmail = form.getAttribute('data-conditional-email');
            if (conditionalEmail) {
                // Add hidden input for conditional email
                let emailInput = form.querySelector('input[name="conditional_email"]');
                if (!emailInput) {
                    emailInput = document.createElement('input');
                    emailInput.type = 'hidden';
                    emailInput.name = 'conditional_email';
                    form.appendChild(emailInput);
                }
                emailInput.value = conditionalEmail;
            }
        });
    });

    /**
     * Get the value of a field
     */
    function getFieldValue(form, fieldName) {
        const field = form.querySelector('[name="' + fieldName + '"]');
        if (!field) return '';

        if (field.type === 'checkbox') {
            return field.checked ? field.value : '';
        } else if (field.type === 'radio') {
            const checkedRadio = form.querySelector('[name="' + fieldName + '"]:checked');
            return checkedRadio ? checkedRadio.value : '';
        } else {
            return field.value;
        }
    }

    /**
     * Legacy format support
     */
    function evaluateAllLegacyRules(form, rules) {
        rules.forEach(rule => applyLegacyRule(form, rule));
    }

    function applyLegacyRule(form, rule) {
        const currentValue = getFieldValue(form, rule.if);
        const match = currentValue === rule.equals;

        // Show or hide fields
        if (rule.then_show) {
            const el = form.querySelector('.dab-form-field[data-field="' + rule.then_show + '"], .dab-field-group[data-field="' + rule.then_show + '"]');
            if (el) el.style.display = match ? '' : 'none';
        }

        if (rule.then_hide) {
            const el = form.querySelector('.dab-form-field[data-field="' + rule.then_hide + '"], .dab-field-group[data-field="' + rule.then_hide + '"]');
            if (el) el.style.display = match ? 'none' : '';
        }

        // Require or un-require fields
        if (rule.then_require) {
            const el = form.querySelector('[name="' + rule.then_require + '"]');
            if (el) {
                if (match) {
                    el.setAttribute('required', 'required');
                } else {
                    el.removeAttribute('required');
                }
            }
        }
    }

    /**
     * New format support
     */
    function evaluateAllRules(form, rules) {
        if (Array.isArray(rules)) {
            rules.forEach(rule => {
                evaluateRule(form, rule);
            });
        }
    }

    function evaluateRule(form, rule) {
        if (!rule.conditions || !Array.isArray(rule.conditions)) return;

        // Special handling for email notification
        if (rule.target === 'notification' && rule.action === 'email') {
            const result = evaluateConditions(form, rule.conditions, rule.logic || 'all');

            // Store the result in a data attribute on the form
            // This will be checked when the form is submitted
            if (result && rule.value) {
                form.setAttribute('data-conditional-email', rule.value);
            } else {
                form.removeAttribute('data-conditional-email');
            }

            return;
        }

        // Regular field targeting
        if (!rule.target) return;

        const targetField = form.querySelector('.dab-form-field[data-field="' + rule.target + '"], .dab-field-group[data-field="' + rule.target + '"]');
        if (!targetField) return;

        const result = evaluateConditions(form, rule.conditions, rule.logic || 'all');

        // Apply the action based on the result
        if (result) {
            if (rule.action === 'show') {
                targetField.style.display = '';
            } else if (rule.action === 'hide') {
                targetField.style.display = 'none';
            } else if (rule.action === 'require') {
                const input = targetField.querySelector('input, select, textarea');
                if (input) input.setAttribute('required', 'required');
            } else if (rule.action === 'unrequire') {
                const input = targetField.querySelector('input, select, textarea');
                if (input) input.removeAttribute('required');
            }
        } else {
            if (rule.action === 'show') {
                targetField.style.display = 'none';
            } else if (rule.action === 'hide') {
                targetField.style.display = '';
            } else if (rule.action === 'require') {
                const input = targetField.querySelector('input, select, textarea');
                if (input) input.removeAttribute('required');
            } else if (rule.action === 'unrequire') {
                const input = targetField.querySelector('input, select, textarea');
                if (input) input.setAttribute('required', 'required');
            }
        }
    }

    function evaluateConditions(form, conditions, logic) {
        if (!Array.isArray(conditions) || conditions.length === 0) return true;

        if (logic === 'all') {
            // All conditions must be true
            return conditions.every(condition => evaluateCondition(form, condition));
        } else {
            // Any condition can be true
            return conditions.some(condition => evaluateCondition(form, condition));
        }
    }

    function evaluateCondition(form, condition) {
        if (!condition.field || !condition.operator) return false;

        const value = getFieldValue(form, condition.field);
        const compareValue = condition.value;

        switch (condition.operator) {
            case 'equals':
                return value == compareValue;
            case 'not_equals':
                return value != compareValue;
            case 'contains':
                return String(value).indexOf(String(compareValue)) !== -1;
            case 'not_contains':
                return String(value).indexOf(String(compareValue)) === -1;
            case 'greater_than':
                return parseFloat(value) > parseFloat(compareValue);
            case 'less_than':
                return parseFloat(value) < parseFloat(compareValue);
            case 'starts_with':
                return String(value).startsWith(String(compareValue));
            case 'ends_with':
                return String(value).endsWith(String(compareValue));
            case 'is_empty':
                return value === '' || value === null || value === undefined;
            case 'is_not_empty':
                return value !== '' && value !== null && value !== undefined;
            default:
                return false;
        }
    }
});
