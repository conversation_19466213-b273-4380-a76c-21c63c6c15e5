<?php
/**
 * Enhanced Currency Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Enhanced_Currency_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the enhanced currency field type
        add_filter('dab_field_types', array($this, 'register_enhanced_currency_field_type'));
        
        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_enhanced_currency_field_options'), 10, 2);
        
        // Register field renderer
        add_action('dab_render_field_enhanced_currency', array($this, 'render_enhanced_currency_field'), 10, 2);
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Format currency value for display
        add_filter('dab_format_field_value', array($this, 'format_currency_value'), 10, 3);
    }
    
    /**
     * Register the enhanced currency field type
     * 
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_enhanced_currency_field_type($field_types) {
        $field_types['enhanced_currency'] = __('Currency (Enhanced)', 'db-app-builder');
        return $field_types;
    }
    
    /**
     * Add enhanced currency field options
     * 
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_enhanced_currency_field_options($options, $field_type) {
        if ($field_type === 'enhanced_currency') {
            $options = array(
                'currency' => array(
                    'label' => __('Default Currency', 'db-app-builder'),
                    'type' => 'select',
                    'options' => $this->get_currency_options(),
                    'description' => __('Select the default currency for this field.', 'db-app-builder'),
                ),
                'symbol_position' => array(
                    'label' => __('Symbol Position', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'before' => __('Before amount ($100)', 'db-app-builder'),
                        'after' => __('After amount (100$)', 'db-app-builder'),
                    ),
                    'description' => __('Position of the currency symbol.', 'db-app-builder'),
                ),
                'decimal_places' => array(
                    'label' => __('Decimal Places', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 0,
                    'max' => 6,
                    'default' => 2,
                    'description' => __('Number of decimal places to display.', 'db-app-builder'),
                ),
                'thousand_separator' => array(
                    'label' => __('Thousand Separator', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'comma' => __('Comma (,)', 'db-app-builder'),
                        'dot' => __('Dot (.)', 'db-app-builder'),
                        'space' => __('Space', 'db-app-builder'),
                        'none' => __('None', 'db-app-builder'),
                    ),
                    'description' => __('Character to use as thousand separator.', 'db-app-builder'),
                ),
                'decimal_separator' => array(
                    'label' => __('Decimal Separator', 'db-app-builder'),
                    'type' => 'select',
                    'options' => array(
                        'dot' => __('Dot (.)', 'db-app-builder'),
                        'comma' => __('Comma (,)', 'db-app-builder'),
                    ),
                    'description' => __('Character to use as decimal separator.', 'db-app-builder'),
                ),
                'allow_currency_selection' => array(
                    'label' => __('Allow Currency Selection', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Allow users to select a different currency when entering values.', 'db-app-builder'),
                ),
            );
        }
        
        return $options;
    }
    
    /**
     * Render the enhanced currency field
     * 
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_enhanced_currency_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }
        
        // Get field settings with defaults
        $currency = isset($options['currency']) ? $options['currency'] : 'USD';
        $symbol_position = isset($options['symbol_position']) ? $options['symbol_position'] : 'before';
        $decimal_places = isset($options['decimal_places']) ? intval($options['decimal_places']) : 2;
        $allow_currency_selection = isset($options['allow_currency_selection']) ? (bool)$options['allow_currency_selection'] : false;
        
        // Parse the value if it's stored as JSON
        $amount = '';
        $selected_currency = $currency;
        
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $amount = isset($parsed_value['amount']) ? $parsed_value['amount'] : '';
                    $selected_currency = isset($parsed_value['currency']) ? $parsed_value['currency'] : $currency;
                } else {
                    $amount = $value;
                }
            } else {
                $amount = $value;
            }
        }
        
        // Generate a unique ID for the field
        $field_id = 'dab-currency-' . $field->id . '-' . uniqid();
        
        // Output the field HTML
        ?>
        <div class="dab-enhanced-currency-field" id="<?php echo esc_attr($field_id); ?>" data-field-slug="<?php echo esc_attr($field->field_slug); ?>">
            <div class="dab-currency-input-wrapper">
                <?php if ($symbol_position === 'before'): ?>
                <span class="dab-currency-symbol"><?php echo esc_html($this->get_currency_symbol($selected_currency)); ?></span>
                <?php endif; ?>
                
                <input type="number" 
                       name="<?php echo esc_attr($field->field_slug . '_amount'); ?>" 
                       id="<?php echo esc_attr($field_id . '-amount'); ?>" 
                       class="dab-currency-amount" 
                       value="<?php echo esc_attr($amount); ?>" 
                       step="<?php echo esc_attr(pow(0.1, $decimal_places)); ?>" 
                       min="0" 
                       <?php echo $field->required ? 'required' : ''; ?>>
                
                <?php if ($symbol_position === 'after'): ?>
                <span class="dab-currency-symbol"><?php echo esc_html($this->get_currency_symbol($selected_currency)); ?></span>
                <?php endif; ?>
                
                <?php if ($allow_currency_selection): ?>
                <select name="<?php echo esc_attr($field->field_slug . '_currency'); ?>" 
                        id="<?php echo esc_attr($field_id . '-currency'); ?>" 
                        class="dab-currency-selector">
                    <?php foreach ($this->get_currency_options() as $code => $label): ?>
                        <option value="<?php echo esc_attr($code); ?>" <?php selected($selected_currency, $code); ?>>
                            <?php echo esc_html($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <?php else: ?>
                <input type="hidden" name="<?php echo esc_attr($field->field_slug . '_currency'); ?>" value="<?php echo esc_attr($selected_currency); ?>">
                <?php endif; ?>
            </div>
            
            <!-- Hidden field to store the combined JSON value -->
            <input type="hidden" name="<?php echo esc_attr($field->field_slug); ?>" id="<?php echo esc_attr($field_id . '-value'); ?>" value="<?php echo esc_attr($value); ?>">
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var $field = $('#<?php echo esc_js($field_id); ?>');
            var $amount = $('#<?php echo esc_js($field_id . '-amount'); ?>');
            var $currency = $('#<?php echo esc_js($field_id . '-currency'); ?>');
            var $value = $('#<?php echo esc_js($field_id . '-value'); ?>');
            var $symbol = $field.find('.dab-currency-symbol');
            
            // Update the hidden value field and symbol when amount or currency changes
            function updateValue() {
                var amount = $amount.val();
                var currency = $currency.val() || '<?php echo esc_js($selected_currency); ?>';
                var symbol = getCurrencySymbol(currency);
                
                // Update the hidden value field with JSON
                var jsonValue = JSON.stringify({
                    amount: amount,
                    currency: currency
                });
                $value.val(jsonValue);
                
                // Update the currency symbol
                $symbol.text(symbol);
            }
            
            // Get currency symbol
            function getCurrencySymbol(currencyCode) {
                var symbols = <?php echo json_encode($this->get_currency_symbols()); ?>;
                return symbols[currencyCode] || currencyCode;
            }
            
            // Bind events
            $amount.on('input change', updateValue);
            $currency.on('change', updateValue);
            
            // Initialize
            updateValue();
        });
        </script>
        <?php
    }
    
    /**
     * Format currency value for display
     * 
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_currency_value($value, $field, $context) {
        if ($field->field_type !== 'enhanced_currency') {
            return $value;
        }
        
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }
        
        // Get formatting options
        $default_currency = isset($options['currency']) ? $options['currency'] : 'USD';
        $symbol_position = isset($options['symbol_position']) ? $options['symbol_position'] : 'before';
        $decimal_places = isset($options['decimal_places']) ? intval($options['decimal_places']) : 2;
        $thousand_separator = isset($options['thousand_separator']) ? $options['thousand_separator'] : 'comma';
        $decimal_separator = isset($options['decimal_separator']) ? $options['decimal_separator'] : 'dot';
        
        // Parse the value
        $amount = '';
        $currency = $default_currency;
        
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '{') === 0) {
                $parsed_value = json_decode($value, true);
                if (is_array($parsed_value)) {
                    $amount = isset($parsed_value['amount']) ? $parsed_value['amount'] : '';
                    $currency = isset($parsed_value['currency']) ? $parsed_value['currency'] : $default_currency;
                } else {
                    $amount = $value;
                }
            } else {
                $amount = $value;
            }
        }
        
        if ($amount === '') {
            return '';
        }
        
        // Format the amount
        $formatted_amount = $this->format_number(
            $amount,
            $decimal_places,
            $this->get_separator($decimal_separator),
            $this->get_separator($thousand_separator)
        );
        
        // Get currency symbol
        $symbol = $this->get_currency_symbol($currency);
        
        // Format with symbol
        if ($symbol_position === 'before') {
            return $symbol . $formatted_amount;
        } else {
            return $formatted_amount . $symbol;
        }
    }
    
    /**
     * Format a number with custom separators
     * 
     * @param float $number Number to format
     * @param int $decimals Number of decimal places
     * @param string $decimal_separator Decimal separator
     * @param string $thousand_separator Thousand separator
     * @return string Formatted number
     */
    private function format_number($number, $decimals, $decimal_separator, $thousand_separator) {
        return number_format((float)$number, $decimals, $decimal_separator, $thousand_separator);
    }
    
    /**
     * Get separator character
     * 
     * @param string $separator Separator type
     * @return string Separator character
     */
    private function get_separator($separator) {
        switch ($separator) {
            case 'comma':
                return ',';
            case 'dot':
                return '.';
            case 'space':
                return ' ';
            case 'none':
                return '';
            default:
                return $separator;
        }
    }
    
    /**
     * Get currency options
     * 
     * @return array Currency options
     */
    public function get_currency_options() {
        return array(
            'USD' => __('US Dollar ($)', 'db-app-builder'),
            'EUR' => __('Euro (€)', 'db-app-builder'),
            'GBP' => __('British Pound (£)', 'db-app-builder'),
            'JPY' => __('Japanese Yen (¥)', 'db-app-builder'),
            'CNY' => __('Chinese Yuan (¥)', 'db-app-builder'),
            'INR' => __('Indian Rupee (₹)', 'db-app-builder'),
            'CAD' => __('Canadian Dollar (C$)', 'db-app-builder'),
            'AUD' => __('Australian Dollar (A$)', 'db-app-builder'),
            'NGN' => __('Nigerian Naira (₦)', 'db-app-builder'),
            'ZAR' => __('South African Rand (R)', 'db-app-builder'),
            'BRL' => __('Brazilian Real (R$)', 'db-app-builder'),
            'MXN' => __('Mexican Peso (Mex$)', 'db-app-builder'),
            'SGD' => __('Singapore Dollar (S$)', 'db-app-builder'),
            'CHF' => __('Swiss Franc (CHF)', 'db-app-builder'),
            'SEK' => __('Swedish Krona (kr)', 'db-app-builder'),
            'NZD' => __('New Zealand Dollar (NZ$)', 'db-app-builder'),
            'KRW' => __('South Korean Won (₩)', 'db-app-builder'),
            'AED' => __('UAE Dirham (د.إ)', 'db-app-builder'),
            'SAR' => __('Saudi Riyal (﷼)', 'db-app-builder'),
            'RUB' => __('Russian Ruble (₽)', 'db-app-builder'),
        );
    }
    
    /**
     * Get currency symbols
     * 
     * @return array Currency symbols
     */
    public function get_currency_symbols() {
        return array(
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CNY' => '¥',
            'INR' => '₹',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'NGN' => '₦',
            'ZAR' => 'R',
            'BRL' => 'R$',
            'MXN' => 'Mex$',
            'SGD' => 'S$',
            'CHF' => 'CHF',
            'SEK' => 'kr',
            'NZD' => 'NZ$',
            'KRW' => '₩',
            'AED' => 'د.إ',
            'SAR' => '﷼',
            'RUB' => '₽',
        );
    }
    
    /**
     * Get currency symbol
     * 
     * @param string $currency Currency code
     * @return string Currency symbol
     */
    public function get_currency_symbol($currency) {
        $symbols = $this->get_currency_symbols();
        return isset($symbols[$currency]) ? $symbols[$currency] : $currency;
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'dab-enhanced-currency-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/enhanced-currency-field.css',
            array(),
            DAB_VERSION
        );
        
        wp_enqueue_script(
            'dab-enhanced-currency-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-currency-field.js',
            array('jquery'),
            DAB_VERSION,
            true
        );
    }
}

// Initialize the class
new DAB_Enhanced_Currency_Field();
