/**
 * Enhanced Inline Table Field JavaScript
 *
 * Provides a more user-friendly inline table experience similar to invoice line items
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initEnhancedInlineTables();
    });

    /**
     * Initialize all enhanced inline tables on the page
     */
    function initEnhancedInlineTables() {
        // Find all enhanced inline table containers
        $('.dab-enhanced-inline-table').each(function() {
            const container = $(this);
            const parentForm = container.closest('form');
            const childTable = container.data('child-table');
            const foreignKey = container.data('foreign-key');
            const fieldName = container.data('field-name');
            const allowAdd = container.data('allow-add') !== '0';
            const allowEdit = container.data('allow-edit') !== '0';
            const allowDelete = container.data('allow-delete') !== '0';

            // Initialize the table
            initTable(container, parentForm, childTable, foreignKey, fieldName, allowAdd, allowEdit, allowDelete);
        });
    }

    /**
     * Initialize a single enhanced inline table
     */
    function initTable(container, parentForm, childTable, foreignKey, fieldName, allowAdd, allowEdit, allowDelete) {
        // Get the table template
        const tableTemplate = container.find('.dab-inline-table-template').html();
        const rowTemplate = container.find('.dab-inline-row-template').html();

        // Create the table if it doesn't exist
        if (container.find('.dab-enhanced-inline-table-content').length === 0) {
            container.append('<div class="dab-enhanced-inline-table-content">' + tableTemplate + '</div>');
        }

        const tableContent = container.find('.dab-enhanced-inline-table-content');
        const table = tableContent.find('table');

        // Add button click handler
        if (allowAdd) {
            container.find('.dab-inline-add-row').on('click', function() {
                addNewRow(table, rowTemplate);
            });
        } else {
            container.find('.dab-inline-add-row').hide();
        }

        // Delete button click handler
        if (allowDelete) {
            table.on('click', '.dab-inline-delete-row', function() {
                $(this).closest('tr').remove();
                updateRowNumbers(table);
                updateFormData(container, table, fieldName);
            });
        }

        // Handle changes to input fields
        table.on('change', 'input, select, textarea', function() {
            updateFormData(container, table, fieldName);
        });

        // Initialize existing rows if any
        const existingData = container.data('existing-data');
        if (existingData && existingData.length > 0) {
            existingData.forEach(function(rowData) {
                addNewRow(table, rowTemplate, rowData);
            });
        } else {
            // Add an empty row if no existing data and add is allowed
            if (allowAdd) {
                addNewRow(table, rowTemplate);
            }
        }

        // Initialize all rows after they're added
        table.find('tbody tr').each(function() {
            initializeRowFields($(this));
        });

        // Update form data on submit
        parentForm.on('submit', function() {
            updateFormData(container, table, fieldName);
        });
    }

    /**
     * Add a new row to the table
     */
    function addNewRow(table, rowTemplate, rowData = {}) {
        const tbody = table.find('tbody');
        const newRow = $(rowTemplate);

        // Set row number
        const rowNumber = tbody.find('tr').length + 1;
        newRow.find('.dab-inline-row-number').text(rowNumber);

        // Store record ID if it exists (for existing records)
        if (rowData.id) {
            newRow.attr('data-record-id', rowData.id);
        }

        // Add the row to the table
        tbody.append(newRow);

        // Set field values if provided
        if (rowData && Object.keys(rowData).length > 0) {
            Object.keys(rowData).forEach(function(key) {
                const input = newRow.find('[name$="[' + key + ']"]');
                if (input.length) {
                    // Handle select elements differently
                    if (input.is('select')) {
                        // Find the option with the matching value and select it
                        const value = rowData[key];
                        input.find('option').each(function() {
                            if ($(this).val() === value) {
                                $(this).prop('selected', true);
                            }
                        });
                    } else {
                        // For other input types
                        input.val(rowData[key]);
                    }
                }
            });
        }

        // Update row numbers
        updateRowNumbers(table);

        // Initialize any special field types
        initializeRowFields(newRow);

        // Focus the first input in the new row
        newRow.find('input, select, textarea').first().focus();
    }

    /**
     * Initialize special field types in a row
     */
    function initializeRowFields(row) {
        // Initialize select2 dropdowns if available
        if ($.fn.select2 && row.find('select.dab-select2').length > 0) {
            row.find('select.dab-select2').select2({
                width: '100%',
                dropdownAutoWidth: true,
                dropdownParent: row.closest('.dab-enhanced-inline-table')
            });
        }

        // Initialize datepickers if available
        if ($.fn.datepicker && row.find('.dab-datepicker').length > 0) {
            row.find('.dab-datepicker').datepicker({
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true
            });
        }
    }

    /**
     * Update row numbers after adding/removing rows
     */
    function updateRowNumbers(table) {
        table.find('tbody tr').each(function(index) {
            $(this).find('.dab-inline-row-number').text(index + 1);

            // Update input names to match row index
            $(this).find('input, select, textarea').each(function() {
                const name = $(this).attr('name');
                if (name) {
                    const newName = name.replace(/\[\d+\]/, '[' + index + ']');
                    $(this).attr('name', newName);

                    // If this is a select2 element, we need to destroy and reinitialize it
                    if ($(this).hasClass('select2-hidden-accessible')) {
                        const value = $(this).val();
                        $(this).select2('destroy');
                        $(this).val(value);
                        $(this).select2({
                            width: '100%',
                            dropdownAutoWidth: true,
                            dropdownParent: $(this).closest('.dab-enhanced-inline-table')
                        });
                    }
                }
            });
        });
    }

    /**
     * Update the hidden form data field with the table data
     */
    function updateFormData(container, table, fieldName) {
        const rows = [];

        table.find('tbody tr').each(function() {
            const row = {};

            // Include record ID if this is an existing record
            const recordId = $(this).attr('data-record-id');
            if (recordId) {
                row.id = recordId;
            }

            $(this).find('input, select, textarea').each(function() {
                const name = $(this).attr('name');
                if (name) {
                    // Extract the field name from the input name (format: fieldName[rowIndex][fieldName])
                    const matches = name.match(/\[\d+\]\[([^\]]+)\]/);
                    if (matches && matches[1]) {
                        const fieldKey = matches[1];
                        row[fieldKey] = $(this).val();
                    }
                }
            });

            if (Object.keys(row).length > 0) {
                rows.push(row);
            }
        });

        // Update the hidden input with the JSON data
        let hiddenInput = container.find('input[name="' + fieldName + '"]');
        if (hiddenInput.length === 0) {
            hiddenInput = $('<input type="hidden" name="' + fieldName + '">');
            container.append(hiddenInput);
        }

        hiddenInput.val(JSON.stringify(rows));
    }

})(jQuery);
