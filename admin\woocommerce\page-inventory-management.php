<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Get inventory statistics
global $wpdb;

// Low stock products
$low_stock_count = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->posts} p 
    INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id 
    INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id 
    WHERE p.post_type = 'product' 
    AND p.post_status = 'publish' 
    AND pm1.meta_key = '_stock' 
    AND pm2.meta_key = '_low_stock_amount' 
    AND CAST(pm1.meta_value AS SIGNED) <= CAST(pm2.meta_value AS SIGNED)
    AND pm2.meta_value > 0
");

// Out of stock products
$out_of_stock_count = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->posts} p 
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
    WHERE p.post_type = 'product' 
    AND p.post_status = 'publish' 
    AND pm.meta_key = '_stock_status' 
    AND pm.meta_value = 'outofstock'
");

// Total products with stock management
$managed_stock_count = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->posts} p 
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
    WHERE p.post_type = 'product' 
    AND p.post_status = 'publish' 
    AND pm.meta_key = '_manage_stock' 
    AND pm.meta_value = 'yes'
");

// Recent stock movements
$stock_movements = $wpdb->get_results("
    SELECT st.*, p.post_title as product_name 
    FROM {$wpdb->prefix}dab_wc_inventory_tracking st 
    INNER JOIN {$wpdb->posts} p ON st.product_id = p.ID 
    ORDER BY st.created_at DESC 
    LIMIT 20
");

// Stock alerts
$stock_alerts = $wpdb->get_results("
    SELECT sa.*, p.post_title as product_name 
    FROM {$wpdb->prefix}dab_wc_stock_alerts sa 
    INNER JOIN {$wpdb->posts} p ON sa.product_id = p.ID 
    WHERE sa.resolved = 0 
    ORDER BY sa.created_at DESC 
    LIMIT 10
");

// Suppliers
$suppliers = $wpdb->get_results("
    SELECT * FROM {$wpdb->prefix}dab_wc_suppliers 
    WHERE is_active = 1 
    ORDER BY supplier_name ASC
");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Inventory Management', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Advanced inventory management with real-time tracking, low stock alerts, supplier management, and inventory forecasting.', 'db-app-builder'); ?></p>
    </div>

    <!-- Inventory Statistics -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($managed_stock_count); ?></div>
            <div class="dab-stat-label"><?php _e('Products with Stock Management', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card dab-stat-warning">
            <div class="dab-stat-number"><?php echo number_format($low_stock_count); ?></div>
            <div class="dab-stat-label"><?php _e('Low Stock Products', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card dab-stat-danger">
            <div class="dab-stat-number"><?php echo number_format($out_of_stock_count); ?></div>
            <div class="dab-stat-label"><?php _e('Out of Stock Products', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo count($suppliers); ?></div>
            <div class="dab-stat-label"><?php _e('Active Suppliers', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Stock Alerts -->
            <?php if (!empty($stock_alerts)): ?>
            <div class="dab-card dab-alert-card">
                <h2><?php _e('Stock Alerts', 'db-app-builder'); ?></h2>
                
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Product', 'db-app-builder'); ?></th>
                            <th><?php _e('Alert Type', 'db-app-builder'); ?></th>
                            <th><?php _e('Current Stock', 'db-app-builder'); ?></th>
                            <th><?php _e('Threshold', 'db-app-builder'); ?></th>
                            <th><?php _e('Date', 'db-app-builder'); ?></th>
                            <th><?php _e('Actions', 'db-app-builder'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stock_alerts as $alert): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($alert->product_name); ?></strong>
                                    <br><small>ID: <?php echo $alert->product_id; ?></small>
                                </td>
                                <td>
                                    <span class="dab-alert-type dab-alert-<?php echo esc_attr($alert->alert_type); ?>">
                                        <?php echo esc_html(ucfirst(str_replace('_', ' ', $alert->alert_type))); ?>
                                    </span>
                                </td>
                                <td><?php echo number_format($alert->current_stock); ?></td>
                                <td><?php echo number_format($alert->threshold_level); ?></td>
                                <td><?php echo date_i18n(get_option('date_format'), strtotime($alert->created_at)); ?></td>
                                <td>
                                    <a href="<?php echo admin_url('post.php?post=' . $alert->product_id . '&action=edit'); ?>" class="button button-small"><?php _e('Edit Product', 'db-app-builder'); ?></a>
                                    <button class="button button-small" onclick="resolveAlert(<?php echo $alert->id; ?>)"><?php _e('Resolve', 'db-app-builder'); ?></button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- Recent Stock Movements -->
            <div class="dab-card">
                <h2><?php _e('Recent Stock Movements', 'db-app-builder'); ?></h2>
                
                <?php if (empty($stock_movements)): ?>
                    <p><?php _e('No stock movements recorded yet.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Product', 'db-app-builder'); ?></th>
                                <th><?php _e('Change', 'db-app-builder'); ?></th>
                                <th><?php _e('Before', 'db-app-builder'); ?></th>
                                <th><?php _e('After', 'db-app-builder'); ?></th>
                                <th><?php _e('Reason', 'db-app-builder'); ?></th>
                                <th><?php _e('Date', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stock_movements as $movement): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($movement->product_name); ?></strong>
                                        <?php if ($movement->variation_id): ?>
                                            <br><small><?php _e('Variation ID:', 'db-app-builder'); ?> <?php echo $movement->variation_id; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="dab-stock-change <?php echo $movement->stock_change >= 0 ? 'positive' : 'negative'; ?>">
                                            <?php echo $movement->stock_change >= 0 ? '+' : ''; ?><?php echo $movement->stock_change; ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($movement->stock_level_before); ?></td>
                                    <td><?php echo number_format($movement->stock_level_after); ?></td>
                                    <td><?php echo esc_html(ucfirst(str_replace('_', ' ', $movement->change_reason))); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($movement->created_at)); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

            <!-- Suppliers Management -->
            <div class="dab-card">
                <h2><?php _e('Suppliers', 'db-app-builder'); ?></h2>
                
                <?php if (empty($suppliers)): ?>
                    <p><?php _e('No suppliers configured yet.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Supplier Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Contact Person', 'db-app-builder'); ?></th>
                                <th><?php _e('Email', 'db-app-builder'); ?></th>
                                <th><?php _e('Lead Time', 'db-app-builder'); ?></th>
                                <th><?php _e('Min Order Value', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($suppliers as $supplier): ?>
                                <tr>
                                    <td><strong><?php echo esc_html($supplier->supplier_name); ?></strong></td>
                                    <td><?php echo esc_html($supplier->contact_person); ?></td>
                                    <td><?php echo esc_html($supplier->supplier_email); ?></td>
                                    <td><?php echo number_format($supplier->lead_time_days); ?> <?php _e('days', 'db-app-builder'); ?></td>
                                    <td><?php echo wc_price($supplier->minimum_order_value); ?></td>
                                    <td>
                                        <button class="button button-small" onclick="editSupplier(<?php echo $supplier->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                        <button class="button button-small" onclick="createPurchaseOrder(<?php echo $supplier->id; ?>)"><?php _e('Create PO', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                
                <p>
                    <button class="button button-primary" onclick="showAddSupplierForm()"><?php _e('Add New Supplier', 'db-app-builder'); ?></button>
                </p>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Inventory Actions -->
            <div class="dab-card">
                <h3><?php _e('Inventory Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="#" onclick="bulkUpdateStock()"><?php _e('Bulk Update Stock', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="exportInventoryReport()"><?php _e('Export Inventory Report', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="importStockLevels()"><?php _e('Import Stock Levels', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="generateReorderReport()"><?php _e('Generate Reorder Report', 'db-app-builder'); ?></a></li>
                </ul>
            </div>

            <!-- Inventory Settings -->
            <div class="dab-card">
                <h3><?php _e('Inventory Settings', 'db-app-builder'); ?></h3>
                <form method="post" action="">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Low Stock Threshold', 'db-app-builder'); ?></th>
                            <td>
                                <input type="number" name="default_low_stock_threshold" value="5" class="small-text">
                                <p class="description"><?php _e('Default threshold for low stock alerts', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Alert Frequency', 'db-app-builder'); ?></th>
                            <td>
                                <select name="alert_frequency">
                                    <option value="immediate"><?php _e('Immediate', 'db-app-builder'); ?></option>
                                    <option value="daily"><?php _e('Daily', 'db-app-builder'); ?></option>
                                    <option value="weekly"><?php _e('Weekly', 'db-app-builder'); ?></option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Auto Reorder', 'db-app-builder'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_reorder_enabled" value="1">
                                    <?php _e('Enable automatic reorder suggestions', 'db-app-builder'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="save_inventory_settings" class="button-primary" value="<?php _e('Save Settings', 'db-app-builder'); ?>">
                    </p>
                </form>
            </div>

            <!-- Quick Stats -->
            <div class="dab-card">
                <h3><?php _e('Quick Stats', 'db-app-builder'); ?></h3>
                <div class="dab-quick-stats">
                    <div class="dab-quick-stat">
                        <div class="dab-quick-stat-label"><?php _e('Stock Value', 'db-app-builder'); ?></div>
                        <div class="dab-quick-stat-value"><?php echo wc_price(0); ?></div>
                    </div>
                    
                    <div class="dab-quick-stat">
                        <div class="dab-quick-stat-label"><?php _e('Avg Lead Time', 'db-app-builder'); ?></div>
                        <div class="dab-quick-stat-value">7 <?php _e('days', 'db-app-builder'); ?></div>
                    </div>
                    
                    <div class="dab-quick-stat">
                        <div class="dab-quick-stat-label"><?php _e('Pending POs', 'db-app-builder'); ?></div>
                        <div class="dab-quick-stat-value">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resolveAlert(alertId) {
    if (confirm('<?php _e('Mark this alert as resolved?', 'db-app-builder'); ?>')) {
        // AJAX call to resolve alert
        jQuery.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'dab_resolve_stock_alert',
                alert_id: alertId,
                nonce: '<?php echo wp_create_nonce('dab_inventory_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('<?php _e('Error resolving alert', 'db-app-builder'); ?>');
                }
            }
        });
    }
}

function editSupplier(supplierId) {
    // TODO: Implement supplier editing modal
    alert('<?php _e('Supplier editing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function createPurchaseOrder(supplierId) {
    // TODO: Implement purchase order creation
    alert('<?php _e('Purchase order creation will be implemented in the next update.', 'db-app-builder'); ?>');
}

function showAddSupplierForm() {
    // TODO: Implement add supplier modal
    alert('<?php _e('Add supplier form will be implemented in the next update.', 'db-app-builder'); ?>');
}

function bulkUpdateStock() {
    // TODO: Implement bulk stock update
    alert('<?php _e('Bulk stock update will be implemented in the next update.', 'db-app-builder'); ?>');
}

function exportInventoryReport() {
    // TODO: Implement inventory report export
    alert('<?php _e('Inventory report export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function importStockLevels() {
    // TODO: Implement stock import
    alert('<?php _e('Stock import will be implemented in the next update.', 'db-app-builder'); ?>');
}

function generateReorderReport() {
    // TODO: Implement reorder report
    alert('<?php _e('Reorder report will be implemented in the next update.', 'db-app-builder'); ?>');
}
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-card.dab-stat-warning {
    border-left: 4px solid #f0b849;
}

.dab-stat-card.dab-stat-danger {
    border-left: 4px solid #d63638;
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-alert-card {
    border-left: 4px solid #f0b849;
}

.dab-alert-type {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.dab-alert-type.dab-alert-low-stock {
    background: #fff3cd;
    color: #856404;
}

.dab-alert-type.dab-alert-out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

.dab-stock-change {
    font-weight: bold;
}

.dab-stock-change.positive {
    color: #00a32a;
}

.dab-stock-change.negative {
    color: #d63638;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-quick-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-quick-stat {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.dab-quick-stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.dab-quick-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #2271b1;
}
</style>
