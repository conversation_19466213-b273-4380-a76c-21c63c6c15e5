<?php
/**
 * User Dashboard Shortcode
 *
 * Provides frontend user dashboard functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register user dashboard shortcode
 */
function dab_user_dashboard_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_stats' => 'true',
        'show_recent_activity' => 'true',
        'show_tables' => 'true'
    ), $atts, 'dab_user_dashboard');

    // Check if user is logged in
    $current_user = DAB_Frontend_User_Manager::get_current_user();
    if (!$current_user) {
        return '<div class="dab-auth-required">
                    <p>' . __('You must be logged in to view this page.', 'db-app-builder') . '</p>
                    <p><a href="' . home_url('/login/') . '" class="dab-btn dab-btn-primary">' . __('Login', 'db-app-builder') . '</a></p>
                </div>';
    }

    // Enqueue required scripts and styles
    wp_enqueue_style('dab-user-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/user-dashboard.css', array(), DAB_VERSION);
    wp_enqueue_script('dab-user-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/user-dashboard.js', array('jquery'), DAB_VERSION, true);

    // Localize script
    wp_localize_script('dab-user-dashboard', 'dab_dashboard', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_frontend_nonce'),
        'current_user' => array(
            'id' => $current_user->id ?? 0,
            'username' => $current_user->username ?? '',
            'email' => $current_user->email ?? '',
            'first_name' => $current_user->first_name ?? '',
            'last_name' => $current_user->last_name ?? ''
        ),
        'messages' => array(
            'loading' => __('Loading...', 'db-app-builder'),
            'error' => __('An error occurred. Please try again.', 'db-app-builder'),
            'no_data' => __('No data available.', 'db-app-builder'),
            'confirm_delete' => __('Are you sure you want to delete this record?', 'db-app-builder')
        )
    ));

    // Get user's accessible tables
    $accessible_tables = DAB_User_Dashboard_Manager::get_user_accessible_tables($current_user->id);

    // Start output buffer
    ob_start();
    ?>
    <div class="dab-user-dashboard">
        <!-- Dashboard Header -->
        <div class="dab-dashboard-header">
            <div class="dab-dashboard-welcome">
                <h1><?php printf(__('Welcome, %s!', 'db-app-builder'), esc_html($current_user->first_name ?: $current_user->username)); ?></h1>
                <p class="dab-dashboard-subtitle"><?php _e('Manage your data and view your statistics below.', 'db-app-builder'); ?></p>
            </div>
            <div class="dab-dashboard-actions">
                <a href="<?php echo home_url('/user-profile/'); ?>" class="dab-btn dab-btn-secondary">
                    <span class="dashicons dashicons-admin-users"></span>
                    <?php _e('Profile', 'db-app-builder'); ?>
                </a>
                <button type="button" id="dab-logout-btn" class="dab-btn dab-btn-outline">
                    <span class="dashicons dashicons-exit"></span>
                    <?php _e('Logout', 'db-app-builder'); ?>
                </button>
            </div>
        </div>

        <?php if ($atts['show_stats'] === 'true'): ?>
        <!-- Statistics Cards -->
        <div class="dab-dashboard-stats" id="dab-dashboard-stats">
            <div class="dab-stats-loading">
                <span class="dab-spinner"></span>
                <?php _e('Loading statistics...', 'db-app-builder'); ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($atts['show_tables'] === 'true' && !empty($accessible_tables)): ?>
        <!-- Data Tables Section -->
        <div class="dab-dashboard-section">
            <h2 class="dab-section-title"><?php _e('Your Data Tables', 'db-app-builder'); ?></h2>

            <div class="dab-tables-grid">
                <?php foreach ($accessible_tables as $table): ?>
                <div class="dab-table-card" data-table-id="<?php echo $table->id; ?>">
                    <div class="dab-table-card-header">
                        <h3 class="dab-table-title"><?php echo esc_html($table->table_label); ?></h3>
                        <div class="dab-table-actions">
                            <button type="button" class="dab-btn dab-btn-sm dab-btn-primary dab-view-table-data"
                                    data-table-id="<?php echo $table->id; ?>"
                                    data-table-name="<?php echo esc_attr($table->table_label); ?>">
                                <?php _e('View Data', 'db-app-builder'); ?>
                            </button>
                        </div>
                    </div>
                    <div class="dab-table-card-body">
                        <?php if (!empty($table->description)): ?>
                        <p class="dab-table-description"><?php echo esc_html($table->description); ?></p>
                        <?php endif; ?>
                        <div class="dab-table-stats">
                            <span class="dab-table-record-count" data-table-id="<?php echo $table->id; ?>">
                                <span class="dab-spinner-sm"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($atts['show_recent_activity'] === 'true'): ?>
        <!-- Recent Activity Section -->
        <div class="dab-dashboard-section">
            <h2 class="dab-section-title"><?php _e('Recent Activity', 'db-app-builder'); ?></h2>
            <div class="dab-recent-activity" id="dab-recent-activity">
                <div class="dab-activity-loading">
                    <span class="dab-spinner"></span>
                    <?php _e('Loading recent activity...', 'db-app-builder'); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Data Modal -->
        <div id="dab-data-modal" class="dab-modal" style="display: none;">
            <div class="dab-modal-content dab-modal-large">
                <div class="dab-modal-header">
                    <h3 id="dab-modal-title"><?php _e('Table Data', 'db-app-builder'); ?></h3>
                    <button type="button" class="dab-modal-close" id="dab-close-data-modal">&times;</button>
                </div>
                <div class="dab-modal-body">
                    <div class="dab-data-controls">
                        <div class="dab-data-search">
                            <input type="text" id="dab-data-search" placeholder="<?php _e('Search...', 'db-app-builder'); ?>" class="dab-form-control">
                        </div>
                        <div class="dab-data-actions">
                            <button type="button" id="dab-add-record" class="dab-btn dab-btn-primary">
                                <span class="dashicons dashicons-plus"></span>
                                <?php _e('Add Record', 'db-app-builder'); ?>
                            </button>
                        </div>
                    </div>
                    <div id="dab-data-content">
                        <div class="dab-data-loading">
                            <span class="dab-spinner"></span>
                            <?php _e('Loading data...', 'db-app-builder'); ?>
                        </div>
                    </div>
                    <div class="dab-data-pagination" id="dab-data-pagination" style="display: none;">
                        <!-- Pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    .dab-user-dashboard {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .dab-dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e1e5e9;
    }

    .dab-dashboard-welcome h1 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 28px;
        font-weight: 600;
    }

    .dab-dashboard-subtitle {
        margin: 0;
        color: #666;
        font-size: 16px;
    }

    .dab-dashboard-actions {
        display: flex;
        gap: 10px;
    }

    .dab-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        border: 1px solid transparent;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s;
        background: none;
    }

    .dab-btn-primary {
        background-color: #007cba;
        color: white;
        border-color: #007cba;
    }

    .dab-btn-primary:hover {
        background-color: #005a87;
        border-color: #005a87;
    }

    .dab-btn-secondary {
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .dab-btn-secondary:hover {
        background-color: #545b62;
        border-color: #545b62;
    }

    .dab-btn-outline {
        color: #6c757d;
        border-color: #6c757d;
    }

    .dab-btn-outline:hover {
        background-color: #6c757d;
        color: white;
    }

    .dab-btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }

    .dab-dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .dab-stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
    }

    .dab-stat-number {
        font-size: 32px;
        font-weight: 700;
        color: #007cba;
        margin-bottom: 5px;
    }

    .dab-stat-label {
        color: #666;
        font-size: 14px;
        margin: 0;
    }

    .dab-dashboard-section {
        margin-bottom: 30px;
    }

    .dab-section-title {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 20px;
        font-weight: 600;
    }

    .dab-tables-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .dab-table-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
        overflow: hidden;
    }

    .dab-table-card-header {
        padding: 15px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e1e5e9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dab-table-title {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
    }

    .dab-table-card-body {
        padding: 20px;
    }

    .dab-table-description {
        margin: 0 0 15px 0;
        color: #666;
        font-size: 14px;
    }

    .dab-spinner, .dab-spinner-sm {
        display: inline-block;
        border: 2px solid #f3f3f3;
        border-radius: 50%;
        border-top-color: #007cba;
        animation: spin 1s ease-in-out infinite;
    }

    .dab-spinner {
        width: 20px;
        height: 20px;
    }

    .dab-spinner-sm {
        width: 16px;
        height: 16px;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .dab-auth-required {
        text-align: center;
        padding: 40px 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Modal Styles */
    .dab-modal {
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .dab-modal-content {
        background-color: #fff;
        margin: 2% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .dab-modal-large {
        max-width: 1000px;
    }

    .dab-modal-header {
        padding: 20px 30px;
        border-bottom: 1px solid #e1e5e9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
    }

    .dab-modal-header h3 {
        margin: 0;
        color: #333;
    }

    .dab-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dab-modal-close:hover {
        color: #333;
    }

    .dab-modal-body {
        padding: 30px;
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .dab-dashboard-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }

        .dab-dashboard-actions {
            justify-content: center;
        }

        .dab-tables-grid {
            grid-template-columns: 1fr;
        }

        .dab-modal-content {
            width: 95%;
            margin: 5% auto;
        }

        .dab-modal-body {
            padding: 20px;
        }
    }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('dab_user_dashboard', 'dab_user_dashboard_shortcode');
