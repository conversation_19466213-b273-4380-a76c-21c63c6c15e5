/* WooCommerce Integration Styles */

/* General Integration Styles */
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
}

.dab-woocommerce-integration-header p {
    margin: 0;
    color: #2c3338;
}

/* Product Fields Styles */
.dab-product-fields-container {
    margin: 20px 0;
}

.dab-product-fields-container .dab-field-wrapper {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #fafafa;
}

.dab-product-fields-container label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3338;
}

.dab-product-fields-container .required {
    color: #d63638;
}

.dab-product-fields-container input,
.dab-product-fields-container textarea,
.dab-product-fields-container select {
    width: 100%;
    max-width: 500px;
    padding: 8px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.dab-product-fields-container input:focus,
.dab-product-fields-container textarea:focus,
.dab-product-fields-container select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Customer Data Styles */
.dab-customer-profile-section {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.dab-customer-segments {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;
}

.dab-customer-segment {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    background: #f0f0f0;
    border-radius: 15px;
    font-size: 12px;
}

.dab-customer-segment-color {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

/* Order Fields Styles */
.dab-order-custom-fields {
    margin: 20px 0;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.dab-order-custom-fields h3 {
    margin-top: 0;
    color: #2c3338;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.dab-order-field-value {
    margin-bottom: 10px;
    padding: 8px;
    background: #fff;
    border-left: 3px solid #2271b1;
}

.dab-order-field-label {
    font-weight: bold;
    color: #2c3338;
    margin-bottom: 3px;
}

.dab-order-field-content {
    color: #50575e;
}

/* Checkout Field Styles */
.woocommerce-checkout .dab-checkout-field {
    margin-bottom: 20px;
}

.woocommerce-checkout .dab-checkout-field label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

.woocommerce-checkout .dab-checkout-field .required {
    color: #d63638;
}

.woocommerce-checkout .dab-checkout-field input,
.woocommerce-checkout .dab-checkout-field textarea,
.woocommerce-checkout .dab-checkout-field select {
    width: 100%;
    padding: 10px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.woocommerce-checkout .dab-checkout-field input:focus,
.woocommerce-checkout .dab-checkout-field textarea:focus,
.woocommerce-checkout .dab-checkout-field select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Sales Dashboard Styles */
.dab-sales-dashboard {
    margin: 20px 0;
}

.dab-sales-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dab-sales-metric {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    transition: box-shadow 0.2s ease;
}

.dab-sales-metric:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dab-sales-metric-value {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
    margin-bottom: 5px;
}

.dab-sales-metric-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.dab-sales-metric-change {
    font-size: 12px;
    font-weight: bold;
}

.dab-sales-metric-change.positive {
    color: #00a32a;
}

.dab-sales-metric-change.negative {
    color: #d63638;
}

.dab-sales-metric-change.neutral {
    color: #666;
}

/* Chart Container */
.dab-chart-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-chart-controls {
    display: flex;
    gap: 10px;
}

.dab-chart-controls select {
    padding: 5px 10px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

/* Product Selector Field */
.dab-wc-product-selector {
    position: relative;
}

.dab-wc-product-search {
    width: 100%;
    padding: 8px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.dab-wc-product-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.dab-wc-product-result {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.dab-wc-product-result:hover {
    background: #f0f6fc;
}

.dab-wc-product-result:last-child {
    border-bottom: none;
}

.dab-wc-selected-products {
    margin-top: 10px;
}

.dab-wc-selected-product {
    display: inline-flex;
    align-items: center;
    background: #f0f6fc;
    border: 1px solid #2271b1;
    border-radius: 15px;
    padding: 5px 10px;
    margin: 2px;
    font-size: 12px;
}

.dab-wc-selected-product .remove {
    margin-left: 5px;
    cursor: pointer;
    color: #d63638;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-sales-metrics {
        grid-template-columns: 1fr;
    }
    
    .dab-chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .dab-chart-controls {
        width: 100%;
        justify-content: flex-end;
    }
    
    .dab-product-fields-container input,
    .dab-product-fields-container textarea,
    .dab-product-fields-container select {
        max-width: 100%;
    }
}

/* Loading States */
.dab-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.dab-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: dab-spin 1s linear infinite;
}

@keyframes dab-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.dab-error {
    background: #fcf2f2;
    border: 1px solid #d63638;
    color: #d63638;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

.dab-success {
    background: #f0f6fc;
    border: 1px solid #00a32a;
    color: #00a32a;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Integration Status Indicators */
.dab-integration-status {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.dab-integration-status.active {
    background: #d4edda;
    color: #155724;
}

.dab-integration-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.dab-integration-status.warning {
    background: #fff3cd;
    color: #856404;
}

/* Field Type Icons */
.dab-field-type-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    vertical-align: middle;
}

.dab-field-type-text::before { content: "📝"; }
.dab-field-type-textarea::before { content: "📄"; }
.dab-field-type-select::before { content: "📋"; }
.dab-field-type-checkbox::before { content: "☑️"; }
.dab-field-type-date::before { content: "📅"; }
.dab-field-type-email::before { content: "📧"; }
.dab-field-type-url::before { content: "🔗"; }
.dab-field-type-number::before { content: "🔢"; }
.dab-field-type-file::before { content: "📎"; }

/* Accessibility Improvements */
.dab-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.dab-woocommerce-integration button:focus,
.dab-woocommerce-integration input:focus,
.dab-woocommerce-integration select:focus,
.dab-woocommerce-integration textarea:focus {
    outline: 2px solid #2271b1;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .dab-chart-controls,
    .dab-admin-sidebar,
    .button,
    .dab-loading::after {
        display: none !important;
    }
    
    .dab-sales-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dab-chart-container {
        break-inside: avoid;
    }
}
