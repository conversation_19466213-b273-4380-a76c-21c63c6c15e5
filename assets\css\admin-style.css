/* Database App Builder Admin + Frontend Style */

.wrap h1 {
    font-size: 28px;
    margin-bottom: 20px;
}

.wp-list-table th, .wp-list-table td {
    vertical-align: middle;
    padding: 10px;
}

.notice-success, .notice-error {
    margin-top: 20px;
}

.form-table th {
    width: 200px;
}

input[type="text"],
input[type="number"],
input[type="date"],
textarea,
select {
    width: 300px;
    max-width: 100%;
    padding: 5px;
}

select[multiple] {
    height: 120px;
}

.dab-form {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    background: #f9f9f9;
    max-width: 600px;
}

.dab-form p {
    margin-bottom: 15px;
}

.dab-form label {
    font-weight: bold;
}

.dab-submit-button {
    background: #2271b1;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dab-submit-button:hover {
    background: #135e96;
}

.dab-form-success {
    background: #dff0d8;
    border: 1px solid #d0e9c6;
    padding: 10px;
    margin-bottom: 20px;
    color: #3c763d;
}

.dab-form-errors {
    background: #f2dede;
    border: 1px solid #ebccd1;
    padding: 10px;
    margin-bottom: 20px;
    color: #a94442;
}

input[readonly] {
    background-color: #f1f1f1;
    cursor: pointer;
}

/* Make list tables clean */
.wp-list-table th, .wp-list-table td {
    padding: 8px 10px;
    vertical-align: middle;
}

/* Better form field spacing */
.form-table td input,
.form-table td textarea,
.form-table td select {
    margin-top: 5px;
    margin-bottom: 5px;
}
/* Data Manager Button Styling */
#dab-data-form p {
    margin-top: 15px;
}

#dab-data-form button {
    margin-right: 10px;
    padding: 8px 15px;
}

#dab-data-form .button-danger {
    background: #d63638;
    color: #fff;
    border: none;
}

#dab-data-form .button-danger:hover {
    background: #a00f10;
}

#dab-data-form .button-primary {
    background: #2271b1;
    color: #fff;
    border: none;
}

#dab-data-form .button-primary:hover {
    background: #135e96;
}

/* Table spacing */
.wp-list-table th, .wp-list-table td {
    padding: 8px 12px;
}



.dab-error {
    color: #d63638;
    padding: 10px;
    background: #ffebe8;
    border: 1px solid #d63638;
}

.dab-success-message {
    color: #2e7d32;
    padding: 15px;
    background: #e8f5e9;
    border: 1px solid #2e7d32;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: bold;
}

.dab-error-message {
    color: #d32f2f;
    padding: 15px;
    background: #ffebee;
    border: 1px solid #d32f2f;
    margin-bottom: 20px;
    border-radius: 4px;
    font-weight: bold;
}

/* Approval Workflow Styling */
.dab-assigned-workflow {
    background-color: #e8f5e9 !important;
}

.dab-status-assigned {
    display: inline-block;
    padding: 4px 8px;
    background-color: #4caf50;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.dab-status-admin {
    display: inline-block;
    padding: 4px 8px;
    background-color: #2196f3;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.dab-status-pending {
    display: inline-block;
    padding: 4px 8px;
    background-color: #ff9800;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

/* Dashboard Approval Widget */
.dab-approval-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.dab-approval-stat {
    flex: 1;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    margin: 0 5px;
}

.dab-approval-stat-pending {
    background-color: #fff3e0;
    border: 1px solid #ff9800;
}

.dab-approval-stat-approved {
    background-color: #e8f5e9;
    border: 1px solid #4caf50;
}

.dab-approval-stat-rejected {
    background-color: #ffebee;
    border: 1px solid #f44336;
}

.dab-approval-stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.dab-approval-stat-label {
    font-size: 14px;
    color: #666;
}

.dab-approval-stat-user-pending {
    font-size: 12px;
    font-weight: bold;
    color: #ff5722;
    margin-top: 5px;
}


