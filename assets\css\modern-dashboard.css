/**
 * Database App Builder Modern Dashboard
 * 
 * Modern dashboard styling for the Database App Builder plugin
 */

/* Import Modern UI Framework */
@import url('modern-ui.css');

/* Dashboard Container */
.dab-dashboard-container {
  background-color: var(--dab-gray-100);
  border-radius: var(--dab-border-radius-md);
  padding: var(--dab-spacing-lg);
  margin-bottom: var(--dab-spacing-lg);
}

.dab-dashboard-header {
  margin-bottom: var(--dab-spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dab-dashboard-title {
  font-size: var(--dab-font-size-2xl);
  font-weight: 600;
  color: var(--dab-gray-900);
  margin: 0;
}

.dab-dashboard-actions {
  display: flex;
  gap: var(--dab-spacing-sm);
  margin-top: var(--dab-spacing-sm);
}

@media (min-width: 768px) {
  .dab-dashboard-actions {
    margin-top: 0;
  }
}

.dab-dashboard-description {
  color: var(--dab-gray-600);
  margin-bottom: var(--dab-spacing-md);
}

/* Dashboard Grid */
.dab-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--dab-spacing-md);
  margin-bottom: var(--dab-spacing-lg);
}

/* Dashboard Widgets */
.dab-widget {
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--dab-transition-normal) ease, transform var(--dab-transition-normal) ease;
  animation: dab-scale-in var(--dab-transition-normal) ease;
}

.dab-widget:hover {
  box-shadow: var(--dab-shadow-md);
  transform: translateY(-2px);
}

.dab-widget-header {
  padding: var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
  background-color: var(--dab-gray-100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dab-widget-title {
  font-size: var(--dab-font-size-lg);
  font-weight: 600;
  color: var(--dab-gray-800);
  margin: 0;
}

.dab-widget-actions {
  display: flex;
  gap: var(--dab-spacing-xs);
}

.dab-widget-body {
  padding: var(--dab-spacing-md);
  height: calc(100% - 60px); /* Adjust based on header height */
  overflow: auto;
}

.dab-widget-footer {
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-top: 1px solid var(--dab-gray-300);
  background-color: var(--dab-gray-100);
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Widget Sizes */
.dab-widget-xs {
  grid-column: span 3;
}

.dab-widget-sm {
  grid-column: span 4;
}

.dab-widget-md {
  grid-column: span 6;
}

.dab-widget-lg {
  grid-column: span 8;
}

.dab-widget-xl {
  grid-column: span 12;
}

@media (max-width: 1200px) {
  .dab-widget-xs {
    grid-column: span 4;
  }
  
  .dab-widget-sm {
    grid-column: span 6;
  }
  
  .dab-widget-md, .dab-widget-lg {
    grid-column: span 12;
  }
}

@media (max-width: 768px) {
  .dab-widget-xs, .dab-widget-sm, .dab-widget-md, .dab-widget-lg, .dab-widget-xl {
    grid-column: span 12;
  }
}

/* Counter Widget */
.dab-counter {
  display: flex;
  align-items: center;
  padding: var(--dab-spacing-md);
}

.dab-counter-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: var(--dab-border-radius-md);
  margin-right: var(--dab-spacing-md);
  font-size: 24px;
  color: var(--dab-white);
}

.dab-counter-icon-primary {
  background-color: var(--dab-primary);
}

.dab-counter-icon-success {
  background-color: var(--dab-success);
}

.dab-counter-icon-warning {
  background-color: var(--dab-warning);
}

.dab-counter-icon-danger {
  background-color: var(--dab-danger);
}

.dab-counter-icon-info {
  background-color: var(--dab-info);
}

.dab-counter-content {
  flex: 1;
}

.dab-counter-value {
  font-size: var(--dab-font-size-3xl);
  font-weight: 700;
  color: var(--dab-gray-900);
  line-height: 1.2;
  margin: 0;
}

.dab-counter-label {
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
  margin: 0;
}

/* Chart Widget */
.dab-chart-container {
  width: 100%;
  height: 300px;
  position: relative;
}

/* Table Widget */
.dab-widget-table {
  width: 100%;
  border-collapse: collapse;
}

.dab-widget-table th {
  background-color: var(--dab-gray-100);
  font-weight: 600;
  text-align: left;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 2px solid var(--dab-gray-300);
}

.dab-widget-table td {
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
  vertical-align: middle;
}

.dab-widget-table tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

.dab-widget-table-empty {
  text-align: center;
  padding: var(--dab-spacing-lg);
  color: var(--dab-gray-600);
  font-style: italic;
}

/* List Widget */
.dab-widget-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dab-widget-list-item {
  padding: var(--dab-spacing-sm) 0;
  border-bottom: 1px solid var(--dab-gray-300);
  display: flex;
  align-items: center;
}

.dab-widget-list-item:last-child {
  border-bottom: none;
}

.dab-widget-list-icon {
  margin-right: var(--dab-spacing-sm);
  color: var(--dab-primary);
}

.dab-widget-list-content {
  flex: 1;
}

.dab-widget-list-title {
  font-weight: 500;
  margin: 0;
}

.dab-widget-list-subtitle {
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
  margin: 0;
}

.dab-widget-list-action {
  margin-left: var(--dab-spacing-sm);
}

/* Status Widget */
.dab-status {
  display: flex;
  align-items: center;
  padding: var(--dab-spacing-md);
}

.dab-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: var(--dab-spacing-sm);
}

.dab-status-indicator-success {
  background-color: var(--dab-success);
}

.dab-status-indicator-warning {
  background-color: var(--dab-warning);
}

.dab-status-indicator-danger {
  background-color: var(--dab-danger);
}

.dab-status-indicator-info {
  background-color: var(--dab-info);
}

.dab-status-content {
  flex: 1;
}

.dab-status-title {
  font-weight: 500;
  margin: 0;
}

.dab-status-message {
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
  margin: 0;
}

/* Dashboard Loader */
.dab-dashboard-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--dab-spacing-lg);
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-sm);
}

.dab-dashboard-loader-spinner {
  display: inline-block;
  width: 3rem;
  height: 3rem;
  border: 0.3rem solid rgba(67, 97, 238, 0.2);
  border-top-color: var(--dab-primary);
  border-radius: 50%;
  animation: dab-spin 1s linear infinite;
}

.dab-dashboard-loader-text {
  margin-left: var(--dab-spacing-md);
  font-size: var(--dab-font-size-lg);
  color: var(--dab-gray-700);
}
