/**
 * Signature Field Scripts
 *
 * JavaScript for signature fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all signature fields
    function initSignatureFields() {
        $('.dab-signature-field').each(function() {
            var $field = $(this);
            
            // Skip if already initialized
            if ($field.data('initialized')) {
                return;
            }
            
            // Mark as initialized
            $field.data('initialized', true);
            
            var $canvas = $field.find('.dab-signature-canvas');
            var $value = $field.find('input[type="hidden"]').first();
            var $name = $field.find('.dab-signature-name');
            var canvas = $canvas[0];
            
            // Skip if canvas not found
            if (!canvas) return;
            
            var ctx = canvas.getContext('2d');
            var isDrawing = false;
            var lastX = 0;
            var lastY = 0;
            var requireName = $name.length > 0;
            var includeTimestamp = $field.data('include-timestamp') || false;
            var penColor = $field.data('pen-color') || '#000000';
            var penSize = $field.data('pen-size') || 2;
            var signatureFormat = $field.data('signature-format') || 'png';
            
            // Set up the canvas
            ctx.strokeStyle = penColor;
            ctx.lineWidth = penSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // Load existing signature if available
            var existingValue = $value.val();
            if (existingValue) {
                try {
                    var signatureData = JSON.parse(existingValue);
                    if (signatureData && signatureData.data) {
                        var img = new Image();
                        img.onload = function() {
                            ctx.drawImage(img, 0, 0);
                        };
                        img.src = signatureData.data;
                    }
                } catch (e) {
                    // Not valid JSON, try as direct image data
                    if (existingValue.indexOf('data:image') === 0) {
                        var img = new Image();
                        img.onload = function() {
                            ctx.drawImage(img, 0, 0);
                        };
                        img.src = existingValue;
                    }
                }
            }
            
            // Drawing functions
            function startDrawing(e) {
                isDrawing = true;
                var rect = canvas.getBoundingClientRect();
                var clientX = e.clientX || (e.touches && e.touches[0].clientX);
                var clientY = e.clientY || (e.touches && e.touches[0].clientY);
                
                lastX = clientX - rect.left;
                lastY = clientY - rect.top;
                
                // Adjust for canvas scaling
                var scaleX = canvas.width / rect.width;
                var scaleY = canvas.height / rect.height;
                
                lastX *= scaleX;
                lastY *= scaleY;
                
                // Prevent scrolling on touch devices
                if (e.type === 'touchstart') {
                    e.preventDefault();
                }
            }
            
            function draw(e) {
                if (!isDrawing) return;
                
                var rect = canvas.getBoundingClientRect();
                var clientX = e.clientX || (e.touches && e.touches[0].clientX);
                var clientY = e.clientY || (e.touches && e.touches[0].clientY);
                
                // Adjust for canvas scaling
                var scaleX = canvas.width / rect.width;
                var scaleY = canvas.height / rect.height;
                
                var currentX = (clientX - rect.left) * scaleX;
                var currentY = (clientY - rect.top) * scaleY;
                
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(currentX, currentY);
                ctx.stroke();
                
                lastX = currentX;
                lastY = currentY;
                
                // Update the value
                updateValue();
                
                // Prevent scrolling on touch devices
                if (e.type === 'touchmove') {
                    e.preventDefault();
                }
            }
            
            function stopDrawing() {
                isDrawing = false;
            }
            
            // Clear the signature
            function clearSignature() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                updateValue();
            }
            
            // Update the hidden value field
            function updateValue() {
                var data = '';
                
                // Check if canvas is empty
                var isEmpty = isCanvasEmpty(canvas);
                
                if (!isEmpty) {
                    if (signatureFormat === 'png') {
                        data = canvas.toDataURL('image/png');
                    } else if (signatureFormat === 'svg') {
                        // Convert canvas to SVG (simplified)
                        data = canvasToSVG(canvas);
                    } else if (signatureFormat === 'json') {
                        // Store as JSON data (simplified)
                        data = JSON.stringify({
                            width: canvas.width,
                            height: canvas.height,
                            strokes: []
                        });
                    }
                }
                
                var signatureData = {
                    data: data
                };
                
                if (requireName) {
                    signatureData.name = $name.val();
                }
                
                if (includeTimestamp) {
                    signatureData.timestamp = new Date().toISOString();
                }
                
                $value.val(JSON.stringify(signatureData));
                
                // Toggle required validation
                if ($value.prop('required')) {
                    if (isEmpty || (requireName && !$name.val())) {
                        $value.attr('data-valid', 'false');
                    } else {
                        $value.attr('data-valid', 'true');
                    }
                }
            }
            
            // Check if canvas is empty
            function isCanvasEmpty(canvas) {
                var ctx = canvas.getContext('2d');
                var pixelData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
                
                // Check if all pixels are transparent
                for (var i = 3; i < pixelData.length; i += 4) {
                    if (pixelData[i] > 0) {
                        return false;
                    }
                }
                
                return true;
            }
            
            // Convert canvas to SVG
            function canvasToSVG(canvas) {
                // This is a simplified version, a real implementation would track all strokes
                var svg = '<svg xmlns="http://www.w3.org/2000/svg" width="' + canvas.width + '" height="' + canvas.height + '">';
                svg += '<path d="M0,0" stroke="' + penColor + '" stroke-width="' + penSize + '" fill="none"/>';
                svg += '</svg>';
                return 'data:image/svg+xml;base64,' + btoa(svg);
            }
            
            // Bind events
            $canvas.on('mousedown touchstart', startDrawing);
            $canvas.on('mousemove touchmove', draw);
            $(document).on('mouseup touchend', stopDrawing);
            
            $field.on('click', '.dab-clear-signature', clearSignature);
            
            if (requireName) {
                $name.on('input', updateValue);
            }
            
            // Handle window resize
            $(window).on('resize', function() {
                // Redraw the signature if the canvas size changes
                var img = new Image();
                img.onload = function() {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(img, 0, 0);
                };
                img.src = canvas.toDataURL('image/png');
            });
            
            // Initialize
            updateValue();
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initSignatureFields();
        
        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            initSignatureFields();
        });
    });
    
    // Add a global function to format signature values
    window.dabFormatSignature = function(value, options) {
        options = options || {};
        
        // Parse the value
        var signatureData = '';
        var signerName = '';
        var timestamp = '';
        
        if (!value) {
            return '';
        }
        
        if (typeof value === 'string' && value.indexOf('{') === 0) {
            try {
                var parsed = JSON.parse(value);
                signatureData = parsed.data || '';
                signerName = parsed.name || '';
                timestamp = parsed.timestamp || '';
            } catch (e) {
                // Not valid JSON, try as direct image data
                if (value.indexOf('data:image') === 0) {
                    signatureData = value;
                } else {
                    return value;
                }
            }
        } else {
            return value;
        }
        
        if (!signatureData) {
            return '';
        }
        
        // Format options
        var showName = options.showName !== undefined ? options.showName : true;
        var showTimestamp = options.showTimestamp !== undefined ? options.showTimestamp : true;
        var asHtml = options.asHtml !== undefined ? options.asHtml : true;
        var maxWidth = options.maxWidth || 0;
        var dateFormat = options.dateFormat || '';
        
        // Format the signature
        if (asHtml) {
            var style = maxWidth > 0 ? ' style="max-width:' + maxWidth + 'px"' : '';
            var output = '<div class="dab-signature-display">';
            output += '<img src="' + signatureData + '" alt="Signature" class="dab-signature-image"' + style + '>';
            
            if (showName && signerName) {
                output += '<div class="dab-signature-name">' + signerName + '</div>';
            }
            
            if (showTimestamp && timestamp) {
                var formattedDate = formatDate(timestamp, dateFormat);
                output += '<div class="dab-signature-timestamp">' + formattedDate + '</div>';
            }
            
            output += '</div>';
            
            return output;
        } else {
            var output = 'Signature';
            
            if (showName && signerName) {
                output += ': ' + signerName;
            }
            
            if (showTimestamp && timestamp) {
                var formattedDate = formatDate(timestamp, dateFormat);
                output += ' (' + formattedDate + ')';
            }
            
            return output;
        }
    };
    
    // Helper function to format dates
    function formatDate(isoString, format) {
        if (!isoString) return '';
        
        var date = new Date(isoString);
        if (isNaN(date.getTime())) return isoString;
        
        if (!format) {
            return date.toLocaleString();
        }
        
        // Simple date formatting (not comprehensive)
        return format
            .replace('YYYY', date.getFullYear())
            .replace('MM', ('0' + (date.getMonth() + 1)).slice(-2))
            .replace('DD', ('0' + date.getDate()).slice(-2))
            .replace('HH', ('0' + date.getHours()).slice(-2))
            .replace('mm', ('0' + date.getMinutes()).slice(-2))
            .replace('ss', ('0' + date.getSeconds()).slice(-2));
    }
})(jQuery);
