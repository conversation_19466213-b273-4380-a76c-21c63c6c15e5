/**
 * Analytics Dashboard JavaScript
 * Phase 3: Data Intelligence & Analytics
 */

(function($) {
    'use strict';

    // Main Analytics Dashboard Object
    window.DAB_AnalyticsDashboard = {
        
        // Configuration
        config: {
            ajaxUrl: ajaxurl,
            nonce: dab_admin_vars.nonce,
            refreshInterval: 30000, // 30 seconds
            autoRefresh: true
        },
        
        // State management
        state: {
            currentDashboard: null,
            widgets: {},
            refreshTimer: null,
            isFullscreen: false
        },
        
        // Initialize the dashboard
        init: function() {
            this.bindEvents();
            this.loadDashboards();
            this.initializeComponents();
            
            // Start auto-refresh if enabled
            if (this.config.autoRefresh) {
                this.startAutoRefresh();
            }
        },
        
        // Bind event handlers
        bindEvents: function() {
            var self = this;
            
            // Dashboard list events
            $(document).on('click', '.dab-dashboard-card', function() {
                var dashboardId = $(this).data('dashboard-id');
                self.viewDashboard(dashboardId);
            });
            
            // Dashboard builder events
            $(document).on('click', '#save-dashboard', function() {
                self.saveDashboard();
            });
            
            $(document).on('click', '#preview-dashboard', function() {
                self.previewDashboard();
            });
            
            $(document).on('click', '#export-dashboard', function() {
                self.exportDashboard();
            });
            
            // Widget events
            $(document).on('click', '.dab-widget-item', function() {
                self.addWidget($(this).data('widget-type'));
            });
            
            $(document).on('click', '.dab-widget-remove', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.removeWidget(widgetId);
            });
            
            $(document).on('click', '.dab-widget-edit', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.editWidget(widgetId);
            });
            
            // Refresh events
            $(document).on('click', '#refresh-dashboards', function() {
                self.loadDashboards();
            });
            
            $(document).on('click', '#refresh-data', function() {
                self.refreshDashboardData();
            });
            
            // Fullscreen events
            $(document).on('click', '#fullscreen-toggle, #fullscreen-view', function() {
                self.toggleFullscreen();
            });
            
            // Grid events
            $(document).on('click', '#grid-toggle', function() {
                self.toggleGrid();
            });
            
            // Share events
            $(document).on('click', '#share-dashboard', function() {
                self.shareDashboard();
            });
            
            // Filter events
            $(document).on('change', '#dashboard-filter, #search-dashboards', function() {
                self.filterDashboards();
            });
        },
        
        // Initialize components
        initializeComponents: function() {
            // Initialize drag and drop for widgets
            this.initializeDragDrop();
            
            // Initialize grid system
            this.initializeGrid();
            
            // Initialize real-time updates
            this.initializeRealTime();
        },
        
        // Load dashboards list
        loadDashboards: function() {
            var self = this;
            
            $('#dashboards-grid').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading dashboards...</p></div>');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_dashboards_list',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.renderDashboardsList(response.data);
                        self.updateOverviewStats(response.data.stats);
                    } else {
                        self.showError('Failed to load dashboards: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while loading dashboards');
                }
            });
        },
        
        // Render dashboards list
        renderDashboardsList: function(data) {
            var html = '';
            
            if (data.dashboards && data.dashboards.length > 0) {
                data.dashboards.forEach(function(dashboard) {
                    html += '<div class="dab-dashboard-card" data-dashboard-id="' + dashboard.id + '">';
                    html += '<div class="dab-card-header">';
                    html += '<h3>' + dashboard.name + '</h3>';
                    html += '<div class="dab-card-status ' + (dashboard.is_public ? 'public' : 'private') + '">';
                    html += dashboard.is_public ? 'Public' : 'Private';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-content">';
                    html += '<p>' + (dashboard.description || 'No description') + '</p>';
                    html += '<div class="dab-card-meta">';
                    html += '<span><i class="dashicons dashicons-chart-line"></i> ' + dashboard.widget_count + ' widgets</span>';
                    html += '<span><i class="dashicons dashicons-visibility"></i> ' + dashboard.access_count + ' views</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-actions">';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.viewDashboard(' + dashboard.id + ')">View</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.editDashboard(' + dashboard.id + ')">Edit</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.duplicateDashboard(' + dashboard.id + ')">Duplicate</button>';
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="dab-empty-state">';
                html += '<div class="dab-empty-icon"><span class="dashicons dashicons-dashboard"></span></div>';
                html += '<h3>No dashboards found</h3>';
                html += '<p>Create your first analytics dashboard to get started.</p>';
                html += '<a href="' + window.location.href + '&action=create" class="button button-primary">Create Dashboard</a>';
                html += '</div>';
            }
            
            $('#dashboards-grid').html(html);
        },
        
        // Update overview statistics
        updateOverviewStats: function(stats) {
            $('#total-dashboards').text(stats.total_dashboards || 0);
            $('#active-widgets').text(stats.active_widgets || 0);
            $('#total-views').text(stats.total_views || 0);
            $('#realtime-updates').text(stats.realtime_updates || 0);
        },
        
        // View dashboard
        viewDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=view&dashboard_id=' + dashboardId;
        },
        
        // Edit dashboard
        editDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=edit&dashboard_id=' + dashboardId;
        },
        
        // Duplicate dashboard
        duplicateDashboard: function(dashboardId) {
            var self = this;
            
            if (!confirm('Are you sure you want to duplicate this dashboard?')) {
                return;
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_duplicate_dashboard',
                    dashboard_id: dashboardId,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess('Dashboard duplicated successfully');
                        self.loadDashboards();
                    } else {
                        self.showError('Failed to duplicate dashboard: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while duplicating dashboard');
                }
            });
        },
        
        // Save dashboard
        saveDashboard: function() {
            var self = this;
            var dashboardData = this.collectDashboardData();
            
            if (!this.validateDashboardData(dashboardData)) {
                return;
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_save_dashboard',
                    dashboard_data: dashboardData,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess('Dashboard saved successfully');
                        self.state.currentDashboard = response.data.dashboard_id;
                        $('#canvas-status').text('Saved').removeClass('draft').addClass('saved');
                    } else {
                        self.showError('Failed to save dashboard: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while saving dashboard');
                }
            });
        },
        
        // Collect dashboard data from form
        collectDashboardData: function() {
            return {
                name: $('#dashboard-name').val(),
                description: $('#dashboard-description').val(),
                refresh_interval: $('#refresh-interval').val(),
                auto_refresh: $('#auto-refresh').is(':checked') ? 1 : 0,
                is_public: $('#is-public').is(':checked') ? 1 : 0,
                layout_config: this.getLayoutConfig(),
                theme_config: this.getThemeConfig(),
                widgets: this.getWidgetsConfig()
            };
        },
        
        // Validate dashboard data
        validateDashboardData: function(data) {
            if (!data.name || data.name.trim() === '') {
                this.showError('Dashboard name is required');
                $('#dashboard-name').focus();
                return false;
            }
            
            return true;
        },
        
        // Get layout configuration
        getLayoutConfig: function() {
            // Collect layout configuration from the grid
            var layout = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                layout.push({
                    widget_id: $widget.data('widget-id'),
                    x: parseInt($widget.css('left')) || 0,
                    y: parseInt($widget.css('top')) || 0,
                    width: parseInt($widget.css('width')) || 200,
                    height: parseInt($widget.css('height')) || 150
                });
            });
            return layout;
        },
        
        // Get theme configuration
        getThemeConfig: function() {
            return {
                background_color: '#ffffff',
                text_color: '#333333',
                accent_color: '#667eea',
                grid_size: 20
            };
        },
        
        // Get widgets configuration
        getWidgetsConfig: function() {
            var widgets = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                var widgetId = $widget.data('widget-id');
                if (this.state.widgets[widgetId]) {
                    widgets.push(this.state.widgets[widgetId]);
                }
            }.bind(this));
            return widgets;
        },
        
        // Initialize drag and drop
        initializeDragDrop: function() {
            var self = this;
            
            // Make widgets draggable from library
            $('.dab-widget-item').draggable({
                helper: 'clone',
                revert: 'invalid',
                zIndex: 1000
            });
            
            // Make dashboard grid droppable
            $('#dashboard-grid').droppable({
                accept: '.dab-widget-item',
                drop: function(event, ui) {
                    var widgetType = ui.draggable.data('widget-type');
                    var position = ui.position;
                    self.addWidget(widgetType, position);
                }
            });
            
            // Make existing widgets draggable
            $(document).on('mousedown', '.dab-widget', function() {
                $(this).draggable({
                    containment: '#dashboard-grid',
                    grid: [20, 20],
                    stop: function() {
                        self.updateWidgetPosition($(this));
                    }
                });
            });
        },
        
        // Initialize grid system
        initializeGrid: function() {
            this.updateGridBackground();
        },
        
        // Update grid background
        updateGridBackground: function() {
            var gridSize = 20;
            var $grid = $('.dab-grid-background');
            $grid.css({
                'background-size': gridSize + 'px ' + gridSize + 'px'
            });
        },
        
        // Toggle grid visibility
        toggleGrid: function() {
            $('.dab-grid-background').toggle();
            $('#grid-toggle').toggleClass('active');
        },
        
        // Initialize real-time updates
        initializeRealTime: function() {
            // Set up WebSocket or polling for real-time updates
            this.setupRealTimePolling();
        },
        
        // Set up real-time polling
        setupRealTimePolling: function() {
            var self = this;
            
            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
            }
            
            this.state.refreshTimer = setInterval(function() {
                if (self.config.autoRefresh && self.state.currentDashboard) {
                    self.refreshDashboardData();
                }
            }, this.config.refreshInterval);
        },
        
        // Start auto-refresh
        startAutoRefresh: function() {
            this.config.autoRefresh = true;
            this.setupRealTimePolling();
        },
        
        // Stop auto-refresh
        stopAutoRefresh: function() {
            this.config.autoRefresh = false;
            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
                this.state.refreshTimer = null;
            }
        },
        
        // Refresh dashboard data
        refreshDashboardData: function() {
            var self = this;
            
            if (!this.state.currentDashboard) {
                return;
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_realtime_data',
                    dashboard_id: this.state.currentDashboard,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateWidgetsData(response.data);
                        self.updateLastRefreshTime();
                    }
                },
                error: function() {
                    console.warn('Failed to refresh dashboard data');
                }
            });
        },
        
        // Update widgets with new data
        updateWidgetsData: function(data) {
            for (var widgetId in data) {
                if (data.hasOwnProperty(widgetId)) {
                    this.updateWidgetData(widgetId, data[widgetId]);
                }
            }
        },
        
        // Update individual widget data
        updateWidgetData: function(widgetId, data) {
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            if ($widget.length > 0) {
                // Update widget content based on type
                var widgetType = $widget.data('widget-type');
                this.renderWidgetData($widget, widgetType, data);
            }
        },
        
        // Render widget data based on type
        renderWidgetData: function($widget, type, data) {
            var $content = $widget.find('.dab-widget-content');
            
            switch (type) {
                case 'metric':
                    this.renderMetricWidget($content, data);
                    break;
                case 'chart':
                    this.renderChartWidget($content, data);
                    break;
                case 'table':
                    this.renderTableWidget($content, data);
                    break;
                case 'gauge':
                    this.renderGaugeWidget($content, data);
                    break;
                default:
                    $content.html('<p>Unknown widget type: ' + type + '</p>');
            }
        },
        
        // Render metric widget
        renderMetricWidget: function($content, data) {
            var html = '<div class="dab-metric-widget">';
            html += '<div class="dab-metric-value">' + (data.value || 0) + '</div>';
            html += '<div class="dab-metric-label">' + (data.label || 'Metric') + '</div>';
            if (data.change) {
                var changeClass = data.change > 0 ? 'positive' : 'negative';
                html += '<div class="dab-metric-change ' + changeClass + '">' + data.change + '%</div>';
            }
            html += '</div>';
            $content.html(html);
        },
        
        // Show success message
        showSuccess: function(message) {
            this.showNotification(message, 'success');
        },
        
        // Show error message
        showError: function(message) {
            this.showNotification(message, 'error');
        },
        
        // Show notification
        showNotification: function(message, type) {
            var $notification = $('<div class="dab-notification dab-notification-' + type + '">' + message + '</div>');
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof dab_admin_vars !== 'undefined') {
            DAB_AnalyticsDashboard.init();
        }
    });

})(jQuery);
