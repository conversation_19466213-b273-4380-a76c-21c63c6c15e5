/**
 * Workflow Builder Styles
 */

/* Workflow Editor Layout */
.dab-workflow-editor {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dab-workflow-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.dab-workflow-editor-actions {
    display: flex;
    gap: 10px;
}

.dab-workflow-editor-content {
    display: grid;
    grid-template-columns: 300px 1fr 250px;
    min-height: 600px;
}

/* Configuration Panel */
.dab-workflow-config-panel {
    padding: 20px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    overflow-y: auto;
}

.dab-workflow-config-panel h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.dab-form-group {
    margin-bottom: 20px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.dab-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dab-form-control:focus {
    outline: 0;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dab-form-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.dab-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.dab-checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

/* Trigger Configuration */
.dab-trigger-config {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.dab-trigger-config-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Workflow Canvas */
.dab-workflow-canvas {
    padding: 20px;
    background: #fff;
    position: relative;
}

.dab-workflow-canvas h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.dab-canvas {
    min-height: 500px;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    position: relative;
    background: #fafbfc;
}

.dab-canvas-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
}

.dab-canvas-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.dab-canvas-placeholder p {
    margin: 0;
    font-size: 14px;
}

/* Action Palette */
.dab-action-palette {
    padding: 20px;
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    overflow-y: auto;
}

.dab-action-palette h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.dab-action-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-action-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s ease;
}

.dab-action-item:hover {
    border-color: #007cba;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.1);
    transform: translateY(-1px);
}

.dab-action-item:active {
    cursor: grabbing;
}

.dab-action-item .dashicons {
    margin-right: 10px;
    color: #007cba;
    font-size: 16px;
}

.dab-action-item span:last-child {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

/* Workflow Steps */
.dab-workflow-step {
    position: absolute;
    background: #fff;
    border: 2px solid #007cba;
    border-radius: 8px;
    padding: 15px;
    min-width: 200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: default;
}

.dab-workflow-step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dab-workflow-step-title {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.dab-workflow-step-title .dashicons {
    margin-right: 8px;
    color: #007cba;
}

.dab-drag-handle {
    cursor: move;
    flex: 1;
    padding: 2px 0;
}

.dab-workflow-step-actions {
    display: flex;
    gap: 5px;
    pointer-events: auto;
    z-index: 10;
    position: relative;
}

.dab-workflow-step-btn {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 3px;
    color: #6c757d;
    transition: all 0.2s ease;
    pointer-events: auto;
}

.dab-workflow-step-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.dab-workflow-step-content {
    font-size: 13px;
    color: #6c757d;
}

/* Workflow Connections */
.dab-workflow-connection {
    position: absolute;
    pointer-events: none;
}

.dab-workflow-connection svg {
    width: 100%;
    height: 100%;
}

.dab-workflow-connection path {
    stroke: #007cba;
    stroke-width: 2;
    fill: none;
    marker-end: url(#arrowhead);
}

/* Workflows List */
.dab-workflows-list {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.dab-workflows-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.dab-workflow-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.dab-workflow-card:hover {
    border-color: #007cba;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.1);
    transform: translateY(-2px);
}

.dab-workflow-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.dab-workflow-card-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.dab-workflow-status {
    margin-left: 10px;
}

.dab-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dab-status-active {
    background: #d4edda;
    color: #155724;
}

.dab-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.dab-workflow-card-body p {
    margin: 0 0 15px 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

.dab-workflow-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 15px;
}

.dab-workflow-trigger {
    display: flex;
    align-items: center;
}

.dab-workflow-trigger .dashicons {
    margin-right: 5px;
    font-size: 14px;
}

.dab-workflow-card-actions {
    display: flex;
    gap: 8px;
}

/* Empty State */
.dab-empty-state {
    text-align: center;
    padding: 60px 20px;
}

.dab-empty-state-icon {
    margin-bottom: 20px;
}

.dab-empty-state-icon .dashicons {
    font-size: 64px;
    color: #6c757d;
    opacity: 0.5;
}

.dab-empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 20px;
    color: #495057;
}

.dab-empty-state p {
    margin: 0 0 30px 0;
    color: #6c757d;
    font-size: 16px;
}

/* Modal Styles */
.dab-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

/* Step Configuration Modal */
#step-config-modal .dab-modal-content {
    max-width: 600px;
}

.dab-step-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dab-step-modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.dab-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.dab-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dab-modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.dab-modal-body {
    padding: 20px;
}

.dab-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

/* Field Mapping Styles */
.dab-field-mappings {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    padding: 15px;
}

.dab-field-mappings-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.dab-field-mappings-header p {
    margin: 0;
    font-size: 13px;
    color: #6c757d;
}

.dab-field-mappings-header code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #495057;
}

.dab-field-mapping-row {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 15px;
    align-items: start;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.dab-field-mapping-row:last-child {
    border-bottom: none;
}

.dab-field-mapping-label {
    padding-top: 8px;
}

.dab-field-mapping-label label {
    margin: 0;
    font-weight: 500;
    color: #495057;
}

.dab-field-type {
    font-size: 11px;
    color: #6c757d;
    font-weight: normal;
    margin-left: 5px;
}

.dab-field-mapping-input {
    width: 100%;
}

.dab-field-mapping-value {
    width: 100%;
}

.dab-form-text.error {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dab-workflow-editor-content {
        grid-template-columns: 280px 1fr 220px;
    }
}

@media (max-width: 992px) {
    .dab-workflow-editor-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
    }

    .dab-workflow-config-panel,
    .dab-action-palette {
        border-right: none;
        border-left: none;
        border-bottom: 1px solid #e9ecef;
    }

    .dab-workflows-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dab-workflow-editor-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .dab-workflow-editor-actions {
        justify-content: center;
    }

    .dab-workflow-card-actions {
        flex-direction: column;
    }

    .dab-workflow-meta {
        flex-direction: column;
        gap: 5px;
        align-items: flex-start;
    }

    .dab-field-mapping-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .dab-field-mapping-label {
        padding-top: 0;
    }
}
