<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Quick Start Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .step {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #3498db;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .note {
            background-color: #f1f9ff;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Database App Builder - Quick Start Guide</h1>
    <p>This quick start guide will help you create your first database application in just a few minutes.</p>

    <div class="step">
        <h3>Step 1: Install and Activate the Plugin</h3>
        <ol>
            <li>Download the plugin from ThemeForest</li>
            <li>Go to your WordPress admin panel</li>
            <li>Navigate to <strong>Plugins > Add New > Upload Plugin</strong></li>
            <li>Choose the downloaded zip file and click <strong>Install Now</strong></li>
            <li>After installation, click <strong>Activate Plugin</strong></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 2: Create Your First Table</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Tables</strong></li>
            <li>Click the <strong>Add New Table</strong> button</li>
            <li>Enter a table label (e.g., "Customers")</li>
            <li>The table slug will be generated automatically</li>
            <li>Add an optional description</li>
            <li>Click <strong>Create Table</strong></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 3: Add Fields to Your Table</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Fields</strong></li>
            <li>Select your newly created table from the dropdown</li>
            <li>Click the <strong>Add New Field</strong> button</li>
            <li>Enter a field label (e.g., "First Name")</li>
            <li>Select the field type (e.g., "Text")</li>
            <li>Configure any additional options (required, placeholder, etc.)</li>
            <li>Click <strong>Add Field</strong></li>
            <li>Repeat for additional fields (e.g., Last Name, Email, Phone)</li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 4: Create a Form</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Forms</strong></li>
            <li>Click the <strong>Add New Form</strong> button</li>
            <li>Enter a form name (e.g., "Customer Form")</li>
            <li>Select your table from the dropdown</li>
            <li>Select the fields you want to include in the form</li>
            <li>Configure form settings (success message, redirect, etc.)</li>
            <li>Click <strong>Create Form</strong></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 5: Create a View</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Views</strong></li>
            <li>Click the <strong>Add New View</strong> button</li>
            <li>Enter a view name (e.g., "Customer List")</li>
            <li>Select your table from the dropdown</li>
            <li>Select the fields you want to display in the view</li>
            <li>Configure view settings (filters, sorting, etc.)</li>
            <li>Click <strong>Create View</strong></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 6: Add Shortcodes to Your Pages</h3>
        <ol>
            <li>Create a new page for your form (e.g., "Add Customer")</li>
            <li>Add the form shortcode to the page: <code>[dab_form id="1"]</code> (replace "1" with your form ID)</li>
            <li>Create a new page for your view (e.g., "Customer List")</li>
            <li>Add the view shortcode to the page: <code>[dab_view id="1"]</code> (replace "1" with your view ID)</li>
            <li>Publish both pages</li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 7: Test Your Application</h3>
        <ol>
            <li>Visit your form page and submit a test entry</li>
            <li>Visit your view page to see the submitted data</li>
            <li>Make any necessary adjustments to your form or view</li>
        </ol>
    </div>

    <div class="note">
        <p><strong>Congratulations!</strong> You've created your first database application with Database App Builder. To learn more about advanced features like dashboards, approval workflows, and integrations, please refer to the full documentation.</p>
    </div>

    <p><a href="index.html">Back to Full Documentation</a></p>
</body>
</html>
