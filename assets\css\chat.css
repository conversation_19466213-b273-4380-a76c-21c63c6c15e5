/**
 * Chat System Styles
 *
 * Modern, responsive chat interface
 */

/* Base Chat Container */
.dab-chat-container {
    display: flex;
    height: 600px;
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Chat Sidebar */
.dab-chat-sidebar {
    width: 320px;
    background: #f8fafc;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
}

.dab-chat-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dab-chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
}

.dab-chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dab-chat-avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
}

.dab-chat-status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #22c55e;
    border: 2px solid white;
}

.dab-chat-status-indicator.offline {
    background: #6b7280;
}

.dab-chat-user-details {
    flex: 1;
}

.dab-chat-username {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 2px;
}

.dab-chat-status-text {
    font-size: 12px;
    opacity: 0.8;
}

.dab-chat-actions {
    display: flex;
    gap: 8px;
}

/* Chat Navigation */
.dab-chat-nav {
    display: flex;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.dab-chat-nav-btn {
    flex: 1;
    padding: 16px 12px;
    border: none;
    background: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
}

.dab-chat-nav-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.dab-chat-nav-btn.active {
    background: #3b82f6;
    color: white;
}

.dab-chat-badge {
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* Search Bar */
.dab-chat-search {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.dab-chat-search-input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.dab-chat-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dab-chat-search-icon {
    position: absolute;
    left: 28px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
}

/* Sidebar Content */
.dab-chat-sidebar-content {
    flex: 1;
    overflow-y: auto;
}

.dab-chat-list {
    height: 100%;
}

/* Chat Items */
.dab-chat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f3f4f6;
    position: relative;
}

.dab-chat-item:hover {
    background: #f9fafb;
}

.dab-chat-item.active {
    background: #eff6ff;
    border-right: 3px solid #3b82f6;
}

.dab-chat-item-content {
    flex: 1;
    min-width: 0;
}

.dab-chat-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.dab-chat-item-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.dab-chat-item-time,
.dab-chat-item-members,
.dab-chat-item-username {
    font-size: 12px;
    color: #6b7280;
}

.dab-chat-item-message {
    font-size: 13px;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dab-chat-unread-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #3b82f6;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* Chat Main Area */
.dab-chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

/* Welcome Screen */
.dab-chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dab-chat-welcome-content {
    text-align: center;
    color: #6b7280;
}

.dab-chat-welcome-icon {
    font-size: 64px;
    margin-bottom: 16px;
    color: #d1d5db;
}

.dab-chat-welcome h3 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 20px;
    font-weight: 600;
}

.dab-chat-welcome p {
    margin: 0;
    font-size: 14px;
}

/* Chat Area */
.dab-chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Chat Header */
.dab-chat-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-chat-header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dab-chat-header-details {
    flex: 1;
}

.dab-chat-header-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 16px;
    margin-bottom: 2px;
}

.dab-chat-header-status {
    font-size: 12px;
    color: #6b7280;
}

.dab-chat-header-actions {
    display: flex;
    gap: 8px;
}

/* Messages Container */
.dab-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #f9fafb;
    scroll-behavior: smooth;
}

/* Messages */
.dab-chat-message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: flex-start;
}

.dab-chat-message.own {
    flex-direction: row-reverse;
}

.dab-chat-message-content {
    max-width: 70%;
    min-width: 120px;
}

.dab-chat-message.own .dab-chat-message-content {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
}

.dab-chat-message:not(.own) .dab-chat-message-content {
    background: white;
    color: #1f2937;
    border-radius: 18px 18px 18px 4px;
    padding: 12px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dab-chat-message-sender {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #6b7280;
}

.dab-chat-message.own .dab-chat-message-sender {
    color: rgba(255, 255, 255, 0.8);
}

.dab-chat-message-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.dab-chat-message-time {
    font-size: 11px;
    margin-top: 4px;
    opacity: 0.7;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dab-message-delete {
    background: none;
    border: none;
    color: inherit;
    opacity: 0.5;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    transition: opacity 0.2s ease;
}

.dab-message-delete:hover {
    opacity: 1;
}

/* Date Separator */
.dab-chat-date-separator {
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
}

.dab-chat-date-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
    z-index: 1;
}

.dab-chat-date-separator::after {
    content: attr(data-date);
    background: #f9fafb;
    padding: 0 12px;
    position: relative;
    z-index: 2;
}

/* Typing Indicator */
.dab-chat-typing {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 12px;
}

.dab-chat-typing-indicator {
    display: flex;
    gap: 2px;
}

.dab-chat-typing-indicator span {
    width: 4px;
    height: 4px;
    background: #6b7280;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.dab-chat-typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.dab-chat-typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Message Input */
.dab-chat-input-container {
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
    background: white;
}

.dab-chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #f3f4f6;
    border-radius: 24px;
    padding: 8px 8px 8px 16px;
}

.dab-chat-input {
    flex: 1;
    border: none;
    background: none;
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
    padding: 8px 0;
    font-family: inherit;
}

.dab-chat-input::placeholder {
    color: #9ca3af;
}

.dab-chat-input-actions {
    display: flex;
    gap: 4px;
}

/* Buttons */
.dab-chat-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.dab-chat-btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 50%;
}

.dab-chat-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.dab-chat-btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dab-chat-btn-secondary {
    background: #6b7280;
    color: white;
}

.dab-chat-btn-secondary:hover {
    background: #4b5563;
}

.dab-chat-btn-icon {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.dab-chat-btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.dab-chat-header .dab-chat-btn-icon {
    background: #f3f4f6;
    color: #6b7280;
}

.dab-chat-header .dab-chat-btn-icon:hover {
    background: #e5e7eb;
    color: #374151;
}

.dab-chat-btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Loading States */
.dab-chat-loading,
.dab-chat-empty,
.dab-chat-error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

.dab-chat-error {
    color: #ef4444;
}

.dab-chat-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f4f6;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modals */
.dab-chat-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-chat-modal-content {
    background-color: white;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: modal-appear 0.2s ease-out;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dab-chat-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.dab-chat-modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
}

.dab-chat-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-chat-modal-close:hover {
    color: #374151;
    background-color: #e5e7eb;
}

.dab-chat-modal-body {
    padding: 24px;
    max-height: calc(80vh - 120px);
    overflow-y: auto;
}

/* Form Elements */
.dab-chat-form-group {
    margin-bottom: 20px;
}

.dab-chat-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.dab-chat-form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
    font-family: inherit;
}

.dab-chat-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dab-chat-form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* User List in Modal */
.dab-chat-user-list {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 16px;
}

.dab-chat-user-list .dab-chat-item {
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #e5e7eb;
}

.dab-chat-user-list .dab-chat-item:hover {
    background: #f3f4f6;
    border-color: #3b82f6;
}

/* Auth Required */
.dab-auth-required {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    max-width: 400px;
    margin: 40px auto;
}

.dab-auth-required p {
    margin-bottom: 24px;
    color: #6b7280;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-chat-container {
        height: 100vh;
        border-radius: 0;
        box-shadow: none;
        border: none;
    }

    .dab-chat-sidebar {
        width: 100%;
        position: absolute;
        z-index: 10;
        height: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .dab-chat-sidebar.open {
        transform: translateX(0);
    }

    .dab-chat-main {
        width: 100%;
    }

    .dab-chat-header {
        padding: 12px 16px;
    }

    .dab-chat-messages {
        padding: 12px;
    }

    .dab-chat-input-container {
        padding: 12px 16px;
    }

    .dab-chat-modal-content {
        width: 95%;
        margin: 20px;
    }

    .dab-chat-modal-body {
        padding: 20px;
    }

    .dab-chat-message-content {
        max-width: 85%;
    }

    .dab-chat-nav-btn {
        padding: 12px 8px;
        font-size: 12px;
    }

    .dab-chat-nav-btn .dashicons {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .dab-chat-sidebar-header {
        padding: 16px;
    }

    .dab-chat-search {
        padding: 12px;
    }

    .dab-chat-item {
        padding: 12px;
    }

    .dab-chat-avatar {
        width: 36px;
        height: 36px;
    }

    .dab-chat-input-wrapper {
        padding: 6px 6px 6px 12px;
    }

    .dab-chat-btn-icon {
        width: 32px;
        height: 32px;
    }

    .dab-chat-form-actions {
        flex-direction: column;
    }

    .dab-chat-form-actions .dab-chat-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dab-chat-container {
        background: #1f2937;
        border-color: #374151;
    }

    .dab-chat-sidebar {
        background: #111827;
        border-color: #374151;
    }

    .dab-chat-nav {
        background: #1f2937;
        border-color: #374151;
    }

    .dab-chat-nav-btn {
        color: #9ca3af;
    }

    .dab-chat-nav-btn:hover {
        background: #374151;
        color: #e5e7eb;
    }

    .dab-chat-search-input {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }

    .dab-chat-item {
        border-color: #374151;
    }

    .dab-chat-item:hover {
        background: #374151;
    }

    .dab-chat-item-name {
        color: #e5e7eb;
    }

    .dab-chat-main {
        background: #1f2937;
    }

    .dab-chat-header {
        background: #1f2937;
        border-color: #374151;
    }

    .dab-chat-header-name {
        color: #e5e7eb;
    }

    .dab-chat-messages {
        background: #111827;
    }

    .dab-chat-message:not(.own) .dab-chat-message-content {
        background: #374151;
        color: #e5e7eb;
    }

    .dab-chat-input-container {
        background: #1f2937;
        border-color: #374151;
    }

    .dab-chat-input-wrapper {
        background: #374151;
    }

    .dab-chat-input {
        color: #e5e7eb;
    }

    .dab-chat-modal-content {
        background: #1f2937;
    }

    .dab-chat-modal-header {
        background: #111827;
        border-color: #374151;
    }

    .dab-chat-modal-header h3 {
        color: #e5e7eb;
    }

    .dab-chat-form-control {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }

    .dab-chat-form-group label {
        color: #e5e7eb;
    }
}

/* Scrollbar Styling */
.dab-chat-sidebar-content::-webkit-scrollbar,
.dab-chat-messages::-webkit-scrollbar,
.dab-chat-modal-body::-webkit-scrollbar {
    width: 6px;
}

.dab-chat-sidebar-content::-webkit-scrollbar-track,
.dab-chat-messages::-webkit-scrollbar-track,
.dab-chat-modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.dab-chat-sidebar-content::-webkit-scrollbar-thumb,
.dab-chat-messages::-webkit-scrollbar-thumb,
.dab-chat-modal-body::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.dab-chat-sidebar-content::-webkit-scrollbar-thumb:hover,
.dab-chat-messages::-webkit-scrollbar-thumb:hover,
.dab-chat-modal-body::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Required field indicator */
.required {
    color: #ef4444;
    font-weight: 700;
}
