<?php
/**
 * Multi-Step Form Builder
 * 
 * Wizard-style forms with conditional logic and progress tracking
 * 
 * @package Database App Builder
 * @subpackage Forms
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Multistep_Form_Builder {
    
    /**
     * Initialize the Multi-Step Form Builder
     */
    public static function init() {
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_save_form_progress', array(__CLASS__, 'save_form_progress'));
        add_action('wp_ajax_nopriv_dab_save_form_progress', array(__CLASS__, 'save_form_progress'));
        add_action('wp_ajax_dab_get_form_progress', array(__CLASS__, 'get_form_progress'));
        add_action('wp_ajax_nopriv_dab_get_form_progress', array(__CLASS__, 'get_form_progress'));
        add_action('wp_ajax_dab_validate_form_step', array(__CLASS__, 'validate_form_step'));
        add_action('wp_ajax_nopriv_dab_validate_form_step', array(__CLASS__, 'validate_form_step'));
        
        // Create database tables
        add_action('init', array(__CLASS__, 'create_tables'));
        
        // Add shortcode for multi-step forms
        add_shortcode('dab_multistep_form', array(__CLASS__, 'render_multistep_form_shortcode'));
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_style(
            'dab-multistep-forms',
            plugin_dir_url(__FILE__) . '../../assets/css/multistep-forms.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'dab-form-wizard',
            plugin_dir_url(__FILE__) . '../../assets/js/form-wizard.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_script(
            'dab-conditional-logic',
            plugin_dir_url(__FILE__) . '../../assets/js/conditional-logic.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('dab-form-wizard', 'dabFormWizardData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_form_wizard_nonce'),
            'i18n' => array(
                'next' => __('Next', 'db-app-builder'),
                'previous' => __('Previous', 'db-app-builder'),
                'submit' => __('Submit', 'db-app-builder'),
                'save' => __('Save Progress', 'db-app-builder'),
                'validationError' => __('Please fix the errors before continuing', 'db-app-builder'),
                'saveSuccess' => __('Progress saved successfully', 'db-app-builder'),
                'saveError' => __('Error saving progress', 'db-app-builder'),
                'loadError' => __('Error loading form data', 'db-app-builder'),
                'confirmLeave' => __('You have unsaved changes. Are you sure you want to leave?', 'db-app-builder')
            )
        ));
    }
    
    /**
     * Create database tables for Multi-Step Forms
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Multi-step forms table
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $sql_forms = "CREATE TABLE $multistep_forms_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            form_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            steps_config longtext,
            conditional_rules longtext,
            settings longtext,
            is_active tinyint(1) DEFAULT 1,
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY form_id (form_id),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        // Form progress table
        $form_progress_table = $wpdb->prefix . 'dab_form_progress';
        $sql_progress = "CREATE TABLE $form_progress_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            multistep_form_id bigint(20) NOT NULL,
            user_id bigint(20) DEFAULT 0,
            session_id varchar(255),
            current_step int(11) DEFAULT 1,
            form_data longtext,
            completed_steps text,
            is_completed tinyint(1) DEFAULT 0,
            started_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            completed_at datetime NULL,
            PRIMARY KEY (id),
            KEY multistep_form_id (multistep_form_id),
            KEY user_id (user_id),
            KEY session_id (session_id)
        ) $charset_collate;";
        
        // Conditional rules table
        $conditional_rules_table = $wpdb->prefix . 'dab_conditional_rules';
        $sql_rules = "CREATE TABLE $conditional_rules_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            multistep_form_id bigint(20) NOT NULL,
            rule_name varchar(255) NOT NULL,
            trigger_field varchar(255) NOT NULL,
            trigger_condition varchar(50) NOT NULL,
            trigger_value text,
            action_type varchar(50) NOT NULL,
            action_target varchar(255) NOT NULL,
            action_value text,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY multistep_form_id (multistep_form_id),
            KEY trigger_field (trigger_field)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_forms);
        dbDelta($sql_progress);
        dbDelta($sql_rules);
    }
    
    /**
     * Render multi-step form
     */
    public static function render_multistep_form($form_id, $multistep_config = array()) {
        global $wpdb;
        
        // Get form data
        $forms_table = $wpdb->prefix . 'dab_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id));
        
        if (!$form) {
            return '<div class="dab-error">' . __('Form not found', 'db-app-builder') . '</div>';
        }
        
        // Get form fields
        $fields_table = $wpdb->prefix . 'dab_form_fields';
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE form_id = %d ORDER BY field_order ASC",
            $form_id
        ));
        
        if (empty($fields)) {
            return '<div class="dab-error">' . __('No fields found for this form', 'db-app-builder') . '</div>';
        }
        
        // Get multi-step configuration
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $multistep_form = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $multistep_forms_table WHERE form_id = %d AND is_active = 1",
            $form_id
        ));
        
        if (!$multistep_form) {
            // Create default multi-step configuration
            $multistep_form = self::create_default_multistep_config($form_id, $fields);
        }
        
        $steps_config = json_decode($multistep_form->steps_config, true);
        $conditional_rules = json_decode($multistep_form->conditional_rules, true);
        $settings = json_decode($multistep_form->settings, true);
        
        // Get user progress
        $progress = self::get_user_progress($multistep_form->id);
        
        ob_start();
        
        echo '<div class="dab-multistep-form-container" data-form-id="' . $form_id . '" data-multistep-id="' . $multistep_form->id . '">';
        
        // Form header
        echo '<div class="dab-multistep-header">';
        if (!empty($form->form_title)) {
            echo '<h2 class="dab-form-title">' . esc_html($form->form_title) . '</h2>';
        }
        if (!empty($form->form_description)) {
            echo '<div class="dab-form-description">' . wp_kses_post($form->form_description) . '</div>';
        }
        echo '</div>';
        
        // Progress indicator
        self::render_progress_indicator($steps_config, $progress['current_step']);
        
        // Form steps
        echo '<form class="dab-multistep-form" id="dab-multistep-form-' . $form_id . '" method="post" enctype="multipart/form-data">';
        
        // Hidden fields
        echo '<input type="hidden" name="dab_multistep_form_id" value="' . $multistep_form->id . '">';
        echo '<input type="hidden" name="dab_form_id" value="' . $form_id . '">';
        echo '<input type="hidden" name="dab_current_step" id="dab_current_step" value="' . $progress['current_step'] . '">';
        echo wp_nonce_field('dab_multistep_form_submit', 'dab_multistep_nonce', true, false);
        
        // Render form steps
        foreach ($steps_config as $step_index => $step) {
            $step_number = $step_index + 1;
            $is_active = ($step_number == $progress['current_step']);
            $is_completed = in_array($step_number, $progress['completed_steps']);
            
            echo '<div class="dab-form-step" data-step="' . $step_number . '" style="display: ' . ($is_active ? 'block' : 'none') . '">';
            
            // Step header
            echo '<div class="dab-step-header">';
            echo '<h3 class="dab-step-title">' . esc_html($step['title']) . '</h3>';
            if (!empty($step['description'])) {
                echo '<p class="dab-step-description">' . esc_html($step['description']) . '</p>';
            }
            echo '</div>';
            
            // Step fields
            echo '<div class="dab-step-fields">';
            foreach ($step['fields'] as $field_id) {
                $field = self::get_field_by_id($fields, $field_id);
                if ($field) {
                    self::render_form_field($field, $progress['form_data']);
                }
            }
            echo '</div>';
            
            echo '</div>';
        }
        
        // Form navigation
        self::render_form_navigation($steps_config, $progress['current_step']);
        
        echo '</form>';
        
        // Save progress button
        echo '<div class="dab-form-actions">';
        echo '<button type="button" class="dab-btn dab-btn-secondary dab-save-progress">';
        echo '<span class="dashicons dashicons-cloud"></span> ' . __('Save Progress', 'db-app-builder');
        echo '</button>';
        echo '</div>';
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * Render progress indicator
     */
    private static function render_progress_indicator($steps_config, $current_step) {
        echo '<div class="dab-progress-indicator">';
        echo '<div class="dab-progress-bar">';
        
        $total_steps = count($steps_config);
        $progress_percentage = (($current_step - 1) / $total_steps) * 100;
        
        echo '<div class="dab-progress-fill" style="width: ' . $progress_percentage . '%"></div>';
        echo '</div>';
        
        echo '<div class="dab-progress-steps">';
        foreach ($steps_config as $step_index => $step) {
            $step_number = $step_index + 1;
            $is_current = ($step_number == $current_step);
            $is_completed = ($step_number < $current_step);
            
            $step_class = 'dab-progress-step';
            if ($is_current) $step_class .= ' current';
            if ($is_completed) $step_class .= ' completed';
            
            echo '<div class="' . $step_class . '" data-step="' . $step_number . '">';
            echo '<div class="dab-step-number">';
            if ($is_completed) {
                echo '<span class="dashicons dashicons-yes"></span>';
            } else {
                echo $step_number;
            }
            echo '</div>';
            echo '<div class="dab-step-label">' . esc_html($step['title']) . '</div>';
            echo '</div>';
        }
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Render form navigation
     */
    private static function render_form_navigation($steps_config, $current_step) {
        $total_steps = count($steps_config);
        
        echo '<div class="dab-form-navigation">';
        
        // Previous button
        if ($current_step > 1) {
            echo '<button type="button" class="dab-btn dab-btn-secondary dab-prev-step">';
            echo '<span class="dashicons dashicons-arrow-left-alt2"></span> ' . __('Previous', 'db-app-builder');
            echo '</button>';
        }
        
        // Next/Submit button
        if ($current_step < $total_steps) {
            echo '<button type="button" class="dab-btn dab-btn-primary dab-next-step">';
            echo __('Next', 'db-app-builder') . ' <span class="dashicons dashicons-arrow-right-alt2"></span>';
            echo '</button>';
        } else {
            echo '<button type="submit" class="dab-btn dab-btn-success dab-submit-form">';
            echo '<span class="dashicons dashicons-yes"></span> ' . __('Submit', 'db-app-builder');
            echo '</button>';
        }
        
        echo '</div>';
    }
    
    /**
     * Render a form field
     */
    private static function render_form_field($field, $form_data = array()) {
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $field_label = esc_html($field->field_label);
        $required = !empty($field->required) ? ' required' : '';
        $required_mark = !empty($field->required) ? ' <span class="dab-required">*</span>' : '';
        $placeholder = !empty($field->placeholder) ? esc_attr($field->placeholder) : '';
        $value = isset($form_data[$field_name]) ? $form_data[$field_name] : '';
        
        echo '<div class="dab-form-field dab-field-type-' . esc_attr($field->field_type) . '" data-field="' . $field_name . '">';
        
        // Label
        echo '<label for="' . $field_id . '">' . $field_label . $required_mark . '</label>';
        
        // Field description
        if (!empty($field->field_description)) {
            echo '<div class="dab-field-description">' . wp_kses_post($field->field_description) . '</div>';
        }
        
        // Field input
        switch ($field->field_type) {
            case 'text':
            case 'email':
            case 'url':
                echo '<input type="' . $field->field_type . '" id="' . $field_id . '" name="' . $field_name . '" value="' . esc_attr($value) . '" placeholder="' . $placeholder . '" class="dab-form-control"' . $required . '>';
                break;
                
            case 'textarea':
                echo '<textarea id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '" class="dab-form-control" rows="4"' . $required . '>' . esc_textarea($value) . '</textarea>';
                break;
                
            case 'number':
                $min = !empty($field->field_options['min']) ? ' min="' . esc_attr($field->field_options['min']) . '"' : '';
                $max = !empty($field->field_options['max']) ? ' max="' . esc_attr($field->field_options['max']) . '"' : '';
                echo '<input type="number" id="' . $field_id . '" name="' . $field_name . '" value="' . esc_attr($value) . '" placeholder="' . $placeholder . '" class="dab-form-control"' . $required . $min . $max . '>';
                break;
                
            case 'date':
                echo '<input type="date" id="' . $field_id . '" name="' . $field_name . '" value="' . esc_attr($value) . '" class="dab-form-control"' . $required . '>';
                break;
                
            case 'select':
                echo '<select id="' . $field_id . '" name="' . $field_name . '" class="dab-form-control"' . $required . '>';
                if (!$required) {
                    echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';
                }
                
                $options = !empty($field->field_options['options']) ? $field->field_options['options'] : array();
                foreach ($options as $option) {
                    $selected = ($value == $option['value']) ? ' selected' : '';
                    echo '<option value="' . esc_attr($option['value']) . '"' . $selected . '>' . esc_html($option['label']) . '</option>';
                }
                echo '</select>';
                break;
                
            case 'radio':
                $options = !empty($field->field_options['options']) ? $field->field_options['options'] : array();
                foreach ($options as $option) {
                    $checked = ($value == $option['value']) ? ' checked' : '';
                    echo '<label class="dab-radio-label">';
                    echo '<input type="radio" name="' . $field_name . '" value="' . esc_attr($option['value']) . '"' . $checked . $required . '>';
                    echo ' ' . esc_html($option['label']);
                    echo '</label>';
                }
                break;
                
            case 'checkbox':
                $checked = !empty($value) ? ' checked' : '';
                echo '<label class="dab-checkbox-label">';
                echo '<input type="checkbox" id="' . $field_id . '" name="' . $field_name . '" value="1"' . $checked . $required . '>';
                echo ' ' . $field_label;
                echo '</label>';
                break;
                
            default:
                // Handle custom field types
                do_action('dab_render_custom_field_type', $field, $value);
                break;
        }
        
        // Field validation message
        echo '<div class="dab-field-error" style="display: none;"></div>';
        
        echo '</div>';
    }
    
    /**
     * Get field by ID from fields array
     */
    private static function get_field_by_id($fields, $field_id) {
        foreach ($fields as $field) {
            if ($field->id == $field_id) {
                return $field;
            }
        }
        return null;
    }
    
    /**
     * Create default multi-step configuration
     */
    private static function create_default_multistep_config($form_id, $fields) {
        // Group fields into steps (max 5 fields per step)
        $steps = array();
        $current_step = array(
            'title' => __('Step 1', 'db-app-builder'),
            'description' => '',
            'fields' => array()
        );
        
        $field_count = 0;
        foreach ($fields as $field) {
            if ($field_count >= 5) {
                $steps[] = $current_step;
                $step_number = count($steps) + 1;
                $current_step = array(
                    'title' => sprintf(__('Step %d', 'db-app-builder'), $step_number),
                    'description' => '',
                    'fields' => array()
                );
                $field_count = 0;
            }
            
            $current_step['fields'][] = $field->id;
            $field_count++;
        }
        
        if (!empty($current_step['fields'])) {
            $steps[] = $current_step;
        }
        
        // Create multi-step form record
        global $wpdb;
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        
        $data = array(
            'form_id' => $form_id,
            'title' => __('Multi-Step Form', 'db-app-builder'),
            'description' => '',
            'steps_config' => json_encode($steps),
            'conditional_rules' => json_encode(array()),
            'settings' => json_encode(array(
                'save_progress' => true,
                'show_progress' => true,
                'allow_back' => true
            )),
            'created_by' => get_current_user_id()
        );
        
        $wpdb->insert($multistep_forms_table, $data);
        
        return (object) array(
            'id' => $wpdb->insert_id,
            'form_id' => $form_id,
            'steps_config' => json_encode($steps),
            'conditional_rules' => json_encode(array()),
            'settings' => json_encode($data['settings'])
        );
    }
    
    /**
     * Get user progress for a multi-step form
     */
    private static function get_user_progress($multistep_form_id) {
        global $wpdb;
        
        $progress_table = $wpdb->prefix . 'dab_form_progress';
        $user_id = get_current_user_id();
        $session_id = session_id();
        
        if ($user_id > 0) {
            $progress = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $progress_table WHERE multistep_form_id = %d AND user_id = %d ORDER BY updated_at DESC LIMIT 1",
                $multistep_form_id, $user_id
            ));
        } else {
            $progress = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $progress_table WHERE multistep_form_id = %d AND session_id = %s ORDER BY updated_at DESC LIMIT 1",
                $multistep_form_id, $session_id
            ));
        }
        
        if ($progress) {
            return array(
                'current_step' => $progress->current_step,
                'form_data' => json_decode($progress->form_data, true),
                'completed_steps' => explode(',', $progress->completed_steps),
                'is_completed' => $progress->is_completed
            );
        }
        
        return array(
            'current_step' => 1,
            'form_data' => array(),
            'completed_steps' => array(),
            'is_completed' => false
        );
    }
    
    /**
     * Shortcode for rendering multi-step forms
     */
    public static function render_multistep_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'form_id' => 0
        ), $atts);
        
        $form_id = intval($atts['form_id']) ?: intval($atts['id']);
        
        if (!$form_id) {
            return '<div class="dab-error">' . __('Form ID is required', 'db-app-builder') . '</div>';
        }
        
        return self::render_multistep_form($form_id);
    }
    
    /**
     * AJAX handler to save form progress
     */
    public static function save_form_progress() {
        check_ajax_referer('dab_form_wizard_nonce', 'nonce');
        
        $multistep_form_id = intval($_POST['multistep_form_id']);
        $current_step = intval($_POST['current_step']);
        $form_data = $_POST['form_data'];
        $completed_steps = $_POST['completed_steps'];
        
        global $wpdb;
        $progress_table = $wpdb->prefix . 'dab_form_progress';
        
        $user_id = get_current_user_id();
        $session_id = session_id();
        
        $data = array(
            'multistep_form_id' => $multistep_form_id,
            'current_step' => $current_step,
            'form_data' => json_encode($form_data),
            'completed_steps' => implode(',', $completed_steps)
        );
        
        if ($user_id > 0) {
            $data['user_id'] = $user_id;
            $existing = $wpdb->get_row($wpdb->prepare(
                "SELECT id FROM $progress_table WHERE multistep_form_id = %d AND user_id = %d",
                $multistep_form_id, $user_id
            ));
        } else {
            $data['session_id'] = $session_id;
            $existing = $wpdb->get_row($wpdb->prepare(
                "SELECT id FROM $progress_table WHERE multistep_form_id = %d AND session_id = %s",
                $multistep_form_id, $session_id
            ));
        }
        
        if ($existing) {
            $result = $wpdb->update($progress_table, $data, array('id' => $existing->id));
        } else {
            $result = $wpdb->insert($progress_table, $data);
        }
        
        if ($result !== false) {
            wp_send_json_success(__('Progress saved successfully', 'db-app-builder'));
        } else {
            wp_send_json_error(__('Error saving progress', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to get form progress
     */
    public static function get_form_progress() {
        check_ajax_referer('dab_form_wizard_nonce', 'nonce');
        
        $multistep_form_id = intval($_POST['multistep_form_id']);
        $progress = self::get_user_progress($multistep_form_id);
        
        wp_send_json_success($progress);
    }
    
    /**
     * AJAX handler to validate form step
     */
    public static function validate_form_step() {
        check_ajax_referer('dab_form_wizard_nonce', 'nonce');
        
        $multistep_form_id = intval($_POST['multistep_form_id']);
        $step_number = intval($_POST['step_number']);
        $step_data = $_POST['step_data'];
        
        // Perform validation logic here
        $errors = array();
        
        // Basic validation example
        foreach ($step_data as $field_name => $value) {
            if (empty($value) && isset($_POST['required_fields']) && in_array($field_name, $_POST['required_fields'])) {
                $errors[$field_name] = __('This field is required', 'db-app-builder');
            }
        }
        
        if (empty($errors)) {
            wp_send_json_success(__('Step validation passed', 'db-app-builder'));
        } else {
            wp_send_json_error($errors);
        }
    }
}
