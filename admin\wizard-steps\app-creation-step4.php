<?php
/**
 * Application Creation Wizard - Step 4: Form Creation
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get saved data
$table_id = isset($progress['data']['table_id']) ? intval($progress['data']['table_id']) : 0;

// Check if table exists
$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$forms_table = $wpdb->prefix . 'dab_forms';

$table_info = null;
if ($table_id) {
    $table_info = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $table_id
    ));
}

// Get fields for this table
$fields = [];
if ($table_id) {
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
        $table_id
    ));
}

// Get existing forms for this table
$forms = [];
if ($table_id) {
    $forms = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $forms_table WHERE table_id = %d",
        $table_id
    ));
}

// Process form creation if form was submitted via AJAX
if (isset($_POST['dab_create_form']) && $_POST['dab_create_form'] === '1' && $table_id) {
    $form_name = sanitize_text_field($_POST['form_name']);
    $selected_fields = isset($_POST['selected_fields']) ? $_POST['selected_fields'] : [];
    $notify_email = sanitize_email($_POST['notify_email'] ?? '');
    $notify_message = sanitize_textarea_field($_POST['notify_message'] ?? '');

    // Insert the form
    $wpdb->insert($forms_table, [
        'form_name' => $form_name,
        'table_id' => $table_id,
        'fields' => maybe_serialize($selected_fields),
        'notify_email' => $notify_email,
        'notify_message' => $notify_message,
        'created_at' => current_time('mysql')
    ]);

    $form_id = $wpdb->insert_id;

    // Store the form ID in the progress data
    $progress['data']['form_id'] = $form_id;
    update_option('dab_wizard_progress', [$wizard_type => $progress]);

    // Refresh forms
    $forms = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $forms_table WHERE table_id = %d",
        $table_id
    ));
}
?>

<div class="dab-wizard-form">
    <?php if (!$table_id || !$table_info): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Please go back and create a table with fields first.', 'db-app-builder'); ?></p>
        </div>
    <?php elseif (empty($fields)): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Please go back and add fields to your table first.', 'db-app-builder'); ?></p>
        </div>
    <?php else: ?>
        <div class="dab-wizard-section">
            <h3><?php _e('Table Information', 'db-app-builder'); ?></h3>
            <div class="dab-wizard-table-info">
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Table Name:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo esc_html($table_info->table_label); ?></span>
                </div>
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Fields Count:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo count($fields); ?></span>
                </div>
            </div>
        </div>

        <div class="dab-wizard-section">
            <h3><?php _e('Existing Forms', 'db-app-builder'); ?></h3>
            <?php if (empty($forms)): ?>
                <p><?php _e('No forms have been created yet.', 'db-app-builder'); ?></p>
            <?php else: ?>
                <div class="dab-wizard-forms-list">
                    <table class="dab-wizard-table">
                        <thead>
                            <tr>
                                <th><?php _e('Form Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Fields', 'db-app-builder'); ?></th>
                                <th><?php _e('Created', 'db-app-builder'); ?></th>
                                <th><?php _e('Shortcode', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($forms as $form):
                                $form_fields = maybe_unserialize($form->fields);
                                $field_count = is_array($form_fields) ? count($form_fields) : 0;
                            ?>
                                <tr>
                                    <td><?php echo esc_html($form->form_name); ?></td>
                                    <td><?php echo esc_html($field_count); ?></td>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($form->created_at))); ?></td>
                                    <td><code>[dab_form id="<?php echo esc_attr($form->id); ?>"]</code></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <div class="dab-wizard-section">
            <h3><?php _e('Create New Form', 'db-app-builder'); ?></h3>
            <form id="dab-create-form" method="post" class="dab-wizard-form-standard">
                <input type="hidden" name="dab_create_form" value="1">
                <input type="hidden" name="table_id" value="<?php echo esc_attr($table_id); ?>">

                <div class="dab-wizard-form-group">
                    <label for="form_name" class="dab-wizard-form-label"><?php _e('Form Name', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="text" id="form_name" name="form_name" class="dab-wizard-form-input" required>
                    <p class="dab-wizard-form-help"><?php _e('Enter a descriptive name for your form.', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-wizard-form-group">
                    <label class="dab-wizard-form-label"><?php _e('Select Fields', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <div class="dab-wizard-field-selector">
                        <?php foreach ($fields as $field): ?>
                            <div class="dab-wizard-field-option">
                                <input type="checkbox" id="field_<?php echo esc_attr($field->id); ?>" name="selected_fields[]" value="<?php echo esc_attr($field->field_slug); ?>" checked>
                                <label for="field_<?php echo esc_attr($field->id); ?>"><?php echo esc_html($field->field_label); ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <p class="dab-wizard-form-help"><?php _e('Select which fields to include in your form.', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-wizard-form-group">
                    <label for="notify_email" class="dab-wizard-form-label"><?php _e('Notification Email', 'db-app-builder'); ?></label>
                    <input type="email" id="notify_email" name="notify_email" class="dab-wizard-form-input">
                    <p class="dab-wizard-form-help"><?php _e('Enter an email address to receive notifications when the form is submitted.', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-wizard-form-group">
                    <label for="notify_message" class="dab-wizard-form-label"><?php _e('Notification Message', 'db-app-builder'); ?></label>
                    <textarea id="notify_message" name="notify_message" class="dab-wizard-form-textarea" rows="4"></textarea>
                    <p class="dab-wizard-form-help"><?php _e('Enter a custom message to include in the notification email.', 'db-app-builder'); ?></p>
                </div>

                <div class="dab-wizard-form-actions">
                    <button type="submit" class="dab-btn dab-btn-primary"><?php _e('Create Form', 'db-app-builder'); ?></button>
                </div>
            </form>
        </div>

        <div class="dab-wizard-notice dab-wizard-notice-info">
            <p><?php _e('Once you have created your form, click "Next" to continue to the data view setup.', 'db-app-builder'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.dab-wizard-notice-info {
    background-color: #e5f5fa;
    border-left: 4px solid #00a0d2;
}

.dab-wizard-notice-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
}

.dab-wizard-section {
    margin-bottom: 30px;
}

.dab-wizard-table-info {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.dab-wizard-table-detail {
    margin-bottom: 10px;
}

.dab-wizard-table-detail-label {
    font-weight: 600;
    margin-right: 10px;
}

.dab-wizard-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.dab-wizard-table th,
.dab-wizard-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dab-wizard-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.dab-wizard-form-standard {
    max-width: 800px;
}

.dab-wizard-form-group {
    margin-bottom: 20px;
}

.dab-wizard-field-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.dab-wizard-field-option {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dab-wizard-form-actions {
    margin-top: 20px;
}

.dab-spin {
    animation: dab-spin 2s infinite linear;
}

@keyframes dab-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dab-wizard-notice-success {
    background-color: #ecf9ec;
    border-left: 4px solid #46b450;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Handle form submission via AJAX
    $('#dab-create-form').on('submit', function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        const $form = $(this);
        const $submitButton = $form.find('button[type="submit"]');

        // Disable submit button and show loading state
        $submitButton.prop('disabled', true).html('<span class="dashicons dashicons-update-alt dab-spin"></span> Creating...');

        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            success: function(response) {
                // Show success message before reload
                const $successMessage = $('<div class="dab-wizard-notice dab-wizard-notice-success">' +
                    '<p>Form created successfully!</p>' +
                    '</div>');

                // Add the success message after the form
                $form.after($successMessage);

                // Reset form changed flag to prevent "Changes you made may not be saved" message
                window.wizardFormChanged = false;

                // Trigger custom event for form submission
                $(document).trigger('dab:form:submitted', ['dab-create-form']);

                // Reload the page after a short delay to show the new form
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);

                // Show error message
                alert('Error creating form. Please try again.');

                // Re-enable submit button
                $submitButton.prop('disabled', false).text('Create Form');
            }
        });
    });
});
</script>
