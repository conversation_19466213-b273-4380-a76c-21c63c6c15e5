<?php
/**
 * Application Creation Wizard - Step 5: Data View
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

// Get saved data
$table_id = isset($progress['data']['table_id']) ? intval($progress['data']['table_id']) : 0;

// Check if table exists
$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$views_table = $wpdb->prefix . 'dab_views';

$table_info = null;
if ($table_id) {
    $table_info = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $table_id
    ));
}

// Get fields for this table
$fields = [];
if ($table_id) {
    $fields = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC",
        $table_id
    ));
}

// Get existing views for this table
$views = [];
if ($table_id) {
    $views = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $views_table WHERE table_id = %d",
        $table_id
    ));
}

// Process view creation if form was submitted via AJAX
if (isset($_POST['dab_create_view']) && $_POST['dab_create_view'] === '1' && $table_id) {
    $view_name = sanitize_text_field($_POST['view_name']);
    $visible_fields = isset($_POST['visible_fields']) ? $_POST['visible_fields'] : [];
    $sort_order = sanitize_text_field($_POST['sort_order'] ?? '');
    $is_public = isset($_POST['is_public']) ? 1 : 0;
    
    // Insert the view
    $wpdb->insert($views_table, [
        'view_name' => $view_name,
        'table_id' => $table_id,
        'selected_fields' => maybe_serialize($visible_fields),
        'sort_order' => $sort_order,
        'is_public' => $is_public,
        'created_at' => current_time('mysql')
    ]);
    
    $view_id = $wpdb->insert_id;
    
    // Store the view ID in the progress data
    $progress['data']['view_id'] = $view_id;
    update_option('dab_wizard_progress', [$wizard_type => $progress]);
    
    // Refresh views
    $views = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $views_table WHERE table_id = %d",
        $table_id
    ));
}
?>

<div class="dab-wizard-form">
    <?php if (!$table_id || !$table_info): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Please go back and create a table with fields first.', 'db-app-builder'); ?></p>
        </div>
    <?php elseif (empty($fields)): ?>
        <div class="dab-wizard-notice dab-wizard-notice-warning">
            <p><?php _e('Please go back and add fields to your table first.', 'db-app-builder'); ?></p>
        </div>
    <?php else: ?>
        <div class="dab-wizard-section">
            <h3><?php _e('Table Information', 'db-app-builder'); ?></h3>
            <div class="dab-wizard-table-info">
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Table Name:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo esc_html($table_info->table_label); ?></span>
                </div>
                <div class="dab-wizard-table-detail">
                    <span class="dab-wizard-table-detail-label"><?php _e('Fields Count:', 'db-app-builder'); ?></span>
                    <span class="dab-wizard-table-detail-value"><?php echo count($fields); ?></span>
                </div>
            </div>
        </div>
        
        <div class="dab-wizard-section">
            <h3><?php _e('Existing Views', 'db-app-builder'); ?></h3>
            <?php if (empty($views)): ?>
                <p><?php _e('No views have been created yet.', 'db-app-builder'); ?></p>
            <?php else: ?>
                <div class="dab-wizard-views-list">
                    <table class="dab-wizard-table">
                        <thead>
                            <tr>
                                <th><?php _e('View Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Fields', 'db-app-builder'); ?></th>
                                <th><?php _e('Public', 'db-app-builder'); ?></th>
                                <th><?php _e('Created', 'db-app-builder'); ?></th>
                                <th><?php _e('Shortcode', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($views as $view): 
                                $view_fields = maybe_unserialize($view->selected_fields);
                                $field_count = is_array($view_fields) ? count($view_fields) : 0;
                            ?>
                                <tr>
                                    <td><?php echo esc_html($view->view_name); ?></td>
                                    <td><?php echo esc_html($field_count); ?></td>
                                    <td><?php echo $view->is_public ? '<span class="dashicons dashicons-yes"></span>' : '<span class="dashicons dashicons-no"></span>'; ?></td>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($view->created_at))); ?></td>
                                    <td><code>[dab_view id="<?php echo esc_attr($view->id); ?>"]</code></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="dab-wizard-section">
            <h3><?php _e('Create New View', 'db-app-builder'); ?></h3>
            <form id="dab-create-view" method="post" class="dab-wizard-form-standard">
                <input type="hidden" name="dab_create_view" value="1">
                <input type="hidden" name="table_id" value="<?php echo esc_attr($table_id); ?>">
                
                <div class="dab-wizard-form-group">
                    <label for="view_name" class="dab-wizard-form-label"><?php _e('View Name', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <input type="text" id="view_name" name="view_name" class="dab-wizard-form-input" required>
                    <p class="dab-wizard-form-help"><?php _e('Enter a descriptive name for your view.', 'db-app-builder'); ?></p>
                </div>
                
                <div class="dab-wizard-form-group">
                    <label class="dab-wizard-form-label"><?php _e('Visible Fields', 'db-app-builder'); ?> <span class="required">*</span></label>
                    <div class="dab-wizard-field-selector">
                        <?php foreach ($fields as $field): ?>
                            <div class="dab-wizard-field-option">
                                <input type="checkbox" id="field_<?php echo esc_attr($field->id); ?>" name="visible_fields[]" value="<?php echo esc_attr($field->field_slug); ?>" checked>
                                <label for="field_<?php echo esc_attr($field->id); ?>"><?php echo esc_html($field->field_label); ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <p class="dab-wizard-form-help"><?php _e('Select which fields to display in your view.', 'db-app-builder'); ?></p>
                </div>
                
                <div class="dab-wizard-form-group">
                    <label for="sort_order" class="dab-wizard-form-label"><?php _e('Sort Order', 'db-app-builder'); ?></label>
                    <select id="sort_order" name="sort_order" class="dab-wizard-form-select">
                        <option value=""><?php _e('Default (ID Ascending)', 'db-app-builder'); ?></option>
                        <option value="id DESC"><?php _e('ID Descending', 'db-app-builder'); ?></option>
                        <option value="created_at ASC"><?php _e('Date Created (Oldest First)', 'db-app-builder'); ?></option>
                        <option value="created_at DESC"><?php _e('Date Created (Newest First)', 'db-app-builder'); ?></option>
                        <?php foreach ($fields as $field): ?>
                            <option value="<?php echo esc_attr($field->field_slug); ?> ASC"><?php echo esc_html($field->field_label); ?> (<?php _e('Ascending', 'db-app-builder'); ?>)</option>
                            <option value="<?php echo esc_attr($field->field_slug); ?> DESC"><?php echo esc_html($field->field_label); ?> (<?php _e('Descending', 'db-app-builder'); ?>)</option>
                        <?php endforeach; ?>
                    </select>
                    <p class="dab-wizard-form-help"><?php _e('Choose how records should be sorted in the view.', 'db-app-builder'); ?></p>
                </div>
                
                <div class="dab-wizard-form-group dab-wizard-form-checkbox">
                    <input type="checkbox" id="is_public" name="is_public" value="1">
                    <label for="is_public" class="dab-wizard-form-label"><?php _e('Make View Public', 'db-app-builder'); ?></label>
                    <p class="dab-wizard-form-help"><?php _e('If checked, the view will be accessible to non-logged-in users.', 'db-app-builder'); ?></p>
                </div>
                
                <div class="dab-wizard-form-actions">
                    <button type="submit" class="dab-btn dab-btn-primary"><?php _e('Create View', 'db-app-builder'); ?></button>
                </div>
            </form>
        </div>
        
        <div class="dab-wizard-notice dab-wizard-notice-info">
            <p><?php _e('Once you have created your view, click "Next" to complete the application setup.', 'db-app-builder'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.dab-wizard-notice {
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.dab-wizard-notice-info {
    background-color: #e5f5fa;
    border-left: 4px solid #00a0d2;
}

.dab-wizard-notice-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
}

.dab-wizard-section {
    margin-bottom: 30px;
}

.dab-wizard-table-info {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.dab-wizard-table-detail {
    margin-bottom: 10px;
}

.dab-wizard-table-detail-label {
    font-weight: 600;
    margin-right: 10px;
}

.dab-wizard-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.dab-wizard-table th,
.dab-wizard-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dab-wizard-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.dab-wizard-form-standard {
    max-width: 800px;
}

.dab-wizard-form-group {
    margin-bottom: 20px;
}

.dab-wizard-field-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.dab-wizard-field-option {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dab-wizard-form-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.dab-wizard-form-checkbox input {
    margin-top: 5px;
}

.dab-wizard-form-actions {
    margin-top: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Handle form submission via AJAX
    $('#dab-create-view').on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            success: function(response) {
                // Reload the page to show the new view
                window.location.reload();
            }
        });
    });
});
</script>
