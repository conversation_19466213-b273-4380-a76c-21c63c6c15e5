<?php
/**
 * Integrations Settings Page
 *
 * Handles integration settings for external services
 */
if (!defined('ABSPATH')) exit;

// Check if form is submitted
if (isset($_POST['dab_save_google_sheets_settings'])) {
    // Verify nonce
    if (!isset($_POST['dab_google_sheets_nonce']) || !wp_verify_nonce($_POST['dab_google_sheets_nonce'], 'dab_google_sheets_settings')) {
        echo '<div class="notice notice-error is-dismissible"><p>' . __('Security check failed', 'db-app-builder') . '</p></div>';
    } else {
        // Save Google Sheets settings
        if (isset($_POST['google_sheets_client_id'])) {
            DAB_Settings_Manager::set('google_sheets_client_id', sanitize_text_field($_POST['google_sheets_client_id']));
        }
        if (isset($_POST['google_sheets_client_secret'])) {
            DAB_Settings_Manager::set('google_sheets_client_secret', sanitize_text_field($_POST['google_sheets_client_secret']));
        }
        if (isset($_POST['google_sheets_api_key'])) {
            DAB_Settings_Manager::set('google_sheets_api_key', sanitize_text_field($_POST['google_sheets_api_key']));
        }
        if (isset($_POST['google_sheets_refresh_token'])) {
            DAB_Settings_Manager::set('google_sheets_refresh_token', sanitize_text_field($_POST['google_sheets_refresh_token']));
        }

        echo '<div class="notice notice-success is-dismissible"><p>' . __('Integration settings saved successfully!', 'db-app-builder') . '</p></div>';
    }
}

// Load current settings
$google_sheets_client_id = DAB_Settings_Manager::get('google_sheets_client_id', '');
$google_sheets_client_secret = DAB_Settings_Manager::get('google_sheets_client_secret', '');
$google_sheets_api_key = DAB_Settings_Manager::get('google_sheets_api_key', '');
$google_sheets_refresh_token = DAB_Settings_Manager::get('google_sheets_refresh_token', '');

// Get active tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'google_sheets';
?>

<div class="wrap dab-admin-wrap">
    <h1><?php _e('Integration Settings', 'db-app-builder'); ?></h1>

    <p class="description"><?php _e('Configure integrations with external services.', 'db-app-builder'); ?></p>

    <h2 class="nav-tab-wrapper">
        <a href="?page=dab_integrations&tab=google_sheets" class="nav-tab <?php echo $active_tab === 'google_sheets' ? 'nav-tab-active' : ''; ?>"><?php _e('Google Sheets', 'db-app-builder'); ?></a>
        <a href="?page=dab_integrations&tab=zapier" class="nav-tab <?php echo $active_tab === 'zapier' ? 'nav-tab-active' : ''; ?>"><?php _e('Zapier', 'db-app-builder'); ?></a>
        <!-- Add more tabs for future integrations -->
    </h2>

    <?php if ($active_tab === 'google_sheets'): ?>
        <div id="google-sheets-settings" class="dab-settings-tab">
            <h3><?php _e('Google Sheets Integration', 'db-app-builder'); ?></h3>

            <p class="description">
                <?php _e('Connect your forms to Google Sheets to automatically send form submissions to a spreadsheet.', 'db-app-builder'); ?>
            </p>

            <div class="dab-setup-guide">
                <h4><?php _e('Setup Guide', 'db-app-builder'); ?></h4>
                <ol>
                    <li><?php _e('Go to the <a href="https://console.cloud.google.com/apis/dashboard" target="_blank">Google Cloud Console</a> and create a new project.', 'db-app-builder'); ?></li>
                    <li><?php _e('Enable the Google Sheets API for your project.', 'db-app-builder'); ?></li>
                    <li><?php _e('Create OAuth 2.0 credentials (Client ID and Client Secret).', 'db-app-builder'); ?></li>
                    <li><?php _e('Create an API Key.', 'db-app-builder'); ?></li>
                    <li><?php _e('Set up the OAuth consent screen.', 'db-app-builder'); ?></li>
                    <li><?php _e('Use the authorization URL below to get a refresh token.', 'db-app-builder'); ?></li>
                </ol>
            </div>

            <form method="post" action="">
                <?php wp_nonce_field('dab_google_sheets_settings', 'dab_google_sheets_nonce'); ?>

                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Client ID', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="google_sheets_client_id" value="<?php echo esc_attr($google_sheets_client_id); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Google OAuth 2.0 Client ID.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>

                    <tr valign="top">
                        <th scope="row"><?php _e('Client Secret', 'db-app-builder'); ?></th>
                        <td>
                            <input type="password" name="google_sheets_client_secret" value="<?php echo esc_attr($google_sheets_client_secret); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Google OAuth 2.0 Client Secret.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>

                    <tr valign="top">
                        <th scope="row"><?php _e('API Key', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="google_sheets_api_key" value="<?php echo esc_attr($google_sheets_api_key); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Google API Key.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>

                    <tr valign="top">
                        <th scope="row"><?php _e('Refresh Token', 'db-app-builder'); ?></th>
                        <td>
                            <input type="text" name="google_sheets_refresh_token" value="<?php echo esc_attr($google_sheets_refresh_token); ?>" class="regular-text">
                            <p class="description"><?php _e('Enter your Google OAuth 2.0 Refresh Token.', 'db-app-builder'); ?></p>
                        </td>
                    </tr>
                </table>

                <p><input type="submit" name="dab_save_google_sheets_settings" class="button-primary" value="<?php _e('Save Settings', 'db-app-builder'); ?>"></p>
            </form>

            <?php if (!empty($google_sheets_client_id) && !empty($google_sheets_client_secret)): ?>
                <div class="card" style="max-width: 600px; margin-top: 20px;">
                    <h3><?php _e('Authorization URL', 'db-app-builder'); ?></h3>
                    <p><?php _e('Use this URL to authorize the application and get a refresh token:', 'db-app-builder'); ?></p>

                    <?php
                    $auth_url = 'https://accounts.google.com/o/oauth2/auth';
                    $auth_params = array(
                        'client_id' => $google_sheets_client_id,
                        'redirect_uri' => 'urn:ietf:wg:oauth:2.0:oob',
                        'response_type' => 'code',
                        'scope' => 'https://www.googleapis.com/auth/spreadsheets',
                        'access_type' => 'offline',
                        'prompt' => 'consent',
                    );
                    $auth_url .= '?' . http_build_query($auth_params);
                    ?>

                    <textarea class="large-text code" rows="3" readonly><?php echo esc_url($auth_url); ?></textarea>

                    <p><?php _e('After authorizing, you will receive a code. Use this code to get a refresh token:', 'db-app-builder'); ?></p>

                    <div class="dab-code-example">
                        <pre>curl -d "code=YOUR_AUTH_CODE&client_id=<?php echo esc_attr($google_sheets_client_id); ?>&client_secret=<?php echo esc_attr($google_sheets_client_secret); ?>&redirect_uri=urn:ietf:wg:oauth:2.0:oob&grant_type=authorization_code" https://oauth2.googleapis.com/token</pre>
                    </div>

                    <p><?php _e('The response will contain a refresh token. Copy this token and paste it in the Refresh Token field above.', 'db-app-builder'); ?></p>
                </div>
            <?php endif; ?>

            <?php if (class_exists('DAB_Google_Sheets_Integration') && DAB_Google_Sheets_Integration::is_configured()): ?>
                <div class="card" style="max-width: 600px; margin-top: 20px;">
                    <h3><?php _e('Test Connection', 'db-app-builder'); ?></h3>
                    <p><?php _e('Test your Google Sheets connection by adding a test row to a spreadsheet:', 'db-app-builder'); ?></p>

                    <form id="dab-test-google-sheets-connection">
                        <table class="form-table">
                            <tr valign="top">
                                <th scope="row"><?php _e('Spreadsheet ID', 'db-app-builder'); ?></th>
                                <td>
                                    <input type="text" name="spreadsheet_id" class="regular-text" required>
                                    <p class="description"><?php _e('Enter the ID of the spreadsheet (found in the URL).', 'db-app-builder'); ?></p>
                                </td>
                            </tr>

                            <tr valign="top">
                                <th scope="row"><?php _e('Worksheet Name', 'db-app-builder'); ?></th>
                                <td>
                                    <input type="text" name="worksheet_name" class="regular-text" placeholder="Sheet1">
                                    <p class="description"><?php _e('Enter the name of the worksheet (default: Sheet1).', 'db-app-builder'); ?></p>
                                </td>
                            </tr>
                        </table>

                        <p><button type="submit" class="button-secondary"><?php _e('Test Connection', 'db-app-builder'); ?></button></p>

                        <div id="dab-test-connection-result" style="display: none;"></div>
                    </form>

                    <script>
                    jQuery(document).ready(function($) {
                        $('#dab-test-google-sheets-connection').on('submit', function(e) {
                            e.preventDefault();

                            const spreadsheetId = $(this).find('input[name="spreadsheet_id"]').val();
                            const worksheetName = $(this).find('input[name="worksheet_name"]').val();

                            if (!spreadsheetId) {
                                alert('<?php _e('Please enter a spreadsheet ID', 'db-app-builder'); ?>');
                                return;
                            }

                            // Show loading
                            $('#dab-test-connection-result')
                                .html('<p><span class="spinner is-active"></span> <?php _e('Testing connection...', 'db-app-builder'); ?></p>')
                                .show();

                            // Send AJAX request
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'dab_test_google_sheets_connection',
                                    nonce: '<?php echo wp_create_nonce('dab_google_sheets_nonce'); ?>',
                                    spreadsheet_id: spreadsheetId,
                                    worksheet_name: worksheetName
                                },
                                success: function(response) {
                                    if (response.success) {
                                        $('#dab-test-connection-result')
                                            .html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                                    } else {
                                        $('#dab-test-connection-result')
                                            .html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                                    }
                                },
                                error: function() {
                                    $('#dab-test-connection-result')
                                        .html('<div class="notice notice-error"><p><?php _e('An error occurred while testing the connection.', 'db-app-builder'); ?></p></div>');
                                }
                            });
                        });
                    });
                    </script>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if ($active_tab === 'zapier'): ?>
        <div id="zapier-settings" class="dab-settings-tab">
            <h3><?php _e('Zapier Integration', 'db-app-builder'); ?></h3>

            <p class="description">
                <?php _e('Connect your forms to Zapier to automate workflows and integrate with thousands of apps.', 'db-app-builder'); ?>
            </p>

            <div class="dab-setup-guide">
                <h4><?php _e('How to Use Zapier Integration', 'db-app-builder'); ?></h4>
                <ol>
                    <li><?php _e('Go to <a href="https://zapier.com/" target="_blank">Zapier</a> and log in to your account.', 'db-app-builder'); ?></li>
                    <li><?php _e('Create a new Zap.', 'db-app-builder'); ?></li>
                    <li><?php _e('Choose "Webhooks by Zapier" as the trigger app.', 'db-app-builder'); ?></li>
                    <li><?php _e('Select "Catch Hook" as the trigger event.', 'db-app-builder'); ?></li>
                    <li><?php _e('Copy the webhook URL provided by Zapier.', 'db-app-builder'); ?></li>
                    <li><?php _e('Go to the form settings in Database App Builder and enable Zapier integration.', 'db-app-builder'); ?></li>
                    <li><?php _e('Paste the webhook URL and save the form.', 'db-app-builder'); ?></li>
                    <li><?php _e('Submit a test form to send data to Zapier.', 'db-app-builder'); ?></li>
                    <li><?php _e('Continue setting up your Zap in Zapier to connect with other apps.', 'db-app-builder'); ?></li>
                </ol>
            </div>

            <div class="card" style="max-width: 600px; margin-top: 20px;">
                <h4><?php _e('Test Your Zapier Integration', 'db-app-builder'); ?></h4>
                <p><?php _e('You can test your Zapier integration directly from the form edit page.', 'db-app-builder'); ?></p>
                <p><?php _e('Each webhook URL has a "Test" button that sends sample data to Zapier.', 'db-app-builder'); ?></p>

                <h4><?php _e('Zapier Integration Features', 'db-app-builder'); ?></h4>
                <ul>
                    <li><?php _e('Connect forms to thousands of apps through Zapier', 'db-app-builder'); ?></li>
                    <li><?php _e('Add multiple webhook URLs to a single form', 'db-app-builder'); ?></li>
                    <li><?php _e('Test webhooks directly from the form settings', 'db-app-builder'); ?></li>
                    <li><?php _e('Send all form field data to Zapier for use in your Zaps', 'db-app-builder'); ?></li>
                </ul>

                <h4><?php _e('Example Use Cases', 'db-app-builder'); ?></h4>
                <ul>
                    <li><?php _e('Add form submissions to a Google Sheet', 'db-app-builder'); ?></li>
                    <li><?php _e('Create tasks in Trello, Asana, or other project management tools', 'db-app-builder'); ?></li>
                    <li><?php _e('Add contacts to your CRM or email marketing platform', 'db-app-builder'); ?></li>
                    <li><?php _e('Send notifications to Slack or other messaging apps', 'db-app-builder'); ?></li>
                    <li><?php _e('Create calendar events from form submissions', 'db-app-builder'); ?></li>
                </ul>

                <p><a href="<?php echo admin_url('admin.php?page=dab_forms'); ?>" class="button button-primary"><?php _e('Go to Forms', 'db-app-builder'); ?></a></p>
            </div>
        </div>
    <?php endif; ?>
</div>
