/**
 * Timeline View CSS
 * 
 * Styles for the timeline field component
 */

.dab-timeline-view {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dab-timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.dab-timeline-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.dab-timeline-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-add-timeline-item {
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.dab-add-timeline-item:hover {
    background: #005a87;
}

.dab-timeline-container {
    padding: 20px;
    position: relative;
}

.dab-timeline {
    position: relative;
    padding-left: 30px;
}

.dab-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.dab-timeline-item {
    position: relative;
    margin-bottom: 30px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-left: 20px;
    transition: all 0.2s ease;
}

.dab-timeline-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #ccc;
}

.dab-timeline-item::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #0073aa;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #ddd;
}

.dab-timeline-item.completed::before {
    background: #4caf50;
}

.dab-timeline-item.in-progress::before {
    background: #ff9800;
}

.dab-timeline-item.pending::before {
    background: #f44336;
}

.dab-timeline-item.milestone::before {
    background: #9c27b0;
    transform: rotate(45deg);
    border-radius: 0;
}

.dab-timeline-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.dab-timeline-item-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex: 1;
}

.dab-timeline-item-date {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
    margin-left: 10px;
}

.dab-timeline-item-status {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-left: 10px;
}

.dab-timeline-item-status.completed {
    background: #e8f5e8;
    color: #2e7d32;
}

.dab-timeline-item-status.in-progress {
    background: #fff3e0;
    color: #f57c00;
}

.dab-timeline-item-status.pending {
    background: #ffebee;
    color: #c62828;
}

.dab-timeline-item-status.milestone {
    background: #f3e5f5;
    color: #7b1fa2;
}

.dab-timeline-item-description {
    color: #666;
    line-height: 1.5;
    margin: 10px 0;
}

.dab-timeline-item-actions {
    display: flex;
    gap: 8px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.dab-timeline-action-btn {
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.2s ease;
}

.dab-timeline-action-btn:hover {
    background: #f5f5f5;
    border-color: #999;
    color: #333;
}

.dab-timeline-action-btn.edit {
    color: #0073aa;
    border-color: #0073aa;
}

.dab-timeline-action-btn.edit:hover {
    background: #e3f2fd;
}

.dab-timeline-action-btn.delete {
    color: #dc3545;
    border-color: #dc3545;
}

.dab-timeline-action-btn.delete:hover {
    background: #ffebee;
}

/* Timeline Item Modal */
.dab-timeline-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.dab-timeline-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.dab-timeline-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-timeline-modal-title {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.dab-timeline-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.dab-timeline-modal-close:hover {
    background: #f0f0f0;
}

.dab-timeline-modal-body {
    padding: 20px;
}

.dab-timeline-form-group {
    margin-bottom: 15px;
}

.dab-timeline-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.dab-timeline-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.dab-timeline-form-control:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.dab-timeline-form-control.textarea {
    min-height: 80px;
    resize: vertical;
}

.dab-timeline-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.dab-timeline-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dab-timeline-btn-primary {
    background: #0073aa;
    color: white;
}

.dab-timeline-btn-primary:hover {
    background: #005a87;
}

.dab-timeline-btn-secondary {
    background: #f0f0f0;
    color: #333;
}

.dab-timeline-btn-secondary:hover {
    background: #e0e0e0;
}

.dab-timeline-btn-danger {
    background: #dc3545;
    color: white;
}

.dab-timeline-btn-danger:hover {
    background: #c82333;
}

/* Empty State */
.dab-timeline-empty {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.dab-timeline-empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.dab-timeline-empty h3 {
    margin: 0 0 10px 0;
    color: #333;
}

/* Loading State */
.dab-timeline-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.dab-timeline-loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-timeline-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dab-timeline-controls {
        justify-content: center;
    }
    
    .dab-timeline {
        padding-left: 20px;
    }
    
    .dab-timeline-item {
        margin-left: 15px;
        padding: 15px;
    }
    
    .dab-timeline-item::before {
        left: -26px;
    }
    
    .dab-timeline-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .dab-timeline-item-date,
    .dab-timeline-item-status {
        margin-left: 0;
    }
    
    .dab-timeline-modal-content {
        width: 95%;
    }
}
