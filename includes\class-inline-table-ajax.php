<?php
/**
 * Inline Table AJAX Handler
 *
 * Handles AJAX requests for inline table fields
 */
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Inline_Table_AJAX {
    
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_get_inline_table', array($this, 'get_inline_table'));
        add_action('wp_ajax_dab_add_inline_record', array($this, 'add_inline_record'));
        add_action('wp_ajax_dab_update_inline_field', array($this, 'update_inline_field'));
        add_action('wp_ajax_dab_delete_inline_record', array($this, 'delete_inline_record'));
    }
    
    /**
     * Get inline table HTML
     */
    public function get_inline_table() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_inline_table_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }
        
        // Check required parameters
        if (!isset($_POST['parent_id']) || !isset($_POST['parent_table']) || !isset($_POST['child_table']) || !isset($_POST['foreign_key'])) {
            wp_send_json_error(array('message' => 'Missing required parameters'));
        }
        
        $parent_id = intval($_POST['parent_id']);
        $parent_table_id = intval($_POST['parent_table']);
        $child_table_id = intval($_POST['child_table']);
        $foreign_key = sanitize_text_field($_POST['foreign_key']);
        
        global $wpdb;
        
        // Get the field object
        $fields_table = $wpdb->prefix . 'dab_fields';
        $field = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d AND field_type = 'inline_table' AND inline_table_id = %d AND inline_table_foreign_key = %s",
            $parent_table_id, $child_table_id, $foreign_key
        ));
        
        if (!$field) {
            wp_send_json_error(array('message' => 'Field not found'));
        }
        
        // Get the HTML for the inline table
        $html = DAB_Data_Manager::render_inline_table_value($field, $parent_id, $parent_table_id);
        
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Add a new record to the inline table
     */
    public function add_inline_record() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_inline_table_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }
        
        // Check required parameters
        if (!isset($_POST['parent_id']) || !isset($_POST['child_table']) || !isset($_POST['foreign_key'])) {
            wp_send_json_error(array('message' => 'Missing required parameters'));
        }
        
        $parent_id = intval($_POST['parent_id']);
        $child_table_id = intval($_POST['child_table']);
        $foreign_key = sanitize_text_field($_POST['foreign_key']);
        
        global $wpdb;
        
        // Get child table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $child_table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $child_table_id
        ));
        
        if (!$child_table) {
            wp_send_json_error(array('message' => 'Child table not found'));
        }
        
        // Get fields for the child table
        $fields_table = $wpdb->prefix . 'dab_fields';
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d",
            $child_table_id
        ));
        
        if (empty($fields)) {
            wp_send_json_error(array('message' => 'No fields found for child table'));
        }
        
        // Prepare data for insertion
        $data = array(
            $foreign_key => $parent_id,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        // Add current user ID if user is logged in
        if (is_user_logged_in()) {
            $data['user_id'] = get_current_user_id();
        }
        
        // Add field values from POST data
        foreach ($fields as $field) {
            if (isset($_POST[$field->field_slug]) && $field->field_slug !== $foreign_key) {
                $data[$field->field_slug] = sanitize_text_field($_POST[$field->field_slug]);
            }
        }
        
        // Insert the record
        $child_table_name = $wpdb->prefix . 'dab_' . sanitize_title($child_table->table_slug);
        $result = $wpdb->insert($child_table_name, $data);
        
        if ($result) {
            $record_id = $wpdb->insert_id;
            wp_send_json_success(array('record_id' => $record_id));
        } else {
            wp_send_json_error(array('message' => 'Failed to add record'));
        }
    }
    
    /**
     * Update a field value in an inline table record
     */
    public function update_inline_field() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_inline_table_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }
        
        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['record_id']) || !isset($_POST['field_slug']) || !isset($_POST['field_value'])) {
            wp_send_json_error(array('message' => 'Missing required parameters'));
        }
        
        $table_id = intval($_POST['table_id']);
        $record_id = intval($_POST['record_id']);
        $field_slug = sanitize_text_field($_POST['field_slug']);
        $field_value = sanitize_text_field($_POST['field_value']);
        
        // Update the field
        $result = DAB_Data_Manager::update_record_field($table_id, $record_id, $field_slug, $field_value);
        
        if ($result !== false) {
            // Get the formatted value
            global $wpdb;
            $fields_table = $wpdb->prefix . 'dab_fields';
            $field = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $fields_table WHERE table_id = %d AND field_slug = %s",
                $table_id, $field_slug
            ));
            
            $formatted_value = DAB_Data_Manager::render_field_value($table_id, $field, $field_value, $record_id);
            
            wp_send_json_success(array('formatted_value' => $formatted_value));
        } else {
            wp_send_json_error(array('message' => 'Failed to update field'));
        }
    }
    
    /**
     * Delete a record from an inline table
     */
    public function delete_inline_record() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_inline_table_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }
        
        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['record_id'])) {
            wp_send_json_error(array('message' => 'Missing required parameters'));
        }
        
        $table_id = intval($_POST['table_id']);
        $record_id = intval($_POST['record_id']);
        
        // Delete the record
        $result = DAB_Data_Manager::delete_record($table_id, $record_id);
        
        if ($result) {
            wp_send_json_success();
        } else {
            wp_send_json_error(array('message' => 'Failed to delete record'));
        }
    }
}

// Initialize the class
new DAB_Inline_Table_AJAX();
