<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Fields Tutorial - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .field-type-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .field-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .field-type-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .field-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: bold;
            font-size: 1.2em;
        }
        .field-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
        }
        .field-description {
            color: #6c757d;
            margin-bottom: 20px;
            font-size: 1.1em;
        }
        .use-cases {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .use-cases h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .configuration {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .configuration h5 {
            color: #2980b9;
            margin-top: 0;
            font-weight: bold;
        }
        .example-setup {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .example-setup h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            border: 1px solid #34495e;
        }
        .step-list {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .step-list li {
            margin-bottom: 10px;
            font-weight: 500;
        }
        .warning-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h5 {
            color: #721c24;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Advanced Fields Tutorial - Power User Features</h1>

        <h2>🔗 Relationship & Lookup Fields</h2>

        <div class="field-type-card">
            <div class="field-type-header">
                <div class="field-icon">🔗</div>
                <div class="field-name">Lookup Field</div>
            </div>
            <div class="field-description">Connect data from other tables with dropdown selection</div>
            
            <div class="use-cases">
                <h5>🎯 Perfect For:</h5>
                <ul>
                    <li>Customer selection in order forms</li>
                    <li>Product categories and subcategories</li>
                    <li>Employee assignment to projects</li>
                    <li>Location and department selection</li>
                    <li>Vendor selection in purchase orders</li>
                </ul>
            </div>

            <div class="step-list">
                <h5>📋 Setup Steps:</h5>
                <ol>
                    <li>Create your reference table first (e.g., "Customers")</li>
                    <li>Add data to the reference table</li>
                    <li>In your main table, add a "Lookup" field</li>
                    <li>Select the reference table</li>
                    <li>Choose which field to display (e.g., customer name)</li>
                    <li>Choose which field to store (usually the ID)</li>
                </ol>
            </div>

            <div class="example-setup">
                <h5>📝 Example: Order Management System</h5>
                <p><strong>Reference Table:</strong> Customers</p>
                <p><strong>Display Field:</strong> Customer Name</p>
                <p><strong>Store Field:</strong> Customer ID</p>
                <p><strong>Result:</strong> Dropdown shows "John Smith", but stores "123"</p>
            </div>

            <div class="code-block">
Field Label: Customer
Field Type: Lookup
Reference Table: customers
Display Field: customer_name
Store Field: id
Allow Empty: No
</div>
        </div>

        <div class="field-type-card">
            <div class="field-type-header">
                <div class="field-icon">🧮</div>
                <div class="field-name">Formula Field</div>
            </div>
            <div class="field-description">Automatically calculate values based on other fields</div>
            
            <div class="use-cases">
                <h5>🎯 Perfect For:</h5>
                <ul>
                    <li>Total price calculations (quantity × unit price)</li>
                    <li>Age calculation from birth date</li>
                    <li>Full name from first + last name</li>
                    <li>Tax calculations and discounts</li>
                    <li>Performance scores and ratings</li>
                </ul>
            </div>

            <div class="configuration">
                <h5>⚙️ Available Functions:</h5>
                <table>
                    <tr>
                        <th>Function</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><strong>SUM</strong></td>
                        <td>Add multiple fields</td>
                        <td>SUM(field1, field2, field3)</td>
                    </tr>
                    <tr>
                        <td><strong>MULTIPLY</strong></td>
                        <td>Multiply fields</td>
                        <td>MULTIPLY(quantity, price)</td>
                    </tr>
                    <tr>
                        <td><strong>CONCAT</strong></td>
                        <td>Join text fields</td>
                        <td>CONCAT(first_name, " ", last_name)</td>
                    </tr>
                    <tr>
                        <td><strong>IF</strong></td>
                        <td>Conditional logic</td>
                        <td>IF(age >= 18, "Adult", "Minor")</td>
                    </tr>
                    <tr>
                        <td><strong>DATE_DIFF</strong></td>
                        <td>Calculate date difference</td>
                        <td>DATE_DIFF(birth_date, TODAY(), "years")</td>
                    </tr>
                </table>
            </div>

            <div class="example-setup">
                <h5>📝 Example: Invoice Total Calculation</h5>
                <div class="code-block">
Formula: MULTIPLY(quantity, unit_price)
Result Type: Number
Decimal Places: 2
Prefix: $
</div>
            </div>
        </div>

        <h2>📁 File & Media Fields</h2>

        <div class="field-type-card">
            <div class="field-type-header">
                <div class="field-icon">📁</div>
                <div class="field-name">File Upload Field</div>
            </div>
            <div class="field-description">Allow users to upload documents and files</div>
            
            <div class="use-cases">
                <h5>🎯 Perfect For:</h5>
                <ul>
                    <li>Document management systems</li>
                    <li>Resume and CV uploads</li>
                    <li>Contract and agreement files</li>
                    <li>Product manuals and specifications</li>
                    <li>Legal document storage</li>
                </ul>
            </div>

            <div class="configuration">
                <h5>⚙️ Security & Validation:</h5>
                <ul>
                    <li><strong>Allowed File Types:</strong> PDF, DOC, DOCX, XLS, etc.</li>
                    <li><strong>Maximum File Size:</strong> Set upload limits</li>
                    <li><strong>Virus Scanning:</strong> Automatic malware detection</li>
                    <li><strong>Storage Location:</strong> WordPress uploads or cloud</li>
                    <li><strong>Access Control:</strong> Who can download files</li>
                </ul>
            </div>

            <div class="code-block">
Field Label: Contract Document
Allowed Types: pdf,doc,docx
Max Size: 10MB
Required: Yes
Storage: WordPress Uploads
Access: Logged-in users only
</div>
        </div>

        <div class="field-type-card">
            <div class="field-type-header">
                <div class="field-icon">🖼️</div>
                <div class="field-name">Image Upload Field</div>
            </div>
            <div class="field-description">Upload and display images with automatic optimization</div>
            
            <div class="use-cases">
                <h5>🎯 Perfect For:</h5>
                <ul>
                    <li>Product photos and galleries</li>
                    <li>Profile pictures and avatars</li>
                    <li>Property photos for real estate</li>
                    <li>Before/after comparison images</li>
                    <li>Event photos and documentation</li>
                </ul>
            </div>

            <div class="configuration">
                <h5>⚙️ Image Processing:</h5>
                <ul>
                    <li><strong>Auto Resize:</strong> Optimize for web display</li>
                    <li><strong>Thumbnail Generation:</strong> Create preview images</li>
                    <li><strong>Image Compression:</strong> Reduce file sizes</li>
                    <li><strong>Format Conversion:</strong> Convert to WebP for speed</li>
                    <li><strong>Watermarking:</strong> Add copyright protection</li>
                </ul>
            </div>

            <div class="code-block">
Field Label: Product Image
Max Size: 5MB
Auto Resize: 800x600
Generate Thumbnails: Yes
Compression: 85%
Allowed: jpg,jpeg,png,webp
</div>
        </div>

        <h2>✍️ Signature & Interactive Fields</h2>

        <div class="field-type-card">
            <div class="field-type-header">
                <div class="field-icon">✍️</div>
                <div class="field-name">Signature Capture</div>
            </div>
            <div class="field-description">Digital signature capture with legal validity</div>
            
            <div class="use-cases">
                <h5>🎯 Perfect For:</h5>
                <ul>
                    <li>Contract signing and agreements</li>
                    <li>Delivery confirmations</li>
                    <li>Medical consent forms</li>
                    <li>Employee onboarding documents</li>
                    <li>Service completion confirmations</li>
                </ul>
            </div>

            <div class="configuration">
                <h5>⚙️ Legal & Technical Features:</h5>
                <ul>
                    <li><strong>Timestamp Recording:</strong> When signature was captured</li>
                    <li><strong>IP Address Logging:</strong> Location verification</li>
                    <li><strong>Device Information:</strong> Capture device details</li>
                    <li><strong>Signature Validation:</strong> Ensure signature quality</li>
                    <li><strong>PDF Integration:</strong> Embed in documents</li>
                </ul>
            </div>

            <div class="example-setup">
                <h5>📝 Example: Service Agreement</h5>
                <div class="code-block">
Field Label: Customer Signature
Canvas Size: 400x200
Background: White
Pen Color: Blue
Required: Yes
Store Metadata: Yes (timestamp, IP, device)
</div>
            </div>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Signature Legal Validity</h5>
            <p>Digital signatures captured through this field include metadata (timestamp, IP address, device info) to support legal validity. Ensure your privacy policy covers this data collection.</p>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tip: Field Dependencies</h5>
            <p>Use conditional logic to show advanced fields only when needed. For example, show a "Signature" field only after a user agrees to terms and conditions.</p>
        </div>

        <div class="navigation-buttons">
            <a href="field-types-tutorial.html" class="nav-button">← Previous: Basic Fields</a>
            <a href="form-builder-tutorial.html" class="nav-button">Next: Form Builder →</a>
        </div>
    </div>
</body>
</html>
