<?php
/**
 * Permissions Dashboard
 *
 * Displays all permissions in one place.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Get all WordPress roles
$wp_roles = wp_roles();
$all_roles = $wp_roles->get_names();

// Get all tables
global $wpdb;
$tables_table = $wpdb->prefix . 'dab_tables';
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label ASC");

// Get all fields
$fields_table = $wpdb->prefix . 'dab_fields';
$fields = $wpdb->get_results("SELECT f.*, t.table_label FROM $fields_table f JOIN $tables_table t ON f.table_id = t.id ORDER BY t.table_label ASC, f.field_label ASC");

// Get selected tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'tables';

// Get selected role filter
$role_filter = isset($_GET['role']) ? sanitize_text_field($_GET['role']) : '';

// Process bulk actions
if (isset($_POST['dab_bulk_permissions']) && isset($_POST['dab_admin_nonce']) && wp_verify_nonce($_POST['dab_admin_nonce'], 'dab_admin_nonce')) {
    $bulk_action = isset($_POST['bulk_action']) ? sanitize_text_field($_POST['bulk_action']) : '';
    $selected_items = isset($_POST['selected_items']) ? $_POST['selected_items'] : [];
    $selected_roles = isset($_POST['selected_roles']) ? $_POST['selected_roles'] : [];
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];

    if ($bulk_action && !empty($selected_items) && !empty($selected_roles) && !empty($permissions)) {
        if ($active_tab === 'tables') {
            // Process table permissions
            foreach ($selected_items as $table_id) {
                $table_id = intval($table_id);
                $table_permissions = [];

                // Get existing permissions
                $existing_permissions = DAB_Role_Permissions_Manager::get_table_permissions($table_id);
                $role_permissions = [];
                foreach ($existing_permissions as $perm) {
                    $role_permissions[$perm['role']] = [
                        'can_view' => $perm['can_view'],
                        'can_edit' => $perm['can_edit'],
                        'can_delete' => $perm['can_delete'],
                        'can_export' => $perm['can_export']
                    ];
                }

                // Update permissions for selected roles
                foreach ($selected_roles as $role) {
                    if (!isset($role_permissions[$role])) {
                        $role_permissions[$role] = [
                            'can_view' => 0,
                            'can_edit' => 0,
                            'can_delete' => 0,
                            'can_export' => 0
                        ];
                    }

                    foreach ($permissions as $permission) {
                        $role_permissions[$role][$permission] = ($bulk_action === 'grant') ? 1 : 0;
                    }
                }

                // Save updated permissions
                DAB_Role_Permissions_Manager::save_table_permissions($table_id, $role_permissions);
            }
        } elseif ($active_tab === 'fields') {
            // Process field permissions
            foreach ($selected_items as $field_id) {
                $field_id = intval($field_id);
                $field_permissions = [];

                // Get existing permissions
                $existing_permissions = DAB_Role_Permissions_Manager::get_field_permissions($field_id);
                $role_permissions = [];
                foreach ($existing_permissions as $perm) {
                    $role_permissions[$perm['role']] = [
                        'can_view' => $perm['can_view'],
                        'can_edit' => $perm['can_edit']
                    ];
                }

                // Update permissions for selected roles
                foreach ($selected_roles as $role) {
                    if (!isset($role_permissions[$role])) {
                        $role_permissions[$role] = [
                            'can_view' => 0,
                            'can_edit' => 0
                        ];
                    }

                    foreach ($permissions as $permission) {
                        if (in_array($permission, ['can_view', 'can_edit'])) {
                            $role_permissions[$role][$permission] = ($bulk_action === 'grant') ? 1 : 0;
                        }
                    }
                }

                // Save updated permissions
                DAB_Role_Permissions_Manager::save_field_permissions($field_id, $role_permissions);
            }
        }

        // Show success message
        echo '<div class="notice notice-success is-dismissible"><p>Permissions updated successfully.</p></div>';
    }
}


?>

<div class="wrap dab-admin-wrap">
    <h1>Permissions Dashboard</h1>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title">Manage Permissions</h2>
            <p class="description">View and manage all permissions in one place. Use the tabs to switch between table, field, and file permissions.</p>

            <div class="dab-admin-tabs">
                <a href="?page=dab_permissions_dashboard&tab=tables" class="dab-admin-tab-link <?php echo $active_tab === 'tables' ? 'active' : ''; ?>">Table Permissions</a>
                <a href="?page=dab_permissions_dashboard&tab=fields" class="dab-admin-tab-link <?php echo $active_tab === 'fields' ? 'active' : ''; ?>">Field Permissions</a>
                <a href="?page=dab_permissions_dashboard&tab=files" class="dab-admin-tab-link <?php echo $active_tab === 'files' ? 'active' : ''; ?>">File Permissions</a>
            </div>
        </div>
        <div class="dab-card-body">
            <!-- Role Filter -->
            <div class="dab-filter-container">
                <form method="get">
                    <input type="hidden" name="page" value="dab_permissions_dashboard">
                    <input type="hidden" name="tab" value="<?php echo esc_attr($active_tab); ?>">

                    <label for="role-filter">Filter by Role:</label>
                    <select id="role-filter" name="role" onchange="this.form.submit()">
                        <option value="">All Roles</option>
                        <?php foreach ($all_roles as $role_id => $role_name): ?>
                            <?php if ($role_id !== 'administrator'): ?>
                                <option value="<?php echo esc_attr($role_id); ?>" <?php selected($role_filter, $role_id); ?>><?php echo esc_html($role_name); ?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                </form>
            </div>

            <!-- Bulk Actions Form -->
            <form method="post" id="dab-bulk-permissions-form">
                <?php wp_nonce_field('dab_admin_nonce', 'dab_admin_nonce'); ?>
                <input type="hidden" name="dab_bulk_permissions" value="1">

                <div class="dab-bulk-actions">
                    <select name="bulk_action" id="bulk-action">
                        <option value="">Bulk Actions</option>
                        <option value="grant">Grant Permissions</option>
                        <option value="revoke">Revoke Permissions</option>
                    </select>

                    <div class="dab-bulk-roles">
                        <label>For Roles:</label>
                        <div class="dab-role-checkboxes">
                            <?php foreach ($all_roles as $role_id => $role_name): ?>
                                <?php if ($role_id !== 'administrator'): ?>
                                    <label>
                                        <input type="checkbox" name="selected_roles[]" value="<?php echo esc_attr($role_id); ?>">
                                        <?php echo esc_html($role_name); ?>
                                    </label>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="dab-bulk-permissions">
                        <label>Permissions:</label>
                        <?php if ($active_tab === 'tables'): ?>
                            <label><input type="checkbox" name="permissions[]" value="can_view"> View</label>
                            <label><input type="checkbox" name="permissions[]" value="can_edit"> Edit</label>
                            <label><input type="checkbox" name="permissions[]" value="can_delete"> Delete</label>
                            <label><input type="checkbox" name="permissions[]" value="can_export"> Export</label>
                        <?php else: ?>
                            <label><input type="checkbox" name="permissions[]" value="can_view"> View</label>
                            <label><input type="checkbox" name="permissions[]" value="can_edit"> Edit</label>
                        <?php endif; ?>
                    </div>

                    <button type="submit" class="button button-primary">Apply</button>
                </div>

                <?php if ($active_tab === 'tables'): ?>
                    <!-- Table Permissions -->
                    <table class="widefat striped dab-permissions-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-tables"></th>
                                <th>Table</th>
                                <?php foreach ($all_roles as $role_id => $role_name): ?>
                                    <?php if ($role_id !== 'administrator' && (empty($role_filter) || $role_filter === $role_id)): ?>
                                        <th><?php echo esc_html($role_name); ?></th>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tables as $table): ?>
                                <tr>
                                    <td><input type="checkbox" name="selected_items[]" value="<?php echo $table->id; ?>" class="table-checkbox"></td>
                                    <td>
                                        <strong><?php echo esc_html($table->table_label); ?></strong>
                                        <div class="row-actions">
                                            <span class="edit"><a href="<?php echo admin_url('admin.php?page=dab_tables&action=edit&table_id=' . $table->id); ?>">Edit</a> | </span>
                                            <span class="permissions"><a href="<?php echo admin_url('admin.php?page=dab_tables&action=edit&table_id=' . $table->id . '&tab=permissions'); ?>">Manage Permissions</a></span>
                                        </div>
                                    </td>
                                    <?php
                                    // Get permissions for this table
                                    $table_permissions = DAB_Role_Permissions_Manager::get_table_permissions($table->id);
                                    $role_permissions = [];
                                    foreach ($table_permissions as $perm) {
                                        $role_permissions[$perm['role']] = [
                                            'can_view' => $perm['can_view'],
                                            'can_edit' => $perm['can_edit'],
                                            'can_delete' => $perm['can_delete'],
                                            'can_export' => $perm['can_export']
                                        ];
                                    }

                                    foreach ($all_roles as $role_id => $role_name):
                                        if ($role_id !== 'administrator' && (empty($role_filter) || $role_filter === $role_id)):
                                            $can_view = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_view'] ? true : false;
                                            $can_edit = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_edit'] ? true : false;
                                            $can_delete = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_delete'] ? true : false;
                                            $can_export = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_export'] ? true : false;
                                    ?>
                                        <td class="permission-cell">
                                            <span class="permission-icon <?php echo $can_view ? 'has-permission' : 'no-permission'; ?>" title="View: <?php echo $can_view ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_view ? 'dashicons-visibility' : 'dashicons-hidden'; ?>"></span>
                                            </span>
                                            <span class="permission-icon <?php echo $can_edit ? 'has-permission' : 'no-permission'; ?>" title="Edit: <?php echo $can_edit ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_edit ? 'dashicons-edit' : 'dashicons-no'; ?>"></span>
                                            </span>
                                            <span class="permission-icon <?php echo $can_delete ? 'has-permission' : 'no-permission'; ?>" title="Delete: <?php echo $can_delete ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_delete ? 'dashicons-trash' : 'dashicons-no'; ?>"></span>
                                            </span>
                                            <span class="permission-icon <?php echo $can_export ? 'has-permission' : 'no-permission'; ?>" title="Export: <?php echo $can_export ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_export ? 'dashicons-download' : 'dashicons-no'; ?>"></span>
                                            </span>
                                        </td>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php elseif ($active_tab === 'fields'): ?>
                    <!-- Field Permissions -->
                    <table class="widefat striped dab-permissions-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-fields"></th>
                                <th>Field</th>
                                <th>Table</th>
                                <?php foreach ($all_roles as $role_id => $role_name): ?>
                                    <?php if ($role_id !== 'administrator' && (empty($role_filter) || $role_filter === $role_id)): ?>
                                        <th><?php echo esc_html($role_name); ?></th>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($fields as $field): ?>
                                <tr>
                                    <td><input type="checkbox" name="selected_items[]" value="<?php echo $field->id; ?>" class="field-checkbox"></td>
                                    <td>
                                        <strong><?php echo esc_html($field->field_label); ?></strong>
                                        <div class="row-actions">
                                            <span class="edit"><a href="<?php echo admin_url('admin.php?page=dab_fields&action=edit&field_id=' . $field->id); ?>">Edit</a></span>
                                        </div>
                                    </td>
                                    <td><?php echo esc_html($field->table_label); ?></td>
                                    <?php
                                    // Get permissions for this field
                                    $field_permissions = DAB_Role_Permissions_Manager::get_field_permissions($field->id);
                                    $role_permissions = [];
                                    foreach ($field_permissions as $perm) {
                                        $role_permissions[$perm['role']] = [
                                            'can_view' => $perm['can_view'],
                                            'can_edit' => $perm['can_edit']
                                        ];
                                    }

                                    foreach ($all_roles as $role_id => $role_name):
                                        if ($role_id !== 'administrator' && (empty($role_filter) || $role_filter === $role_id)):
                                            $can_view = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_view'] ? true : false;
                                            $can_edit = isset($role_permissions[$role_id]) && $role_permissions[$role_id]['can_edit'] ? true : false;
                                    ?>
                                        <td class="permission-cell">
                                            <span class="permission-icon <?php echo $can_view ? 'has-permission' : 'no-permission'; ?>" title="View: <?php echo $can_view ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_view ? 'dashicons-visibility' : 'dashicons-hidden'; ?>"></span>
                                            </span>
                                            <span class="permission-icon <?php echo $can_edit ? 'has-permission' : 'no-permission'; ?>" title="Edit: <?php echo $can_edit ? 'Yes' : 'No'; ?>">
                                                <span class="dashicons <?php echo $can_edit ? 'dashicons-edit' : 'dashicons-no'; ?>"></span>
                                            </span>
                                        </td>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php elseif ($active_tab === 'files'): ?>
                    <!-- File Permissions -->
                    <div class="dab-notice dab-notice-info">
                        <p>File permissions management will be available in a future update. This feature will allow you to control which user roles can upload, view, and delete files in your database.</p>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</div>

<style>
    .dab-filter-container {
        margin-bottom: 20px;
    }

    .dab-bulk-actions {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .dab-bulk-roles,
    .dab-bulk-permissions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .dab-role-checkboxes {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .dab-permissions-table .permission-cell {
        white-space: nowrap;
    }

    .permission-icon {
        display: inline-block;
        margin-right: 5px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
    }

    .permission-icon.has-permission {
        background-color: #d4edda;
        color: #155724;
    }

    .permission-icon.no-permission {
        background-color: #f8d7da;
        color: #721c24;
    }

    .permission-icon .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
        line-height: 20px;
    }

    .dab-notice {
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    .dab-notice-info {
        background-color: #e5f5fa;
        border-left: 4px solid #00a0d2;
        color: #00739c;
    }
</style>

<script>
jQuery(document).ready(function($) {
    // Fix for tab navigation
    $('.dab-admin-tab-link').on('click', function(e) {
        // Get the tab name from the href
        var tabName = $(this).attr('href').split('tab=')[1];

        // Update URL without reloading the page
        var newUrl = '?page=dab_permissions_dashboard&tab=' + tabName;
        history.pushState(null, null, newUrl);

        // Store the current tab in session storage
        sessionStorage.setItem('dab_active_permissions_tab', tabName);

        // Update active tab UI
        $('.dab-admin-tab-link').removeClass('active');
        $(this).addClass('active');

        // Show the appropriate content based on the tab
        if (tabName === 'tables' || tabName === 'fields' || tabName === 'files') {
            // Reload the page to ensure proper content is displayed
            location.reload();
        }

        // Prevent default link behavior
        e.preventDefault();
    });

    // Handle select all checkboxes
    $('#select-all-tables').on('change', function() {
        $('.table-checkbox').prop('checked', $(this).prop('checked'));
    });

    $('#select-all-fields').on('change', function() {
        $('.field-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Restore active tab from session storage if available
    var storedTab = sessionStorage.getItem('dab_active_permissions_tab');
    if (storedTab && window.location.href.indexOf('tab=') === -1) {
        window.location.href = '?page=dab_permissions_dashboard&tab=' + storedTab;
    }

    // Form validation
    $('#dab-bulk-permissions-form').on('submit', function(e) {
        var bulkAction = $('#bulk-action').val();
        var selectedItems = $('input[name="selected_items[]"]:checked').length;
        var selectedRoles = $('input[name="selected_roles[]"]:checked').length;
        var selectedPermissions = $('input[name="permissions[]"]:checked').length;

        if (!bulkAction) {
            alert('Please select a bulk action.');
            e.preventDefault();
            return false;
        }

        if (selectedItems === 0) {
            alert('Please select at least one item.');
            e.preventDefault();
            return false;
        }

        if (selectedRoles === 0) {
            alert('Please select at least one role.');
            e.preventDefault();
            return false;
        }

        if (selectedPermissions === 0) {
            alert('Please select at least one permission.');
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>
