// Lookup field functionality
$(document).on('change', '#lookup_table_id', function() {
    var tableId = $(this).val();
    var displayFieldSelect = $('#lookup_display_field');
    var valueFieldSelect = $('#lookup_value_field');

    // Clear current options
    displayFieldSelect.empty();
    valueFieldSelect.empty();

    if (!tableId) {
        displayFieldSelect.append('<option value="">Select a table first</option>');
        valueFieldSelect.append('<option value="">Select a table first</option>');
        return;
    }

    // Show loading indicator
    displayFieldSelect.append('<option value="">Loading fields...</option>');
    valueFieldSelect.append('<option value="">Loading fields...</option>');

    // Fetch fields from the selected table - use GET request for simplicity
    $.ajax({
        url: ajaxurl,
        type: 'GET',
        data: {
            action: 'dab_get_table_fields',
            table_id: tableId
        },
        success: function(response) {
            console.log("AJAX Response:", response); // Debug log

            displayFieldSelect.empty();
            valueFieldSelect.empty();

            // Add default option
            displayFieldSelect.append('<option value="">Select display field</option>');
            valueFieldSelect.append('<option value="">Select value field</option>');

            // Check for both response formats (success object or direct array)
            var fieldsData = [];

            if (response.success && response.data && Array.isArray(response.data)) {
                // New API format with success property
                fieldsData = response.data;
            } else if (Array.isArray(response)) {
                // Legacy API format (direct array)
                fieldsData = response;
            } else if (typeof response === 'object' && !response.success) {
                // Error response
                console.error("Error response:", response); // Debug log
                displayFieldSelect.append('<option value="">No fields available: ' + (response.message || 'Unknown error') + '</option>');
                valueFieldSelect.append('<option value="">No fields available: ' + (response.message || 'Unknown error') + '</option>');
                return;
            }

            if (fieldsData.length > 0) {
                // Add fields as options
                $.each(fieldsData, function(index, field) {
                    displayFieldSelect.append('<option value="' + field.field_slug + '">' + field.field_label + '</option>');
                    valueFieldSelect.append('<option value="' + field.field_slug + '">' + field.field_label + '</option>');
                });
            } else {
                displayFieldSelect.append('<option value="">No fields available in this table</option>');
                valueFieldSelect.append('<option value="">No fields available in this table</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", xhr, status, error); // Debug log
            displayFieldSelect.empty();
            valueFieldSelect.empty();
            displayFieldSelect.append('<option value="">Error loading fields: ' + error + '</option>');
            valueFieldSelect.append('<option value="">Error loading fields: ' + error + '</option>');
        }
    });
});


