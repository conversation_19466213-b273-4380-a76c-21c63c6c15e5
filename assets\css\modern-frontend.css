/**
 * Database App Builder Modern Frontend UI
 * 
 * Modern frontend interface styling for the Database App Builder plugin
 */

/* Import Modern UI Framework */
@import url('modern-ui.css');

/* Frontend Container */
.dab-frontend-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--dab-spacing-md);
}

/* Frontend Forms */
.dab-form-container {
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-md);
  padding: var(--dab-spacing-lg);
  margin-bottom: var(--dab-spacing-lg);
  animation: dab-fade-in var(--dab-transition-normal) ease;
}

.dab-form-header {
  margin-bottom: var(--dab-spacing-lg);
  padding-bottom: var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
}

.dab-form-title {
  font-size: var(--dab-font-size-2xl);
  font-weight: 600;
  color: var(--dab-gray-900);
  margin-top: 0;
  margin-bottom: var(--dab-spacing-sm);
}

.dab-form-description {
  color: var(--dab-gray-600);
  margin-bottom: 0;
}

.dab-form-body {
  margin-bottom: var(--dab-spacing-lg);
}

.dab-form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--dab-spacing-md) * -1);
  margin-left: calc(var(--dab-spacing-md) * -1);
  margin-bottom: var(--dab-spacing-md);
}

.dab-form-group {
  flex: 0 0 100%;
  max-width: 100%;
  padding-right: var(--dab-spacing-md);
  padding-left: var(--dab-spacing-md);
  margin-bottom: var(--dab-spacing-md);
}

@media (min-width: 768px) {
  .dab-form-group-half {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .dab-form-group-third {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .dab-form-group-two-thirds {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
}

.dab-form-label {
  display: block;
  margin-bottom: var(--dab-spacing-sm);
  font-weight: 500;
  color: var(--dab-gray-800);
}

.dab-form-required {
  color: var(--dab-danger);
  margin-left: var(--dab-spacing-xs);
}

.dab-form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--dab-font-size-md);
  line-height: 1.5;
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  background-clip: padding-box;
  border: 1px solid var(--dab-gray-400);
  border-radius: var(--dab-border-radius-md);
  transition: border-color var(--dab-transition-normal) ease-in-out, box-shadow var(--dab-transition-normal) ease-in-out;
}

.dab-form-control:focus {
  color: var(--dab-gray-700);
  background-color: var(--dab-white);
  border-color: var(--dab-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.dab-form-control.dab-error {
  border-color: var(--dab-danger);
}

.dab-form-help {
  display: block;
  margin-top: var(--dab-spacing-xs);
  font-size: var(--dab-font-size-sm);
  color: var(--dab-gray-600);
}

.dab-form-error {
  display: block;
  margin-top: var(--dab-spacing-xs);
  font-size: var(--dab-font-size-sm);
  color: var(--dab-danger);
}

.dab-form-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--dab-spacing-lg);
  padding-top: var(--dab-spacing-md);
  border-top: 1px solid var(--dab-gray-300);
}

/* Checkbox and Radio Styles */
.dab-form-check {
  position: relative;
  display: block;
  padding-left: 1.5rem;
  margin-bottom: var(--dab-spacing-sm);
}

.dab-form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.5rem;
}

.dab-form-check-label {
  margin-bottom: 0;
  cursor: pointer;
}

.dab-form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}

.dab-form-check-inline .dab-form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

/* Frontend Views */
.dab-view-container {
  background-color: var(--dab-white);
  border-radius: var(--dab-border-radius-md);
  box-shadow: var(--dab-shadow-md);
  padding: var(--dab-spacing-lg);
  margin-bottom: var(--dab-spacing-lg);
  animation: dab-fade-in var(--dab-transition-normal) ease;
}

.dab-view-header {
  margin-bottom: var(--dab-spacing-lg);
  padding-bottom: var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dab-view-title {
  font-size: var(--dab-font-size-2xl);
  font-weight: 600;
  color: var(--dab-gray-900);
  margin: 0;
}

.dab-view-actions {
  display: flex;
  gap: var(--dab-spacing-sm);
  margin-top: var(--dab-spacing-sm);
}

@media (min-width: 768px) {
  .dab-view-actions {
    margin-top: 0;
  }
}

.dab-view-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--dab-spacing-sm);
  margin-bottom: var(--dab-spacing-md);
  padding: var(--dab-spacing-md);
  background-color: var(--dab-gray-100);
  border-radius: var(--dab-border-radius-md);
}

.dab-view-filter-item {
  flex: 1;
  min-width: 200px;
}

.dab-view-filter-label {
  display: block;
  margin-bottom: var(--dab-spacing-xs);
  font-size: var(--dab-font-size-sm);
  font-weight: 500;
}

.dab-view-filter-control {
  width: 100%;
}

.dab-view-filter-actions {
  display: flex;
  align-items: flex-end;
  gap: var(--dab-spacing-sm);
}

.dab-view-table-container {
  overflow-x: auto;
  margin-bottom: var(--dab-spacing-md);
}

.dab-view-table {
  width: 100%;
  border-collapse: collapse;
}

.dab-view-table th {
  background-color: var(--dab-gray-100);
  font-weight: 600;
  text-align: left;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 2px solid var(--dab-gray-300);
  position: sticky;
  top: 0;
  z-index: 10;
}

.dab-view-table td {
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  border-bottom: 1px solid var(--dab-gray-300);
  vertical-align: middle;
}

.dab-view-table tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

.dab-view-table-actions {
  display: flex;
  gap: var(--dab-spacing-sm);
}

.dab-view-pagination {
  display: flex;
  justify-content: center;
  padding-left: 0;
  list-style: none;
  margin-top: var(--dab-spacing-md);
}

.dab-view-page-item {
  margin: 0 var(--dab-spacing-xs);
}

.dab-view-page-link {
  display: block;
  padding: var(--dab-spacing-sm) var(--dab-spacing-md);
  color: var(--dab-primary);
  background-color: var(--dab-white);
  border: 1px solid var(--dab-gray-300);
  border-radius: var(--dab-border-radius-md);
  text-decoration: none;
  transition: all var(--dab-transition-normal) ease;
}

.dab-view-page-link:hover {
  color: var(--dab-primary-dark);
  background-color: var(--dab-gray-200);
  border-color: var(--dab-gray-300);
}

.dab-view-page-item.active .dab-view-page-link {
  color: var(--dab-white);
  background-color: var(--dab-primary);
  border-color: var(--dab-primary);
}

.dab-view-page-item.disabled .dab-view-page-link {
  color: var(--dab-gray-500);
  pointer-events: none;
  background-color: var(--dab-white);
  border-color: var(--dab-gray-300);
}

/* Frontend Loader */
.dab-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--dab-spacing-lg);
}

.dab-loader {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid rgba(67, 97, 238, 0.2);
  border-top-color: var(--dab-primary);
  border-radius: 50%;
  animation: dab-spin 1s linear infinite;
}
