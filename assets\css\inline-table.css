/**
 * Inline Table Field Styles
 */

/* Container */
.dab-inline-table-container {
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
}

/* Error message */
.dab-inline-table-error {
    padding: 10px;
    color: #d63638;
    background-color: #ffebe8;
    border-left: 4px solid #d63638;
    margin: 10px 0;
}

/* Empty message */
.dab-inline-table-empty {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
    background-color: #f9f9f9;
}

/* Table */
.dab-inline-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.dab-inline-table thead th {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
    text-align: left;
    padding: 10px;
    border-bottom: 2px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.dab-inline-table tbody td {
    padding: 8px 10px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
}

.dab-inline-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.dab-inline-table tbody tr:hover {
    background-color: #f0f7ff;
}

/* Action buttons */
.dab-inline-actions {
    white-space: nowrap;
    text-align: center;
}

.dab-inline-edit,
.dab-inline-delete {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    margin: 0 2px;
    border-radius: 3px;
    color: #555;
}

.dab-inline-edit:hover {
    color: #0073aa;
    background-color: rgba(0, 115, 170, 0.1);
}

.dab-inline-delete:hover {
    color: #d63638;
    background-color: rgba(214, 54, 56, 0.1);
}

/* Add button container */
.dab-inline-add-container {
    padding: 10px;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.dab-inline-add-btn {
    padding: 6px 12px !important;
    font-size: 13px !important;
}

/* Modal for adding/editing records */
.dab-inline-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    overflow: auto;
}

.dab-inline-modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dab-inline-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
}

.dab-inline-modal-title {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.dab-inline-form-field {
    margin-bottom: 15px;
}

.dab-inline-form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.dab-inline-form-field input,
.dab-inline-form-field select,
.dab-inline-form-field textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dab-inline-form-actions {
    margin-top: 20px;
    text-align: right;
}

.dab-inline-form-actions button {
    margin-left: 10px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .dab-inline-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .dab-inline-modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .dab-inline-form-field input,
    .dab-inline-form-field select,
    .dab-inline-form-field textarea {
        font-size: 16px; /* Prevent zoom on mobile */
    }
}

/* Editable field styles */
.dab-inline-field {
    position: relative;
}

.dab-inline-field.editing {
    padding: 0;
}

.dab-inline-field input,
.dab-inline-field select {
    width: 100%;
    padding: 6px;
    border: 1px solid #0073aa;
    border-radius: 3px;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Loading indicator */
.dab-inline-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.dab-inline-loading::after {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #0073aa;
    border-radius: 50%;
    border-top-color: transparent;
    animation: dab-spin 1s linear infinite;
}

@keyframes dab-spin {
    to {
        transform: rotate(360deg);
    }
}
