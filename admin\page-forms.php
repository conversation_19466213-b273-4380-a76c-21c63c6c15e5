<?php
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$forms_table  = $wpdb->prefix . 'dab_forms';
$message = '';

// Make sure the forms table has all required columns
DAB_Forms_Manager::check_and_update_forms_table();

// Handle Delete
if (isset($_GET['delete_form'])) {
    $form_id = intval($_GET['delete_form']);
    DAB_Forms_Manager::delete_form($form_id);
    $message = "Form deleted successfully.";
}

// Handle Edit
$edit_mode = false;
$edit_form = null;
if (isset($_GET['edit_form'])) {
    $edit_mode = true;
    $edit_id = intval($_GET['edit_form']);
    $edit_form = DAB_Forms_Manager::get_form($edit_id);
}

// Handle Create/Update
if (isset($_POST['dab_save_form'])) {
    $form_name = sanitize_text_field($_POST['form_name']);
    $table_id = intval($_POST['table_id']);
    $selected_fields = isset($_POST['selected_fields']) ? array_map('sanitize_text_field', $_POST['selected_fields']) : [];
    $logic_rules = isset($_POST['conditional_logic_json']) ? sanitize_textarea_field($_POST['conditional_logic_json']) : '';
    $notify_email = isset($_POST['notify_email']) ? sanitize_text_field($_POST['notify_email']) : '';
    $notify_message = isset($_POST['notify_message']) ? sanitize_textarea_field($_POST['notify_message']) : '';

    // Google Sheets integration
    $google_sheets_enabled = isset($_POST['google_sheets_enabled']) ? 1 : 0;
    $google_sheets_spreadsheet_id = isset($_POST['google_sheets_spreadsheet_id']) ? sanitize_text_field($_POST['google_sheets_spreadsheet_id']) : '';
    $google_sheets_worksheet_name = isset($_POST['google_sheets_worksheet_name']) ? sanitize_text_field($_POST['google_sheets_worksheet_name']) : '';

    // Zapier integration
    $zapier_enabled = isset($_POST['zapier_enabled']) ? 1 : 0;
    $zapier_webhooks = isset($_POST['zapier_webhooks']) ? $_POST['zapier_webhooks'] : [];

    // Filter out empty webhook URLs
    $zapier_webhooks = array_filter($zapier_webhooks, function($url) {
        return !empty(trim($url));
    });

    // Sanitize webhook URLs
    $zapier_webhooks = array_map('esc_url_raw', $zapier_webhooks);

    // Process field order if provided
    if (isset($_POST['field_order']) && !empty($_POST['field_order'])) {
        $field_order = json_decode(stripslashes($_POST['field_order']), true);

        if (is_array($field_order) && !empty($field_order)) {
            global $wpdb;
            $fields_table = $wpdb->prefix . 'dab_fields';

            // Check if field_order column exists
            $field_order_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = 'field_order'",
                    DB_NAME,
                    $fields_table
                )
            );

            // Add field_order column if it doesn't exist
            if (!$field_order_exists) {
                $wpdb->query("ALTER TABLE $fields_table ADD COLUMN field_order INT DEFAULT 0");

                // Initialize field_order values based on id to maintain existing order
                $wpdb->query("UPDATE $fields_table SET field_order = id");
            }

            // Update field order for each field
            foreach ($field_order as $order => $field_id) {
                $wpdb->update(
                    $fields_table,
                    ['field_order' => $order],
                    ['id' => intval($field_id), 'table_id' => $table_id]
                );
            }
        }
    }

    // Prepare form data
    $form_data = array(
        'form_name' => $form_name,
        'table_id' => $table_id,
        'fields' => $selected_fields,
        'conditional_logic' => $logic_rules,
        'notify_email' => $notify_email,
        'notify_message' => $notify_message,
        'google_sheets_enabled' => $google_sheets_enabled,
        'google_sheets_spreadsheet_id' => $google_sheets_spreadsheet_id,
        'google_sheets_worksheet_name' => $google_sheets_worksheet_name,
        'zapier_enabled' => $zapier_enabled,
        'zapier_webhooks' => $zapier_webhooks
    );

    if (!empty($_POST['form_id'])) {
        $form_data['id'] = intval($_POST['form_id']);
        DAB_Forms_Manager::save_form($form_data);
        $message = "Form updated successfully.";
        $edit_mode = false;
    } else {
        DAB_Forms_Manager::save_form($form_data);
        $message = "Form created successfully.";
    }
}

$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY id DESC");
$forms = DAB_Forms_Manager::get_forms();

$selected_table_id = $edit_mode ? $edit_form->table_id : (isset($_POST['table_id']) ? intval($_POST['table_id']) : 0);
$fields = $selected_table_id ? $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $selected_table_id)) : [];
?>

<div class="wrap">
    <h1><?php echo $edit_mode ? 'Edit Form' : 'Create Form'; ?></h1>

    <?php if (!empty($message)): ?>
        <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
    <?php endif; ?>

    <form method="post">
        <?php if ($edit_mode): ?>
            <input type="hidden" name="form_id" value="<?php echo esc_attr($edit_form->id); ?>">
        <?php endif; ?>

        <table class="form-table">
            <tr>
                <th>Form Name</th>
                <td><input type="text" name="form_name" value="<?php echo $edit_mode ? esc_attr($edit_form->form_name) : ''; ?>" required></td>
            </tr>
            <tr>
                <th>Select Table</th>
                <td>
                    <select name="table_id" onchange="this.form.submit();" required>
                        <option value="">-- Choose Table --</option>
                        <?php foreach ($tables as $tbl): ?>
                            <option value="<?php echo esc_attr($tbl->id); ?>" <?php selected($selected_table_id, $tbl->id); ?>>
                                <?php echo esc_html($tbl->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </td>
            </tr>
            <tr>
                <th>Email Notification (Optional)</th>
                <td><input type="email" name="notify_email" value="<?php echo $edit_mode ? esc_attr($edit_form->notify_email ?? '') : ''; ?>" placeholder="<EMAIL>"></td>
            </tr>

            <!-- Google Sheets Integration -->
            <tr>
                <th>Google Sheets Integration</th>
                <td>
                    <label>
                        <input type="checkbox" name="google_sheets_enabled" value="1" <?php checked($edit_mode && isset($edit_form->google_sheets_enabled) ? $edit_form->google_sheets_enabled : 0); ?>>
                        Enable Google Sheets Integration
                    </label>
                    <p class="description">Send form submissions to a Google Sheet</p>
                </td>
            </tr>
            <tr class="google-sheets-fields" style="<?php echo ($edit_mode && isset($edit_form->google_sheets_enabled) && $edit_form->google_sheets_enabled) ? '' : 'display: none;'; ?>">
                <th>Google Spreadsheet ID</th>
                <td>
                    <input type="text" name="google_sheets_spreadsheet_id" value="<?php echo $edit_mode ? esc_attr($edit_form->google_sheets_spreadsheet_id ?? '') : ''; ?>" class="regular-text">
                    <p class="description">Enter the ID of the Google Spreadsheet (found in the URL)</p>
                </td>
            </tr>
            <tr class="google-sheets-fields" style="<?php echo ($edit_mode && isset($edit_form->google_sheets_enabled) && $edit_form->google_sheets_enabled) ? '' : 'display: none;'; ?>">
                <th>Worksheet Name</th>
                <td>
                    <input type="text" name="google_sheets_worksheet_name" value="<?php echo $edit_mode ? esc_attr($edit_form->google_sheets_worksheet_name ?? '') : ''; ?>" class="regular-text" placeholder="Sheet1">
                    <p class="description">Enter the name of the worksheet (default: Sheet1)</p>
                </td>
            </tr>

            <!-- Zapier Integration -->
            <tr>
                <th>Zapier Integration</th>
                <td>
                    <label>
                        <input type="checkbox" name="zapier_enabled" value="1" <?php checked($edit_mode && isset($edit_form->zapier_enabled) ? $edit_form->zapier_enabled : 0); ?>>
                        Enable Zapier Integration
                    </label>
                    <p class="description">Send form submissions to Zapier webhooks</p>
                </td>
            </tr>
            <tr class="zapier-fields" style="<?php echo ($edit_mode && isset($edit_form->zapier_enabled) && $edit_form->zapier_enabled) ? '' : 'display: none;'; ?>">
                <th>Webhook URLs</th>
                <td>
                    <div id="zapier-webhooks-container">
                        <?php
                        $webhooks = array();
                        if ($edit_mode && isset($edit_form->zapier_webhooks)) {
                            $webhooks = maybe_unserialize($edit_form->zapier_webhooks);
                        }

                        if (empty($webhooks)) {
                            // Add one empty field by default
                            $webhooks = array('');
                        }

                        foreach ($webhooks as $index => $webhook) {
                            ?>
                            <div class="zapier-webhook-row" style="margin-bottom: 10px;">
                                <input type="url" name="zapier_webhooks[]" value="<?php echo esc_attr($webhook); ?>" class="regular-text zapier-webhook-url" placeholder="https://hooks.zapier.com/hooks/catch/...">
                                <button type="button" class="button test-webhook" data-index="<?php echo $index; ?>">Test</button>
                                <button type="button" class="button remove-webhook" style="color: #a00;">Remove</button>
                                <span class="webhook-test-result" style="margin-left: 10px;"></span>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                    <button type="button" class="button add-webhook">Add Webhook</button>
                    <p class="description">Enter the webhook URLs from your Zapier Zaps</p>
                    <p class="description">
                        <a href="https://zapier.com/apps/webhook/integrations" target="_blank">Learn more about Zapier webhooks</a>
                    </p>
                </td>
            </tr>
            <tr>
                <th>Email Message</th>
                <td><textarea name="notify_message" rows="5" placeholder="You can use {{field_slug}} placeholders."><?php echo $edit_mode ? esc_textarea($edit_form->notify_message ?? '') : ''; ?></textarea></td>
            </tr>
        </table>

        <?php if ($selected_table_id && $fields): ?>
            <h3>Select Fields to Show in the Form</h3>
            <p class="description">Drag and drop fields to reorder them in the form. The order you set here will be used when displaying the form.</p>
            <ul id="sortable-fields-list" class="dab-sortable-fields" style="list-style:none;padding-left:0;">
                <?php foreach ($fields as $f): ?>
                    <li class="dab-field-item" data-field-id="<?php echo esc_attr($f->id); ?>">
                        <div class="dab-field-handle"><span class="dashicons dashicons-menu"></span></div>
                        <label>
                            <input type="checkbox" name="selected_fields[]" value="<?php echo esc_attr($f->field_slug); ?>"
                                <?php
                                    $selected = $edit_mode ? maybe_unserialize($edit_form->fields) : (isset($_POST['selected_fields']) ? $_POST['selected_fields'] : []);
                                    checked(in_array($f->field_slug, $selected));
                                ?>>
                            <?php echo esc_html($f->field_label); ?>
                        </label>
                    </li>
                <?php endforeach; ?>
            </ul>
            <input type="hidden" name="field_order" id="field-order-input" value="">

            <h3>Conditional Logic (Visual Builder)</h3>
            <div id="logic-builder-container"></div>
            <textarea name="conditional_logic_json" id="conditional_logic_json" style="display:none;"><?php
                echo $edit_mode ? esc_textarea($edit_form->conditional_logic ?? '') : '';
            ?></textarea>
        <?php endif; ?>

        <p><input type="submit" name="dab_save_form" class="button button-primary" value="<?php echo $edit_mode ? 'Update Form' : 'Create Form'; ?>"></p>
    </form>

    <script>
    jQuery(document).ready(function($) {
        // Toggle Google Sheets fields visibility
        $('input[name="google_sheets_enabled"]').on('change', function() {
            if ($(this).is(':checked')) {
                $('.google-sheets-fields').show();
            } else {
                $('.google-sheets-fields').hide();
            }
        });

        // Toggle Zapier fields visibility
        $('input[name="zapier_enabled"]').on('change', function() {
            if ($(this).is(':checked')) {
                $('.zapier-fields').show();
            } else {
                $('.zapier-fields').hide();
            }
        });

        // Add webhook button
        $('.add-webhook').on('click', function(e) {
            e.preventDefault();

            const webhookRow = `
                <div class="zapier-webhook-row" style="margin-bottom: 10px;">
                    <input type="url" name="zapier_webhooks[]" value="" class="regular-text zapier-webhook-url" placeholder="https://hooks.zapier.com/hooks/catch/...">
                    <button type="button" class="button test-webhook">Test</button>
                    <button type="button" class="button remove-webhook" style="color: #a00;">Remove</button>
                    <span class="webhook-test-result" style="margin-left: 10px;"></span>
                </div>
            `;

            $('#zapier-webhooks-container').append(webhookRow);
        });

        // Remove webhook button
        $(document).on('click', '.remove-webhook', function(e) {
            e.preventDefault();

            // Don't remove the last webhook row
            if ($('.zapier-webhook-row').length > 1) {
                $(this).closest('.zapier-webhook-row').remove();
            } else {
                // Clear the input instead
                $(this).closest('.zapier-webhook-row').find('input').val('');
                $(this).closest('.zapier-webhook-row').find('.webhook-test-result').html('');
            }
        });

        // Test webhook button
        $(document).on('click', '.test-webhook', function(e) {
            e.preventDefault();

            const $button = $(this);
            const $row = $button.closest('.zapier-webhook-row');
            const $result = $row.find('.webhook-test-result');
            const webhookUrl = $row.find('input').val();

            if (!webhookUrl) {
                $result.html('<span style="color: #a00;">Please enter a webhook URL</span>');
                return;
            }

            // Disable button and show loading
            $button.prop('disabled', true).text('Testing...');
            $result.html('<span style="color: #666;">Testing webhook...</span>');

            // Send AJAX request to test webhook
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'dab_test_zapier_webhook',
                    nonce: '<?php echo wp_create_nonce('dab_zapier_nonce'); ?>',
                    webhook_url: webhookUrl
                },
                success: function(response) {
                    if (response.success) {
                        $result.html('<span style="color: #0a0;">✓ ' + response.data.message + '</span>');
                    } else {
                        $result.html('<span style="color: #a00;">✗ ' + response.data.message + '</span>');
                    }
                },
                error: function() {
                    $result.html('<span style="color: #a00;">✗ Error testing webhook</span>');
                },
                complete: function() {
                    // Re-enable button
                    $button.prop('disabled', false).text('Test');
                }
            });
        });
    });
    </script>

    <hr>
    <h2>Existing Forms</h2>
    <?php if (!empty($forms)): ?>
        <table class="widefat striped">
            <thead>
                <tr>
                    <th>Form Name</th>
                    <th>Table</th>
                    <th>Fields Count</th>
                    <th>Shortcode</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($forms as $form): ?>
                    <?php
                        $table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $form->table_id));
                        $field_list = maybe_unserialize($form->fields);
                    ?>
                    <tr>
                        <td><?php echo esc_html($form->form_name); ?></td>
                        <td><?php echo $table ? esc_html($table->table_label) : '—'; ?></td>
                        <td><?php echo is_array($field_list) ? count($field_list) : 0; ?></td>
                        <td><code>[dab_form id="<?php echo esc_attr($form->id); ?>"]</code></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=dab_forms&edit_form=' . $form->id); ?>">Edit</a> |
                            <a href="<?php echo admin_url('admin.php?page=dab_forms&delete_form=' . $form->id); ?>"
                               onclick="return confirm('Are you sure you want to delete this form?');">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No forms have been created yet.</p>
    <?php endif; ?>
</div>
