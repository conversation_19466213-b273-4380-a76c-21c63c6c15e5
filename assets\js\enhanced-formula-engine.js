/**
 * Enhanced Formula Engine JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize enhanced formula engine
    function initEnhancedFormulaEngine() {
        bindFormulaEvents();
        initFormulaBuilder();
        initFormulaValidation();
        updateFormulaFields();
    }

    // Bind formula-related events
    function bindFormulaEvents() {
        // Real-time formula validation
        $(document).on('input', '.dab-formula-expression', debounce(validateFormula, 500));
        
        // Formula field updates
        $(document).on('input change', '.dab-form input, .dab-form select, .dab-form textarea', updateFormulaFields);
        
        // Function insertion
        $(document).on('click', '.dab-formula-function', insertFunction);
        
        // Field reference insertion
        $(document).on('click', '.dab-formula-field-ref', insertFieldReference);
        
        // Formula help toggle
        $(document).on('click', '.dab-formula-help-toggle', toggleFormulaHelp);
    }

    // Initialize formula builder interface
    function initFormulaBuilder() {
        $('.dab-formula-field-container').each(function() {
            const container = $(this);
            const formulaInput = container.find('.dab-formula-expression');
            
            if (formulaInput.length) {
                createFormulaBuilder(container, formulaInput);
            }
        });
    }

    // Create formula builder interface
    function createFormulaBuilder(container, formulaInput) {
        const builderId = 'formula-builder-' + Math.random().toString(36).substr(2, 9);
        
        const builderHtml = `
            <div class="dab-formula-builder" id="${builderId}">
                <div class="dab-formula-builder-header">
                    <h4>Formula Builder</h4>
                    <button type="button" class="dab-formula-help-toggle" title="Show Help">
                        <span class="dashicons dashicons-editor-help"></span>
                    </button>
                </div>
                
                <div class="dab-formula-builder-content">
                    <div class="dab-formula-functions">
                        <h5>Functions</h5>
                        <div class="dab-function-categories">
                            ${createFunctionCategories()}
                        </div>
                    </div>
                    
                    <div class="dab-formula-fields">
                        <h5>Fields</h5>
                        <div class="dab-field-references">
                            ${createFieldReferences()}
                        </div>
                    </div>
                </div>
                
                <div class="dab-formula-preview">
                    <h5>Preview</h5>
                    <div class="dab-formula-result"></div>
                </div>
                
                <div class="dab-formula-help" style="display: none;">
                    <h5>Formula Help</h5>
                    <div class="dab-formula-help-content">
                        ${createFormulaHelp()}
                    </div>
                </div>
            </div>
        `;
        
        container.append(builderHtml);
        
        // Update field references when table changes
        const tableSelect = container.closest('form').find('select[name="table_id"]');
        if (tableSelect.length) {
            tableSelect.on('change', function() {
                updateFieldReferences(builderId, $(this).val());
            });
        }
    }

    // Create function categories
    function createFunctionCategories() {
        const functions = window.dabFormulaEngine.functions;
        const categories = {
            'Mathematical': ['SUM', 'AVERAGE', 'MIN', 'MAX', 'COUNT', 'ROUND', 'ABS', 'POWER', 'SQRT'],
            'Date/Time': ['NOW', 'TODAY', 'YEAR', 'MONTH', 'DAY', 'DATEDIFF', 'DATEADD'],
            'Text': ['CONCAT', 'UPPER', 'LOWER', 'LEN', 'LEFT', 'RIGHT', 'MID'],
            'Conditional': ['IF', 'ISNULL', 'ISBLANK'],
            'Cross-table': ['LOOKUP', 'SUMIF', 'COUNTIF']
        };
        
        let html = '';
        
        Object.keys(categories).forEach(category => {
            html += `<div class="dab-function-category">
                <h6>${category}</h6>
                <div class="dab-function-list">`;
            
            categories[category].forEach(funcName => {
                if (functions[funcName]) {
                    html += `<button type="button" class="dab-formula-function" data-function="${funcName}" title="${functions[funcName].description}">
                        ${funcName}
                    </button>`;
                }
            });
            
            html += '</div></div>';
        });
        
        return html;
    }

    // Create field references
    function createFieldReferences() {
        // This will be populated dynamically based on the selected table
        return '<div class="dab-field-list">Select a table to see available fields</div>';
    }

    // Create formula help content
    function createFormulaHelp() {
        return `
            <div class="dab-help-section">
                <h6>Basic Syntax</h6>
                <ul>
                    <li>Use <code>{field_name}</code> to reference field values</li>
                    <li>Use functions like <code>SUM({field1}, {field2})</code></li>
                    <li>Combine with operators: <code>+</code>, <code>-</code>, <code>*</code>, <code>/</code></li>
                    <li>Use parentheses for grouping: <code>({field1} + {field2}) * 2</code></li>
                </ul>
            </div>
            
            <div class="dab-help-section">
                <h6>Examples</h6>
                <ul>
                    <li><code>SUM({price}, {tax})</code> - Add price and tax</li>
                    <li><code>IF({quantity} > 10, {price} * 0.9, {price})</code> - Apply discount</li>
                    <li><code>DATEDIFF({end_date}, {start_date})</code> - Days between dates</li>
                    <li><code>CONCAT({first_name}, " ", {last_name})</code> - Full name</li>
                </ul>
            </div>
        `;
    }

    // Update field references for a specific table
    function updateFieldReferences(builderId, tableId) {
        if (!tableId) {
            return;
        }
        
        $.ajax({
            url: window.dabFormulaEngine.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_get_table_fields',
                table_id: tableId,
                nonce: window.dabFormulaEngine.nonce
            },
            success: function(response) {
                if (response.success) {
                    const fieldList = $('#' + builderId + ' .dab-field-list');
                    let html = '';
                    
                    response.data.forEach(field => {
                        html += `<button type="button" class="dab-formula-field-ref" data-field="${field.field_slug}" title="${field.field_type}">
                            ${field.field_label}
                        </button>`;
                    });
                    
                    fieldList.html(html);
                }
            }
        });
    }

    // Insert function into formula
    function insertFunction() {
        const functionName = $(this).data('function');
        const functions = window.dabFormulaEngine.functions;
        const functionDef = functions[functionName];
        
        if (functionDef) {
            const formulaInput = $(this).closest('.dab-formula-builder').siblings('.dab-formula-expression');
            const currentValue = formulaInput.val();
            const cursorPos = formulaInput[0].selectionStart;
            
            // Insert function template
            const template = functionDef.syntax.replace(/[^(),\s]+/g, match => {
                if (match.includes('value') || match.includes('text') || match.includes('date')) {
                    return '{field}';
                }
                return match;
            });
            
            const newValue = currentValue.slice(0, cursorPos) + template + currentValue.slice(cursorPos);
            formulaInput.val(newValue);
            
            // Position cursor inside first parameter
            const firstParamPos = newValue.indexOf('{field}', cursorPos);
            if (firstParamPos !== -1) {
                formulaInput[0].setSelectionRange(firstParamPos, firstParamPos + 7);
            }
            
            formulaInput.focus();
            validateFormula.call(formulaInput[0]);
        }
    }

    // Insert field reference into formula
    function insertFieldReference() {
        const fieldSlug = $(this).data('field');
        const formulaInput = $(this).closest('.dab-formula-builder').siblings('.dab-formula-expression');
        const currentValue = formulaInput.val();
        const cursorPos = formulaInput[0].selectionStart;
        
        const fieldRef = '{' + fieldSlug + '}';
        const newValue = currentValue.slice(0, cursorPos) + fieldRef + currentValue.slice(cursorPos);
        formulaInput.val(newValue);
        
        // Position cursor after the field reference
        const newCursorPos = cursorPos + fieldRef.length;
        formulaInput[0].setSelectionRange(newCursorPos, newCursorPos);
        
        formulaInput.focus();
        validateFormula.call(formulaInput[0]);
    }

    // Toggle formula help
    function toggleFormulaHelp() {
        const helpSection = $(this).closest('.dab-formula-builder').find('.dab-formula-help');
        helpSection.slideToggle();
        
        const icon = $(this).find('.dashicons');
        icon.toggleClass('dashicons-editor-help dashicons-no-alt');
    }

    // Validate formula
    function validateFormula() {
        const formulaInput = $(this);
        const formula = formulaInput.val();
        const resultContainer = formulaInput.siblings('.dab-formula-builder').find('.dab-formula-result');
        
        if (!formula.trim()) {
            resultContainer.html('<span class="dab-formula-empty">Enter a formula</span>');
            return;
        }
        
        // Show loading state
        resultContainer.html('<span class="dab-formula-loading">Validating...</span>');
        
        $.ajax({
            url: window.dabFormulaEngine.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_validate_formula',
                formula: formula,
                nonce: window.dabFormulaEngine.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContainer.html('<span class="dab-formula-valid">✓ Formula is valid</span>');
                    formulaInput.removeClass('dab-formula-error');
                } else {
                    const errors = Array.isArray(response.data) ? response.data : [response.data];
                    resultContainer.html('<span class="dab-formula-error">✗ ' + errors.join(', ') + '</span>');
                    formulaInput.addClass('dab-formula-error');
                }
            },
            error: function() {
                resultContainer.html('<span class="dab-formula-error">✗ Validation failed</span>');
                formulaInput.addClass('dab-formula-error');
            }
        });
    }

    // Initialize formula validation
    function initFormulaValidation() {
        $('.dab-formula-expression').each(function() {
            validateFormula.call(this);
        });
    }

    // Update formula fields when other fields change
    function updateFormulaFields() {
        $('.dab-formula-field').each(function() {
            const formulaField = $(this);
            const formula = formulaField.data('formula');
            
            if (formula) {
                calculateFormulaValue(formulaField, formula);
            }
        });
    }

    // Calculate formula value
    function calculateFormulaValue(field, formula) {
        // Collect current field values
        const context = {
            fields: {}
        };
        
        $('.dab-form input, .dab-form select, .dab-form textarea').each(function() {
            const fieldName = $(this).attr('name');
            if (fieldName) {
                context.fields[fieldName] = $(this).val();
            }
        });
        
        $.ajax({
            url: window.dabFormulaEngine.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dab_calculate_formula',
                formula: formula,
                context: context,
                nonce: window.dabFormulaEngine.nonce
            },
            success: function(response) {
                if (response.success) {
                    field.val(response.data);
                    field.removeClass('dab-formula-error');
                } else {
                    field.val('');
                    field.addClass('dab-formula-error');
                }
            },
            error: function() {
                field.val('');
                field.addClass('dab-formula-error');
            }
        });
    }

    // Debounce function to limit API calls
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Initialize the enhanced formula engine
    initEnhancedFormulaEngine();
});
