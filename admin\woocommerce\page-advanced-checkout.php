<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo '<div class="notice notice-error"><p>' . __('WooCommerce is required for this feature.', 'db-app-builder') . '</p></div>';
    return;
}

// Handle configuration updates
if (isset($_POST['save_checkout_config']) && wp_verify_nonce($_POST['dab_checkout_nonce'], 'dab_save_checkout_config')) {
    global $wpdb;
    $config_table = $wpdb->prefix . 'dab_wc_checkout_config';
    
    $configs = array(
        'multi_step_enabled' => isset($_POST['multi_step_enabled']) ? '1' : '0',
        'progress_bar_enabled' => isset($_POST['progress_bar_enabled']) ? '1' : '0',
        'auto_save_enabled' => isset($_POST['auto_save_enabled']) ? '1' : '0',
        'abandoned_cart_recovery' => isset($_POST['abandoned_cart_recovery']) ? '1' : '0'
    );
    
    foreach ($configs as $config_name => $config_value) {
        $wpdb->replace(
            $config_table,
            array(
                'config_name' => $config_name,
                'config_value' => $config_value,
                'config_type' => 'general',
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s')
        );
    }
    
    echo '<div class="notice notice-success"><p>' . __('Checkout configuration saved successfully.', 'db-app-builder') . '</p></div>';
}

// Get current configurations
$current_configs = array();
global $wpdb;
$config_table = $wpdb->prefix . 'dab_wc_checkout_config';
$configs = $wpdb->get_results("SELECT config_name, config_value FROM $config_table WHERE is_active = 1");
foreach ($configs as $config) {
    $current_configs[$config->config_name] = $config->config_value;
}

// Get checkout analytics
$analytics_table = $wpdb->prefix . 'dab_wc_checkout_analytics';
$abandoned_table = $wpdb->prefix . 'dab_wc_abandoned_carts';

$checkout_stats = array(
    'total_sessions' => $wpdb->get_var("SELECT COUNT(DISTINCT session_id) FROM $analytics_table WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)"),
    'completed_checkouts' => $wpdb->get_var("SELECT COUNT(DISTINCT session_id) FROM $analytics_table WHERE completed = 1 AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)"),
    'abandoned_carts' => $wpdb->get_var("SELECT COUNT(*) FROM $abandoned_table WHERE abandoned_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND recovered = 0"),
    'average_completion_time' => $wpdb->get_var("SELECT AVG(time_spent) FROM $analytics_table WHERE completed = 1 AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")
);

$conversion_rate = $checkout_stats['total_sessions'] > 0 ? 
    ($checkout_stats['completed_checkouts'] / $checkout_stats['total_sessions']) * 100 : 0;

// Get recent abandoned carts
$recent_abandoned = $wpdb->get_results("
    SELECT ac.*, u.display_name as customer_name 
    FROM $abandoned_table ac 
    LEFT JOIN {$wpdb->users} u ON ac.user_id = u.ID 
    WHERE ac.abandoned_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
    ORDER BY ac.abandoned_at DESC 
    LIMIT 10
");

// Get checkout field configurations
$fields_table = $wpdb->prefix . 'dab_wc_checkout_fields';
$checkout_fields = $wpdb->get_results("SELECT * FROM $fields_table WHERE is_active = 1 ORDER BY field_step ASC, field_order ASC");
?>

<div class="wrap">
    <h1><?php _e('WooCommerce Advanced Checkout', 'db-app-builder'); ?></h1>
    
    <div class="dab-woocommerce-integration-header">
        <p><?php _e('Configure advanced checkout features including multi-step checkout, conditional fields, and checkout optimization.', 'db-app-builder'); ?></p>
    </div>

    <!-- Checkout Statistics -->
    <div class="dab-stats-grid">
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($checkout_stats['total_sessions']); ?></div>
            <div class="dab-stat-label"><?php _e('Checkout Sessions (30 days)', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($conversion_rate, 1); ?>%</div>
            <div class="dab-stat-label"><?php _e('Conversion Rate', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card dab-stat-warning">
            <div class="dab-stat-number"><?php echo number_format($checkout_stats['abandoned_carts']); ?></div>
            <div class="dab-stat-label"><?php _e('Abandoned Carts (30 days)', 'db-app-builder'); ?></div>
        </div>
        
        <div class="dab-stat-card">
            <div class="dab-stat-number"><?php echo number_format($checkout_stats['average_completion_time'] / 60, 1); ?></div>
            <div class="dab-stat-label"><?php _e('Avg Completion Time (min)', 'db-app-builder'); ?></div>
        </div>
    </div>

    <div class="dab-admin-container">
        <div class="dab-admin-main">
            <!-- Checkout Configuration -->
            <div class="dab-card">
                <h2><?php _e('Checkout Configuration', 'db-app-builder'); ?></h2>
                
                <form method="post" action="">
                    <?php wp_nonce_field('dab_save_checkout_config', 'dab_checkout_nonce'); ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Multi-Step Checkout', 'db-app-builder'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="multi_step_enabled" value="1" 
                                           <?php checked(isset($current_configs['multi_step_enabled']) ? $current_configs['multi_step_enabled'] : 0, 1); ?>>
                                    <?php _e('Enable multi-step checkout process', 'db-app-builder'); ?>
                                </label>
                                <p class="description"><?php _e('Break checkout into multiple steps for better user experience', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Progress Bar', 'db-app-builder'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="progress_bar_enabled" value="1" 
                                           <?php checked(isset($current_configs['progress_bar_enabled']) ? $current_configs['progress_bar_enabled'] : 0, 1); ?>>
                                    <?php _e('Show progress bar during checkout', 'db-app-builder'); ?>
                                </label>
                                <p class="description"><?php _e('Display checkout progress to customers', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Auto-Save Progress', 'db-app-builder'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_save_enabled" value="1" 
                                           <?php checked(isset($current_configs['auto_save_enabled']) ? $current_configs['auto_save_enabled'] : 0, 1); ?>>
                                    <?php _e('Automatically save checkout progress', 'db-app-builder'); ?>
                                </label>
                                <p class="description"><?php _e('Save customer progress to prevent data loss', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Abandoned Cart Recovery', 'db-app-builder'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="abandoned_cart_recovery" value="1" 
                                           <?php checked(isset($current_configs['abandoned_cart_recovery']) ? $current_configs['abandoned_cart_recovery'] : 0, 1); ?>>
                                    <?php _e('Enable abandoned cart recovery tracking', 'db-app-builder'); ?>
                                </label>
                                <p class="description"><?php _e('Track and recover abandoned checkout sessions', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="save_checkout_config" class="button-primary" value="<?php _e('Save Configuration', 'db-app-builder'); ?>">
                    </p>
                </form>
            </div>

            <!-- Checkout Fields -->
            <div class="dab-card">
                <h2><?php _e('Checkout Fields', 'db-app-builder'); ?></h2>
                
                <?php if (empty($checkout_fields)): ?>
                    <p><?php _e('No custom checkout fields configured yet.', 'db-app-builder'); ?></p>
                    <p><em><?php _e('Custom checkout fields allow you to collect additional information during the checkout process.', 'db-app-builder'); ?></em></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Field Name', 'db-app-builder'); ?></th>
                                <th><?php _e('Label', 'db-app-builder'); ?></th>
                                <th><?php _e('Type', 'db-app-builder'); ?></th>
                                <th><?php _e('Section', 'db-app-builder'); ?></th>
                                <th><?php _e('Step', 'db-app-builder'); ?></th>
                                <th><?php _e('Required', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($checkout_fields as $field): ?>
                                <tr>
                                    <td><code><?php echo esc_html($field->field_name); ?></code></td>
                                    <td><?php echo esc_html($field->field_label); ?></td>
                                    <td><?php echo esc_html(ucfirst($field->field_type)); ?></td>
                                    <td><?php echo esc_html(ucfirst($field->field_section)); ?></td>
                                    <td><?php echo esc_html($field->field_step); ?></td>
                                    <td><?php echo $field->required ? '✓' : '—'; ?></td>
                                    <td>
                                        <button class="button button-small" onclick="editCheckoutField(<?php echo $field->id; ?>)"><?php _e('Edit', 'db-app-builder'); ?></button>
                                        <button class="button button-small button-link-delete" onclick="deleteCheckoutField(<?php echo $field->id; ?>)"><?php _e('Delete', 'db-app-builder'); ?></button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
                
                <p>
                    <button class="button button-primary" onclick="showAddFieldForm()"><?php _e('Add Checkout Field', 'db-app-builder'); ?></button>
                </p>
            </div>

            <!-- Recent Abandoned Carts -->
            <div class="dab-card">
                <h2><?php _e('Recent Abandoned Carts', 'db-app-builder'); ?></h2>
                
                <?php if (empty($recent_abandoned)): ?>
                    <p><?php _e('No abandoned carts in the last 7 days.', 'db-app-builder'); ?></p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Customer', 'db-app-builder'); ?></th>
                                <th><?php _e('Email', 'db-app-builder'); ?></th>
                                <th><?php _e('Cart Total', 'db-app-builder'); ?></th>
                                <th><?php _e('Abandoned At', 'db-app-builder'); ?></th>
                                <th><?php _e('Recovery Status', 'db-app-builder'); ?></th>
                                <th><?php _e('Actions', 'db-app-builder'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_abandoned as $cart): ?>
                                <tr>
                                    <td>
                                        <?php if ($cart->customer_name): ?>
                                            <?php echo esc_html($cart->customer_name); ?>
                                        <?php else: ?>
                                            <em><?php _e('Guest', 'db-app-builder'); ?></em>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo esc_html($cart->email); ?></td>
                                    <td><?php echo wc_price($cart->cart_total); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($cart->abandoned_at)); ?></td>
                                    <td>
                                        <?php if ($cart->recovery_email_sent): ?>
                                            <span class="dab-status-sent"><?php _e('Email Sent', 'db-app-builder'); ?></span>
                                        <?php else: ?>
                                            <span class="dab-status-pending"><?php _e('Pending', 'db-app-builder'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="button button-small" onclick="viewCartDetails(<?php echo $cart->id; ?>)"><?php _e('View Details', 'db-app-builder'); ?></button>
                                        <?php if (!$cart->recovery_email_sent): ?>
                                            <button class="button button-small" onclick="sendRecoveryEmail(<?php echo $cart->id; ?>)"><?php _e('Send Recovery', 'db-app-builder'); ?></button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <div class="dab-admin-sidebar">
            <!-- Checkout Optimization Tips -->
            <div class="dab-card">
                <h3><?php _e('Checkout Optimization Tips', 'db-app-builder'); ?></h3>
                <ul class="dab-optimization-tips">
                    <li><?php _e('Enable multi-step checkout to reduce form complexity', 'db-app-builder'); ?></li>
                    <li><?php _e('Use progress bars to show checkout completion status', 'db-app-builder'); ?></li>
                    <li><?php _e('Auto-save progress to prevent data loss', 'db-app-builder'); ?></li>
                    <li><?php _e('Minimize required fields to reduce friction', 'db-app-builder'); ?></li>
                    <li><?php _e('Enable guest checkout for faster conversions', 'db-app-builder'); ?></li>
                    <li><?php _e('Use conditional fields to show relevant options only', 'db-app-builder'); ?></li>
                </ul>
            </div>

            <!-- Quick Actions -->
            <div class="dab-card">
                <h3><?php _e('Quick Actions', 'db-app-builder'); ?></h3>
                <ul class="dab-quick-actions">
                    <li><a href="#" onclick="exportCheckoutAnalytics()"><?php _e('Export Checkout Analytics', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="testCheckoutFlow()"><?php _e('Test Checkout Flow', 'db-app-builder'); ?></a></li>
                    <li><a href="#" onclick="previewCheckoutSteps()"><?php _e('Preview Checkout Steps', 'db-app-builder'); ?></a></li>
                    <li><a href="<?php echo wc_get_checkout_url(); ?>" target="_blank"><?php _e('View Live Checkout', 'db-app-builder'); ?></a></li>
                </ul>
            </div>

            <!-- Checkout Performance -->
            <div class="dab-card">
                <h3><?php _e('Performance Metrics', 'db-app-builder'); ?></h3>
                <div class="dab-performance-metrics">
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Conversion Rate', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format($conversion_rate, 1); ?>%</div>
                    </div>
                    
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Abandonment Rate', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format(100 - $conversion_rate, 1); ?>%</div>
                    </div>
                    
                    <div class="dab-metric">
                        <div class="dab-metric-label"><?php _e('Avg Time to Complete', 'db-app-builder'); ?></div>
                        <div class="dab-metric-value"><?php echo number_format($checkout_stats['average_completion_time'] / 60, 1); ?> min</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editCheckoutField(fieldId) {
    // TODO: Implement field editing modal
    alert('<?php _e('Checkout field editing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function deleteCheckoutField(fieldId) {
    if (confirm('<?php _e('Are you sure you want to delete this field?', 'db-app-builder'); ?>')) {
        // TODO: Implement field deletion
        alert('<?php _e('Field deletion will be implemented in the next update.', 'db-app-builder'); ?>');
    }
}

function showAddFieldForm() {
    // TODO: Implement add field modal
    alert('<?php _e('Add field form will be implemented in the next update.', 'db-app-builder'); ?>');
}

function viewCartDetails(cartId) {
    // TODO: Implement cart details modal
    alert('<?php _e('Cart details view will be implemented in the next update.', 'db-app-builder'); ?>');
}

function sendRecoveryEmail(cartId) {
    if (confirm('<?php _e('Send recovery email for this abandoned cart?', 'db-app-builder'); ?>')) {
        // TODO: Implement recovery email sending
        alert('<?php _e('Recovery email functionality will be implemented in the next update.', 'db-app-builder'); ?>');
    }
}

function exportCheckoutAnalytics() {
    // TODO: Implement analytics export
    alert('<?php _e('Analytics export will be implemented in the next update.', 'db-app-builder'); ?>');
}

function testCheckoutFlow() {
    // TODO: Implement checkout flow testing
    alert('<?php _e('Checkout flow testing will be implemented in the next update.', 'db-app-builder'); ?>');
}

function previewCheckoutSteps() {
    // TODO: Implement checkout steps preview
    alert('<?php _e('Checkout steps preview will be implemented in the next update.', 'db-app-builder'); ?>');
}
</script>

<style>
.dab-woocommerce-integration-header {
    background: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-left: 4px solid #2271b1;
    padding: 15px;
    margin: 20px 0;
}

.dab-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dab-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.dab-stat-card.dab-stat-warning {
    border-left: 4px solid #f0b849;
}

.dab-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.dab-stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.dab-admin-container {
    display: flex;
    gap: 20px;
}

.dab-admin-main {
    flex: 2;
}

.dab-admin-sidebar {
    flex: 1;
}

.dab-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.dab-card h2, .dab-card h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.dab-status-sent {
    color: #00a32a;
    font-weight: bold;
}

.dab-status-pending {
    color: #f0b849;
    font-weight: bold;
}

.dab-optimization-tips {
    list-style: disc;
    padding-left: 20px;
    margin: 15px 0;
}

.dab-optimization-tips li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.dab-quick-actions {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.dab-quick-actions li {
    padding: 5px 0;
}

.dab-quick-actions a {
    text-decoration: none;
}

.dab-performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.dab-metric {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.dab-metric-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.dab-metric-value {
    font-size: 18px;
    font-weight: bold;
    color: #2271b1;
}
</style>
