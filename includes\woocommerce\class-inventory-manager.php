<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Inventory Manager
 *
 * Manages advanced inventory features including real-time tracking, low stock alerts,
 * supplier management, and inventory forecasting
 */
class DAB_Inventory_Manager {

    /**
     * Initialize the Inventory Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Hook into WooCommerce inventory actions
        add_action('woocommerce_product_set_stock', array(__CLASS__, 'track_stock_change'), 10, 1);
        add_action('woocommerce_variation_set_stock', array(__CLASS__, 'track_stock_change'), 10, 1);

        // Low stock alerts
        add_action('woocommerce_low_stock', array(__CLASS__, 'handle_low_stock_alert'));
        add_action('woocommerce_no_stock', array(__CLASS__, 'handle_out_of_stock_alert'));

        // Custom inventory fields
        add_action('woocommerce_product_options_inventory_product_data', array(__CLASS__, 'add_inventory_fields'));
        add_action('woocommerce_process_product_meta', array(__CLASS__, 'save_inventory_fields'));

        // Supplier management
        add_action('add_meta_boxes', array(__CLASS__, 'add_supplier_meta_box'));
        add_action('save_post', array(__CLASS__, 'save_supplier_data'));

        // Inventory reports
        add_action('admin_menu', array(__CLASS__, 'add_inventory_reports_menu'));

        // AJAX handlers
        add_action('wp_ajax_dab_get_inventory_data', array(__CLASS__, 'ajax_get_inventory_data'));
        add_action('wp_ajax_dab_update_stock_levels', array(__CLASS__, 'ajax_update_stock_levels'));
        add_action('wp_ajax_dab_create_purchase_order', array(__CLASS__, 'ajax_create_purchase_order'));

        // Scheduled tasks
        add_action('dab_inventory_daily_check', array(__CLASS__, 'daily_inventory_check'));
        if (!wp_next_scheduled('dab_inventory_daily_check')) {
            wp_schedule_event(time(), 'daily', 'dab_inventory_daily_check');
        }
    }

    /**
     * Create database tables for inventory management
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Inventory tracking table
        $inventory_table = $wpdb->prefix . 'dab_wc_inventory_tracking';

        $sql_inventory = "CREATE TABLE $inventory_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT 0,
            stock_change int(11) NOT NULL,
            stock_level_before int(11) NOT NULL,
            stock_level_after int(11) NOT NULL,
            change_reason varchar(100) DEFAULT 'manual',
            order_id bigint(20) DEFAULT 0,
            user_id bigint(20) DEFAULT 0,
            notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY variation_id (variation_id),
            KEY order_id (order_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        // Suppliers table
        $suppliers_table = $wpdb->prefix . 'dab_wc_suppliers';

        $sql_suppliers = "CREATE TABLE $suppliers_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            supplier_name varchar(255) NOT NULL,
            supplier_email varchar(255),
            supplier_phone varchar(50),
            supplier_address text,
            supplier_website varchar(255),
            contact_person varchar(255),
            payment_terms varchar(100),
            lead_time_days int(11) DEFAULT 7,
            minimum_order_value decimal(10,2) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY supplier_name (supplier_name)
        ) $charset_collate;";

        // Product suppliers relationship table
        $product_suppliers_table = $wpdb->prefix . 'dab_wc_product_suppliers';

        $sql_product_suppliers = "CREATE TABLE $product_suppliers_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            supplier_id mediumint(9) NOT NULL,
            supplier_sku varchar(100),
            cost_price decimal(10,2),
            minimum_quantity int(11) DEFAULT 1,
            lead_time_days int(11) DEFAULT 7,
            is_primary tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY product_supplier (product_id, supplier_id),
            KEY product_id (product_id),
            KEY supplier_id (supplier_id)
        ) $charset_collate;";

        // Purchase orders table
        $purchase_orders_table = $wpdb->prefix . 'dab_wc_purchase_orders';

        $sql_purchase_orders = "CREATE TABLE $purchase_orders_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            po_number varchar(50) NOT NULL,
            supplier_id mediumint(9) NOT NULL,
            status varchar(20) DEFAULT 'pending',
            order_date date NOT NULL,
            expected_delivery_date date,
            actual_delivery_date date,
            total_amount decimal(10,2) DEFAULT 0,
            notes text,
            created_by bigint(20),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY po_number (po_number),
            KEY supplier_id (supplier_id),
            KEY status (status),
            KEY order_date (order_date)
        ) $charset_collate;";

        // Purchase order items table
        $po_items_table = $wpdb->prefix . 'dab_wc_purchase_order_items';

        $sql_po_items = "CREATE TABLE $po_items_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            purchase_order_id mediumint(9) NOT NULL,
            product_id bigint(20) NOT NULL,
            quantity_ordered int(11) NOT NULL,
            quantity_received int(11) DEFAULT 0,
            unit_cost decimal(10,2) NOT NULL,
            total_cost decimal(10,2) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY purchase_order_id (purchase_order_id),
            KEY product_id (product_id)
        ) $charset_collate;";

        // Low stock alerts table
        $alerts_table = $wpdb->prefix . 'dab_wc_stock_alerts';

        $sql_alerts = "CREATE TABLE $alerts_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT 0,
            alert_type varchar(20) NOT NULL,
            current_stock int(11) NOT NULL,
            threshold_level int(11) NOT NULL,
            alert_sent tinyint(1) DEFAULT 0,
            alert_sent_at datetime,
            resolved tinyint(1) DEFAULT 0,
            resolved_at datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY variation_id (variation_id),
            KEY alert_type (alert_type),
            KEY resolved (resolved)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_inventory);
        dbDelta($sql_suppliers);
        dbDelta($sql_product_suppliers);
        dbDelta($sql_purchase_orders);
        dbDelta($sql_po_items);
        dbDelta($sql_alerts);

        // Create default suppliers
        self::create_default_suppliers();
    }

    /**
     * Create default suppliers
     */
    public static function create_default_suppliers() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_suppliers';

        $default_suppliers = array(
            array(
                'supplier_name' => 'Default Supplier',
                'supplier_email' => '<EMAIL>',
                'contact_person' => 'Supplier Manager',
                'payment_terms' => 'Net 30',
                'lead_time_days' => 7,
                'minimum_order_value' => 100.00
            )
        );

        foreach ($default_suppliers as $supplier) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE supplier_name = %s",
                    $supplier['supplier_name']
                )
            );

            if (!$existing) {
                $wpdb->insert($table_name, $supplier);
            }
        }
    }

    /**
     * Track stock changes
     */
    public static function track_stock_change($product) {
        global $wpdb;

        $product_id = $product->get_id();
        $parent_id = $product->get_parent_id();
        $variation_id = $parent_id ? $product_id : 0;
        $actual_product_id = $parent_id ? $parent_id : $product_id;

        $current_stock = $product->get_stock_quantity();
        $previous_stock = get_post_meta($product_id, '_previous_stock', true);

        if ($previous_stock === '') {
            $previous_stock = $current_stock;
        }

        $stock_change = $current_stock - $previous_stock;

        if ($stock_change != 0) {
            $table_name = $wpdb->prefix . 'dab_wc_inventory_tracking';

            $wpdb->insert(
                $table_name,
                array(
                    'product_id' => $actual_product_id,
                    'variation_id' => $variation_id,
                    'stock_change' => $stock_change,
                    'stock_level_before' => $previous_stock,
                    'stock_level_after' => $current_stock,
                    'change_reason' => self::determine_change_reason(),
                    'user_id' => get_current_user_id()
                ),
                array('%d', '%d', '%d', '%d', '%d', '%s', '%d')
            );
        }

        // Update previous stock for next comparison
        update_post_meta($product_id, '_previous_stock', $current_stock);

        // Check for low stock alerts
        self::check_low_stock_alert($product);
    }

    /**
     * Determine the reason for stock change
     */
    public static function determine_change_reason() {
        // Check if we're in an order context
        if (did_action('woocommerce_checkout_order_processed')) {
            return 'order_sale';
        }

        // Check if we're in admin context
        if (is_admin()) {
            return 'manual_adjustment';
        }

        // Check if we're processing a refund
        if (did_action('woocommerce_order_refunded')) {
            return 'refund';
        }

        return 'unknown';
    }

    /**
     * Check for low stock alerts
     */
    public static function check_low_stock_alert($product) {
        $product_id = $product->get_id();
        $current_stock = $product->get_stock_quantity();
        $low_stock_threshold = $product->get_low_stock_amount();

        if ($low_stock_threshold && $current_stock <= $low_stock_threshold) {
            self::create_stock_alert($product_id, 'low_stock', $current_stock, $low_stock_threshold);
        }

        if ($current_stock <= 0) {
            self::create_stock_alert($product_id, 'out_of_stock', $current_stock, 0);
        }
    }

    /**
     * Create stock alert
     */
    public static function create_stock_alert($product_id, $alert_type, $current_stock, $threshold) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_stock_alerts';

        // Check if alert already exists and is not resolved
        $existing_alert = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $table_name WHERE product_id = %d AND alert_type = %s AND resolved = 0",
                $product_id,
                $alert_type
            )
        );

        if (!$existing_alert) {
            $wpdb->insert(
                $table_name,
                array(
                    'product_id' => $product_id,
                    'alert_type' => $alert_type,
                    'current_stock' => $current_stock,
                    'threshold_level' => $threshold
                ),
                array('%d', '%s', '%d', '%d')
            );

            // Send alert notification
            self::send_stock_alert_notification($product_id, $alert_type, $current_stock);
        }
    }

    /**
     * Send stock alert notification
     */
    public static function send_stock_alert_notification($product_id, $alert_type, $current_stock) {
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        $product_name = $product->get_name();
        $admin_email = get_option('admin_email');

        $subject = sprintf(
            __('[%s] %s Alert: %s', 'db-app-builder'),
            get_bloginfo('name'),
            ucfirst(str_replace('_', ' ', $alert_type)),
            $product_name
        );

        $message = sprintf(
            __('Product: %s (ID: %d)%sAlert Type: %s%sCurrent Stock: %d%s%sPlease review and take appropriate action.', 'db-app-builder'),
            $product_name,
            $product_id,
            "\n",
            ucfirst(str_replace('_', ' ', $alert_type)),
            "\n",
            $current_stock,
            "\n\n",
            "\n"
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Add inventory reports menu
     */
    public static function add_inventory_reports_menu() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        add_submenu_page(
            'woocommerce',
            __('Inventory Reports', 'db-app-builder'),
            __('Inventory Reports', 'db-app-builder'),
            'manage_woocommerce',
            'dab-inventory-reports',
            array(__CLASS__, 'render_inventory_reports_page')
        );
    }

    /**
     * Render inventory reports page
     */
    public static function render_inventory_reports_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Inventory Reports', 'db-app-builder'); ?></h1>

            <div class="dab-inventory-reports">
                <div class="dab-reports-grid">
                    <div class="dab-report-card">
                        <h3><?php _e('Low Stock Alert', 'db-app-builder'); ?></h3>
                        <div id="low-stock-report"></div>
                    </div>

                    <div class="dab-report-card">
                        <h3><?php _e('Stock Movement', 'db-app-builder'); ?></h3>
                        <div id="stock-movement-report"></div>
                    </div>

                    <div class="dab-report-card">
                        <h3><?php _e('Supplier Performance', 'db-app-builder'); ?></h3>
                        <div id="supplier-performance-report"></div>
                    </div>
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // Load inventory reports data
                loadInventoryReports();
            });

            function loadInventoryReports() {
                // Implementation for loading reports
                console.log('Loading inventory reports...');
            }
            </script>

            <style>
            .dab-reports-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }

            .dab-report-card {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .dab-report-card h3 {
                margin-top: 0;
                color: #333;
            }
            </style>
        </div>
        <?php
    }

    /**
     * Add supplier meta box to product edit screen
     */
    public static function add_supplier_meta_box() {
        // Only add meta boxes for WooCommerce products
        $screen = get_current_screen();
        if (!$screen) {
            return;
        }

        // Ensure screen ID is not null before comparison
        $screen_id = $screen->id !== null ? (string)$screen->id : '';
        if ($screen_id !== 'product') {
            return;
        }

        add_meta_box(
            'dab_product_supplier_info',
            __('Supplier Information', 'db-app-builder'),
            array(__CLASS__, 'render_supplier_meta_box'),
            'product',
            'normal',
            'high'
        );
    }

    /**
     * Render supplier meta box
     */
    public static function render_supplier_meta_box($post) {
        $product_id = $post->ID;
        $suppliers = self::get_product_suppliers($product_id);
        $all_suppliers = self::get_all_suppliers();

        wp_nonce_field('dab_supplier_data', 'dab_supplier_data_nonce');

        echo '<div class="dab-supplier-fields-container">';
        echo '<table class="form-table">';

        // Primary supplier selection
        echo '<tr>';
        echo '<th scope="row"><label for="dab_primary_supplier">' . __('Primary Supplier', 'db-app-builder') . '</label></th>';
        echo '<td>';
        echo '<select id="dab_primary_supplier" name="dab_primary_supplier">';
        echo '<option value="">' . __('Select a supplier', 'db-app-builder') . '</option>';

        $primary_supplier_id = '';
        foreach ($suppliers as $supplier) {
            if ($supplier->is_primary) {
                $primary_supplier_id = $supplier->supplier_id;
                break;
            }
        }

        foreach ($all_suppliers as $supplier) {
            $selected = selected($primary_supplier_id, $supplier->id, false);
            echo '<option value="' . esc_attr($supplier->id) . '"' . $selected . '>' . esc_html($supplier->supplier_name) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '</tr>';

        // Supplier SKU
        $supplier_sku = '';
        foreach ($suppliers as $supplier) {
            if ($supplier->is_primary && !empty($supplier->supplier_sku)) {
                $supplier_sku = $supplier->supplier_sku;
                break;
            }
        }

        echo '<tr>';
        echo '<th scope="row"><label for="dab_supplier_sku">' . __('Supplier SKU', 'db-app-builder') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="dab_supplier_sku" name="dab_supplier_sku" value="' . esc_attr($supplier_sku) . '" class="regular-text" />';
        echo '</td>';
        echo '</tr>';

        // Cost price
        $cost_price = '';
        foreach ($suppliers as $supplier) {
            if ($supplier->is_primary && !empty($supplier->cost_price)) {
                $cost_price = $supplier->cost_price;
                break;
            }
        }

        echo '<tr>';
        echo '<th scope="row"><label for="dab_cost_price">' . __('Cost Price', 'db-app-builder') . '</label></th>';
        echo '<td>';
        echo '<input type="number" id="dab_cost_price" name="dab_cost_price" value="' . esc_attr($cost_price) . '" step="0.01" min="0" class="regular-text" />';
        echo '</td>';
        echo '</tr>';

        // Minimum quantity
        $minimum_quantity = 1;
        foreach ($suppliers as $supplier) {
            if ($supplier->is_primary && !empty($supplier->minimum_quantity)) {
                $minimum_quantity = $supplier->minimum_quantity;
                break;
            }
        }

        echo '<tr>';
        echo '<th scope="row"><label for="dab_minimum_quantity">' . __('Minimum Order Quantity', 'db-app-builder') . '</label></th>';
        echo '<td>';
        echo '<input type="number" id="dab_minimum_quantity" name="dab_minimum_quantity" value="' . esc_attr($minimum_quantity) . '" min="1" class="regular-text" />';
        echo '</td>';
        echo '</tr>';

        // Lead time
        $lead_time = 7;
        foreach ($suppliers as $supplier) {
            if ($supplier->is_primary && !empty($supplier->lead_time_days)) {
                $lead_time = $supplier->lead_time_days;
                break;
            }
        }

        echo '<tr>';
        echo '<th scope="row"><label for="dab_lead_time">' . __('Lead Time (Days)', 'db-app-builder') . '</label></th>';
        echo '<td>';
        echo '<input type="number" id="dab_lead_time" name="dab_lead_time" value="' . esc_attr($lead_time) . '" min="0" class="regular-text" />';
        echo '</td>';
        echo '</tr>';

        echo '</table>';
        echo '</div>';
    }

    /**
     * Save supplier data
     */
    public static function save_supplier_data($post_id) {
        // Validate post_id to prevent null value warnings
        if (!$post_id || !is_numeric($post_id)) {
            return;
        }

        // Verify this is a valid request
        if (!isset($_POST['dab_supplier_data_nonce']) ||
            !wp_verify_nonce($_POST['dab_supplier_data_nonce'], 'dab_supplier_data')) {
            return;
        }

        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if this is a WooCommerce product
        $post_type = get_post_type($post_id);
        if ($post_type !== 'product') {
            return;
        }

        // Ensure WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Save supplier data
        $primary_supplier_id = isset($_POST['dab_primary_supplier']) ? intval($_POST['dab_primary_supplier']) : 0;
        $supplier_sku = isset($_POST['dab_supplier_sku']) ? sanitize_text_field($_POST['dab_supplier_sku']) : '';
        $cost_price = isset($_POST['dab_cost_price']) ? floatval($_POST['dab_cost_price']) : 0;
        $minimum_quantity = isset($_POST['dab_minimum_quantity']) ? intval($_POST['dab_minimum_quantity']) : 1;
        $lead_time = isset($_POST['dab_lead_time']) ? intval($_POST['dab_lead_time']) : 7;

        if ($primary_supplier_id > 0) {
            self::save_product_supplier_data($post_id, $primary_supplier_id, array(
                'supplier_sku' => $supplier_sku,
                'cost_price' => $cost_price,
                'minimum_quantity' => $minimum_quantity,
                'lead_time_days' => $lead_time,
                'is_primary' => 1
            ));
        }
    }

    /**
     * Get all suppliers
     */
    public static function get_all_suppliers() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_suppliers';

        return $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY supplier_name ASC"
        );
    }

    /**
     * Get suppliers for a specific product
     */
    public static function get_product_suppliers($product_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_product_suppliers';

        return $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE product_id = %d ORDER BY is_primary DESC",
                $product_id
            )
        );
    }

    /**
     * Save product supplier data
     */
    public static function save_product_supplier_data($product_id, $supplier_id, $data) {
        global $wpdb;

        // Validate inputs
        if (!$product_id || !$supplier_id || !is_array($data)) {
            return false;
        }

        $table_name = $wpdb->prefix . 'dab_wc_product_suppliers';

        // Remove existing primary supplier if setting a new one
        if (!empty($data['is_primary'])) {
            $wpdb->update(
                $table_name,
                array('is_primary' => 0),
                array('product_id' => $product_id),
                array('%d'),
                array('%d')
            );
        }

        // Check if relationship already exists
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $table_name WHERE product_id = %d AND supplier_id = %d",
                $product_id,
                $supplier_id
            )
        );

        $supplier_data = array(
            'product_id' => intval($product_id),
            'supplier_id' => intval($supplier_id),
            'supplier_sku' => sanitize_text_field($data['supplier_sku'] ?? ''),
            'cost_price' => floatval($data['cost_price'] ?? 0),
            'minimum_quantity' => intval($data['minimum_quantity'] ?? 1),
            'lead_time_days' => intval($data['lead_time_days'] ?? 7),
            'is_primary' => intval($data['is_primary'] ?? 0),
            'updated_at' => current_time('mysql')
        );

        if ($existing) {
            // Update existing relationship
            return $wpdb->update(
                $table_name,
                $supplier_data,
                array('id' => $existing),
                array('%d', '%d', '%s', '%f', '%d', '%d', '%d', '%s'),
                array('%d')
            );
        } else {
            // Insert new relationship
            $supplier_data['created_at'] = current_time('mysql');
            return $wpdb->insert(
                $table_name,
                $supplier_data,
                array('%d', '%d', '%s', '%f', '%d', '%d', '%d', '%s', '%s')
            );
        }
    }

    /**
     * Add inventory fields to product data
     */
    public static function add_inventory_fields() {
        global $post;

        if (!$post) {
            return;
        }

        $product_id = $post->ID;
        $reorder_level = get_post_meta($product_id, '_dab_reorder_level', true);
        $reorder_quantity = get_post_meta($product_id, '_dab_reorder_quantity', true);

        echo '<div class="options_group">';

        woocommerce_wp_text_input(array(
            'id' => '_dab_reorder_level',
            'label' => __('Reorder Level', 'db-app-builder'),
            'desc_tip' => true,
            'description' => __('Stock level at which to reorder this product', 'db-app-builder'),
            'type' => 'number',
            'custom_attributes' => array(
                'step' => '1',
                'min' => '0'
            ),
            'value' => $reorder_level
        ));

        woocommerce_wp_text_input(array(
            'id' => '_dab_reorder_quantity',
            'label' => __('Reorder Quantity', 'db-app-builder'),
            'desc_tip' => true,
            'description' => __('Quantity to order when restocking', 'db-app-builder'),
            'type' => 'number',
            'custom_attributes' => array(
                'step' => '1',
                'min' => '1'
            ),
            'value' => $reorder_quantity
        ));

        echo '</div>';
    }

    /**
     * Save inventory fields
     */
    public static function save_inventory_fields($product_id) {
        // Validate product_id
        if (!$product_id || !is_numeric($product_id)) {
            return;
        }

        $reorder_level = isset($_POST['_dab_reorder_level']) ? intval($_POST['_dab_reorder_level']) : '';
        $reorder_quantity = isset($_POST['_dab_reorder_quantity']) ? intval($_POST['_dab_reorder_quantity']) : '';

        update_post_meta($product_id, '_dab_reorder_level', $reorder_level);
        update_post_meta($product_id, '_dab_reorder_quantity', $reorder_quantity);
    }

    /**
     * Handle low stock alert
     */
    public static function handle_low_stock_alert($product) {
        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $current_stock = $product->get_stock_quantity();
        $low_stock_threshold = $product->get_low_stock_amount();

        self::create_stock_alert($product_id, 'low_stock', $current_stock, $low_stock_threshold);
    }

    /**
     * Handle out of stock alert
     */
    public static function handle_out_of_stock_alert($product) {
        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $current_stock = $product->get_stock_quantity();

        self::create_stock_alert($product_id, 'out_of_stock', $current_stock, 0);
    }

    /**
     * Daily inventory check
     */
    public static function daily_inventory_check() {
        // Get all products with low stock
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_manage_stock',
                    'value' => 'yes'
                )
            )
        );

        $products = get_posts($args);

        foreach ($products as $post) {
            $product = wc_get_product($post->ID);
            if ($product) {
                self::check_low_stock_alert($product);
            }
        }
    }

    /**
     * AJAX handler to get inventory data
     */
    public static function ajax_get_inventory_data() {
        check_ajax_referer('dab_inventory_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        // Implementation for getting inventory data
        wp_send_json_success(array('message' => 'Inventory data retrieved'));
    }

    /**
     * AJAX handler to update stock levels
     */
    public static function ajax_update_stock_levels() {
        check_ajax_referer('dab_inventory_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        // Implementation for updating stock levels
        wp_send_json_success(array('message' => 'Stock levels updated'));
    }

    /**
     * AJAX handler to create purchase order
     */
    public static function ajax_create_purchase_order() {
        check_ajax_referer('dab_inventory_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        // Implementation for creating purchase order
        wp_send_json_success(array('message' => 'Purchase order created'));
    }
}
