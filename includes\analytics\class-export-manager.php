<?php
/**
 * Export Manager Class
 * Phase 3: Data Intelligence & Analytics
 * 
 * Handles export functionality for reports including PDF, Excel, CSV,
 * and other formats with customizable templates and styling.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Export_Manager {
    
    /**
     * Initialize the Export Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_export_report', array(__CLASS__, 'export_report'));
        add_action('wp_ajax_dab_export_pivot_table', array(__CLASS__, 'export_pivot_table'));
        add_action('wp_ajax_dab_export_chart', array(__CLASS__, 'export_chart'));
        add_action('wp_ajax_nopriv_dab_export_report', array(__CLASS__, 'export_report_public'));
    }
    
    /**
     * Export report in specified format
     */
    public static function export_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $report_id = intval($_POST['report_id']);
        $format = sanitize_text_field($_POST['format']);
        $options = isset($_POST['options']) ? $_POST['options'] : array();
        
        try {
            self::process_export($report_id, $format, $options);
        } catch (Exception $e) {
            wp_send_json_error('Export failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Export report for public access
     */
    public static function export_report_public() {
        $report_id = intval($_GET['report_id']);
        $format = sanitize_text_field($_GET['format']);
        $token = sanitize_text_field($_GET['token']);
        
        // Verify public access token
        if (!self::verify_public_token($report_id, $token)) {
            wp_die('Invalid access token');
        }
        
        try {
            self::process_export($report_id, $format, array());
        } catch (Exception $e) {
            wp_die('Export failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Process export based on format
     */
    private static function process_export($report_id, $format, $options = array()) {
        // Load report
        global $wpdb;
        $reports_table = $wpdb->prefix . 'dab_reports';
        
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $report_id
        ));
        
        if (!$report) {
            throw new Exception('Report not found');
        }
        
        // Check permissions
        if (!$report->is_public && !current_user_can('manage_options')) {
            throw new Exception('Insufficient permissions');
        }
        
        // Generate report data
        $result = DAB_Report_Builder::execute_report($report);
        
        $filename = sanitize_file_name($report->name . '_' . date('Y-m-d_H-i-s'));
        
        switch ($format) {
            case 'csv':
                self::export_to_csv($result, $filename, $options);
                break;
            case 'excel':
                self::export_to_excel($result, $filename, $options);
                break;
            case 'pdf':
                self::export_to_pdf($result, $report, $filename, $options);
                break;
            case 'json':
                self::export_to_json($result, $filename, $options);
                break;
            default:
                throw new Exception('Unsupported export format: ' . $format);
        }
    }
    
    /**
     * Export to CSV format
     */
    private static function export_to_csv($result, $filename, $options) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        if (!empty($result['data'])) {
            // Write headers
            $headers = array();
            foreach ($result['fields'] as $field) {
                $headers[] = $field->field_label;
            }
            fputcsv($output, $headers);
            
            // Write data
            foreach ($result['data'] as $row) {
                $csv_row = array();
                foreach ($result['fields'] as $field) {
                    $value = isset($row->{$field->field_slug}) ? $row->{$field->field_slug} : '';
                    $csv_row[] = $value;
                }
                fputcsv($output, $csv_row);
            }
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export to Excel format (using simple HTML table that Excel can read)
     */
    private static function export_to_excel($result, $filename, $options) {
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        echo '<html>';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<style>';
        echo 'table { border-collapse: collapse; width: 100%; }';
        echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
        echo 'th { background-color: #f2f2f2; font-weight: bold; }';
        echo '</style>';
        echo '</head>';
        echo '<body>';
        
        if (!empty($result['data'])) {
            echo '<table>';
            
            // Headers
            echo '<thead><tr>';
            foreach ($result['fields'] as $field) {
                echo '<th>' . esc_html($field->field_label) . '</th>';
            }
            echo '</tr></thead>';
            
            // Data
            echo '<tbody>';
            foreach ($result['data'] as $row) {
                echo '<tr>';
                foreach ($result['fields'] as $field) {
                    $value = isset($row->{$field->field_slug}) ? $row->{$field->field_slug} : '';
                    echo '<td>' . esc_html($value) . '</td>';
                }
                echo '</tr>';
            }
            echo '</tbody>';
            
            echo '</table>';
        } else {
            echo '<p>No data available</p>';
        }
        
        echo '</body>';
        echo '</html>';
        exit;
    }
    
    /**
     * Export to PDF format
     */
    private static function export_to_pdf($result, $report, $filename, $options) {
        // Check if we can use a PDF library
        if (class_exists('TCPDF')) {
            self::export_to_pdf_tcpdf($result, $report, $filename, $options);
        } else {
            // Fallback to HTML-to-PDF using browser print
            self::export_to_pdf_html($result, $report, $filename, $options);
        }
    }
    
    /**
     * Export to PDF using TCPDF library
     */
    private static function export_to_pdf_tcpdf($result, $report, $filename, $options) {
        require_once(ABSPATH . 'wp-content/plugins/db-app-builder/includes/libs/tcpdf/tcpdf.php');
        
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Database App Builder');
        $pdf->SetAuthor('Database App Builder');
        $pdf->SetTitle($report->name);
        $pdf->SetSubject('Report Export');
        
        // Set margins
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        
        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);
        
        // Add a page
        $pdf->AddPage();
        
        // Set font
        $pdf->SetFont('helvetica', '', 10);
        
        // Title
        $pdf->SetFont('helvetica', 'B', 16);
        $pdf->Cell(0, 10, $report->name, 0, 1, 'C');
        $pdf->Ln(5);
        
        // Description
        if (!empty($report->description)) {
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(0, 10, $report->description, 0, 1, 'L');
            $pdf->Ln(5);
        }
        
        // Date
        $pdf->SetFont('helvetica', '', 8);
        $pdf->Cell(0, 10, 'Generated on: ' . date('Y-m-d H:i:s'), 0, 1, 'R');
        $pdf->Ln(5);
        
        if (!empty($result['data'])) {
            // Table headers
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->SetFillColor(240, 240, 240);
            
            $col_width = 180 / count($result['fields']);
            foreach ($result['fields'] as $field) {
                $pdf->Cell($col_width, 8, $field->field_label, 1, 0, 'C', true);
            }
            $pdf->Ln();
            
            // Table data
            $pdf->SetFont('helvetica', '', 8);
            $pdf->SetFillColor(255, 255, 255);
            
            foreach ($result['data'] as $row) {
                foreach ($result['fields'] as $field) {
                    $value = isset($row->{$field->field_slug}) ? $row->{$field->field_slug} : '';
                    $pdf->Cell($col_width, 6, $value, 1, 0, 'L');
                }
                $pdf->Ln();
            }
        } else {
            $pdf->Cell(0, 10, 'No data available', 0, 1, 'C');
        }
        
        // Output PDF
        $pdf->Output($filename . '.pdf', 'D');
        exit;
    }
    
    /**
     * Export to PDF using HTML (fallback)
     */
    private static function export_to_pdf_html($result, $report, $filename, $options) {
        header('Content-Type: text/html');
        
        echo '<!DOCTYPE html>';
        echo '<html>';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<title>' . esc_html($report->name) . '</title>';
        echo '<style>';
        echo 'body { font-family: Arial, sans-serif; margin: 20px; }';
        echo 'h1 { color: #333; text-align: center; }';
        echo 'table { border-collapse: collapse; width: 100%; margin-top: 20px; }';
        echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
        echo 'th { background-color: #f2f2f2; font-weight: bold; }';
        echo '.report-info { margin-bottom: 20px; }';
        echo '@media print { body { margin: 0; } }';
        echo '</style>';
        echo '</head>';
        echo '<body>';
        
        echo '<h1>' . esc_html($report->name) . '</h1>';
        
        echo '<div class="report-info">';
        if (!empty($report->description)) {
            echo '<p><strong>Description:</strong> ' . esc_html($report->description) . '</p>';
        }
        echo '<p><strong>Generated on:</strong> ' . date('Y-m-d H:i:s') . '</p>';
        echo '</div>';
        
        if (!empty($result['data'])) {
            echo '<table>';
            
            // Headers
            echo '<thead><tr>';
            foreach ($result['fields'] as $field) {
                echo '<th>' . esc_html($field->field_label) . '</th>';
            }
            echo '</tr></thead>';
            
            // Data
            echo '<tbody>';
            foreach ($result['data'] as $row) {
                echo '<tr>';
                foreach ($result['fields'] as $field) {
                    $value = isset($row->{$field->field_slug}) ? $row->{$field->field_slug} : '';
                    echo '<td>' . esc_html($value) . '</td>';
                }
                echo '</tr>';
            }
            echo '</tbody>';
            
            echo '</table>';
        } else {
            echo '<p>No data available</p>';
        }
        
        echo '<script>';
        echo 'window.onload = function() { window.print(); };';
        echo '</script>';
        
        echo '</body>';
        echo '</html>';
        exit;
    }
    
    /**
     * Export to JSON format
     */
    private static function export_to_json($result, $filename, $options) {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '.json"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        $export_data = array(
            'exported_at' => date('Y-m-d H:i:s'),
            'total_records' => count($result['data']),
            'fields' => array(),
            'data' => array()
        );
        
        // Add field information
        foreach ($result['fields'] as $field) {
            $export_data['fields'][] = array(
                'slug' => $field->field_slug,
                'label' => $field->field_label,
                'type' => $field->field_type
            );
        }
        
        // Add data
        foreach ($result['data'] as $row) {
            $data_row = array();
            foreach ($result['fields'] as $field) {
                $data_row[$field->field_slug] = isset($row->{$field->field_slug}) ? $row->{$field->field_slug} : null;
            }
            $export_data['data'][] = $data_row;
        }
        
        echo wp_json_encode($export_data, JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Export pivot table
     */
    public static function export_pivot_table() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $pivot_data = $_POST['pivot_data'];
        $format = sanitize_text_field($_POST['format']);
        $filename = sanitize_file_name($_POST['filename'] ?? 'pivot_table');
        
        switch ($format) {
            case 'csv':
                self::export_pivot_to_csv($pivot_data, $filename);
                break;
            case 'excel':
                self::export_pivot_to_excel($pivot_data, $filename);
                break;
            default:
                wp_send_json_error('Unsupported format');
        }
    }
    
    /**
     * Export pivot table to CSV
     */
    private static function export_pivot_to_csv($pivot_data, $filename) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Write headers
        if (!empty($pivot_data['headers'])) {
            fputcsv($output, $pivot_data['headers']);
        }
        
        // Write data rows
        foreach ($pivot_data['rows'] as $row) {
            $csv_row = array();
            foreach ($pivot_data['headers'] as $header) {
                $csv_row[] = isset($row[$header]) ? $row[$header] : '';
            }
            fputcsv($output, $csv_row);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export pivot table to Excel
     */
    private static function export_pivot_to_excel($pivot_data, $filename) {
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
        
        echo '<html>';
        echo '<head><meta charset="UTF-8"></head>';
        echo '<body>';
        echo '<table border="1">';
        
        // Headers
        if (!empty($pivot_data['headers'])) {
            echo '<tr>';
            foreach ($pivot_data['headers'] as $header) {
                echo '<th>' . esc_html($header) . '</th>';
            }
            echo '</tr>';
        }
        
        // Data rows
        foreach ($pivot_data['rows'] as $row) {
            echo '<tr>';
            foreach ($pivot_data['headers'] as $header) {
                $value = isset($row[$header]) ? $row[$header] : '';
                echo '<td>' . esc_html($value) . '</td>';
            }
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</body>';
        echo '</html>';
        exit;
    }
    
    /**
     * Verify public access token
     */
    private static function verify_public_token($report_id, $token) {
        global $wpdb;
        $shares_table = $wpdb->prefix . 'dab_report_shares';
        
        $share = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $shares_table WHERE report_id = %d AND share_token = %s",
            $report_id,
            $token
        ));
        
        if (!$share) {
            return false;
        }
        
        // Check if token has expired
        if ($share->expires_at && strtotime($share->expires_at) < time()) {
            return false;
        }
        
        // Update access count
        $wpdb->update(
            $shares_table,
            array(
                'access_count' => $share->access_count + 1,
                'last_accessed' => current_time('mysql')
            ),
            array('id' => $share->id)
        );
        
        return true;
    }
    
    /**
     * Generate public share token
     */
    public static function generate_share_token($report_id, $expires_in_days = 30) {
        global $wpdb;
        $shares_table = $wpdb->prefix . 'dab_report_shares';
        
        $token = wp_generate_password(32, false);
        $expires_at = date('Y-m-d H:i:s', strtotime("+{$expires_in_days} days"));
        
        $result = $wpdb->insert($shares_table, array(
            'report_id' => $report_id,
            'share_token' => $token,
            'share_type' => 'view',
            'expires_at' => $expires_at,
            'created_by' => get_current_user_id()
        ));
        
        if ($result) {
            return $token;
        }
        
        return false;
    }
}
