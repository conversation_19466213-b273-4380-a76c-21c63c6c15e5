document.addEventListener('DOMContentLoaded', function () {
    const containers = document.querySelectorAll('.dab-view-container');

    containers.forEach(container => {
        const viewId = container.getAttribute('data-view-id');
        const tableArea = container.querySelector('.dab-view-table');
        const modal = document.getElementById('dab-edit-modal');
        const editForm = document.getElementById('dab-edit-form');
        const cancelBtn = document.getElementById('dab-cancel-edit');

        // 1. Handle Edit Button Click
        container.addEventListener('click', function (e) {
            if (e.target.classList.contains('dab-edit-record')) {
                e.preventDefault();
                const row = e.target.closest('tr');
                const recordId = e.target.dataset.id;

                editForm.querySelector('[name="record_id"]').value = recordId;

                // Populate fields
                Array.from(editForm.elements).forEach(input => {
                    const name = input.name;
                    if (name && name !== 'record_id' && name !== 'view_id') {
                        const cell = row.querySelector(`.dab-field-${name}`);
                        if (cell) input.value = cell.innerText.trim();
                    }
                });

                modal.style.display = 'block';
            }
        });

        // 2. Handle Cancel Button
        cancelBtn.addEventListener('click', function () {
            modal.style.display = 'none';
        });

        // 3. Handle Form Submission (AJAX Update)
        editForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(editForm);
            formData.append('action', 'dab_update_record');

            fetch(dabFrontend.ajaxurl, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Record updated successfully.');
                        modal.style.display = 'none';
                        location.reload(); // Or reload via AJAX if needed
                    } else {
                        alert(data.data?.message || 'Update failed.');
                    }
                })
                .catch(error => {
                    alert('An error occurred.');
                    console.error(error);
                });
        });
    });
});
