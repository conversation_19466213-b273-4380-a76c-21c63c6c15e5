/**
 * Enhanced Currency Field Scripts
 *
 * JavaScript for enhanced currency fields in the Database App Builder plugin.
 */
(function($) {
    'use strict';

    // Initialize all enhanced currency fields
    function initEnhancedCurrencyFields() {
        $('.dab-enhanced-currency-field').each(function() {
            var $field = $(this);
            var $amount = $field.find('.dab-currency-amount');
            var $currency = $field.find('.dab-currency-selector');
            var $value = $field.find('input[type="hidden"]');
            var $symbol = $field.find('.dab-currency-symbol');
            
            // Skip if already initialized
            if ($field.data('initialized')) {
                return;
            }
            
            // Mark as initialized
            $field.data('initialized', true);
            
            // Update the hidden value field and symbol when amount or currency changes
            function updateValue() {
                var amount = $amount.val();
                var currency = $currency.val();
                var symbol = getCurrencySymbol(currency);
                
                // Update the hidden value field with JSON
                var jsonValue = JSON.stringify({
                    amount: amount,
                    currency: currency
                });
                $value.val(jsonValue);
                
                // Update the currency symbol
                $symbol.text(symbol);
            }
            
            // Get currency symbol from data attribute or use the currency code
            function getCurrencySymbol(currencyCode) {
                // Try to get from data attribute first
                var symbols = $field.data('currency-symbols');
                if (symbols && symbols[currencyCode]) {
                    return symbols[currencyCode];
                }
                
                // Fallback to common symbols
                var commonSymbols = {
                    'USD': '$',
                    'EUR': '€',
                    'GBP': '£',
                    'JPY': '¥',
                    'CNY': '¥',
                    'INR': '₹',
                    'CAD': 'C$',
                    'AUD': 'A$',
                    'NGN': '₦',
                    'ZAR': 'R'
                };
                
                return commonSymbols[currencyCode] || currencyCode;
            }
            
            // Bind events
            $amount.on('input change', updateValue);
            $currency.on('change', updateValue);
            
            // Initialize
            updateValue();
        });
    }
    
    // Initialize on document ready
    $(document).ready(function() {
        initEnhancedCurrencyFields();
        
        // Also initialize when new content is added to the page (e.g., AJAX forms)
        $(document).on('dab_content_loaded', function() {
            initEnhancedCurrencyFields();
        });
    });
    
    // Add a global function to format currency values
    window.dabFormatCurrency = function(value, options) {
        options = options || {};
        
        var amount = '';
        var currency = options.currency || 'USD';
        
        // Parse the value if it's stored as JSON
        if (typeof value === 'string' && value.indexOf('{') === 0) {
            try {
                var parsed = JSON.parse(value);
                amount = parsed.amount || '';
                currency = parsed.currency || currency;
            } catch (e) {
                amount = value;
            }
        } else {
            amount = value;
        }
        
        if (amount === '' || amount === null || amount === undefined) {
            return '';
        }
        
        // Format options
        var decimalPlaces = options.decimalPlaces !== undefined ? options.decimalPlaces : 2;
        var decimalSeparator = options.decimalSeparator || '.';
        var thousandSeparator = options.thousandSeparator || ',';
        var symbolPosition = options.symbolPosition || 'before';
        
        // Get currency symbol
        var symbol = options.symbol || getCurrencySymbol(currency);
        
        // Format the number
        var formattedAmount = formatNumber(
            parseFloat(amount),
            decimalPlaces,
            decimalSeparator,
            thousandSeparator
        );
        
        // Return formatted currency
        if (symbolPosition === 'before') {
            return symbol + formattedAmount;
        } else {
            return formattedAmount + symbol;
        }
    };
    
    // Helper function to format numbers
    function formatNumber(number, decimals, decimalSeparator, thousandSeparator) {
        var n = !isFinite(+number) ? 0 : +number;
        var prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
        var sep = thousandSeparator === undefined ? ',' : thousandSeparator;
        var dec = decimalSeparator === undefined ? '.' : decimalSeparator;
        
        var toFixedFix = function(n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
        
        var s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }
    
    // Helper function to get currency symbol
    function getCurrencySymbol(currencyCode) {
        var symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'CAD': 'C$',
            'AUD': 'A$',
            'NGN': '₦',
            'ZAR': 'R',
            'BRL': 'R$',
            'MXN': 'Mex$',
            'SGD': 'S$',
            'CHF': 'CHF',
            'SEK': 'kr',
            'NZD': 'NZ$',
            'KRW': '₩',
            'AED': 'د.إ',
            'SAR': '﷼',
            'RUB': '₽'
        };
        
        return symbols[currencyCode] || currencyCode;
    }
})(jQuery);
