/**
 * Database App Builder Modern Animations
 * 
 * Animations and transitions for the modern UI
 */
(function($) {
    'use strict';

    // Animation settings
    const animationDuration = 300; // milliseconds
    const animationEasing = 'ease';

    /**
     * Initialize animations when document is ready
     */
    $(document).ready(function() {
        initAnimations();
    });

    /**
     * Initialize all animations
     */
    function initAnimations() {
        // Animate elements with data-animate attribute
        animateElements();
        
        // Initialize hover animations
        initHoverAnimations();
        
        // Initialize click animations
        initClickAnimations();
        
        // Initialize scroll animations
        initScrollAnimations();
        
        // Initialize form animations
        initFormAnimations();
        
        // Initialize modal animations
        initModalAnimations();
    }

    /**
     * Animate elements with data-animate attribute
     */
    function animateElements() {
        $('[data-animate]').each(function() {
            const $element = $(this);
            const animation = $element.data('animate');
            const delay = $element.data('delay') || 0;
            const duration = $element.data('duration') || animationDuration;
            
            // Add animation class after delay
            setTimeout(function() {
                $element.addClass('dab-animate-' + animation);
                $element.css('animation-duration', duration + 'ms');
            }, delay);
        });
    }

    /**
     * Initialize hover animations
     */
    function initHoverAnimations() {
        // Card hover effect
        $('.dab-card').hover(
            function() {
                $(this).css('transform', 'translateY(-5px)');
                $(this).css('box-shadow', 'var(--dab-shadow-lg)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
                $(this).css('box-shadow', 'var(--dab-shadow-sm)');
            }
        );
        
        // Button hover effect
        $('.dab-btn').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );
        
        // Table row hover effect
        $('.dab-table tbody tr, .dab-admin-table tbody tr, .dab-view-table tbody tr').hover(
            function() {
                $(this).css('background-color', 'rgba(67, 97, 238, 0.05)');
                $(this).css('transition', 'background-color ' + animationDuration + 'ms ' + animationEasing);
            },
            function() {
                $(this).css('background-color', '');
            }
        );
    }

    /**
     * Initialize click animations
     */
    function initClickAnimations() {
        // Button click effect
        $('.dab-btn').on('mousedown', function() {
            $(this).css('transform', 'scale(0.95)');
        }).on('mouseup mouseleave', function() {
            $(this).css('transform', '');
        });
        
        // Tab click effect
        $('.dab-admin-tab-link').on('click', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const $tabs = $this.closest('.dab-admin-tabs');
            const $tabContent = $('.dab-admin-tab-content');
            const target = $this.attr('href');
            
            // Remove active class from all tabs
            $tabs.find('.dab-admin-tab-link').removeClass('active');
            
            // Add active class to clicked tab
            $this.addClass('active');
            
            // Hide all tab content
            $tabContent.hide();
            
            // Show target tab content with animation
            $(target).fadeIn(animationDuration);
        });
    }

    /**
     * Initialize scroll animations
     */
    function initScrollAnimations() {
        // Animate elements when they come into view
        $(window).on('scroll', function() {
            $('.dab-animate-on-scroll:not(.animated)').each(function() {
                const $element = $(this);
                const elementTop = $element.offset().top;
                const elementHeight = $element.outerHeight();
                const viewportTop = $(window).scrollTop();
                const viewportHeight = $(window).height();
                
                // Check if element is in viewport
                if (elementTop < viewportTop + viewportHeight - elementHeight / 2) {
                    const animation = $element.data('animation') || 'fade-in';
                    const delay = $element.data('delay') || 0;
                    
                    // Add animation class after delay
                    setTimeout(function() {
                        $element.addClass('dab-animate-' + animation);
                        $element.addClass('animated');
                    }, delay);
                }
            });
        }).trigger('scroll');
    }

    /**
     * Initialize form animations
     */
    function initFormAnimations() {
        // Form field focus effect
        $('.dab-form-control, .dab-admin-form-control').on('focus', function() {
            $(this).closest('.dab-form-group, .dab-admin-form-group').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.dab-form-group, .dab-admin-form-group').removeClass('focused');
        });
        
        // Form submission animation
        $('.dab-form, .dab-admin-form').on('submit', function() {
            const $form = $(this);
            const $submitBtn = $form.find('[type="submit"]');
            
            // Disable submit button and show loading indicator
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<span class="dab-loader"></span> Submitting...');
            
            // Add loading class to form
            $form.addClass('dab-form-loading');
            
            // Note: The actual form submission is handled elsewhere
            // This is just for the animation effect
            
            // For demo purposes, remove loading state after 2 seconds
            // In a real implementation, this would be handled in the AJAX success/error callbacks
            setTimeout(function() {
                $submitBtn.prop('disabled', false);
                $submitBtn.html('Submit');
                $form.removeClass('dab-form-loading');
            }, 2000);
        });
    }

    /**
     * Initialize modal animations
     */
    function initModalAnimations() {
        // Open modal animation
        $(document).on('click', '[data-toggle="modal"]', function(e) {
            e.preventDefault();
            
            const target = $(this).data('target');
            const $modal = $(target);
            
            // Show modal backdrop with fade effect
            $('<div class="dab-modal-backdrop"></div>').appendTo('body').fadeIn(animationDuration);
            
            // Show modal with animation
            $modal.addClass('dab-modal-open');
            $modal.find('.dab-modal-dialog').css('transform', 'translateY(0)');
        });
        
        // Close modal animation
        $(document).on('click', '.dab-modal-close, .dab-modal-cancel', function() {
            const $modal = $(this).closest('.dab-modal');
            
            // Hide modal with animation
            $modal.find('.dab-modal-dialog').css('transform', 'translateY(-50px)');
            
            setTimeout(function() {
                $modal.removeClass('dab-modal-open');
                $('.dab-modal-backdrop').fadeOut(animationDuration, function() {
                    $(this).remove();
                });
            }, animationDuration);
        });
        
        // Close modal when clicking on backdrop
        $(document).on('click', '.dab-modal', function(e) {
            if ($(e.target).hasClass('dab-modal')) {
                $(this).find('.dab-modal-close').trigger('click');
            }
        });
    }

})(jQuery);
