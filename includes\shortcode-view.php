<?php
if (!defined('ABSPATH')) exit;

add_shortcode('dab_view', function ($atts) {
    global $wpdb;

    $atts = shortcode_atts(['id' => 0], $atts);
    $view_id = intval($atts['id']);
    if (!$view_id) return '<p>Invalid view ID.</p>';

    $views_table = $wpdb->prefix . 'dab_views';
    $tables_table = $wpdb->prefix . 'dab_tables';
    $fields_table = $wpdb->prefix . 'dab_fields';

    $view = $wpdb->get_row($wpdb->prepare("SELECT * FROM $views_table WHERE id = %d", $view_id));
    if (!$view) return '<p>View not found.</p>';

    if (!empty($view->is_public) && !$view->is_public && !current_user_can('manage_options')) {
        return '<p>This view is private.</p>';
    }

    $table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $view->table_id));
    if (!$table) return '<p>Associated table not found.</p>';

    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
    $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $data_table));
    if (!$table_exists) return '<p>Data table does not exist yet.</p>';

    $all_fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d", $view->table_id));
    $visible_fields = isset($view->visible_fields) ? maybe_unserialize($view->visible_fields) : [];
    $sort_order = isset($view->sort_order) ? sanitize_text_field($view->sort_order) : '';

    $selected_fields = [];
    foreach ($all_fields as $field) {
        if (empty($visible_fields) || in_array($field->field_slug, $visible_fields)) {
            $selected_fields[] = $field;
        }
    }

    $search = isset($_GET['dab_search']) ? sanitize_text_field($_GET['dab_search']) : '';
    $paged = isset($_GET['dab_page']) ? max(1, intval($_GET['dab_page'])) : 1;
    $limit = 10;
    $offset = ($paged - 1) * $limit;

    $where_sql = 'WHERE 1=1';
    $params = [];

    if (!current_user_can('administrator')) {
        $where_sql .= " AND approval_status = %s";
        $params[] = 'Approved';
    }

    if (!empty($search)) {
        foreach ($selected_fields as $field) {
            $where_sql .= " AND `$field->field_slug` LIKE %s";
            $params[] = '%' . $search . '%';
        }
    }

    $order_sql = !empty($sort_order) ? "ORDER BY `$sort_order` ASC" : '';
    $limit_sql = "LIMIT %d OFFSET %d";
    $params[] = $limit;
    $params[] = $offset;

    $sql = "SELECT * FROM `$data_table` $where_sql $order_sql $limit_sql";
    $records = $wpdb->get_results($wpdb->prepare($sql, $params));

    $count_sql = "SELECT COUNT(*) FROM `$data_table` $where_sql";
    $total = $wpdb->get_var($wpdb->prepare($count_sql, array_slice($params, 0, -2)));
    $total_pages = ceil($total / $limit);

    ob_start();
    ?>

    <!-- Enhanced search and export form -->
    <div class="dab-enhanced-search" data-has-export="true">
        <form method="get" class="dab-search-form">
            <?php
            // Preserve existing query parameters
            foreach ($_GET as $key => $value) {
                if ($key !== 'dab_search' && $key !== 'dab_column' && $key !== 'dab_export') {
                    echo '<input type="hidden" name="' . esc_attr($key) . '" value="' . esc_attr($value) . '">';
                }
            }
            ?>
            <input type="text" name="dab_search" value="<?php echo esc_attr($search); ?>" placeholder="Search..." class="dab-search-input" />

            <select name="dab_column" class="dab-column-filter">
                <option value="">All Columns</option>
                <?php
                $current_column = isset($_GET['dab_column']) ? intval($_GET['dab_column']) : '';
                foreach ($selected_fields as $index => $field):
                ?>
                    <option value="<?php echo esc_attr($index); ?>" <?php selected($current_column, $index); ?>>
                        <?php echo esc_html($field->field_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <button type="submit" class="dab-search-button">Search</button>

            <button type="button" class="dab-reset-button" onclick="window.location.href='<?php echo esc_url(remove_query_arg(['dab_search', 'dab_column'])); ?>'">Reset</button>
        </form>

        <?php
        // Check if user has permission to export
        $current_user_id = get_current_user_id();
        $can_export = current_user_can('administrator');
        if (!$can_export && class_exists('DAB_Role_Permissions_Manager')) {
            $can_export = DAB_Role_Permissions_Manager::can_user_export_records($current_user_id, $view->table_id);
        }

        ?>
        <div class="dab-export-container">
            <?php if ($can_export): ?>
            <button type="button" class="dab-export-button dab-btn dab-btn-primary">
                <span class="dashicons dashicons-download"></span> Export
            </button>
            <div class="dab-export-options" style="display:none;">
                <a href="<?php echo esc_url(add_query_arg('dab_export', 'csv')); ?>" class="dab-export-option dab-btn dab-btn-sm dab-btn-primary">
                    <span class="dashicons dashicons-media-spreadsheet"></span> Export as CSV
                </a>
                <a href="<?php echo esc_url(add_query_arg('dab_export', 'excel')); ?>" class="dab-export-option dab-btn dab-btn-sm dab-btn-primary">
                    <span class="dashicons dashicons-media-spreadsheet"></span> Export as Excel
                </a>
                <a href="#" class="dab-export-option dab-print-table dab-btn dab-btn-sm dab-btn-primary">
                    <span class="dashicons dashicons-printer"></span> Print Table
                </a>
            </div>
            <?php else: ?>
            <button type="button" class="dab-btn dab-btn-disabled" disabled title="You don't have permission to export records from this table">
                <span class="dashicons dashicons-download"></span> Export
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- JavaScript for table export functionality is loaded from table-export.js -->
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Direct binding for export button
        $('.dab-export-button').on('click', function() {
            $(this).next('.dab-export-options').toggle();
        });

        // Close export options when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.dab-export-container').length) {
                $('.dab-export-options').hide();
            }
        });
    });
    </script>

    <style>
        .dab-error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        /* Button styles */
        .dab-btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            text-decoration: none;
            cursor: pointer;
            margin: 0 2px;
        }

        .dab-btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .dab-btn-primary {
            color: #fff;
            background-color: #007bff;
            border-color: #007bff;
        }

        .dab-btn-primary:hover {
            color: #fff;
            background-color: #0069d9;
            border-color: #0062cc;
        }

        .dab-btn-danger {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .dab-btn-danger:hover {
            color: #fff;
            background-color: #c82333;
            border-color: #bd2130;
        }

        .dab-btn-disabled {
            color: #6c757d;
            background-color: #e9ecef;
            border-color: #dee2e6;
            cursor: not-allowed;
            opacity: 0.65;
        }

        .dab-actions-column {
            white-space: nowrap;
            text-align: center;
        }

        .dab-actions-column .dashicons,
        .dab-export-container .dashicons {
            font-size: 16px;
            width: 16px;
            height: 16px;
            vertical-align: middle;
            margin-top: -2px;
        }

        .dab-export-container {
            margin-bottom: 15px;
            position: relative;
        }

        .dab-export-options {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-top: 5px;
        }

        .dab-export-option {
            display: block;
            margin: 5px 0;
            text-decoration: none;
        }
    </style>

    <div class="dab-view-container" data-view-id="<?php echo esc_attr($view_id); ?>">
        <table class="dab-view-table">
            <thead>
                <tr>
                    <?php foreach ($selected_fields as $field): ?>
                        <th><?php echo esc_html($field->field_label); ?></th>
                    <?php endforeach; ?>
                    <?php
                    $current_user_id = get_current_user_id();
                    $can_edit = class_exists('DAB_Role_Permissions_Manager') &&
                                (DAB_Role_Permissions_Manager::can_user_edit_records($current_user_id, $view->table_id) ||
                                 DAB_Role_Permissions_Manager::can_user_delete_records($current_user_id, $view->table_id));

                    if (current_user_can('manage_options') || $can_edit):
                    ?>
                        <th>Actions</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php if ($records): ?>
                    <?php foreach ($records as $record): ?>
                        <tr data-id="<?php echo $record->id; ?>">
                            <?php foreach ($selected_fields as $field): ?>
                                <td class="dab-field-<?php echo esc_attr($field->field_slug); ?>">
                                    <?php
                                    if ($field->field_type === 'lookup') {
                                        $lookup_table_id = intval($field->lookup_table_id);
                                        $display_column = sanitize_text_field($field->lookup_display_column);
                                        if ($lookup_table_id && $display_column) {
                                            $lookup_table_slug = $wpdb->get_var(
                                                $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                                            );
                                            if ($lookup_table_slug) {
                                                $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                                                $lookup_id = intval($record->{$field->field_slug});
                                                $lookup_val = $wpdb->get_var(
                                                    $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                                                );
                                                echo esc_html($lookup_val ?: '');
                                            } else {
                                                echo esc_html($record->{$field->field_slug} ?? '');
                                            }
                                        } else {
                                            echo esc_html($record->{$field->field_slug} ?? '');
                                        }
                                    } elseif ($field->field_type === 'formula') {
                                        // Format numeric values with 2 decimal places
                                        $value = $record->{$field->field_slug} ?? '';
                                        if (is_numeric($value)) {
                                            echo esc_html(number_format((float)$value, 2));
                                        } else {
                                            echo esc_html($value);
                                        }
                                    } elseif ($field->field_type === 'currency') {
                                        // Format currency values
                                        $value = $record->{$field->field_slug} ?? '';
                                        if (is_numeric($value)) {
                                            echo '$' . esc_html(number_format((float)$value, 2));
                                        } else {
                                            echo esc_html($value);
                                        }
                                    } elseif ($field->field_type === 'date') {
                                        // Format date values
                                        $value = $record->{$field->field_slug} ?? '';
                                        if (!empty($value)) {
                                            echo esc_html(date('Y-m-d', strtotime($value)));
                                        }
                                    } elseif ($field->field_type === 'boolean') {
                                        // Format boolean values
                                        $value = $record->{$field->field_slug} ?? '';
                                        echo $value ? '✓' : '✗';
                                    } else {
                                        echo esc_html($record->{$field->field_slug} ?? '');
                                    }
                                    ?>
                                </td>
                            <?php endforeach; ?>
                            <?php
                            $current_user_id = get_current_user_id();
                            $can_edit = class_exists('DAB_Role_Permissions_Manager') &&
                                        DAB_Role_Permissions_Manager::can_user_edit_records($current_user_id, $view->table_id, $record->id);
                            $can_delete = class_exists('DAB_Role_Permissions_Manager') &&
                                          DAB_Role_Permissions_Manager::can_user_delete_records($current_user_id, $view->table_id, $record->id);

                            // Always show the actions column, but disable buttons if no permission
                            ?>
                                <td class="dab-actions-column">
                                    <?php if (current_user_can('manage_options') || $can_edit): ?>
                                        <a href="#" class="dab-edit-record dab-btn dab-btn-sm dab-btn-primary" data-id="<?php echo $record->id; ?>">
                                            <span class="dashicons dashicons-edit"></span> Edit
                                        </a>
                                    <?php else: ?>
                                        <button class="dab-btn dab-btn-sm dab-btn-disabled" disabled title="You don't have permission to edit this record">
                                            <span class="dashicons dashicons-edit"></span> Edit
                                        </button>
                                    <?php endif; ?>

                                    <?php if (current_user_can('manage_options') || $can_delete): ?>
                                        <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=dab_delete_record&table=' . esc_attr($data_table) . '&id=' . $record->id), 'dab_delete_' . $record->id); ?>" onclick="return confirm('Are you sure you want to delete this record?');" class="dab-delete-record dab-btn dab-btn-sm dab-btn-danger">
                                            <span class="dashicons dashicons-trash"></span> Delete
                                        </a>
                                    <?php else: ?>
                                        <button class="dab-btn dab-btn-sm dab-btn-disabled" disabled title="You don't have permission to delete this record">
                                            <span class="dashicons dashicons-trash"></span> Delete
                                        </button>
                                    <?php endif; ?>
                                </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr><td colspan="<?php echo count($selected_fields) + (current_user_can('manage_options') ? 1 : 0); ?>">No records found.</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <?php if ($total_pages > 1): ?>
        <div class="dab-pagination">
            <?php
            // Previous page link
            if ($paged > 1): ?>
                <a href="<?php echo esc_url(add_query_arg('dab_page', $paged - 1)); ?>" class="dab-prev-page">
                    &laquo; Previous
                </a>
            <?php endif; ?>

            <?php
            // Determine which page numbers to show
            $show_dots = false;
            $max_links = 5; // Maximum number of page links to show

            for ($i = 1; $i <= $total_pages; $i++):
                // Always show first page, last page, current page, and pages around current page
                if ($i == 1 || $i == $total_pages || abs($i - $paged) <= 1):
                    if ($i == $paged): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="<?php echo esc_url(add_query_arg('dab_page', $i)); ?>"><?php echo $i; ?></a>
                    <?php endif;
                    $show_dots = true;
                elseif ($show_dots):
                    echo '<span class="dots">...</span>';
                    $show_dots = false;
                endif;
            endfor; ?>

            <?php
            // Next page link
            if ($paged < $total_pages): ?>
                <a href="<?php echo esc_url(add_query_arg('dab_page', $paged + 1)); ?>" class="dab-next-page">
                    Next &raquo;
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php
    // Export Logic
    if (isset($_GET['dab_export'])) {
        $export_type = sanitize_text_field($_GET['dab_export']);
        $current_user_id = get_current_user_id();

        // Check if user has permission to export
        $can_export = current_user_can('administrator');
        if (!$can_export && class_exists('DAB_Role_Permissions_Manager')) {
            $can_export = DAB_Role_Permissions_Manager::can_user_export_records($current_user_id, $view->table_id);
        }

        if (!$can_export) {
            // Display error message
            echo '<div class="dab-error-message">You do not have permission to export records from this table.</div>';
        } else {
            // Get filter parameters
            $filter_params = [
                'search' => $search,
                'column' => isset($_GET['dab_column']) ? intval($_GET['dab_column']) : null
            ];

            if ($export_type === 'csv') {
                // Use the improved CSV export function
                DAB_Data_Manager::export_records_to_csv($view->table_id, $filter_params);
                exit;
            } elseif ($export_type === 'excel') {
                // For Excel export, we'll use the same CSV function but with Excel-specific headers
                // This ensures Excel opens the CSV file correctly with proper encoding
                DAB_Data_Manager::export_records_to_csv($view->table_id, $filter_params);
                exit;
            }
        }
    }

    return ob_get_clean();
});

/**
 * Render field value based on field type
 */
function dab_render_field_value($table_id, $field, $value, $record_id) {
    global $wpdb;

    if (empty($value)) {
        return '';
    }

    switch ($field->field_type) {
        case 'image':
            $attachment_id = intval($value);
            $image_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
            return $image_url ? '<img src="' . esc_url($image_url) . '" style="max-width:100px; max-height:100px;">' : '';

        case 'file':
            $attachment_id = intval($value);
            $file_url = wp_get_attachment_url($attachment_id);
            $filename = basename($file_url);
            return $file_url ? '<a href="' . esc_url($file_url) . '" target="_blank">' . esc_html($filename) . '</a>' : '';

        case 'boolean':
            return $value ? '✓' : '✗';

        case 'date':
            return date('Y-m-d', strtotime($value));

        case 'datetime':
            return date('Y-m-d H:i', strtotime($value));

        case 'lookup':
            if (empty($field->lookup_table_id) || empty($field->lookup_display_column)) {
                return esc_html($value);
            }

            $lookup_table = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}dab_tables WHERE id = %d",
                $field->lookup_table_id
            ));

            if (!$lookup_table) {
                return esc_html($value);
            }

            $lookup_table_name = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table->table_slug);
            $display_value = $wpdb->get_var($wpdb->prepare(
                "SELECT {$field->lookup_display_column} FROM $lookup_table_name WHERE id = %d",
                $value
            ));

            return esc_html($display_value);

        case 'inline_table':
            return DAB_Data_Manager::render_inline_table_value($field, $record_id, $table_id);

        case 'rich_text':
            return wp_kses_post($value);

        default:
            return esc_html($value);
    }
}
