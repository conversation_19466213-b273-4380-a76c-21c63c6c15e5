/**
 * Conditional Logic Builder
 * 
 * Provides a visual interface for creating conditional logic rules for forms
 */

jQuery(document).ready(function($) {
    // Initialize the logic builder if the container exists
    if ($('#logic-builder-container').length === 0) return;

    // Get the form fields
    const formFields = [];
    $('#sortable-fields-list .dab-field-item').each(function() {
        const fieldId = $(this).data('field-id');
        const fieldSlug = $(this).find('input[type="checkbox"]').val();
        const fieldLabel = $(this).find('label').text().trim();
        
        formFields.push({
            id: fieldId,
            slug: fieldSlug,
            label: fieldLabel
        });
    });

    // Initialize the logic builder
    initLogicBuilder(formFields);

    /**
     * Initialize the logic builder
     */
    function initLogicBuilder(fields) {
        const $container = $('#logic-builder-container');
        const $logicInput = $('#conditional_logic_json');
        
        // Create the builder UI
        const $builder = $('<div class="dab-logic-builder"></div>');
        
        // Add header
        const $header = $('<div class="dab-logic-builder-header"></div>');
        $header.append('<h3 class="dab-logic-builder-title">Form Conditional Logic</h3>');
        $builder.append($header);
        
        // Add description
        $builder.append('<p class="dab-logic-builder-description">Create rules to show/hide fields, send emails, or perform other actions based on form values.</p>');
        
        // Add rules container
        const $rules = $('<div class="dab-logic-rules"></div>');
        $builder.append($rules);
        
        // Add "Add Rule" button
        const $addRule = $('<button type="button" class="dab-add-rule">Add New Rule</button>');
        $builder.append($addRule);
        
        // Add the builder to the container
        $container.append($builder);
        
        // Load existing rules if any
        let existingRules = [];
        if ($logicInput.val()) {
            try {
                existingRules = JSON.parse($logicInput.val());
                
                // Add existing rules to the UI
                if (Array.isArray(existingRules)) {
                    existingRules.forEach(rule => {
                        addRuleToUI($rules, fields, rule);
                    });
                }
            } catch (e) {
                console.error('Error parsing conditional logic JSON:', e);
            }
        }
        
        // Add rule button click handler
        $addRule.on('click', function() {
            // Create a new empty rule
            const newRule = {
                target: '',
                action: 'show',
                logic: 'all',
                conditions: [
                    {
                        field: '',
                        operator: 'equals',
                        value: ''
                    }
                ]
            };
            
            // Add the rule to the UI
            addRuleToUI($rules, fields, newRule);
            
            // Update the JSON
            updateLogicJSON();
        });
        
        // Update JSON when form is submitted
        $('form').on('submit', function() {
            updateLogicJSON();
        });
    }

    /**
     * Add a rule to the UI
     */
    function addRuleToUI($rulesContainer, fields, rule) {
        // Create rule container
        const $rule = $('<div class="dab-logic-rule"></div>');
        
        // Add rule header
        const $ruleHeader = $('<div class="dab-rule-header"></div>');
        $ruleHeader.append('<h4 class="dab-rule-title">Rule</h4>');
        
        // Add rule actions
        const $ruleActions = $('<div class="dab-rule-actions"></div>');
        $ruleActions.append('<button type="button" class="dab-rule-action dab-delete-rule" title="Delete Rule"><span class="dashicons dashicons-trash"></span></button>');
        $ruleHeader.append($ruleActions);
        
        $rule.append($ruleHeader);
        
        // Add rule body
        const $ruleBody = $('<div class="dab-rule-body"></div>');
        
        // Add conditions section
        const $conditionsSection = $('<div class="dab-rule-section"></div>');
        $conditionsSection.append('<div class="dab-rule-section-title">Conditions</div>');
        
        // Add logic type (all/any)
        const $logicType = $('<div class="dab-logic-type"></div>');
        $logicType.append(
            '<label><input type="radio" name="logic-type-' + Date.now() + '" value="all" ' + (rule.logic === 'all' ? 'checked' : '') + '> All conditions must be true</label>' +
            '<label><input type="radio" name="logic-type-' + Date.now() + '" value="any" ' + (rule.logic === 'any' ? 'checked' : '') + '> Any condition can be true</label>'
        );
        $conditionsSection.append($logicType);
        
        // Add conditions container
        const $conditions = $('<div class="dab-conditions"></div>');
        $conditionsSection.append($conditions);
        
        // Add existing conditions
        if (rule.conditions && Array.isArray(rule.conditions)) {
            rule.conditions.forEach(condition => {
                addConditionToUI($conditions, fields, condition);
            });
        }
        
        // Add "Add Condition" button
        const $addCondition = $('<button type="button" class="dab-add-condition">Add Condition</button>');
        $conditionsSection.append($addCondition);
        
        $ruleBody.append($conditionsSection);
        
        // Add actions section
        const $actionsSection = $('<div class="dab-rule-section"></div>');
        $actionsSection.append('<div class="dab-rule-section-title">Actions</div>');
        
        // Add action
        const $action = $('<div class="dab-action"></div>');
        
        // Action type
        const $actionType = $('<div class="dab-action-type"></div>');
        const $actionTypeSelect = $('<select></select>');
        $actionTypeSelect.append(
            '<option value="show" ' + (rule.action === 'show' ? 'selected' : '') + '>Show</option>' +
            '<option value="hide" ' + (rule.action === 'hide' ? 'selected' : '') + '>Hide</option>' +
            '<option value="require" ' + (rule.action === 'require' ? 'selected' : '') + '>Make Required</option>' +
            '<option value="unrequire" ' + (rule.action === 'unrequire' ? 'selected' : '') + '>Make Optional</option>' +
            '<option value="email" ' + (rule.action === 'email' ? 'selected' : '') + '>Send Email</option>'
        );
        $actionType.append($actionTypeSelect);
        $action.append($actionType);
        
        // Action target
        const $actionTarget = $('<div class="dab-action-target"></div>');
        const $actionTargetSelect = $('<select></select>');
        
        // Add option for email notification
        $actionTargetSelect.append('<option value="notification" ' + (rule.target === 'notification' ? 'selected' : '') + '>Email Notification</option>');
        
        // Add field options
        fields.forEach(field => {
            $actionTargetSelect.append('<option value="' + field.slug + '" ' + (rule.target === field.slug ? 'selected' : '') + '>' + field.label + '</option>');
        });
        
        $actionTarget.append($actionTargetSelect);
        $action.append($actionTarget);
        
        // Add email value field if action is email
        if (rule.action === 'email') {
            const $actionValue = $('<div class="dab-action-value"></div>');
            $actionValue.append('<input type="email" placeholder="<EMAIL>" value="' + (rule.value || '') + '">');
            $action.append($actionValue);
        }
        
        $actionsSection.append($action);
        $ruleBody.append($actionsSection);
        
        $rule.append($ruleBody);
        
        // Add the rule to the container
        $rulesContainer.append($rule);
        
        // Add event handlers
        
        // Delete rule button
        $rule.find('.dab-delete-rule').on('click', function() {
            $rule.remove();
            updateLogicJSON();
        });
        
        // Add condition button
        $addCondition.on('click', function() {
            addConditionToUI($conditions, fields, { field: '', operator: 'equals', value: '' });
            updateLogicJSON();
        });
        
        // Logic type change
        $logicType.find('input[type="radio"]').on('change', function() {
            updateLogicJSON();
        });
        
        // Action type change
        $actionTypeSelect.on('change', function() {
            const actionType = $(this).val();
            
            // Remove existing value field
            $action.find('.dab-action-value').remove();
            
            // Add email value field if action is email
            if (actionType === 'email') {
                const $actionValue = $('<div class="dab-action-value"></div>');
                $actionValue.append('<input type="email" placeholder="<EMAIL>">');
                $action.append($actionValue);
            }
            
            updateLogicJSON();
        });
        
        // Action target change
        $actionTargetSelect.on('change', function() {
            updateLogicJSON();
        });
    }

    /**
     * Add a condition to the UI
     */
    function addConditionToUI($conditionsContainer, fields, condition) {
        // Create condition container
        const $condition = $('<div class="dab-condition"></div>');
        
        // Field select
        const $fieldSelect = $('<div class="dab-condition-field"></div>');
        const $fieldSelectEl = $('<select></select>');
        $fieldSelectEl.append('<option value="">Select Field</option>');
        
        fields.forEach(field => {
            $fieldSelectEl.append('<option value="' + field.slug + '" ' + (condition.field === field.slug ? 'selected' : '') + '>' + field.label + '</option>');
        });
        
        $fieldSelect.append($fieldSelectEl);
        $condition.append($fieldSelect);
        
        // Operator select
        const $operatorSelect = $('<div class="dab-condition-operator"></div>');
        const $operatorSelectEl = $('<select></select>');
        $operatorSelectEl.append(
            '<option value="equals" ' + (condition.operator === 'equals' ? 'selected' : '') + '>Equals</option>' +
            '<option value="not_equals" ' + (condition.operator === 'not_equals' ? 'selected' : '') + '>Not Equals</option>' +
            '<option value="contains" ' + (condition.operator === 'contains' ? 'selected' : '') + '>Contains</option>' +
            '<option value="not_contains" ' + (condition.operator === 'not_contains' ? 'selected' : '') + '>Not Contains</option>' +
            '<option value="greater_than" ' + (condition.operator === 'greater_than' ? 'selected' : '') + '>Greater Than</option>' +
            '<option value="less_than" ' + (condition.operator === 'less_than' ? 'selected' : '') + '>Less Than</option>' +
            '<option value="starts_with" ' + (condition.operator === 'starts_with' ? 'selected' : '') + '>Starts With</option>' +
            '<option value="ends_with" ' + (condition.operator === 'ends_with' ? 'selected' : '') + '>Ends With</option>' +
            '<option value="is_empty" ' + (condition.operator === 'is_empty' ? 'selected' : '') + '>Is Empty</option>' +
            '<option value="is_not_empty" ' + (condition.operator === 'is_not_empty' ? 'selected' : '') + '>Is Not Empty</option>'
        );
        $operatorSelect.append($operatorSelectEl);
        $condition.append($operatorSelect);
        
        // Value input
        const $valueInput = $('<div class="dab-condition-value"></div>');
        $valueInput.append('<input type="text" value="' + (condition.value || '') + '">');
        $condition.append($valueInput);
        
        // Remove button
        const $removeButton = $('<button type="button" class="dab-condition-remove" title="Remove Condition"><span class="dashicons dashicons-no-alt"></span></button>');
        $condition.append($removeButton);
        
        // Add the condition to the container
        $conditionsContainer.append($condition);
        
        // Add event handlers
        
        // Remove condition button
        $removeButton.on('click', function() {
            $condition.remove();
            updateLogicJSON();
        });
        
        // Field, operator, and value change
        $fieldSelectEl.on('change', function() {
            updateLogicJSON();
        });
        
        $operatorSelectEl.on('change', function() {
            const operator = $(this).val();
            
            // Hide value input for is_empty and is_not_empty operators
            if (operator === 'is_empty' || operator === 'is_not_empty') {
                $valueInput.hide();
            } else {
                $valueInput.show();
            }
            
            updateLogicJSON();
        });
        
        $valueInput.find('input').on('input', function() {
            updateLogicJSON();
        });
        
        // Hide value input for is_empty and is_not_empty operators
        if (condition.operator === 'is_empty' || condition.operator === 'is_not_empty') {
            $valueInput.hide();
        }
    }

    /**
     * Update the conditional logic JSON
     */
    function updateLogicJSON() {
        const $logicInput = $('#conditional_logic_json');
        const rules = [];
        
        // Get all rules
        $('.dab-logic-rule').each(function() {
            const $rule = $(this);
            
            // Get logic type
            const logicType = $rule.find('.dab-logic-type input:checked').val() || 'all';
            
            // Get conditions
            const conditions = [];
            $rule.find('.dab-condition').each(function() {
                const $condition = $(this);
                const field = $condition.find('.dab-condition-field select').val();
                const operator = $condition.find('.dab-condition-operator select').val();
                const value = $condition.find('.dab-condition-value input').val();
                
                if (field) {
                    conditions.push({
                        field: field,
                        operator: operator,
                        value: value
                    });
                }
            });
            
            // Get action
            const actionType = $rule.find('.dab-action-type select').val();
            const actionTarget = $rule.find('.dab-action-target select').val();
            
            // Get action value for email
            let actionValue = '';
            if (actionType === 'email') {
                actionValue = $rule.find('.dab-action-value input').val();
            }
            
            // Add rule if it has conditions and a target
            if (conditions.length > 0 && actionTarget) {
                const rule = {
                    target: actionTarget,
                    action: actionType,
                    logic: logicType,
                    conditions: conditions
                };
                
                // Add value for email action
                if (actionType === 'email' && actionValue) {
                    rule.value = actionValue;
                }
                
                rules.push(rule);
            }
        });
        
        // Update the JSON input
        $logicInput.val(JSON.stringify(rules));
    }
});
