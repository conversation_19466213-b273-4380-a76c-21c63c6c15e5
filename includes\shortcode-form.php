<?php
/**
 * Form shortcode handler
 *
 * Registers the [dab_form] shortcode and initializes the form builder
 */
if (!defined('ABSPATH')) exit;

// Include the form builder class
require_once plugin_dir_path(__FILE__) . 'class-form-builder.php';

// Initialize the form builder on init hook to prevent output during activation
function dab_init_form_builder() {
    global $dab_form_builder;
    $dab_form_builder = new DAB_Form_Builder();
}
add_action('init', 'dab_init_form_builder');

// The shortcode is registered in the form builder class constructor
// No need to register it here

/**
 * Function to process inline table data for backward compatibility
 *
 * @param int $table_id The ID of the child table
 * @param int $record_id The ID of the parent record
 * @param array $data The inline table data to process
 * @return bool Success or failure
 */
function dab_process_inline_table_data($table_id, $record_id, $data) {
    // For backward compatibility, call the method in DAB_Data_Manager
    if (!class_exists('DAB_Data_Manager')) {
        return false;
    }

    global $wpdb;
    $fields_table = $wpdb->prefix . 'dab_fields';

    // Find the inline table field for this table
    $field = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $fields_table WHERE field_type = 'inline_table' AND inline_table_id = %d",
        $table_id
    ));

    if (!$field) {
        return false;
    }

    // Convert data to JSON if it's an array
    if (is_array($data)) {
        $json_data = json_encode($data);
    } else {
        $json_data = $data;
    }

    // Process the inline table data
    return DAB_Data_Manager::process_inline_table_data($field, $json_data, $record_id);
}
